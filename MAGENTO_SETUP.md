# Magento 2 Dynamic Homepage Setup Guide

This guide explains how to set up the dynamic homepage functionality that fetches content from Magento 2 CMS using GraphQL and renders it with Page Builder support.

## 🚀 Features

- **Dynamic Homepage**: Automatically loads homepage content from Magento 2 CMS
- **Page Builder Support**: Renders Magento 2 Page Builder content as React components
- **Store Configuration**: Fetches homepage identifier and metadata from store config
- **Fallback Content**: Shows static content when <PERSON><PERSON><PERSON> is unavailable
- **SEO Optimization**: Dynamic metadata generation from store configuration
- **Responsive Design**: All components are mobile-optimized
- **Animated Loading**: Beautiful skeleton loading states

## 📋 Prerequisites

1. **Magento 2.4+** with GraphQL enabled
2. **Page Builder module** installed and enabled (optional but recommended)
3. **CORS configured** to allow requests from your Next.js domain
4. **CMS homepage** configured in Magento admin

## ⚙️ Environment Configuration

Create or update your `.env.local` file with the following variables:

```bash
# Magento 2 Configuration
NEXT_PUBLIC_MAGENTO_GRAPHQL_URL=https://your-magento-store.com/graphql
NEXT_PUBLIC_MAGENTO_BASE_URL=https://your-magento-store.com
NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL=https://your-magento-store.com/media
NEXT_PUBLIC_MAGENTO_BASE_STATIC_URL=https://your-magento-store.com/static

# Store Configuration
NEXT_PUBLIC_MAGENTO_STORE_CODE=default
NEXT_PUBLIC_MAGENTO_WEBSITE_CODE=base

# Optional: Access Token for Admin API
MAGENTO_ACCESS_TOKEN=your_admin_access_token

# Cache Configuration
NEXT_PUBLIC_DEFAULT_CACHE_TIME=3600

# Feature Flags
NEXT_PUBLIC_ENABLE_PAGE_BUILDER=true
NEXT_PUBLIC_ENABLE_DYNAMIC_HOMEPAGE=true
NEXT_PUBLIC_ENABLE_CMS_PAGES=true

# Development Settings
NEXT_PUBLIC_ENABLE_MOCK_DATA=false
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=true
```

## 🔧 Magento 2 Configuration

### 1. Enable GraphQL

Ensure GraphQL is enabled in your Magento 2 installation:

```bash
# In your Magento root directory
bin/magento module:enable Magento_GraphQl
bin/magento setup:upgrade
bin/magento cache:flush
```

### 2. Configure CORS

Add CORS headers to allow requests from your Next.js domain. Create or update `.htaccess` in your Magento root:

```apache
# Allow CORS for GraphQL
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "https://your-nextjs-domain.com"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, Store"
    Header always set Access-Control-Max-Age "86400"
</IfModule>
```

### 3. Set Homepage in Admin

1. Go to **Content > Configuration**
2. Select your store view
3. Set **Default Web URL** to your desired CMS page identifier
4. Save configuration

### 4. Create Homepage Content

#### Option A: Using Page Builder (Recommended)

1. Go to **Content > Pages**
2. Create or edit your homepage
3. Use Page Builder to create content with:
   - Rows and columns
   - Banners with background images
   - Text and heading elements
   - Buttons with links
   - Images and videos
   - Custom HTML blocks

#### Option B: Using HTML Editor

1. Create a CMS page with regular HTML content
2. The system will render it as a standard CMS page

## 🏗️ Implementation Details

### Homepage Loading Flow

```mermaid
graph TD
    A[User visits homepage] --> B[Fetch store configuration]
    B --> C[Get cms_home_page identifier]
    C --> D[Fetch CMS page content]
    D --> E{Has Page Builder content?}
    E -->|Yes| F[Parse Page Builder HTML]
    E -->|No| G[Render as standard CMS]
    F --> H[Render with Page Builder components]
    G --> I[Render with CMS components]
    H --> J[Display homepage]
    I --> J
    D -->|Error| K[Show fallback content]
    K --> J
```

### GraphQL Queries Used

#### Store Configuration
```graphql
query GetHomepageIdentifier {
  storeConfig {
    cms_home_page
    store_name
    default_title
    default_description
    base_media_url
  }
}
```

#### CMS Page Content
```graphql
query GetCmsPage($identifier: String!) {
  cmsPage(identifier: $identifier) {
    identifier
    title
    content
    content_heading
    meta_title
    meta_description
    meta_keywords
  }
}
```

## 🎨 Customization

### Custom Page Builder Elements

To add custom Page Builder elements:

1. **Create the component**:
```tsx
// src/components/pagebuilder/elements/CustomElement.tsx
export const CustomElement: React.FC<CustomElementProps> = ({ element }) => {
  return (
    <div className="custom-element">
      {/* Your custom implementation */}
    </div>
  );
};
```

2. **Add to component mapping**:
```tsx
// src/components/pagebuilder/PageBuilderRenderer.tsx
const COMPONENT_MAP = {
  // ... existing components
  [PageBuilderElementType.CUSTOM]: CustomElement,
};
```

3. **Update parser**:
```tsx
// src/components/pagebuilder/parser/ElementParser.ts
// Add parsing logic for your custom element
```

### Custom Fallback Content

Update the fallback content in `src/app/page.tsx`:

```tsx
function FallbackHomepage() {
  return (
    <main>
      {/* Your custom fallback content */}
    </main>
  );
}
```

## 🧪 Testing

### Test Dynamic Homepage

1. Visit `http://localhost:3000/dynamic-homepage-demo`
2. Check browser console for any GraphQL errors
3. Verify fallback content displays when Magento is unavailable

### Test Page Builder

1. Visit `http://localhost:3000/pagebuilder-demo`
2. See how Page Builder content is rendered
3. Test responsive behavior on different screen sizes

### Debug Mode

Enable debug logging in development:

```bash
NEXT_PUBLIC_ENABLE_DEBUG_LOGS=true
```

This will log:
- GraphQL queries and responses
- Page Builder parsing steps
- Cache hits and misses
- Error details

## 🚨 Troubleshooting

### Common Issues

#### 1. GraphQL Endpoint Not Accessible
```
Error: Failed to connect to Magento GraphQL endpoint
```
**Solution**: Check CORS configuration and network connectivity

#### 2. Homepage Identifier Not Found
```
Warning: No homepage identifier found in store configuration
```
**Solution**: Set default web URL in Magento admin

#### 3. CMS Page Not Found
```
Warning: Homepage CMS page not found: home
```
**Solution**: Create a CMS page with the correct identifier

#### 4. Page Builder Content Not Rendering
```
Info: Page Builder content detected but not rendering
```
**Solution**: Check Page Builder HTML structure and parser configuration

### Debug Commands

```bash
# Test GraphQL connection
curl -X POST https://your-magento-store.com/graphql \
  -H "Content-Type: application/json" \
  -H "Store: default" \
  -d '{"query": "{ storeConfig { cms_home_page } }"}'

# Check CMS page
curl -X POST https://your-magento-store.com/graphql \
  -H "Content-Type: application/json" \
  -H "Store: default" \
  -d '{"query": "query { cmsPage(identifier: \"home\") { title content } }"}'
```

## 📚 Additional Resources

- [Magento 2 GraphQL Documentation](https://devdocs.magento.com/guides/v2.4/graphql/)
- [Page Builder Developer Guide](https://devdocs.magento.com/page-builder/docs/)
- [Next.js Documentation](https://nextjs.org/docs)

## 🤝 Support

If you encounter issues:

1. Check the browser console for errors
2. Verify Magento GraphQL endpoint is accessible
3. Ensure CORS is properly configured
4. Check that the CMS homepage exists in Magento
5. Review the debug logs if enabled

For additional help, please refer to the troubleshooting section above or check the component documentation.
