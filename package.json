{"name": "next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.2.4", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.3", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "sharp": "^0.34.3", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.5", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.7", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}