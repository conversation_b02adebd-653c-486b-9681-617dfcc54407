// Image utilities for Magento media URL handling

// Image size configurations
export const IMAGE_SIZES = {
  // Product images
  product: {
    thumbnail: { width: 150, height: 150 },
    small: { width: 300, height: 300 },
    medium: { width: 600, height: 600 },
    large: { width: 1200, height: 1200 },
  },
  // Category images
  category: {
    thumbnail: { width: 200, height: 200 },
    small: { width: 400, height: 400 },
    medium: { width: 800, height: 600 },
    large: { width: 1200, height: 900 },
  },
  // Banner/Hero images
  banner: {
    mobile: { width: 768, height: 400 },
    tablet: { width: 1024, height: 500 },
    desktop: { width: 1920, height: 600 },
  },
  // Logo images
  logo: {
    small: { width: 120, height: 40 },
    medium: { width: 180, height: 60 },
    large: { width: 240, height: 80 },
  },
} as const;

// Image type definitions
export type ImageSize = keyof typeof IMAGE_SIZES;
export type ImageVariant<T extends ImageSize> = keyof typeof IMAGE_SIZES[T];

// Get base media URL from environment or store config
export function getBaseMediaUrl(storeConfig?: any): string {
  return (
    storeConfig?.base_media_url ||
    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL ||
    'https://your-magento-store.com/media'
  );
}

// Build Magento media URL
export function buildMediaUrl(
  imagePath: string,
  storeConfig?: any,
  type: 'product' | 'category' | 'logo' | 'cms' | 'banner' = 'product'
): string {
  if (!imagePath) return '';

  // If already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  const baseMediaUrl = getBaseMediaUrl(storeConfig);
  const cleanPath = imagePath.replace(/^\//, ''); // Remove leading slash

  // Build URL based on type
  switch (type) {
    case 'product':
      return `${baseMediaUrl}/catalog/product/${cleanPath}`;
    case 'category':
      return `${baseMediaUrl}/catalog/category/${cleanPath}`;
    case 'logo':
      return `${baseMediaUrl}/logo/${cleanPath}`;
    case 'cms':
      return `${baseMediaUrl}/wysiwyg/${cleanPath}`;
    case 'banner':
      return `${baseMediaUrl}/banner/${cleanPath}`;
    default:
      return `${baseMediaUrl}/${cleanPath}`;
  }
}

// Build product image URL with cache busting
export function buildProductImageUrl(
  imagePath: string,
  storeConfig?: any,
  variant: 'image' | 'small_image' | 'thumbnail' = 'image'
): string {
  if (!imagePath) return '';

  const baseUrl = buildMediaUrl(imagePath, storeConfig, 'product');
  
  // Add cache busting parameter
  const cacheBuster = new Date().getTime();
  const separator = baseUrl.includes('?') ? '&' : '?';
  
  return `${baseUrl}${separator}v=${cacheBuster}`;
}

// Build category image URL
export function buildCategoryImageUrl(
  imagePath: string,
  storeConfig?: any
): string {
  return buildMediaUrl(imagePath, storeConfig, 'category');
}

// Build logo URL
export function buildLogoUrl(
  imagePath: string,
  storeConfig?: any
): string {
  return buildMediaUrl(imagePath, storeConfig, 'logo');
}

// Build CMS/WYSIWYG image URL
export function buildCmsImageUrl(
  imagePath: string,
  storeConfig?: any
): string {
  return buildMediaUrl(imagePath, storeConfig, 'cms');
}

// Generate responsive image srcSet
export function generateSrcSet(
  imagePath: string,
  sizes: Array<{ width: number; height?: number }>,
  storeConfig?: any,
  type: 'product' | 'category' | 'banner' = 'product'
): string {
  if (!imagePath) return '';

  return sizes
    .map(size => {
      const url = buildMediaUrl(imagePath, storeConfig, type);
      return `${url} ${size.width}w`;
    })
    .join(', ');
}

// Generate responsive image sizes attribute
export function generateSizesAttribute(
  breakpoints: Array<{ minWidth?: number; maxWidth?: number; size: string }>
): string {
  return breakpoints
    .map(bp => {
      if (bp.minWidth && bp.maxWidth) {
        return `(min-width: ${bp.minWidth}px) and (max-width: ${bp.maxWidth}px) ${bp.size}`;
      } else if (bp.minWidth) {
        return `(min-width: ${bp.minWidth}px) ${bp.size}`;
      } else if (bp.maxWidth) {
        return `(max-width: ${bp.maxWidth}px) ${bp.size}`;
      } else {
        return bp.size;
      }
    })
    .join(', ');
}

// Get optimized image props for Next.js Image component
export function getOptimizedImageProps(
  imagePath: string,
  alt: string,
  size: ImageSize,
  variant: string,
  storeConfig?: any,
  type: 'product' | 'category' | 'banner' = 'product'
) {
  const imageConfig = IMAGE_SIZES[size]?.[variant as keyof typeof IMAGE_SIZES[typeof size]];
  
  if (!imageConfig) {
    throw new Error(`Invalid image size configuration: ${size}.${variant}`);
  }

  const src = buildMediaUrl(imagePath, storeConfig, type);
  
  return {
    src,
    alt,
    width: imageConfig.width,
    height: imageConfig.height,
    sizes: generateSizesAttribute([
      { maxWidth: 768, size: '100vw' },
      { minWidth: 769, size: `${imageConfig.width}px` },
    ]),
  };
}

// Extract image path from Magento URL
export function extractImagePath(fullUrl: string): string {
  if (!fullUrl) return '';

  // If it's already a path, return as is
  if (!fullUrl.startsWith('http')) {
    return fullUrl;
  }

  try {
    const url = new URL(fullUrl);
    const pathParts = url.pathname.split('/');
    
    // Find the media part and extract everything after it
    const mediaIndex = pathParts.findIndex(part => part === 'media');
    if (mediaIndex !== -1 && mediaIndex < pathParts.length - 1) {
      return pathParts.slice(mediaIndex + 1).join('/');
    }
    
    // Fallback: return the pathname without leading slash
    return url.pathname.replace(/^\//, '');
  } catch {
    return fullUrl;
  }
}

// Validate image URL
export function isValidImageUrl(url: string): boolean {
  if (!url) return false;
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Get image placeholder for loading states
export function getImagePlaceholder(
  width: number,
  height: number,
  text?: string
): string {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  // Fill with light gray background
  ctx.fillStyle = '#f5f5f5';
  ctx.fillRect(0, 0, width, height);
  
  // Add text if provided
  if (text) {
    ctx.fillStyle = '#999';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, width / 2, height / 2);
  }
  
  return canvas.toDataURL();
}

// Image error handling
export function handleImageError(
  event: React.SyntheticEvent<HTMLImageElement>,
  fallbackSrc?: string
): void {
  const img = event.currentTarget;
  
  if (fallbackSrc && img.src !== fallbackSrc) {
    img.src = fallbackSrc;
  } else {
    // Generate a placeholder
    const placeholder = getImagePlaceholder(
      img.width || 300,
      img.height || 300,
      'Image not found'
    );
    img.src = placeholder;
  }
}

// Preload critical images
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

// Preload multiple images
export async function preloadImages(srcs: string[]): Promise<void> {
  try {
    await Promise.all(srcs.map(src => preloadImage(src)));
  } catch (error) {
    console.warn('Some images failed to preload:', error);
  }
}

// Get image dimensions from URL (requires CORS)
export function getImageDimensions(src: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = reject;
    img.src = src;
  });
}
