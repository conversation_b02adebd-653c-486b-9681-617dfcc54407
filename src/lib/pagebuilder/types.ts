// Magento 2 Page Builder Types for Next.js

import { ReactNode } from 'react';

// Base Page Builder element interface
export interface PageBuilderElement {
  type: PageBuilderElementType;
  id: string;
  attributes: Record<string, any>;
  styles: Record<string, any>;
  children?: PageBuilderElement[];
  content?: string;
  rawHtml?: string;
}

// Page Builder element types
export enum PageBuilderElementType {
  ROW = 'row',
  COLUMN = 'column',
  TEXT = 'text',
  HEADING = 'heading',
  IMAGE = 'image',
  BUTTON = 'button',
  BANNER = 'banner',
  SLIDER = 'slider',
  PRODUCTS = 'products',
  VIDEO = 'video',
  MAP = 'map',
  BLOCK = 'block',
  HTML = 'html',
  DIVIDER = 'divider',
  TABS = 'tabs',
  TAB_ITEM = 'tab-item',
}

// Row element specific types
export interface RowElement extends PageBuilderElement {
  type: PageBuilderElementType.ROW;
  attributes: {
    appearance?: 'contained' | 'full-width' | 'full-bleed';
    enableParallax?: boolean;
    parallaxSpeed?: number;
    backgroundColor?: string;
    backgroundImage?: string;
    backgroundSize?: 'cover' | 'contain' | 'auto';
    backgroundPosition?: string;
    backgroundAttachment?: 'scroll' | 'fixed';
    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
    minHeight?: string;
    verticalAlignment?: 'top' | 'middle' | 'bottom';
  };
}

// Column element specific types
export interface ColumnElement extends PageBuilderElement {
  type: PageBuilderElementType.COLUMN;
  attributes: {
    width?: string;
    appearance?: 'full-height' | 'minimum-height';
    backgroundColor?: string;
    backgroundImage?: string;
    backgroundSize?: 'cover' | 'contain' | 'auto';
    backgroundPosition?: string;
    backgroundAttachment?: 'scroll' | 'fixed';
    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
    minHeight?: string;
    verticalAlignment?: 'top' | 'middle' | 'bottom';
  };
}

// Text element specific types
export interface TextElement extends PageBuilderElement {
  type: PageBuilderElementType.TEXT;
  content: string;
  attributes: {
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    border?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
  };
}

// Heading element specific types
export interface HeadingElement extends PageBuilderElement {
  type: PageBuilderElementType.HEADING;
  content: string;
  attributes: {
    headingType?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
    textAlign?: 'left' | 'center' | 'right';
    border?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
  };
}

// Image element specific types
export interface ImageElement extends PageBuilderElement {
  type: PageBuilderElementType.IMAGE;
  attributes: {
    src: string;
    alt?: string;
    title?: string;
    caption?: string;
    link?: string;
    linkTarget?: '_self' | '_blank';
    alignment?: 'left' | 'center' | 'right';
    border?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
    lazyLoading?: boolean;
  };
}

// Button element specific types
export interface ButtonElement extends PageBuilderElement {
  type: PageBuilderElementType.BUTTON;
  content: string;
  attributes: {
    buttonType?: 'primary' | 'secondary' | 'link';
    link?: string;
    linkTarget?: '_self' | '_blank';
    textAlign?: 'left' | 'center' | 'right';
    border?: string;
    borderColor?: string;
    borderWidth?: string;
    borderRadius?: string;
    backgroundColor?: string;
    textColor?: string;
  };
}

// Banner element specific types
export interface BannerElement extends PageBuilderElement {
  type: PageBuilderElementType.BANNER;
  attributes: {
    appearance?: 'poster' | 'collage-left' | 'collage-centered' | 'collage-right';
    minHeight?: string;
    backgroundColor?: string;
    backgroundImage?: string;
    backgroundSize?: 'cover' | 'contain' | 'auto';
    backgroundPosition?: string;
    backgroundAttachment?: 'scroll' | 'fixed';
    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
    showButton?: boolean;
    buttonText?: string;
    buttonType?: 'primary' | 'secondary' | 'link';
    buttonLink?: string;
    buttonTarget?: '_self' | '_blank';
    showOverlay?: boolean;
    overlayColor?: string;
    content?: string;
    contentPlacement?: 'left' | 'center' | 'right';
  };
}

// Slider element specific types
export interface SliderElement extends PageBuilderElement {
  type: PageBuilderElementType.SLIDER;
  attributes: {
    appearance?: 'default' | 'full-width';
    autoplay?: boolean;
    autoplaySpeed?: number;
    fade?: boolean;
    infiniteLoop?: boolean;
    showArrows?: boolean;
    showDots?: boolean;
    minHeight?: string;
  };
  children: BannerElement[];
}

// Products element specific types
export interface ProductsElement extends PageBuilderElement {
  type: PageBuilderElementType.PRODUCTS;
  attributes: {
    appearance?: 'grid' | 'carousel';
    selectType?: 'product' | 'sku' | 'condition';
    displayMode?: 'selected' | 'crossed' | 'related' | 'upsell';
    productsCount?: number;
    condition?: any[];
    sortOrder?: 'position' | 'name' | 'sku' | 'price' | 'created_at';
    carouselMode?: 'default' | 'continuous';
    autoplay?: boolean;
    autoplaySpeed?: number;
    infiniteLoop?: boolean;
    showArrows?: boolean;
    showDots?: boolean;
  };
}

// Video element specific types
export interface VideoElement extends PageBuilderElement {
  type: PageBuilderElementType.VIDEO;
  attributes: {
    videoType?: 'youtube' | 'vimeo' | 'mp4';
    videoUrl?: string;
    videoId?: string;
    maxWidth?: string;
    autoplay?: boolean;
    muted?: boolean;
    loop?: boolean;
    controls?: boolean;
    lazyLoading?: boolean;
    fallbackImage?: string;
  };
}

// Map element specific types
export interface MapElement extends PageBuilderElement {
  type: PageBuilderElementType.MAP;
  attributes: {
    height?: string;
    showControls?: boolean;
    locations?: Array<{
      position: {
        latitude: number;
        longitude: number;
      };
      name?: string;
      comment?: string;
      phoneNumber?: string;
      address?: string;
      city?: string;
      country?: string;
      postcode?: string;
    }>;
  };
}

// Block element specific types
export interface BlockElement extends PageBuilderElement {
  type: PageBuilderElementType.BLOCK;
  attributes: {
    blockId?: string;
    template?: string;
  };
}

// HTML element specific types
export interface HtmlElement extends PageBuilderElement {
  type: PageBuilderElementType.HTML;
  content: string;
}

// Divider element specific types
export interface DividerElement extends PageBuilderElement {
  type: PageBuilderElementType.DIVIDER;
  attributes: {
    lineColor?: string;
    lineThickness?: string;
    lineWidth?: string;
  };
}

// Tabs element specific types
export interface TabsElement extends PageBuilderElement {
  type: PageBuilderElementType.TABS;
  attributes: {
    defaultActiveTab?: number;
    tabsAlignment?: 'left' | 'center' | 'right';
    tabsNavigation?: 'tabs' | 'pills';
    minHeight?: string;
  };
  children: TabItemElement[];
}

// Tab item element specific types
export interface TabItemElement extends PageBuilderElement {
  type: PageBuilderElementType.TAB_ITEM;
  attributes: {
    tabName?: string;
    backgroundColor?: string;
    backgroundImage?: string;
    backgroundSize?: 'cover' | 'contain' | 'auto';
    backgroundPosition?: string;
    backgroundAttachment?: 'scroll' | 'fixed';
    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
    minHeight?: string;
    verticalAlignment?: 'top' | 'middle' | 'bottom';
  };
}

// Page Builder content structure
export interface PageBuilderContent {
  elements: PageBuilderElement[];
  rawHtml: string;
  version?: string;
}

// Parser configuration
export interface PageBuilderParserConfig {
  enableLazyLoading?: boolean;
  imageOptimization?: boolean;
  customElementParsers?: Record<string, (element: Element) => PageBuilderElement>;
  componentOverrides?: Record<PageBuilderElementType, React.ComponentType<any>>;
}

// Component props for Page Builder elements
export interface PageBuilderElementProps {
  element: PageBuilderElement;
  children?: ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

// Renderer context
export interface PageBuilderContext {
  config: PageBuilderParserConfig;
  isEditing?: boolean;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}
