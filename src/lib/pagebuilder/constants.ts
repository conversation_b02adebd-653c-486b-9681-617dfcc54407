// Magento 2 Page Builder Constants

import { PageBuilderElementType } from './types';

// Page Builder CSS class mappings
export const PAGE_BUILDER_CLASSES = {
  // Row classes
  ROW: 'pagebuilder-row',
  ROW_CONTAINED: 'pagebuilder-row-contained',
  ROW_FULL_WIDTH: 'pagebuilder-row-full-width',
  ROW_FULL_BLEED: 'pagebuilder-row-full-bleed',
  
  // Column classes
  COLUMN: 'pagebuilder-column',
  COLUMN_GROUP: 'pagebuilder-column-group',
  COLUMN_LINE: 'pagebuilder-column-line',
  
  // Content type classes
  TEXT: 'pagebuilder-text',
  HEADING: 'pagebuilder-heading',
  IMAGE: 'pagebuilder-image',
  BUTTON: 'pagebuilder-button-item',
  BANNER: 'pagebuilder-banner-item',
  SLIDER: 'pagebuilder-slider',
  PRODUCTS: 'pagebuilder-products',
  VIDEO: 'pagebuilder-video',
  MAP: 'pagebuilder-map',
  BLOCK: 'pagebuilder-block',
  HTML: 'pagebuilder-html-code',
  DIVIDER: 'pagebuilder-divider',
  TABS: 'pagebuilder-tabs',
  TAB_ITEM: 'pagebuilder-tab-item',
} as const;

// Page Builder data attributes
export const PAGE_BUILDER_ATTRIBUTES = {
  // Common attributes
  CONTENT_TYPE: 'data-content-type',
  APPEARANCE: 'data-appearance',
  ELEMENT: 'data-element',
  
  // Background attributes
  BACKGROUND_IMAGES: 'data-background-images',
  BACKGROUND_TYPE: 'data-background-type',
  VIDEO_LOOP: 'data-video-loop',
  VIDEO_PLAY_ONLY_VISIBLE: 'data-video-play-only-visible',
  VIDEO_LAZY_LOAD: 'data-video-lazy-load',
  VIDEO_FALLBACK_SRC: 'data-video-fallback-src',
  
  // Parallax attributes
  ENABLE_PARALLAX: 'data-enable-parallax',
  PARALLAX_SPEED: 'data-parallax-speed',
  
  // Banner attributes
  SHOW_BUTTON: 'data-show-button',
  SHOW_OVERLAY: 'data-show-overlay',
  
  // Slider attributes
  AUTOPLAY: 'data-autoplay',
  AUTOPLAY_SPEED: 'data-autoplay-speed',
  FADE: 'data-fade',
  INFINITE_LOOP: 'data-infinite-loop',
  SHOW_ARROWS: 'data-show-arrows',
  SHOW_DOTS: 'data-show-dots',
  
  // Products attributes
  PRODUCTS_COUNT: 'data-products-count',
  SORT_ORDER: 'data-sort-order',
  CAROUSEL_MODE: 'data-carousel-mode',
  
  // Map attributes
  SHOW_CONTROLS: 'data-show-controls',
  
  // Tabs attributes
  DEFAULT_ACTIVE_TAB: 'data-default-active-tab',
  TABS_ALIGNMENT: 'data-tabs-alignment',
  TABS_NAVIGATION: 'data-tabs-navigation',
} as const;

// Element type mapping from CSS classes to PageBuilderElementType
export const ELEMENT_TYPE_MAPPING: Record<string, PageBuilderElementType> = {
  [PAGE_BUILDER_CLASSES.ROW]: PageBuilderElementType.ROW,
  [PAGE_BUILDER_CLASSES.COLUMN]: PageBuilderElementType.COLUMN,
  [PAGE_BUILDER_CLASSES.TEXT]: PageBuilderElementType.TEXT,
  [PAGE_BUILDER_CLASSES.HEADING]: PageBuilderElementType.HEADING,
  [PAGE_BUILDER_CLASSES.IMAGE]: PageBuilderElementType.IMAGE,
  [PAGE_BUILDER_CLASSES.BUTTON]: PageBuilderElementType.BUTTON,
  [PAGE_BUILDER_CLASSES.BANNER]: PageBuilderElementType.BANNER,
  [PAGE_BUILDER_CLASSES.SLIDER]: PageBuilderElementType.SLIDER,
  [PAGE_BUILDER_CLASSES.PRODUCTS]: PageBuilderElementType.PRODUCTS,
  [PAGE_BUILDER_CLASSES.VIDEO]: PageBuilderElementType.VIDEO,
  [PAGE_BUILDER_CLASSES.MAP]: PageBuilderElementType.MAP,
  [PAGE_BUILDER_CLASSES.BLOCK]: PageBuilderElementType.BLOCK,
  [PAGE_BUILDER_CLASSES.HTML]: PageBuilderElementType.HTML,
  [PAGE_BUILDER_CLASSES.DIVIDER]: PageBuilderElementType.DIVIDER,
  [PAGE_BUILDER_CLASSES.TABS]: PageBuilderElementType.TABS,
  [PAGE_BUILDER_CLASSES.TAB_ITEM]: PageBuilderElementType.TAB_ITEM,
};

// Content type mapping from data-content-type attribute
export const CONTENT_TYPE_MAPPING: Record<string, PageBuilderElementType> = {
  'row': PageBuilderElementType.ROW,
  'column-group': PageBuilderElementType.COLUMN,
  'column': PageBuilderElementType.COLUMN,
  'text': PageBuilderElementType.TEXT,
  'heading': PageBuilderElementType.HEADING,
  'image': PageBuilderElementType.IMAGE,
  'button-item': PageBuilderElementType.BUTTON,
  'banner': PageBuilderElementType.BANNER,
  'slider': PageBuilderElementType.SLIDER,
  'products': PageBuilderElementType.PRODUCTS,
  'video': PageBuilderElementType.VIDEO,
  'map': PageBuilderElementType.MAP,
  'block': PageBuilderElementType.BLOCK,
  'html': PageBuilderElementType.HTML,
  'divider': PageBuilderElementType.DIVIDER,
  'tabs': PageBuilderElementType.TABS,
  'tab-item': PageBuilderElementType.TAB_ITEM,
};

// Default styles for Page Builder elements
export const DEFAULT_STYLES = {
  ROW: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    boxSizing: 'border-box',
  },
  COLUMN: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    boxSizing: 'border-box',
  },
  TEXT: {
    wordWrap: 'break-word',
  },
  HEADING: {
    wordWrap: 'break-word',
  },
  IMAGE: {
    maxWidth: '100%',
    height: 'auto',
  },
  BUTTON: {
    display: 'inline-block',
    textDecoration: 'none',
    cursor: 'pointer',
  },
  BANNER: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  SLIDER: {
    position: 'relative',
  },
  PRODUCTS: {
    display: 'flex',
    flexWrap: 'wrap',
  },
  VIDEO: {
    position: 'relative',
    width: '100%',
  },
  MAP: {
    width: '100%',
    height: '400px',
  },
  DIVIDER: {
    width: '100%',
    height: '1px',
    backgroundColor: '#e0e0e0',
  },
  TABS: {
    width: '100%',
  },
} as const;

// Responsive breakpoints (matching Magento's defaults)
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200,
} as const;

// Default configuration
export const DEFAULT_CONFIG = {
  enableLazyLoading: true,
  imageOptimization: true,
  customElementParsers: {},
  componentOverrides: {},
} as const;

// CSS property mappings for style parsing
export const CSS_PROPERTY_MAPPING = {
  'background-color': 'backgroundColor',
  'background-image': 'backgroundImage',
  'background-size': 'backgroundSize',
  'background-position': 'backgroundPosition',
  'background-repeat': 'backgroundRepeat',
  'background-attachment': 'backgroundAttachment',
  'min-height': 'minHeight',
  'text-align': 'textAlign',
  'vertical-align': 'verticalAlign',
  'border-color': 'borderColor',
  'border-width': 'borderWidth',
  'border-radius': 'borderRadius',
  'border-style': 'borderStyle',
  'margin-top': 'marginTop',
  'margin-right': 'marginRight',
  'margin-bottom': 'marginBottom',
  'margin-left': 'marginLeft',
  'padding-top': 'paddingTop',
  'padding-right': 'paddingRight',
  'padding-bottom': 'paddingBottom',
  'padding-left': 'paddingLeft',
} as const;

// Video providers
export const VIDEO_PROVIDERS = {
  YOUTUBE: 'youtube',
  VIMEO: 'vimeo',
  MP4: 'mp4',
} as const;

// Button types
export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  LINK: 'link',
} as const;

// Alignment options
export const ALIGNMENT_OPTIONS = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right',
  JUSTIFY: 'justify',
} as const;

// Vertical alignment options
export const VERTICAL_ALIGNMENT_OPTIONS = {
  TOP: 'top',
  MIDDLE: 'middle',
  BOTTOM: 'bottom',
} as const;

// Background size options
export const BACKGROUND_SIZE_OPTIONS = {
  COVER: 'cover',
  CONTAIN: 'contain',
  AUTO: 'auto',
} as const;

// Background repeat options
export const BACKGROUND_REPEAT_OPTIONS = {
  NO_REPEAT: 'no-repeat',
  REPEAT: 'repeat',
  REPEAT_X: 'repeat-x',
  REPEAT_Y: 'repeat-y',
} as const;

// Background attachment options
export const BACKGROUND_ATTACHMENT_OPTIONS = {
  SCROLL: 'scroll',
  FIXED: 'fixed',
} as const;
