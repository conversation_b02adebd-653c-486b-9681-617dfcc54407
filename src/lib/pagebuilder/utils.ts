// Magento 2 Page Builder Utilities

import { 
  PageBuilderElement, 
  PageBuilderElementType,
  PageBuilderParserConfig 
} from './types';
import { 
  ELEMENT_TYPE_MAPPING, 
  CONTENT_TYPE_MAPPING, 
  CSS_PROPERTY_MAPPING,
  DEFAULT_CONFIG 
} from './constants';

// Generate unique ID for Page Builder elements
export function generateElementId(): string {
  return `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Determine element type from DOM element
export function getElementType(element: Element): PageBuilderElementType | null {
  // Check data-content-type attribute first
  const contentType = element.getAttribute('data-content-type');
  if (contentType && CONTENT_TYPE_MAPPING[contentType]) {
    return CONTENT_TYPE_MAPPING[contentType];
  }

  // Check CSS classes
  const classList = Array.from(element.classList);
  for (const className of classList) {
    if (ELEMENT_TYPE_MAPPING[className]) {
      return ELEMENT_TYPE_MAPPING[className];
    }
  }

  // Check for specific element patterns
  if (element.tagName === 'IMG') {
    return PageBuilderElementType.IMAGE;
  }
  
  if (element.tagName === 'VIDEO' || element.tagName === 'IFRAME') {
    return PageBuilderElementType.VIDEO;
  }

  if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
    return PageBuilderElementType.HEADING;
  }

  if (element.tagName === 'A' && element.classList.contains('pagebuilder-button-primary')) {
    return PageBuilderElementType.BUTTON;
  }

  // Default to text for content elements
  if (element.textContent && element.textContent.trim()) {
    return PageBuilderElementType.TEXT;
  }

  return null;
}

// Extract attributes from DOM element
export function extractAttributes(element: Element): Record<string, any> {
  const attributes: Record<string, any> = {};

  // Extract data attributes
  Array.from(element.attributes).forEach(attr => {
    if (attr.name.startsWith('data-')) {
      const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      attributes[key] = attr.value;
    }
  });

  // Extract common HTML attributes
  if (element.getAttribute('id')) {
    attributes.id = element.getAttribute('id');
  }
  
  if (element.getAttribute('class')) {
    attributes.className = element.getAttribute('class');
  }

  // Extract specific attributes based on element type
  if (element.tagName === 'IMG') {
    attributes.src = element.getAttribute('src');
    attributes.alt = element.getAttribute('alt');
    attributes.title = element.getAttribute('title');
  }

  if (element.tagName === 'A') {
    attributes.href = element.getAttribute('href');
    attributes.target = element.getAttribute('target');
  }

  if (element.tagName === 'VIDEO') {
    attributes.src = element.getAttribute('src');
    attributes.autoplay = element.hasAttribute('autoplay');
    attributes.muted = element.hasAttribute('muted');
    attributes.loop = element.hasAttribute('loop');
    attributes.controls = element.hasAttribute('controls');
  }

  return attributes;
}

// Extract styles from DOM element
export function extractStyles(element: Element): Record<string, any> {
  const styles: Record<string, any> = {};
  
  // Get computed styles
  if (typeof window !== 'undefined') {
    const computedStyles = window.getComputedStyle(element);
    
    // Extract relevant CSS properties
    Object.entries(CSS_PROPERTY_MAPPING).forEach(([cssProperty, jsProperty]) => {
      const value = computedStyles.getPropertyValue(cssProperty);
      if (value && value !== 'initial' && value !== 'inherit') {
        styles[jsProperty] = value;
      }
    });
  }

  // Extract inline styles
  const inlineStyle = element.getAttribute('style');
  if (inlineStyle) {
    const styleDeclarations = inlineStyle.split(';');
    styleDeclarations.forEach(declaration => {
      const [property, value] = declaration.split(':').map(s => s.trim());
      if (property && value) {
        const jsProperty = CSS_PROPERTY_MAPPING[property as keyof typeof CSS_PROPERTY_MAPPING] || 
                          property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
        styles[jsProperty] = value;
      }
    });
  }

  return styles;
}

// Parse background images from data attribute
export function parseBackgroundImages(backgroundImagesData: string): string[] {
  try {
    const data = JSON.parse(backgroundImagesData);
    if (data && data.desktop_image) {
      return [data.desktop_image, data.mobile_image].filter(Boolean);
    }
    return [];
  } catch {
    return [];
  }
}

// Convert CSS value to number
export function cssValueToNumber(value: string): number {
  return parseFloat(value.replace(/[^\d.-]/g, ''));
}

// Convert number to CSS value with unit
export function numberToCssValue(value: number, unit: string = 'px'): string {
  return `${value}${unit}`;
}

// Check if element is a Page Builder element
export function isPageBuilderElement(element: Element): boolean {
  return element.hasAttribute('data-content-type') || 
         Array.from(element.classList).some(className => 
           className.startsWith('pagebuilder-')
         );
}

// Get element content (text or HTML)
export function getElementContent(element: Element, preserveHtml: boolean = false): string {
  if (preserveHtml) {
    return element.innerHTML;
  }
  return element.textContent || '';
}

// Clean HTML content
export function cleanHtmlContent(html: string): string {
  // Remove Magento-specific attributes that aren't needed in React
  return html
    .replace(/data-pb-style="[^"]*"/g, '')
    .replace(/data-element="[^"]*"/g, '')
    .replace(/class="[^"]*pagebuilder-[^"]*"/g, '')
    .trim();
}

// Merge configurations
export function mergeConfig(
  userConfig: Partial<PageBuilderParserConfig> = {}
): PageBuilderParserConfig {
  return {
    ...DEFAULT_CONFIG,
    ...userConfig,
    customElementParsers: {
      ...DEFAULT_CONFIG.customElementParsers,
      ...userConfig.customElementParsers,
    },
    componentOverrides: {
      ...DEFAULT_CONFIG.componentOverrides,
      ...userConfig.componentOverrides,
    },
  };
}

// Convert Magento URL to Next.js optimized URL
export function optimizeImageUrl(url: string, width?: number, height?: number): string {
  if (!url) return '';
  
  // If it's already a Next.js optimized URL, return as is
  if (url.includes('/_next/image')) {
    return url;
  }

  // Convert Magento media URLs
  if (url.includes('/media/')) {
    // Remove any existing resize parameters
    const cleanUrl = url.split('?')[0];
    
    // Add Next.js image optimization parameters
    const params = new URLSearchParams();
    params.set('url', cleanUrl);
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    params.set('q', '75'); // Default quality
    
    return `/_next/image?${params.toString()}`;
  }

  return url;
}

// Validate Page Builder element structure
export function validateElement(element: PageBuilderElement): boolean {
  if (!element.type || !element.id) {
    return false;
  }

  // Type-specific validation
  switch (element.type) {
    case PageBuilderElementType.IMAGE:
      return !!(element.attributes?.src);
    
    case PageBuilderElementType.BUTTON:
      return !!(element.content || element.attributes?.buttonText);
    
    case PageBuilderElementType.VIDEO:
      return !!(element.attributes?.videoUrl || element.attributes?.src);
    
    default:
      return true;
  }
}

// Get responsive breakpoint
export function getBreakpoint(width: number): 'mobile' | 'tablet' | 'desktop' {
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
}

// Convert Magento column width to CSS grid
export function convertColumnWidth(width: string): string {
  // Magento uses percentages like "33.3333%"
  const percentage = parseFloat(width);
  if (percentage > 0) {
    return `${percentage}%`;
  }
  
  // Fallback to equal distribution
  return '1fr';
}

// Parse JSON safely
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Check if code is running in browser
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

// Log Page Builder parsing errors in development
export function logParsingError(error: Error, element?: Element): void {
  if (process.env.NODE_ENV === 'development') {
    console.error('Page Builder parsing error:', error);
    if (element) {
      console.error('Element:', element);
    }
  }
}
