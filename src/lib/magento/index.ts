// Main Magento GraphQL API functions
import { magentoClient, MagentoGraphQLError } from './client';

// Re-export client and error for convenience
export { magentoClient, MagentoGraphQLError };

// Helper function for GraphQL queries with better error handling
export async function magentoGraphQLFetch<T>(
  query: string,
  variables?: Record<string, any>,
  options?: {
    cache?: boolean;
    revalidate?: number;
    headers?: Record<string, string>;
  }
): Promise<T> {
  try {
    return await magentoClient.fetch<T>(query, variables, options);
  } catch (error) {
    if (error instanceof MagentoGraphQLError) {
      // Log error for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.error('Magento GraphQL Error:', {
          message: error.message,
          errors: error.errors,
          query: query.substring(0, 100) + '...',
          variables,
        });
      }
      throw error;
    }
    throw error;
  }
}

// Helper for mutations (no caching)
export async function magentoGraphQLMutate<T>(
  mutation: string,
  variables?: Record<string, any>,
  options?: {
    headers?: Record<string, string>;
  }
): Promise<T> {
  return magentoGraphQLFetch<T>(mutation, variables, {
    ...options,
    cache: false,
  });
}

// Helper for queries with ISR (Incremental Static Regeneration)
export async function magentoGraphQLQuery<T>(
  query: string,
  variables?: Record<string, any>,
  revalidate: number = 3600 // 1 hour default
): Promise<T> {
  return magentoGraphQLFetch<T>(query, variables, {
    cache: true,
    revalidate,
  });
}

// Customer token management
export const customerAuth = {
  setToken: (token: string) => magentoClient.setCustomerToken(token),
  removeToken: () => magentoClient.removeCustomerToken(),
  clearCache: () => magentoClient.clearCache(),
};

// Cache management
export const cacheManager = {
  clear: () => magentoClient.clearCache(),
  clearEntry: (query: string, variables?: Record<string, any>) => 
    magentoClient.clearCacheEntry(query, variables),
};
