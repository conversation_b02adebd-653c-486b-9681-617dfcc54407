// CMS GraphQL Queries for Magento 2 (including Page Builder content)

// Fragment for CMS page
export const CMS_PAGE_FRAGMENT = `
  fragment CmsPageFragment on CmsPage {
    identifier
    url_key
    title
    content
    content_heading
    page_layout
    meta_title
    meta_description
    meta_keywords
    created_at
    updated_at
  }
`;

// Fragment for CMS block
export const CMS_BLOCK_FRAGMENT = `
  fragment CmsBlockFragment on CmsBlock {
    identifier
    title
    content
    created_at
    updated_at
  }
`;

// Get CMS page by identifier
export const GET_CMS_PAGE = `
  query GetCmsPage($identifier: String!) {
    cmsPage(identifier: $identifier) {
      ...CmsPageFragment
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;

// Get CMS page by URL key
export const GET_CMS_PAGE_BY_URL_KEY = `
  query GetCmsPageByUrlKey($urlKey: String!) {
    cmsPage(url_key: $urlKey) {
      ...CmsPageFragment
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;

// Get multiple CMS pages
export const GET_CMS_PAGES = `
  query GetCmsPages($identifiers: [String!]) {
    cmsPages(identifiers: $identifiers) {
      items {
        ...CmsPageFragment
      }
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;

// Get CMS block by identifier
export const GET_CMS_BLOCK = `
  query GetCmsBlock($identifier: String!) {
    cmsBlocks(identifiers: [$identifier]) {
      items {
        ...CmsBlockFragment
      }
    }
  }
  ${CMS_BLOCK_FRAGMENT}
`;

// Get multiple CMS blocks
export const GET_CMS_BLOCKS = `
  query GetCmsBlocks($identifiers: [String!]!) {
    cmsBlocks(identifiers: $identifiers) {
      items {
        ...CmsBlockFragment
      }
    }
  }
  ${CMS_BLOCK_FRAGMENT}
`;

// Get all CMS pages (for sitemap/navigation)
export const GET_ALL_CMS_PAGES = `
  query GetAllCmsPages {
    cmsPages {
      items {
        identifier
        url_key
        title
        meta_title
        meta_description
        created_at
        updated_at
      }
    }
  }
`;

// Search CMS pages
export const SEARCH_CMS_PAGES = `
  query SearchCmsPages($search: String!) {
    cmsPages(search: $search) {
      items {
        ...CmsPageFragment
      }
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;
