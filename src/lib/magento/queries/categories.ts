// Category GraphQL Queries for Magento 2

// Fragment for basic category information
export const CATEGORY_FRAGMENT = `
  fragment CategoryFragment on CategoryTree {
    id
    uid
    name
    url_key
    url_path
    url_suffix
    level
    position
    include_in_menu
    is_anchor
    product_count
    image
    description
    meta_title
    meta_keywords
    meta_description
    created_at
    updated_at
  }
`;

// Fragment for category with children
export const CATEGORY_TREE_FRAGMENT = `
  fragment CategoryTreeFragment on CategoryTree {
    ...CategoryFragment
    children {
      ...CategoryFragment
      children {
        ...CategoryFragment
      }
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// Get category tree (navigation menu)
export const GET_CATEGORY_TREE = `
  query GetCategoryTree {
    categoryList(filters: { ids: { eq: "2" } }) {
      ...CategoryTreeFragment
    }
  }
  ${CATEGORY_TREE_FRAGMENT}
`;

// Get category by URL key
export const GET_CATEGORY_BY_URL_KEY = `
  query GetCategoryByUrlKey($urlKey: String!) {
    categoryList(filters: { url_key: { eq: $urlKey } }) {
      ...CategoryFragment
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
      }
      children {
        ...CategoryFragment
      }
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// Get category by ID
export const GET_CATEGORY_BY_ID = `
  query GetCategoryById($id: String!) {
    categoryList(filters: { ids: { eq: $id } }) {
      ...CategoryFragment
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
      }
      children {
        ...CategoryFragment
      }
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// Get category products
export const GET_CATEGORY_PRODUCTS = `
  query GetCategoryProducts(
    $categoryId: String!
    $pageSize: Int = 20
    $currentPage: Int = 1
    $sort: ProductAttributeSortInput
    $filter: ProductAttributeFilterInput
  ) {
    categoryList(filters: { ids: { eq: $categoryId } }) {
      id
      uid
      name
      url_key
      description
      image
      meta_title
      meta_description
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
      }
      products(
        pageSize: $pageSize
        currentPage: $currentPage
        sort: $sort
        filter: $filter
      ) {
        total_count
        page_info {
          page_size
          current_page
          total_pages
        }
        items {
          id
          uid
          name
          sku
          url_key
          url_suffix
          type_id
          stock_status
          only_x_left_in_stock
          rating_summary
          review_count
          price_range {
            minimum_price {
              regular_price {
                value
                currency
              }
              final_price {
                value
                currency
              }
              discount {
                amount_off
                percent_off
              }
            }
          }
          image {
            url
            label
          }
          small_image {
            url
            label
          }
          short_description {
            html
          }
        }
        aggregations {
          attribute_code
          count
          label
          options {
            count
            label
            value
          }
        }
      }
    }
  }
`;

// Get main navigation categories
export const GET_NAVIGATION_CATEGORIES = `
  query GetNavigationCategories {
    categoryList(filters: { parent_id: { eq: "2" }, include_in_menu: { eq: true } }) {
      ...CategoryFragment
      children(filters: { include_in_menu: { eq: true } }) {
        ...CategoryFragment
        children(filters: { include_in_menu: { eq: true } }) {
          ...CategoryFragment
        }
      }
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// Get featured categories (customize filter as needed)
export const GET_FEATURED_CATEGORIES = `
  query GetFeaturedCategories {
    categoryList(
      filters: { 
        parent_id: { eq: "2" }
        include_in_menu: { eq: true }
        is_anchor: { eq: true }
      }
    ) {
      ...CategoryFragment
    }
  }
  ${CATEGORY_FRAGMENT}
`;
