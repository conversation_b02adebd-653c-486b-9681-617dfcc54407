// Store Configuration GraphQL Queries for Magento 2

// Fragment for store configuration
export const STORE_CONFIG_FRAGMENT = `
  fragment StoreConfigFragment on StoreConfig {
    id
    code
    website_id
    locale
    base_currency_code
    default_display_currency_code
    timezone
    weight_unit
    base_url
    base_link_url
    base_static_url
    base_media_url
    secure_base_url
    secure_base_link_url
    secure_base_static_url
    secure_base_media_url
    store_name
    store_sort_order
    is_default_store
    store_group_code
    store_group_name
    is_default_store_group
    website_code
    website_name
    is_default_website
    root_category_id
    root_category_uid
    category_url_suffix
    product_url_suffix
    cms_home_page
    cms_no_route
    cms_no_cookies
    show_cms_breadcrumbs
    catalog_default_sort_by
    grid_per_page
    list_per_page
    grid_per_page_values
    list_per_page_values
    minimum_password_length
    required_character_classes_number
    default_title
    title_prefix
    title_suffix
    default_description
    default_keywords
    head_shortcut_icon
    head_includes
    header_logo_src
    logo_width
    logo_height
    logo_alt
    welcome
    copyright
    absolute_footer
    front
    no_route
    enable_multiple_wishlists
    send_friend {
      enabled_for_customers
      enabled_for_guests
    }
    magento_wishlist_general_is_enabled
    magento_reward_general_is_enabled
    magento_reward_general_is_enabled_on_front
    magento_reward_general_min_points_balance
    magento_reward_general_max_points_balance
    magento_cataloginventory_item_options_manage_stock
    magento_cataloginventory_item_options_backorders
    magento_cataloginventory_item_options_max_sale_qty
    magento_cataloginventory_item_options_min_sale_qty
    magento_cataloginventory_item_options_min_qty
    magento_cataloginventory_item_options_notify_stock_qty
    magento_cataloginventory_item_options_enable_qty_increments
    magento_cataloginventory_item_options_qty_increments
    magento_cataloginventory_item_options_auto_return
    payment_payflowpro_cc_vault_active
    braintree_cc_vault_active
    braintree_paypal_vault_active
    klarna_payments_enabled
    klarna_payments_theme
    klarna_payments_shape
    klarna_payments_color
    klarna_payments_logo
    klarna_payments_data_sharing_onload
    klarna_payments_data_sharing_onclick
    klarna_payments_merchant_name
    klarna_payments_merchant_url
    klarna_payments_terms_url
    klarna_payments_cancellation_terms_url
    klarna_payments_shipping_details
    autocomplete_on_storefront
    optional_zip_countries
    eu_countries_list
    top_destinations
    countries_with_required_region
    allow_guests_to_write_product_reviews
    is_negotiable_quote_active
    is_requisition_list_active
    magento_catalogpermissions_enabled
    magento_wishlist_general_is_enabled
    magento_rma_enabled
    magento_rma_enabled_on_product
    magento_rma_use_store_address
    configurable_thumbnail_source
    category_fixed_product_tax_display_setting
    product_fixed_product_tax_display_setting
    sales_fixed_product_tax_display_setting
    shopping_cart_display_full_summary
    shopping_cart_display_grand_total
    shopping_cart_display_price
    shopping_cart_display_shipping
    shopping_cart_display_subtotal
    shopping_cart_display_tax_gift_wrapping
    shopping_cart_display_zero_tax
    sales_display_full_summary
    sales_display_grand_total
    sales_display_price
    sales_display_shipping
    sales_display_subtotal
    sales_display_tax_gift_wrapping
    sales_display_zero_tax
  }
`;

// Get store configuration
export const GET_STORE_CONFIG = `
  query GetStoreConfig {
    storeConfig {
      ...StoreConfigFragment
    }
  }
  ${STORE_CONFIG_FRAGMENT}
`;

// Get specific store configuration fields
export const GET_STORE_CONFIG_BASIC = `
  query GetStoreConfigBasic {
    storeConfig {
      id
      code
      store_name
      locale
      base_currency_code
      default_display_currency_code
      timezone
      base_url
      base_media_url
      cms_home_page
      cms_no_route
      default_title
      default_description
      default_keywords
      header_logo_src
      logo_alt
      welcome
      copyright
    }
  }
`;

// Get homepage identifier from store config
export const GET_HOMEPAGE_IDENTIFIER = `
  query GetHomepageIdentifier {
    storeConfig {
      cms_home_page
      store_name
      default_title
    }
  }
`;

// Get store configuration for SEO
export const GET_STORE_SEO_CONFIG = `
  query GetStoreSeoConfig {
    storeConfig {
      default_title
      title_prefix
      title_suffix
      default_description
      default_keywords
      head_shortcut_icon
      head_includes
    }
  }
`;

// Get store configuration for branding
export const GET_STORE_BRANDING_CONFIG = `
  query GetStoreBrandingConfig {
    storeConfig {
      store_name
      header_logo_src
      logo_width
      logo_height
      logo_alt
      welcome
      copyright
      absolute_footer
    }
  }
`;

// Get store configuration for catalog
export const GET_STORE_CATALOG_CONFIG = `
  query GetStoreCatalogConfig {
    storeConfig {
      root_category_id
      root_category_uid
      category_url_suffix
      product_url_suffix
      catalog_default_sort_by
      grid_per_page
      list_per_page
      grid_per_page_values
      list_per_page_values
      configurable_thumbnail_source
    }
  }
`;

// Get store configuration for checkout
export const GET_STORE_CHECKOUT_CONFIG = `
  query GetStoreCheckoutConfig {
    storeConfig {
      minimum_password_length
      required_character_classes_number
      autocomplete_on_storefront
      optional_zip_countries
      allow_guests_to_write_product_reviews
      magento_cataloginventory_item_options_manage_stock
      magento_cataloginventory_item_options_backorders
      payment_payflowpro_cc_vault_active
      braintree_cc_vault_active
      braintree_paypal_vault_active
    }
  }
`;
