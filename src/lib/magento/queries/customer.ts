// Customer GraphQL Queries and Mutations for Magento 2

// Fragment for customer information
export const CUSTOMER_FRAGMENT = `
  fragment CustomerFragment on Customer {
    id
    firstname
    lastname
    email
    is_subscribed
    date_of_birth
    gender
    created_at
    updated_at
    addresses {
      id
      firstname
      lastname
      company
      street
      city
      region {
        region_code
        region
        region_id
      }
      postcode
      country_code
      telephone
      default_shipping
      default_billing
    }
  }
`;

// Generate customer token (login)
export const GENERATE_CUSTOMER_TOKEN = `
  mutation GenerateCustomerToken($email: String!, $password: String!) {
    generateCustomerToken(email: $email, password: $password) {
      token
    }
  }
`;

// Create customer account
export const CREATE_CUSTOMER = `
  mutation CreateCustomer($input: CustomerInput!) {
    createCustomer(input: $input) {
      customer {
        ...CustomerFragment
      }
    }
  }
  ${CUSTOMER_FRAGMENT}
`;

// Get customer information
export const GET_CUSTOMER = `
  query GetCustomer {
    customer {
      ...CustomerFragment
    }
  }
  ${CUSTOMER_FRAGMENT}
`;

// Update customer information
export const UPDATE_CUSTOMER = `
  mutation UpdateCustomer($input: CustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        ...CustomerFragment
      }
    }
  }
  ${CUSTOMER_FRAGMENT}
`;

// Change customer password
export const CHANGE_CUSTOMER_PASSWORD = `
  mutation ChangeCustomerPassword($currentPassword: String!, $newPassword: String!) {
    changeCustomerPassword(currentPassword: $currentPassword, newPassword: $newPassword) {
      id
      email
    }
  }
`;

// Request password reset email
export const REQUEST_PASSWORD_RESET_EMAIL = `
  mutation RequestPasswordResetEmail($email: String!) {
    requestPasswordResetEmail(email: $email)
  }
`;

// Reset customer password
export const RESET_PASSWORD = `
  mutation ResetPassword($email: String!, $resetPasswordToken: String!, $newPassword: String!) {
    resetPassword(email: $email, resetPasswordToken: $resetPasswordToken, newPassword: $newPassword)
  }
`;

// Revoke customer token (logout)
export const REVOKE_CUSTOMER_TOKEN = `
  mutation RevokeCustomerToken {
    revokeCustomerToken {
      result
    }
  }
`;

// Create customer address
export const CREATE_CUSTOMER_ADDRESS = `
  mutation CreateCustomerAddress($input: CustomerAddressInput!) {
    createCustomerAddress(input: $input) {
      id
      firstname
      lastname
      company
      street
      city
      region {
        region_code
        region
        region_id
      }
      postcode
      country_code
      telephone
      default_shipping
      default_billing
    }
  }
`;

// Update customer address
export const UPDATE_CUSTOMER_ADDRESS = `
  mutation UpdateCustomerAddress($id: Int!, $input: CustomerAddressInput!) {
    updateCustomerAddress(id: $id, input: $input) {
      id
      firstname
      lastname
      company
      street
      city
      region {
        region_code
        region
        region_id
      }
      postcode
      country_code
      telephone
      default_shipping
      default_billing
    }
  }
`;

// Delete customer address
export const DELETE_CUSTOMER_ADDRESS = `
  mutation DeleteCustomerAddress($id: Int!) {
    deleteCustomerAddress(id: $id)
  }
`;

// Get customer orders
export const GET_CUSTOMER_ORDERS = `
  query GetCustomerOrders($filter: CustomerOrdersFilterInput, $pageSize: Int = 20, $currentPage: Int = 1) {
    customer {
      orders(filter: $filter, pageSize: $pageSize, currentPage: $currentPage) {
        total_count
        page_info {
          page_size
          current_page
          total_pages
        }
        items {
          id
          order_date
          status
          number
          grand_total {
            value
            currency
          }
          items {
            id
            product_name
            product_sku
            product_url_key
            product_sale_price {
              value
              currency
            }
            quantity_ordered
            quantity_shipped
            quantity_invoiced
            quantity_refunded
            quantity_canceled
            quantity_returned
          }
          shipping_address {
            firstname
            lastname
            company
            street
            city
            region
            postcode
            country_code
            telephone
          }
          billing_address {
            firstname
            lastname
            company
            street
            city
            region
            postcode
            country_code
            telephone
          }
          payment_methods {
            name
            type
          }
          shipping_method
          shipments {
            id
            number
            tracking {
              title
              number
              carrier
            }
            items {
              id
              product_name
              product_sku
              quantity_shipped
            }
          }
          invoices {
            id
            number
            grand_total {
              value
              currency
            }
            items {
              id
              product_name
              product_sku
              quantity_invoiced
            }
          }
        }
      }
    }
  }
`;

// Subscribe to newsletter
export const SUBSCRIBE_EMAIL_TO_NEWSLETTER = `
  mutation SubscribeEmailToNewsletter($email: String!) {
    subscribeEmailToNewsletter(email: $email) {
      status
    }
  }
`;

// Get countries for address forms
export const GET_COUNTRIES = `
  query GetCountries {
    countries {
      id
      two_letter_abbreviation
      three_letter_abbreviation
      full_name_locale
      full_name_english
      available_regions {
        id
        code
        name
      }
    }
  }
`;
