// Cart GraphQL Queries and Mutations for Magento 2

// Fragment for cart item
export const CART_ITEM_FRAGMENT = `
  fragment CartItemFragment on CartItemInterface {
    id
    uid
    quantity
    prices {
      price {
        value
        currency
      }
      row_total {
        value
        currency
      }
      row_total_including_tax {
        value
        currency
      }
      total_item_discount {
        value
        currency
      }
    }
    product {
      id
      uid
      name
      sku
      url_key
      url_suffix
      stock_status
      only_x_left_in_stock
      thumbnail {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
        }
      }
    }
    ... on SimpleCartItem {
      customizable_options {
        id
        label
        type
        values {
          id
          label
          value
        }
      }
    }
    ... on ConfigurableCartItem {
      configurable_options {
        id
        option_label
        value_id
        value_label
      }
    }
    ... on BundleCartItem {
      bundle_options {
        id
        label
        type
        values {
          id
          label
          price
          quantity
        }
      }
    }
  }
`;

// Fragment for cart
export const CART_FRAGMENT = `
  fragment CartFragment on Cart {
    id
    email
    is_virtual
    applied_coupons {
      code
    }
    items {
      ...CartItemFragment
    }
    available_payment_methods {
      code
      title
    }
    selected_payment_method {
      code
      title
    }
    shipping_addresses {
      firstname
      lastname
      company
      street
      city
      region {
        code
        label
      }
      postcode
      country {
        code
        label
      }
      telephone
      available_shipping_methods {
        carrier_code
        carrier_title
        method_code
        method_title
        amount {
          value
          currency
        }
        price_excl_tax {
          value
          currency
        }
        price_incl_tax {
          value
          currency
        }
      }
      selected_shipping_method {
        carrier_code
        carrier_title
        method_code
        method_title
        amount {
          value
          currency
        }
      }
    }
    billing_address {
      firstname
      lastname
      company
      street
      city
      region {
        code
        label
      }
      postcode
      country {
        code
        label
      }
      telephone
    }
    prices {
      grand_total {
        value
        currency
      }
      subtotal_excluding_tax {
        value
        currency
      }
      subtotal_including_tax {
        value
        currency
      }
      applied_taxes {
        label
        amount {
          value
          currency
        }
      }
      discounts {
        amount {
          value
          currency
        }
        label
      }
    }
  }
  ${CART_ITEM_FRAGMENT}
`;

// Create empty cart
export const CREATE_EMPTY_CART = `
  mutation CreateEmptyCart {
    createEmptyCart
  }
`;

// Get cart
export const GET_CART = `
  query GetCart($cartId: String!) {
    cart(cart_id: $cartId) {
      ...CartFragment
    }
  }
  ${CART_FRAGMENT}
`;

// Add simple product to cart
export const ADD_SIMPLE_PRODUCT_TO_CART = `
  mutation AddSimpleProductToCart($cartId: String!, $cartItems: [SimpleProductCartItemInput!]!) {
    addSimpleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Add configurable product to cart
export const ADD_CONFIGURABLE_PRODUCT_TO_CART = `
  mutation AddConfigurableProductToCart($cartId: String!, $cartItems: [ConfigurableProductCartItemInput!]!) {
    addConfigurableProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Update cart items
export const UPDATE_CART_ITEMS = `
  mutation UpdateCartItems($cartId: String!, $cartItems: [CartItemUpdateInput!]!) {
    updateCartItems(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Remove item from cart
export const REMOVE_ITEM_FROM_CART = `
  mutation RemoveItemFromCart($cartId: String!, $cartItemId: Int!) {
    removeItemFromCart(input: { cart_id: $cartId, cart_item_id: $cartItemId }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Apply coupon to cart
export const APPLY_COUPON_TO_CART = `
  mutation ApplyCouponToCart($cartId: String!, $couponCode: String!) {
    applyCouponToCart(input: { cart_id: $cartId, coupon_code: $couponCode }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Remove coupon from cart
export const REMOVE_COUPON_FROM_CART = `
  mutation RemoveCouponFromCart($cartId: String!) {
    removeCouponFromCart(input: { cart_id: $cartId }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Set shipping address
export const SET_SHIPPING_ADDRESS_ON_CART = `
  mutation SetShippingAddressOnCart($cartId: String!, $shippingAddresses: [ShippingAddressInput!]!) {
    setShippingAddressesOnCart(input: { cart_id: $cartId, shipping_addresses: $shippingAddresses }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Set billing address
export const SET_BILLING_ADDRESS_ON_CART = `
  mutation SetBillingAddressOnCart($cartId: String!, $billingAddress: BillingAddressInput!) {
    setBillingAddressOnCart(input: { cart_id: $cartId, billing_address: $billingAddress }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Set shipping method
export const SET_SHIPPING_METHOD_ON_CART = `
  mutation SetShippingMethodOnCart($cartId: String!, $shippingMethods: [ShippingMethodInput!]!) {
    setShippingMethodsOnCart(input: { cart_id: $cartId, shipping_methods: $shippingMethods }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Set payment method
export const SET_PAYMENT_METHOD_ON_CART = `
  mutation SetPaymentMethodOnCart($cartId: String!, $paymentMethod: PaymentMethodInput!) {
    setPaymentMethodOnCart(input: { cart_id: $cartId, payment_method: $paymentMethod }) {
      cart {
        ...CartFragment
      }
    }
  }
  ${CART_FRAGMENT}
`;

// Place order
export const PLACE_ORDER = `
  mutation PlaceOrder($cartId: String!) {
    placeOrder(input: { cart_id: $cartId }) {
      order {
        order_number
        order_id
      }
    }
  }
`;
