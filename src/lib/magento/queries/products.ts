// Product GraphQL Queries for Magento 2

// Fragment for basic product information
export const PRODUCT_FRAGMENT = `
  fragment ProductFragment on ProductInterface {
    id
    uid
    name
    sku
    url_key
    url_suffix
    type_id
    stock_status
    only_x_left_in_stock
    rating_summary
    review_count
    price_range {
      minimum_price {
        regular_price {
          value
          currency
        }
        final_price {
          value
          currency
        }
        discount {
          amount_off
          percent_off
        }
      }
      maximum_price {
        regular_price {
          value
          currency
        }
        final_price {
          value
          currency
        }
        discount {
          amount_off
          percent_off
        }
      }
    }
    image {
      url
      label
    }
    small_image {
      url
      label
    }
    thumbnail {
      url
      label
    }
    short_description {
      html
    }
    meta_title
    meta_description
    created_at
    updated_at
  }
`;

// Fragment for detailed product information
export const PRODUCT_DETAIL_FRAGMENT = `
  fragment ProductDetailFragment on ProductInterface {
    ...ProductFragment
    description {
      html
    }
    media_gallery {
      url
      label
      position
      disabled
    }
    categories {
      id
      uid
      name
      url_key
      url_path
      level
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
      }
    }
    attributes {
      attribute_code
      label
      value
    }
    ... on ConfigurableProduct {
      configurable_options {
        id
        attribute_id
        label
        position
        use_default
        attribute_code
        values {
          value_index
          label
          store_label
          default_label
          use_default_value
        }
        product_id
      }
      variants {
        product {
          id
          uid
          name
          sku
          stock_status
          price_range {
            minimum_price {
              regular_price {
                value
                currency
              }
              final_price {
                value
                currency
              }
            }
          }
          image {
            url
            label
          }
          attributes {
            attribute_code
            label
            value
          }
        }
        attributes {
          code
          value_index
          label
        }
      }
    }
    ... on SimpleProduct {
      weight
    }
    ... on BundleProduct {
      items {
        option_id
        title
        required
        type
        position
        sku
        options {
          id
          uid
          label
          quantity
          position
          is_default
          price
          price_type
          can_change_quantity
          product {
            id
            uid
            name
            sku
            price_range {
              minimum_price {
                regular_price {
                  value
                  currency
                }
                final_price {
                  value
                  currency
                }
              }
            }
          }
        }
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;

// Get products with filters and pagination
export const GET_PRODUCTS = `
  query GetProducts(
    $filter: ProductAttributeFilterInput
    $sort: ProductAttributeSortInput
    $pageSize: Int = 20
    $currentPage: Int = 1
  ) {
    products(
      filter: $filter
      sort: $sort
      pageSize: $pageSize
      currentPage: $currentPage
    ) {
      total_count
      page_info {
        page_size
        current_page
        total_pages
      }
      items {
        ...ProductFragment
      }
      aggregations {
        attribute_code
        count
        label
        options {
          count
          label
          value
        }
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;

// Get single product by URL key
export const GET_PRODUCT_BY_URL_KEY = `
  query GetProductByUrlKey($urlKey: String!) {
    products(filter: { url_key: { eq: $urlKey } }) {
      items {
        ...ProductDetailFragment
        related_products {
          ...ProductFragment
        }
        upsell_products {
          ...ProductFragment
        }
        crosssell_products {
          ...ProductFragment
        }
      }
    }
  }
  ${PRODUCT_DETAIL_FRAGMENT}
`;

// Get product by SKU
export const GET_PRODUCT_BY_SKU = `
  query GetProductBySku($sku: String!) {
    products(filter: { sku: { eq: $sku } }) {
      items {
        ...ProductDetailFragment
      }
    }
  }
  ${PRODUCT_DETAIL_FRAGMENT}
`;

// Search products
export const SEARCH_PRODUCTS = `
  query SearchProducts(
    $search: String!
    $filter: ProductAttributeFilterInput
    $sort: ProductAttributeSortInput
    $pageSize: Int = 20
    $currentPage: Int = 1
  ) {
    products(
      search: $search
      filter: $filter
      sort: $sort
      pageSize: $pageSize
      currentPage: $currentPage
    ) {
      total_count
      page_info {
        page_size
        current_page
        total_pages
      }
      items {
        ...ProductFragment
      }
      aggregations {
        attribute_code
        count
        label
        options {
          count
          label
          value
        }
      }
      suggestions {
        search
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;

// Get featured products (you can customize the filter)
export const GET_FEATURED_PRODUCTS = `
  query GetFeaturedProducts($pageSize: Int = 8) {
    products(
      filter: { featured: { eq: "1" } }
      pageSize: $pageSize
      currentPage: 1
    ) {
      items {
        ...ProductFragment
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;

// Get new products
export const GET_NEW_PRODUCTS = `
  query GetNewProducts($pageSize: Int = 8) {
    products(
      filter: { news_from_date: { gteq: "2024-01-01" } }
      sort: { created_at: DESC }
      pageSize: $pageSize
      currentPage: 1
    ) {
      items {
        ...ProductFragment
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;

// Get best selling products
export const GET_BESTSELLER_PRODUCTS = `
  query GetBestsellerProducts($pageSize: Int = 8) {
    products(
      filter: { bestseller: { eq: "1" } }
      pageSize: $pageSize
      currentPage: 1
    ) {
      items {
        ...ProductFragment
      }
    }
  }
  ${PRODUCT_FRAGMENT}
`;
