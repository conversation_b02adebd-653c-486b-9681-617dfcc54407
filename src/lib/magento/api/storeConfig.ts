// Store Configuration API functions using Magento GraphQL

import { serverGraphQL, gql } from '@/lib/graphql/simpleClient';
import {
  GET_STORE_CONFIG_BASIC,
  GET_HOMEPAGE_IDENTIFIER,
} from '@/lib/graphql/queries';

// Store Configuration interface
export interface StoreConfig {
  id: number;
  code: string;
  website_id: number;
  locale: string;
  base_currency_code: string;
  default_display_currency_code: string;
  timezone: string;
  weight_unit: string;
  base_url: string;
  base_link_url: string;
  base_static_url: string;
  base_media_url: string;
  secure_base_url: string;
  secure_base_link_url: string;
  secure_base_static_url: string;
  secure_base_media_url: string;
  store_name: string;
  store_sort_order: number;
  is_default_store: boolean;
  store_group_code: string;
  store_group_name: string;
  is_default_store_group: boolean;
  website_code: string;
  website_name: string;
  is_default_website: boolean;
  root_category_id: number;
  root_category_uid: string;
  category_url_suffix: string;
  product_url_suffix: string;
  cms_home_page: string;
  cms_no_route: string;
  cms_no_cookies: string;
  show_cms_breadcrumbs: boolean;
  catalog_default_sort_by: string;
  grid_per_page: number;
  list_per_page: number;
  grid_per_page_values: string;
  list_per_page_values: string;
  minimum_password_length: number;
  required_character_classes_number: number;
  default_title: string;
  title_prefix: string;
  title_suffix: string;
  default_description: string;
  default_keywords: string;
  head_shortcut_icon: string;
  head_includes: string;
  header_logo_src: string;
  logo_width: number;
  logo_height: number;
  logo_alt: string;
  welcome: string;
  copyright: string;
  absolute_footer: string;
  front: string;
  no_route: string;
  enable_multiple_wishlists: boolean;
  send_friend: {
    enabled_for_customers: boolean;
    enabled_for_guests: boolean;
  };
  magento_wishlist_general_is_enabled: boolean;
  magento_reward_general_is_enabled: boolean;
  magento_reward_general_is_enabled_on_front: boolean;
  magento_reward_general_min_points_balance: number;
  magento_reward_general_max_points_balance: number;
  magento_cataloginventory_item_options_manage_stock: boolean;
  magento_cataloginventory_item_options_backorders: number;
  magento_cataloginventory_item_options_max_sale_qty: number;
  magento_cataloginventory_item_options_min_sale_qty: number;
  magento_cataloginventory_item_options_min_qty: number;
  magento_cataloginventory_item_options_notify_stock_qty: number;
  magento_cataloginventory_item_options_enable_qty_increments: boolean;
  magento_cataloginventory_item_options_qty_increments: number;
  magento_cataloginventory_item_options_auto_return: boolean;
  payment_payflowpro_cc_vault_active: boolean;
  braintree_cc_vault_active: boolean;
  braintree_paypal_vault_active: boolean;
  autocomplete_on_storefront: boolean;
  optional_zip_countries: string;
  eu_countries_list: string;
  top_destinations: string;
  countries_with_required_region: string;
  allow_guests_to_write_product_reviews: boolean;
  is_negotiable_quote_active: boolean;
  is_requisition_list_active: boolean;
  magento_catalogpermissions_enabled: boolean;
  magento_rma_enabled: boolean;
  magento_rma_enabled_on_product: boolean;
  magento_rma_use_store_address: boolean;
  configurable_thumbnail_source: string;
  category_fixed_product_tax_display_setting: number;
  product_fixed_product_tax_display_setting: number;
  sales_fixed_product_tax_display_setting: number;
  shopping_cart_display_full_summary: boolean;
  shopping_cart_display_grand_total: boolean;
  shopping_cart_display_price: number;
  shopping_cart_display_shipping: number;
  shopping_cart_display_subtotal: boolean;
  shopping_cart_display_tax_gift_wrapping: number;
  shopping_cart_display_zero_tax: boolean;
  sales_display_full_summary: boolean;
  sales_display_grand_total: boolean;
  sales_display_price: number;
  sales_display_shipping: number;
  sales_display_subtotal: boolean;
  sales_display_tax_gift_wrapping: number;
  sales_display_zero_tax: boolean;

  // Additional SEO fields
  google_site_verification?: string;
  yandex_verification?: string;
  yahoo_verification?: string;
  bing_verification?: string;
}

// Basic store configuration interface
export interface BasicStoreConfig {
  id: number;
  code: string;
  store_name: string;
  locale: string;
  base_currency_code: string;
  default_display_currency_code: string;
  timezone: string;
  base_url: string;
  base_media_url: string;
  cms_home_page: string;
  cms_no_route: string;
  default_title: string;
  default_description: string;
  default_keywords: string;
  header_logo_src: string;
  logo_alt: string;
  welcome: string;
  copyright: string;
}

// Homepage configuration interface
export interface HomepageConfig {
  cms_home_page: string;
  store_name: string;
  default_title: string;
}

// Get full store configuration
export async function getStoreConfig(
  revalidate: number = 86400 // 24 hours
): Promise<StoreConfig | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: StoreConfig;
    }>(GET_STORE_CONFIG, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching store configuration:', error);
    return null;
  }
}

// Get basic store configuration
export async function getBasicStoreConfig(
  revalidate: number = 86400 // 24 hours
): Promise<BasicStoreConfig | null> {
  try {
    const response = await serverGraphQL<{
      storeConfig: BasicStoreConfig;
    }>(GET_STORE_CONFIG_BASIC, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching basic store configuration:', error);
    return null;
  }
}

// Get homepage identifier from store configuration
export async function getHomepageIdentifier(
  revalidate: number = 86400 // 24 hours
): Promise<string | null> {
  try {
    const response = await serverGraphQL<{
      storeConfig: HomepageConfig;
    }>(GET_HOMEPAGE_IDENTIFIER, {}, revalidate);

    return response.storeConfig.cms_home_page;
  } catch (error) {
    console.error('Error fetching homepage identifier:', error);
    return null;
  }
}

// Get homepage configuration
export async function getHomepageConfig(
  revalidate: number = 86400 // 24 hours
): Promise<HomepageConfig | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: HomepageConfig;
    }>(GET_HOMEPAGE_IDENTIFIER, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching homepage configuration:', error);
    return null;
  }
}

// Get store SEO configuration
export async function getStoreSeoConfig(
  revalidate: number = 86400 // 24 hours
): Promise<Partial<StoreConfig> | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: Partial<StoreConfig>;
    }>(GET_STORE_SEO_CONFIG, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching store SEO configuration:', error);
    return null;
  }
}

// Get store branding configuration
export async function getStoreBrandingConfig(
  revalidate: number = 86400 // 24 hours
): Promise<Partial<StoreConfig> | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: Partial<StoreConfig>;
    }>(GET_STORE_BRANDING_CONFIG, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching store branding configuration:', error);
    return null;
  }
}

// Get store catalog configuration
export async function getStoreCatalogConfig(
  revalidate: number = 86400 // 24 hours
): Promise<Partial<StoreConfig> | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: Partial<StoreConfig>;
    }>(GET_STORE_CATALOG_CONFIG, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching store catalog configuration:', error);
    return null;
  }
}

// Get store checkout configuration
export async function getStoreCheckoutConfig(
  revalidate: number = 86400 // 24 hours
): Promise<Partial<StoreConfig> | null> {
  try {
    const response = await magentoGraphQLQuery<{
      storeConfig: Partial<StoreConfig>;
    }>(GET_STORE_CHECKOUT_CONFIG, {}, revalidate);

    return response.storeConfig;
  } catch (error) {
    console.error('Error fetching store checkout configuration:', error);
    return null;
  }
}

// Utility function to get media URL for assets
export function getMediaUrl(
  path: string,
  storeConfig?: BasicStoreConfig
): string {
  if (!path) return '';

  // If path is already a full URL, return as is
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // Use store config base media URL if available
  if (storeConfig?.base_media_url) {
    return `${storeConfig.base_media_url.replace(/\/$/, '')}/${path.replace(/^\//, '')}`;
  }

  // Fallback to environment variable or default
  const baseMediaUrl =
    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL ||
    'https://your-magento-store.com/media';
  return `${baseMediaUrl.replace(/\/$/, '')}/${path.replace(/^\//, '')}`;
}

// Utility function to format store title
export function formatStoreTitle(
  title: string,
  storeConfig?: Partial<StoreConfig>
): string {
  if (!storeConfig) return title;

  let formattedTitle = title;

  if (storeConfig.title_prefix) {
    formattedTitle = `${storeConfig.title_prefix} ${formattedTitle}`;
  }

  if (storeConfig.title_suffix) {
    formattedTitle = `${formattedTitle} ${storeConfig.title_suffix}`;
  }

  return formattedTitle;
}

// Utility function to get default meta description
export function getDefaultMetaDescription(
  storeConfig?: Partial<StoreConfig>
): string {
  return (
    storeConfig?.default_description ||
    'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.'
  );
}

// Utility function to get default meta keywords
export function getDefaultMetaKeywords(
  storeConfig?: Partial<StoreConfig>
): string {
  return (
    storeConfig?.default_keywords ||
    'ecommerce, online shopping, products, store'
  );
}
