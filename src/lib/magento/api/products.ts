// Product API functions using Magento GraphQL

import { magentoGraphQLQuery, magentoGraphQLFetch } from '../index';
import {
  GET_PRODUCTS,
  GET_PRODUCT_BY_URL_KEY,
  GET_PRODUCT_BY_SKU,
  SEARCH_PRODUCTS,
  GET_FEATURED_PRODUCTS,
  GET_NEW_PRODUCTS,
  GET_BESTSELLER_PRODUCTS,
} from '../queries/products';
import type {
  Products,
  ProductInterface,
  ProductAttributeFilterInput,
  ProductAttributeSortInput,
} from '@/types/product';

// Get products with filters, sorting, and pagination
export async function getProducts(params: {
  filter?: ProductAttributeFilterInput;
  sort?: ProductAttributeSortInput;
  pageSize?: number;
  currentPage?: number;
  revalidate?: number;
}): Promise<Products> {
  const {
    filter,
    sort = { position: 'ASC' },
    pageSize = 20,
    currentPage = 1,
    revalidate = 3600, // 1 hour
  } = params;

  try {
    const response = await magentoGraphQLQuery<{ products: Products }>(
      GET_PRODUCTS,
      {
        filter,
        sort,
        pageSize,
        currentPage,
      },
      revalidate
    );

    return response.products;
  } catch (error) {
    console.error('Error fetching products:', error);
    throw new Error('Failed to fetch products');
  }
}

// Get single product by URL key
export async function getProductByUrlKey(
  urlKey: string,
  revalidate: number = 3600
): Promise<ProductInterface | null> {
  try {
    const response = await magentoGraphQLQuery<{
      products: { items: ProductInterface[] };
    }>(
      GET_PRODUCT_BY_URL_KEY,
      { urlKey },
      revalidate
    );

    return response.products.items[0] || null;
  } catch (error) {
    console.error('Error fetching product by URL key:', error);
    return null;
  }
}

// Get single product by SKU
export async function getProductBySku(
  sku: string,
  revalidate: number = 3600
): Promise<ProductInterface | null> {
  try {
    const response = await magentoGraphQLQuery<{
      products: { items: ProductInterface[] };
    }>(
      GET_PRODUCT_BY_SKU,
      { sku },
      revalidate
    );

    return response.products.items[0] || null;
  } catch (error) {
    console.error('Error fetching product by SKU:', error);
    return null;
  }
}

// Search products
export async function searchProducts(params: {
  search: string;
  filter?: ProductAttributeFilterInput;
  sort?: ProductAttributeSortInput;
  pageSize?: number;
  currentPage?: number;
}): Promise<Products> {
  const {
    search,
    filter,
    sort = { relevance: 'DESC' },
    pageSize = 20,
    currentPage = 1,
  } = params;

  try {
    const response = await magentoGraphQLFetch<{ products: Products }>(
      SEARCH_PRODUCTS,
      {
        search,
        filter,
        sort,
        pageSize,
        currentPage,
      },
      { cache: true, revalidate: 1800 } // 30 minutes for search results
    );

    return response.products;
  } catch (error) {
    console.error('Error searching products:', error);
    throw new Error('Failed to search products');
  }
}

// Get featured products
export async function getFeaturedProducts(
  pageSize: number = 8,
  revalidate: number = 7200 // 2 hours
): Promise<ProductInterface[]> {
  try {
    const response = await magentoGraphQLQuery<{
      products: { items: ProductInterface[] };
    }>(
      GET_FEATURED_PRODUCTS,
      { pageSize },
      revalidate
    );

    return response.products.items;
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

// Get new products
export async function getNewProducts(
  pageSize: number = 8,
  revalidate: number = 3600 // 1 hour
): Promise<ProductInterface[]> {
  try {
    const response = await magentoGraphQLQuery<{
      products: { items: ProductInterface[] };
    }>(
      GET_NEW_PRODUCTS,
      { pageSize },
      revalidate
    );

    return response.products.items;
  } catch (error) {
    console.error('Error fetching new products:', error);
    return [];
  }
}

// Get bestseller products
export async function getBestsellerProducts(
  pageSize: number = 8,
  revalidate: number = 7200 // 2 hours
): Promise<ProductInterface[]> {
  try {
    const response = await magentoGraphQLQuery<{
      products: { items: ProductInterface[] };
    }>(
      GET_BESTSELLER_PRODUCTS,
      { pageSize },
      revalidate
    );

    return response.products.items;
  } catch (error) {
    console.error('Error fetching bestseller products:', error);
    return [];
  }
}

// Get products by category ID
export async function getProductsByCategory(params: {
  categoryId: string;
  pageSize?: number;
  currentPage?: number;
  sort?: ProductAttributeSortInput;
  filter?: ProductAttributeFilterInput;
  revalidate?: number;
}): Promise<Products> {
  const {
    categoryId,
    pageSize = 20,
    currentPage = 1,
    sort = { position: 'ASC' },
    filter = {},
    revalidate = 3600,
  } = params;

  // Add category filter to existing filters
  const categoryFilter: ProductAttributeFilterInput = {
    ...filter,
    category_id: { eq: categoryId },
  };

  return getProducts({
    filter: categoryFilter,
    sort,
    pageSize,
    currentPage,
    revalidate,
  });
}

// Get related products for a product
export async function getRelatedProducts(
  productId: string,
  revalidate: number = 7200
): Promise<{
  related: ProductInterface[];
  upsell: ProductInterface[];
  crosssell: ProductInterface[];
}> {
  try {
    const product = await getProductBySku(productId, revalidate);
    
    if (!product) {
      return { related: [], upsell: [], crosssell: [] };
    }

    // Note: This would need to be implemented based on your specific Magento setup
    // For now, returning empty arrays as placeholder
    return {
      related: [],
      upsell: [],
      crosssell: [],
    };
  } catch (error) {
    console.error('Error fetching related products:', error);
    return { related: [], upsell: [], crosssell: [] };
  }
}

// Get product reviews (if available in your Magento setup)
export async function getProductReviews(
  productId: string,
  pageSize: number = 10,
  currentPage: number = 1
): Promise<any> {
  // This would need to be implemented based on your Magento review setup
  // Placeholder implementation
  try {
    // Implementation would go here
    return {
      items: [],
      page_info: {
        page_size: pageSize,
        current_page: currentPage,
        total_pages: 0,
      },
    };
  } catch (error) {
    console.error('Error fetching product reviews:', error);
    return {
      items: [],
      page_info: {
        page_size: pageSize,
        current_page: currentPage,
        total_pages: 0,
      },
    };
  }
}

// Utility function to build product filters
export function buildProductFilter(params: {
  categoryId?: string;
  priceRange?: { min?: number; max?: number };
  attributes?: Record<string, string | string[]>;
  inStock?: boolean;
}): ProductAttributeFilterInput {
  const { categoryId, priceRange, attributes = {}, inStock } = params;
  const filter: ProductAttributeFilterInput = {};

  if (categoryId) {
    filter.category_id = { eq: categoryId };
  }

  if (priceRange) {
    filter.price = {};
    if (priceRange.min !== undefined) {
      filter.price.gteq = priceRange.min.toString();
    }
    if (priceRange.max !== undefined) {
      filter.price.lteq = priceRange.max.toString();
    }
  }

  if (inStock) {
    // Note: This depends on your Magento configuration
    // You might need to adjust the field name
  }

  // Add custom attribute filters
  Object.entries(attributes).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      (filter as any)[key] = { in: value };
    } else {
      (filter as any)[key] = { eq: value };
    }
  });

  return filter;
}
