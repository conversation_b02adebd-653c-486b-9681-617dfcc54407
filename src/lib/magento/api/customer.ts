// Customer API functions using Magento GraphQL

import { magentoGraphQLMutate, magentoGraphQLFetch, customerAuth } from '../index';
import {
  GENERATE_CUSTOMER_TOKEN,
  CREATE_CUSTOMER,
  GET_CUSTOMER,
  UPDATE_CUSTOMER,
  <PERSON>AN<PERSON>_CUSTOMER_PASSWORD,
  REQUEST_PASSWORD_RESET_EMAIL,
  RESET_PASSWORD,
  REVOKE_CUSTOMER_TOKEN,
  CREATE_CUSTOMER_ADDRESS,
  UPDATE_CUSTOMER_ADDRESS,
  DELETE_CUSTOMER_ADDRESS,
  GET_CUSTOMER_ORDERS,
  SUBSCRIBE_EMAIL_TO_NEWSLETTER,
  GET_COUNTRIES,
} from '../queries/customer';
import type {
  Customer,
  CustomerInput,
  CustomerToken,
  CustomerCreateResponse,
  CustomerUpdateResponse,
  CustomerAddressInput,
  CustomerAddress,
  CustomerOrders,
  CustomerOrdersFilterInput,
  NewsletterSubscriptionStatus,
  Country,
} from '@/types/customer';

// Customer token storage key
const CUSTOMER_TOKEN_KEY = 'magento_customer_token';

// Get customer token from localStorage
function getCustomerToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(CUSTOMER_TOKEN_KEY);
}

// Set customer token in localStorage
function setCustomerToken(token: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem(CUSTOMER_TOKEN_KEY, token);
  customerAuth.setToken(token);
}

// Remove customer token from localStorage
function removeCustomerToken(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(CUSTOMER_TOKEN_KEY);
  customerAuth.removeToken();
}

// Initialize customer token on app start
export function initializeCustomerAuth(): void {
  const token = getCustomerToken();
  if (token) {
    customerAuth.setToken(token);
  }
}

// Generate customer token (login)
export async function loginCustomer(
  email: string,
  password: string
): Promise<string> {
  try {
    const response = await magentoGraphQLMutate<{
      generateCustomerToken: CustomerToken;
    }>(GENERATE_CUSTOMER_TOKEN, {
      email,
      password,
    });

    const token = response.generateCustomerToken.token;
    setCustomerToken(token);
    return token;
  } catch (error) {
    console.error('Error logging in customer:', error);
    throw new Error('Invalid email or password');
  }
}

// Create customer account
export async function createCustomer(
  customerData: CustomerInput
): Promise<Customer> {
  try {
    const response = await magentoGraphQLMutate<{
      createCustomer: CustomerCreateResponse;
    }>(CREATE_CUSTOMER, {
      input: customerData,
    });

    return response.createCustomer.customer;
  } catch (error) {
    console.error('Error creating customer:', error);
    throw new Error('Failed to create customer account');
  }
}

// Get customer information
export async function getCustomer(): Promise<Customer | null> {
  try {
    const token = getCustomerToken();
    if (!token) return null;

    const response = await magentoGraphQLFetch<{
      customer: Customer;
    }>(
      GET_CUSTOMER,
      {},
      { cache: false } // Don't cache customer data
    );

    return response.customer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    // If token is invalid, remove it
    if (error instanceof Error && error.message.includes('token')) {
      removeCustomerToken();
    }
    return null;
  }
}

// Update customer information
export async function updateCustomer(
  customerData: CustomerInput
): Promise<Customer> {
  try {
    const response = await magentoGraphQLMutate<{
      updateCustomer: CustomerUpdateResponse;
    }>(UPDATE_CUSTOMER, {
      input: customerData,
    });

    return response.updateCustomer.customer;
  } catch (error) {
    console.error('Error updating customer:', error);
    throw new Error('Failed to update customer information');
  }
}

// Change customer password
export async function changeCustomerPassword(
  currentPassword: string,
  newPassword: string
): Promise<boolean> {
  try {
    await magentoGraphQLMutate<{
      changeCustomerPassword: { id: number; email: string };
    }>(CHANGE_CUSTOMER_PASSWORD, {
      currentPassword,
      newPassword,
    });

    return true;
  } catch (error) {
    console.error('Error changing customer password:', error);
    throw new Error('Failed to change password');
  }
}

// Request password reset email
export async function requestPasswordResetEmail(email: string): Promise<boolean> {
  try {
    await magentoGraphQLMutate<{
      requestPasswordResetEmail: boolean;
    }>(REQUEST_PASSWORD_RESET_EMAIL, {
      email,
    });

    return true;
  } catch (error) {
    console.error('Error requesting password reset:', error);
    throw new Error('Failed to send password reset email');
  }
}

// Reset customer password
export async function resetPassword(
  email: string,
  resetPasswordToken: string,
  newPassword: string
): Promise<boolean> {
  try {
    await magentoGraphQLMutate<{
      resetPassword: boolean;
    }>(RESET_PASSWORD, {
      email,
      resetPasswordToken,
      newPassword,
    });

    return true;
  } catch (error) {
    console.error('Error resetting password:', error);
    throw new Error('Failed to reset password');
  }
}

// Logout customer (revoke token)
export async function logoutCustomer(): Promise<boolean> {
  try {
    await magentoGraphQLMutate<{
      revokeCustomerToken: { result: boolean };
    }>(REVOKE_CUSTOMER_TOKEN);

    removeCustomerToken();
    customerAuth.clearCache();
    return true;
  } catch (error) {
    console.error('Error logging out customer:', error);
    // Even if the API call fails, remove the token locally
    removeCustomerToken();
    customerAuth.clearCache();
    return false;
  }
}

// Create customer address
export async function createCustomerAddress(
  addressData: CustomerAddressInput
): Promise<CustomerAddress> {
  try {
    const response = await magentoGraphQLMutate<{
      createCustomerAddress: CustomerAddress;
    }>(CREATE_CUSTOMER_ADDRESS, {
      input: addressData,
    });

    return response.createCustomerAddress;
  } catch (error) {
    console.error('Error creating customer address:', error);
    throw new Error('Failed to create address');
  }
}

// Update customer address
export async function updateCustomerAddress(
  addressId: number,
  addressData: CustomerAddressInput
): Promise<CustomerAddress> {
  try {
    const response = await magentoGraphQLMutate<{
      updateCustomerAddress: CustomerAddress;
    }>(UPDATE_CUSTOMER_ADDRESS, {
      id: addressId,
      input: addressData,
    });

    return response.updateCustomerAddress;
  } catch (error) {
    console.error('Error updating customer address:', error);
    throw new Error('Failed to update address');
  }
}

// Delete customer address
export async function deleteCustomerAddress(addressId: number): Promise<boolean> {
  try {
    await magentoGraphQLMutate<{
      deleteCustomerAddress: boolean;
    }>(DELETE_CUSTOMER_ADDRESS, {
      id: addressId,
    });

    return true;
  } catch (error) {
    console.error('Error deleting customer address:', error);
    throw new Error('Failed to delete address');
  }
}

// Get customer orders
export async function getCustomerOrders(params: {
  filter?: CustomerOrdersFilterInput;
  pageSize?: number;
  currentPage?: number;
}): Promise<CustomerOrders> {
  const { filter, pageSize = 20, currentPage = 1 } = params;

  try {
    const response = await magentoGraphQLFetch<{
      customer: { orders: CustomerOrders };
    }>(
      GET_CUSTOMER_ORDERS,
      {
        filter,
        pageSize,
        currentPage,
      },
      { cache: false }
    );

    return response.customer.orders;
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    throw new Error('Failed to fetch orders');
  }
}

// Subscribe to newsletter
export async function subscribeToNewsletter(
  email: string
): Promise<NewsletterSubscriptionStatus> {
  try {
    const response = await magentoGraphQLMutate<{
      subscribeEmailToNewsletter: NewsletterSubscriptionStatus;
    }>(SUBSCRIBE_EMAIL_TO_NEWSLETTER, {
      email,
    });

    return response.subscribeEmailToNewsletter;
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);
    throw new Error('Failed to subscribe to newsletter');
  }
}

// Get countries for address forms
export async function getCountries(
  revalidate: number = 86400 // 24 hours
): Promise<Country[]> {
  try {
    const response = await magentoGraphQLFetch<{
      countries: Country[];
    }>(
      GET_COUNTRIES,
      {},
      { cache: true, revalidate }
    );

    return response.countries;
  } catch (error) {
    console.error('Error fetching countries:', error);
    return [];
  }
}

// Check if customer is logged in
export function isCustomerLoggedIn(): boolean {
  return getCustomerToken() !== null;
}

// Get customer token
export function getCurrentCustomerToken(): string | null {
  return getCustomerToken();
}

// Utility function to validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Utility function to validate password
export function isValidPassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// Utility function to format customer name
export function formatCustomerName(customer: Customer): string {
  return `${customer.firstname} ${customer.lastname}`.trim();
}

// Utility function to get default shipping address
export function getDefaultShippingAddress(customer: Customer): CustomerAddress | null {
  if (!customer.addresses) return null;
  return customer.addresses.find(address => address.default_shipping) || null;
}

// Utility function to get default billing address
export function getDefaultBillingAddress(customer: Customer): CustomerAddress | null {
  if (!customer.addresses) return null;
  return customer.addresses.find(address => address.default_billing) || null;
}
