// Cart API functions using Magento GraphQL

import { magentoGraphQLMutate, magentoGraphQLFetch } from '../index';
import {
  CREATE_EMPTY_CART,
  GET_CART,
  ADD_SIMPLE_PRODUCT_TO_CART,
  ADD_CONFIGURABLE_PRODUCT_TO_CART,
  UPDATE_CART_ITEMS,
  REMOVE_ITEM_FROM_CART,
  APPLY_COUPON_TO_CART,
  REMOVE_COUPON_FROM_CART,
  SET_SHIPPING_ADDRESS_ON_CART,
  SET_BILLING_ADDRESS_ON_CART,
  SET_SHIPPING_METHOD_ON_CART,
  SET_PAYMENT_METHOD_ON_CART,
  PLACE_ORDER,
} from '../queries/cart';
import type {
  Cart,
  SimpleProductCartItemInput,
  ConfigurableProductCartItemInput,
  CartItemUpdateInput,
  CartAddressInput,
  ShippingMethodInput,
  PaymentMethodInput,
  PlaceOrderResponse,
} from '@/types/cart';

// Cart storage key
const CART_ID_KEY = 'magento_cart_id';

// Get cart ID from localStorage
function getCartId(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(CART_ID_KEY);
}

// Set cart ID in localStorage
function setCartId(cartId: string): void {
  if (typeof window === 'undefined') return;
  localStorage.setItem(CART_ID_KEY, cartId);
}

// Remove cart ID from localStorage
function removeCartId(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem(CART_ID_KEY);
}

// Create empty cart
export async function createEmptyCart(): Promise<string> {
  try {
    const response = await magentoGraphQLMutate<{
      createEmptyCart: string;
    }>(CREATE_EMPTY_CART);

    const cartId = response.createEmptyCart;
    setCartId(cartId);
    return cartId;
  } catch (error) {
    console.error('Error creating empty cart:', error);
    throw new Error('Failed to create cart');
  }
}

// Get or create cart
export async function getOrCreateCart(): Promise<string> {
  let cartId = getCartId();
  
  if (!cartId) {
    cartId = await createEmptyCart();
  }
  
  return cartId;
}

// Get cart details
export async function getCart(cartId?: string): Promise<Cart | null> {
  try {
    const id = cartId || getCartId();
    if (!id) return null;

    const response = await magentoGraphQLFetch<{
      cart: Cart;
    }>(
      GET_CART,
      { cartId: id },
      { cache: false } // Don't cache cart data
    );

    return response.cart;
  } catch (error) {
    console.error('Error fetching cart:', error);
    // If cart doesn't exist, remove the stored ID
    if (error instanceof Error && error.message.includes('cart')) {
      removeCartId();
    }
    return null;
  }
}

// Add simple product to cart
export async function addSimpleProductToCart(
  items: SimpleProductCartItemInput[],
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || await getOrCreateCart();

    const response = await magentoGraphQLMutate<{
      addSimpleProductsToCart: { cart: Cart };
    }>(ADD_SIMPLE_PRODUCT_TO_CART, {
      cartId: id,
      cartItems: items,
    });

    return response.addSimpleProductsToCart.cart;
  } catch (error) {
    console.error('Error adding simple product to cart:', error);
    throw new Error('Failed to add product to cart');
  }
}

// Add configurable product to cart
export async function addConfigurableProductToCart(
  items: ConfigurableProductCartItemInput[],
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || await getOrCreateCart();

    const response = await magentoGraphQLMutate<{
      addConfigurableProductsToCart: { cart: Cart };
    }>(ADD_CONFIGURABLE_PRODUCT_TO_CART, {
      cartId: id,
      cartItems: items,
    });

    return response.addConfigurableProductsToCart.cart;
  } catch (error) {
    console.error('Error adding configurable product to cart:', error);
    throw new Error('Failed to add product to cart');
  }
}

// Update cart items
export async function updateCartItems(
  items: CartItemUpdateInput[],
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      updateCartItems: { cart: Cart };
    }>(UPDATE_CART_ITEMS, {
      cartId: id,
      cartItems: items,
    });

    return response.updateCartItems.cart;
  } catch (error) {
    console.error('Error updating cart items:', error);
    throw new Error('Failed to update cart items');
  }
}

// Remove item from cart
export async function removeItemFromCart(
  cartItemId: number,
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      removeItemFromCart: { cart: Cart };
    }>(REMOVE_ITEM_FROM_CART, {
      cartId: id,
      cartItemId,
    });

    return response.removeItemFromCart.cart;
  } catch (error) {
    console.error('Error removing item from cart:', error);
    throw new Error('Failed to remove item from cart');
  }
}

// Apply coupon to cart
export async function applyCouponToCart(
  couponCode: string,
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      applyCouponToCart: { cart: Cart };
    }>(APPLY_COUPON_TO_CART, {
      cartId: id,
      couponCode,
    });

    return response.applyCouponToCart.cart;
  } catch (error) {
    console.error('Error applying coupon to cart:', error);
    throw new Error('Failed to apply coupon');
  }
}

// Remove coupon from cart
export async function removeCouponFromCart(cartId?: string): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      removeCouponFromCart: { cart: Cart };
    }>(REMOVE_COUPON_FROM_CART, {
      cartId: id,
    });

    return response.removeCouponFromCart.cart;
  } catch (error) {
    console.error('Error removing coupon from cart:', error);
    throw new Error('Failed to remove coupon');
  }
}

// Set shipping address on cart
export async function setShippingAddressOnCart(
  shippingAddresses: CartAddressInput[],
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      setShippingAddressesOnCart: { cart: Cart };
    }>(SET_SHIPPING_ADDRESS_ON_CART, {
      cartId: id,
      shippingAddresses,
    });

    return response.setShippingAddressesOnCart.cart;
  } catch (error) {
    console.error('Error setting shipping address:', error);
    throw new Error('Failed to set shipping address');
  }
}

// Set billing address on cart
export async function setBillingAddressOnCart(
  billingAddress: CartAddressInput,
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      setBillingAddressOnCart: { cart: Cart };
    }>(SET_BILLING_ADDRESS_ON_CART, {
      cartId: id,
      billingAddress,
    });

    return response.setBillingAddressOnCart.cart;
  } catch (error) {
    console.error('Error setting billing address:', error);
    throw new Error('Failed to set billing address');
  }
}

// Set shipping method on cart
export async function setShippingMethodOnCart(
  shippingMethods: ShippingMethodInput[],
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      setShippingMethodsOnCart: { cart: Cart };
    }>(SET_SHIPPING_METHOD_ON_CART, {
      cartId: id,
      shippingMethods,
    });

    return response.setShippingMethodsOnCart.cart;
  } catch (error) {
    console.error('Error setting shipping method:', error);
    throw new Error('Failed to set shipping method');
  }
}

// Set payment method on cart
export async function setPaymentMethodOnCart(
  paymentMethod: PaymentMethodInput,
  cartId?: string
): Promise<Cart> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      setPaymentMethodOnCart: { cart: Cart };
    }>(SET_PAYMENT_METHOD_ON_CART, {
      cartId: id,
      paymentMethod,
    });

    return response.setPaymentMethodOnCart.cart;
  } catch (error) {
    console.error('Error setting payment method:', error);
    throw new Error('Failed to set payment method');
  }
}

// Place order
export async function placeOrder(cartId?: string): Promise<PlaceOrderResponse> {
  try {
    const id = cartId || getCartId();
    if (!id) throw new Error('No cart found');

    const response = await magentoGraphQLMutate<{
      placeOrder: PlaceOrderResponse;
    }>(PLACE_ORDER, {
      cartId: id,
    });

    // Clear cart ID after successful order
    removeCartId();

    return response.placeOrder;
  } catch (error) {
    console.error('Error placing order:', error);
    throw new Error('Failed to place order');
  }
}

// Clear cart (remove from localStorage)
export function clearCart(): void {
  removeCartId();
}

// Get cart item count
export function getCartItemCount(cart: Cart | null): number {
  if (!cart || !cart.items) return 0;
  return cart.items.reduce((total, item) => total + item.quantity, 0);
}

// Get cart total
export function getCartTotal(cart: Cart | null): number {
  if (!cart || !cart.prices) return 0;
  return cart.prices.grand_total.value;
}

// Check if cart is empty
export function isCartEmpty(cart: Cart | null): boolean {
  return !cart || !cart.items || cart.items.length === 0;
}
