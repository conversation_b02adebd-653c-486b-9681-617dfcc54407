// CMS API functions using Magento GraphQL

import { serverGraphQL } from '@/lib/graphql/simpleClient';
import { GET_CMS_PAGE, GET_CMS_BLOCK } from '@/lib/graphql/queries';

// CMS Page interface
export interface CmsPage {
  identifier: string;
  url_key: string;
  title: string;
  content: string;
  content_heading?: string;
  page_layout?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  created_at: string;
  updated_at: string;
}

// CMS Block interface
export interface CmsBlock {
  identifier: string;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
}

// Get CMS page by identifier
export async function getCmsPage(
  identifier: string,
  revalidate: number = 3600 // 1 hour
): Promise<CmsPage | null> {
  try {
    const response = await serverGraphQL<{
      cmsPage: CmsPage;
    }>(GET_CMS_PAGE, { identifier }, revalidate);

    return response.cmsPage;
  } catch (error) {
    console.error('Error fetching CMS page:', error);
    return null;
  }
}

// Get CMS page by URL key
export async function getCmsPageByUrlKey(
  urlKey: string,
  revalidate: number = 3600 // 1 hour
): Promise<CmsPage | null> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsPage: CmsPage;
    }>(GET_CMS_PAGE_BY_URL_KEY, { urlKey }, revalidate);

    return response.cmsPage;
  } catch (error) {
    console.error('Error fetching CMS page by URL key:', error);
    return null;
  }
}

// Get multiple CMS pages
export async function getCmsPages(
  identifiers: string[],
  revalidate: number = 3600 // 1 hour
): Promise<CmsPage[]> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsPages: { items: CmsPage[] };
    }>(GET_CMS_PAGES, { identifiers }, revalidate);

    return response.cmsPages.items;
  } catch (error) {
    console.error('Error fetching CMS pages:', error);
    return [];
  }
}

// Get CMS block by identifier
export async function getCmsBlock(
  identifier: string,
  revalidate: number = 7200 // 2 hours
): Promise<CmsBlock | null> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsBlocks: { items: CmsBlock[] };
    }>(GET_CMS_BLOCK, { identifier }, revalidate);

    return response.cmsBlocks.items[0] || null;
  } catch (error) {
    console.error('Error fetching CMS block:', error);
    return null;
  }
}

// Get multiple CMS blocks
export async function getCmsBlocks(
  identifiers: string[],
  revalidate: number = 7200 // 2 hours
): Promise<CmsBlock[]> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsBlocks: { items: CmsBlock[] };
    }>(GET_CMS_BLOCKS, { identifiers }, revalidate);

    return response.cmsBlocks.items;
  } catch (error) {
    console.error('Error fetching CMS blocks:', error);
    return [];
  }
}

// Get all CMS pages (for sitemap/navigation)
export async function getAllCmsPages(
  revalidate: number = 86400 // 24 hours
): Promise<Partial<CmsPage>[]> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsPages: { items: Partial<CmsPage>[] };
    }>(GET_ALL_CMS_PAGES, {}, revalidate);

    return response.cmsPages.items;
  } catch (error) {
    console.error('Error fetching all CMS pages:', error);
    return [];
  }
}

// Search CMS pages
export async function searchCmsPages(
  search: string,
  revalidate: number = 1800 // 30 minutes
): Promise<CmsPage[]> {
  try {
    const response = await magentoGraphQLQuery<{
      cmsPages: { items: CmsPage[] };
    }>(SEARCH_CMS_PAGES, { search }, revalidate);

    return response.cmsPages.items;
  } catch (error) {
    console.error('Error searching CMS pages:', error);
    return [];
  }
}

// Check if content contains Page Builder
export function hasPageBuilderContent(content: string): boolean {
  return (
    content.includes('data-content-type') ||
    content.includes('pagebuilder-') ||
    content.includes('data-pb-style')
  );
}

// Extract Page Builder content from CMS content
export function extractPageBuilderContent(content: string): string {
  // Remove any wrapper divs that might not be part of Page Builder
  const cleanContent = content.trim();

  // If it starts with Page Builder content, return as-is
  if (cleanContent.includes('data-content-type')) {
    return cleanContent;
  }

  // Try to find Page Builder content within the HTML
  const pbMatch = cleanContent.match(
    /<div[^>]*data-content-type[^>]*>.*<\/div>/s
  );
  if (pbMatch) {
    return pbMatch[0];
  }

  return cleanContent;
}

// Get CMS page with Page Builder content parsed
export async function getCmsPageWithPageBuilder(
  identifier: string,
  revalidate: number = 3600
): Promise<
  (CmsPage & { hasPageBuilder: boolean; pageBuilderContent?: string }) | null
> {
  const page = await getCmsPage(identifier, revalidate);

  if (!page) return null;

  const hasPageBuilder = hasPageBuilderContent(page.content);
  const pageBuilderContent = hasPageBuilder
    ? extractPageBuilderContent(page.content)
    : undefined;

  return {
    ...page,
    hasPageBuilder,
    pageBuilderContent,
  };
}

// Get CMS block with Page Builder content parsed
export async function getCmsBlockWithPageBuilder(
  identifier: string,
  revalidate: number = 7200
): Promise<
  (CmsBlock & { hasPageBuilder: boolean; pageBuilderContent?: string }) | null
> {
  const block = await getCmsBlock(identifier, revalidate);

  if (!block) return null;

  const hasPageBuilder = hasPageBuilderContent(block.content);
  const pageBuilderContent = hasPageBuilder
    ? extractPageBuilderContent(block.content)
    : undefined;

  return {
    ...block,
    hasPageBuilder,
    pageBuilderContent,
  };
}

// Utility function to clean CMS content for display
export function cleanCmsContent(content: string): string {
  return content
    .replace(/{{[^}]+}}/g, '') // Remove Magento directives
    .replace(/\{\{[^}]+\}\}/g, '') // Remove any remaining directives
    .trim();
}

// Generate CMS page URL
export function getCmsPageUrl(page: CmsPage): string {
  return `/${page.url_key}`;
}

// Generate breadcrumbs for CMS page
export function getCmsPageBreadcrumbs(
  page: CmsPage
): Array<{ name: string; url: string }> {
  return [
    { name: 'Home', url: '/' },
    { name: page.title, url: getCmsPageUrl(page) },
  ];
}
