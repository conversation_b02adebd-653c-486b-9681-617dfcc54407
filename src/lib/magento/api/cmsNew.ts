// CMS API functions using Magento GraphQL (Simplified)

import { serverGraphQL } from '@/lib/graphql/simpleClient';
import { GET_CMS_PAGE, GET_CMS_BLOCK } from '@/lib/graphql/queries';

// CMS Page interface
export interface CmsPage {
  identifier: string;
  url_key?: string;
  title: string;
  content: string;
  content_heading?: string;
  page_layout?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
}

// CMS Block interface
export interface CmsBlock {
  identifier: string;
  title: string;
  content: string;
}

// Get CMS page by identifier
export async function getCmsPage(
  identifier: string,
  revalidate: number = 3600 // 1 hour
): Promise<CmsPage | null> {
  try {
    const response = await serverGraphQL<{
      cmsPage: CmsPage;
    }>(GET_CMS_PAGE, { identifier }, revalidate);

    return response.cmsPage;
  } catch (error) {
    console.error('Error fetching CMS page:', error);
    return null;
  }
}

// Get CMS page with Page Builder content
export async function getCmsPageWithPageBuilder(
  identifier: string,
  revalidate: number = 3600 // 1 hour
): Promise<CmsPage | null> {
  return getCmsPage(identifier, revalidate);
}

// Get CMS block by identifier
export async function getCmsBlock(
  identifier: string,
  revalidate: number = 7200 // 2 hours
): Promise<CmsBlock | null> {
  try {
    const response = await serverGraphQL<{
      cmsBlocks: { items: CmsBlock[] };
    }>(GET_CMS_BLOCK, { identifier }, revalidate);

    return response.cmsBlocks.items[0] || null;
  } catch (error) {
    console.error('Error fetching CMS block:', error);
    return null;
  }
}

// Check if content contains Page Builder
export function hasPageBuilderContent(content: string): boolean {
  return content.includes('data-content-type') || 
         content.includes('pagebuilder-') ||
         content.includes('data-pb-style');
}

// Extract Page Builder content from HTML
export function extractPageBuilderContent(content: string): string {
  // Remove any wrapping divs and return clean content
  return content.trim();
}
