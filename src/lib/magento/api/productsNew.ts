// Product API functions using Magento GraphQL (Simplified)

import { serverGraphQL } from '@/lib/graphql/simpleClient';
import { SEARCH_PRODUCTS } from '@/lib/graphql/queries';

// Product interfaces (simplified)
export interface Product {
  uid: string;
  id: number;
  name: string;
  sku: string;
  url_key: string;
  image?: {
    url: string;
    label: string;
  };
  small_image?: {
    url: string;
    label: string;
  };
  price_range: {
    minimum_price: {
      regular_price: {
        value: number;
        currency: string;
      };
      final_price: {
        value: number;
        currency: string;
      };
      discount?: {
        amount_off: number;
        percent_off: number;
      };
    };
  };
  rating_summary: number;
  review_count: number;
  stock_status: string;
}

export interface SearchResults {
  items: Product[];
  page_info: {
    page_size: number;
    current_page: number;
    total_pages: number;
  };
  total_count: number;
  aggregations?: Array<{
    label: string;
    count: number;
    attribute_code: string;
    options: Array<{
      label: string;
      value: string;
      count: number;
    }>;
  }>;
  sort_fields?: {
    default: string;
    options: Array<{
      label: string;
      value: string;
    }>;
  };
}

export interface SearchParams {
  search: string;
  pageSize?: number;
  currentPage?: number;
  sort?: any;
  filter?: any;
}

// Search products
export async function searchProducts(
  params: SearchParams,
  revalidate: number = 1800 // 30 minutes
): Promise<SearchResults> {
  const { search, pageSize = 20, currentPage = 1, sort, filter } = params;

  try {
    const response = await serverGraphQL<{
      products: SearchResults;
    }>(
      SEARCH_PRODUCTS,
      {
        search,
        pageSize,
        currentPage,
        sort,
        filter,
      },
      revalidate
    );

    return response.products;
  } catch (error) {
    console.error('Error searching products:', error);
    return {
      items: [],
      page_info: {
        page_size: pageSize,
        current_page: currentPage,
        total_pages: 0,
      },
      total_count: 0,
    };
  }
}
