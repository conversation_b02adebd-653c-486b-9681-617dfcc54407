// Categories API functions using Magento GraphQL

import { serverGraphQL, gql } from '@/lib/graphql/simpleClient';
import {
  GET_CATEGORIES_FOR_MENU,
  GET_CATEGORY_BY_URL_KEY,
  GET_CATEGORY_BY_ID,
} from '@/lib/graphql/queries';

// Category interface
export interface Category {
  uid: string;
  id: number;
  name: string;
  url_key: string;
  url_path: string;
  url_suffix?: string;
  description?: string;
  meta_title?: string;
  meta_keywords?: string;
  meta_description?: string;
  image?: string;
  path: string;
  path_in_store?: string;
  position: number;
  level: number;
  children_count: number;
  include_in_menu: boolean;
  is_anchor?: boolean;
  default_sort_by?: string;
  available_sort_by?: string[];
  landing_page?: number;
  custom_layout_update_file?: string;
  children?: Category[];
  breadcrumbs?: Array<{
    category_id: number;
    category_name: string;
    category_level: number;
    category_url_key: string;
    category_url_path: string;
  }>;
}

// Get categories for menu navigation
export async function getCategoriesForMenu(
  revalidate: number = 3600 // 1 hour
): Promise<Category[]> {
  try {
    const response = await serverGraphQL<{
      categoryList: Category[];
    }>(GET_CATEGORIES_FOR_MENU, {}, revalidate);

    return response.categoryList || [];
  } catch (error) {
    console.error('Error fetching categories for menu:', error);
    return [];
  }
}

// Get category by URL key
export async function getCategoryByUrlKey(
  urlKey: string,
  revalidate: number = 3600 // 1 hour
): Promise<Category | null> {
  try {
    const response = await serverGraphQL<{
      categoryList: Category[];
    }>(GET_CATEGORY_BY_URL_KEY, { urlKey }, revalidate);

    return response.categoryList[0] || null;
  } catch (error) {
    console.error('Error fetching category by URL key:', error);
    return null;
  }
}

// Get category by ID
export async function getCategoryById(
  id: string,
  revalidate: number = 3600 // 1 hour
): Promise<Category | null> {
  try {
    const response = await serverGraphQL<{
      categoryList: Category[];
    }>(GET_CATEGORY_BY_ID, { id }, revalidate);

    return response.categoryList[0] || null;
  } catch (error) {
    console.error('Error fetching category by ID:', error);
    return null;
  }
}

// Client-side function to get categories for menu
export async function getCategoriesForMenuClient(): Promise<Category[]> {
  try {
    const response = await gql<{
      categoryList: Category[];
    }>(GET_CATEGORIES_FOR_MENU);

    return response.categoryList || [];
  } catch (error) {
    console.error('Error fetching categories for menu (client):', error);
    return [];
  }
}

// Build category URL
export function getCategoryUrl(category: Category, storeConfig?: any): string {
  const suffix = storeConfig?.category_url_suffix || '.html';
  return `/${category.url_path}${suffix}`;
}

// Get category image URL
export function getCategoryImageUrl(
  category: Category,
  storeConfig?: any
): string | null {
  if (!category.image) return null;

  const baseMediaUrl =
    storeConfig?.base_media_url ||
    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL;

  if (category.image.startsWith('http')) {
    return category.image;
  }

  return `${baseMediaUrl}/catalog/category/${category.image}`;
}

// Get top-level categories
export function getTopLevelCategories(categories: Category[]): Category[] {
  return categories.filter(category => category.level === 2); // Level 2 is typically top-level in Magento
}

// Find category by URL key in tree
export function findCategoryByUrlKey(
  categories: Category[],
  urlKey: string
): Category | null {
  for (const category of categories) {
    if (category.url_key === urlKey) {
      return category;
    }

    if (category.children && category.children.length > 0) {
      const found = findCategoryByUrlKey(category.children, urlKey);
      if (found) return found;
    }
  }

  return null;
}

// Build category breadcrumbs
export function getCategoryBreadcrumbs(
  category: Category
): Array<{ name: string; url: string }> {
  const breadcrumbs = [{ name: 'Home', url: '/' }];

  if (category.breadcrumbs) {
    category.breadcrumbs.forEach(breadcrumb => {
      breadcrumbs.push({
        name: breadcrumb.category_name,
        url: `/${breadcrumb.category_url_path}`,
      });
    });
  }

  return breadcrumbs;
}

// Build category tree for navigation
export function buildCategoryTree(categories: Category[]): Category[] {
  const categoryMap = new Map<number, Category>();
  const rootCategories: Category[] = [];

  // First pass: create map of all categories
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Second pass: build tree structure
  categories.forEach(category => {
    const cat = categoryMap.get(category.id);
    if (!cat) return;

    if (category.level === 2) {
      // Top-level category
      rootCategories.push(cat);
    } else {
      // Find parent and add as child
      const parentPath = category.path.split('/').slice(0, -1).join('/');
      const parent = Array.from(categoryMap.values()).find(
        c => c.path === parentPath
      );
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(cat);
      }
    }
  });

  return rootCategories;
}
