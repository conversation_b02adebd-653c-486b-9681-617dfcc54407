// Search API functions using Magento GraphQL

import { serverGraphQL, gql } from '@/lib/graphql/simpleClient';
import { SEARCH_PRODUCTS } from '@/lib/graphql/queries';

// Product interface for search results
export interface SearchProduct {
  uid: string;
  id: number;
  name: string;
  sku: string;
  url_key: string;
  image?: {
    url: string;
    label: string;
  };
  small_image?: {
    url: string;
    label: string;
  };
  price_range: {
    minimum_price: {
      regular_price: {
        value: number;
        currency: string;
      };
      final_price: {
        value: number;
        currency: string;
      };
      discount?: {
        amount_off: number;
        percent_off: number;
      };
    };
  };
  rating_summary: number;
  review_count: number;
  stock_status: string;
}

// Search results interface
export interface SearchResults {
  items: SearchProduct[];
  page_info: {
    page_size: number;
    current_page: number;
    total_pages: number;
  };
  total_count: number;
  aggregations?: Array<{
    label: string;
    count: number;
    attribute_code: string;
    options: Array<{
      label: string;
      value: string;
      count: number;
    }>;
  }>;
  sort_fields?: {
    default: string;
    options: Array<{
      label: string;
      value: string;
    }>;
  };
}

// Search parameters interface
export interface SearchParams {
  search: string;
  pageSize?: number;
  currentPage?: number;
  sort?: any;
  filter?: any;
}

// Search products
export async function searchProducts(
  params: SearchParams,
  revalidate: number = 1800 // 30 minutes
): Promise<SearchResults> {
  const { search, pageSize = 20, currentPage = 1, sort, filter } = params;

  try {
    const response = await serverGraphQL<{
      products: SearchResults;
    }>(
      SEARCH_PRODUCTS,
      {
        search,
        pageSize,
        currentPage,
        sort,
        filter,
      },
      revalidate
    );

    return response.products;
  } catch (error) {
    console.error('Error searching products:', error);
    return {
      items: [],
      page_info: {
        page_size: pageSize,
        current_page: currentPage,
        total_pages: 0,
      },
      total_count: 0,
    };
  }
}

// Client-side search products
export async function searchProductsClient(
  params: SearchParams
): Promise<SearchResults> {
  const { search, pageSize = 20, currentPage = 1, sort, filter } = params;

  try {
    const response = await gql<{
      products: SearchResults;
    }>(SEARCH_PRODUCTS, {
      search,
      pageSize,
      currentPage,
      sort,
      filter,
    });

    return response.products;
  } catch (error) {
    console.error('Error searching products (client):', error);
    return {
      items: [],
      page_info: {
        page_size: pageSize,
        current_page: currentPage,
        total_pages: 0,
      },
      total_count: 0,
    };
  }
}

// Get search suggestions
export async function getSearchSuggestions(
  query: string,
  limit: number = 10
): Promise<string[]> {
  try {
    // This would typically use a dedicated search suggestions endpoint
    // For now, we'll search products and extract names
    const results = await searchProducts({
      search: query,
      pageSize: limit,
    });

    return results.items.map(product => product.name);
  } catch (error) {
    console.error('Error getting search suggestions:', error);
    return [];
  }
}

// Build search URL
export function buildSearchUrl(
  query: string,
  filters?: Record<string, any>
): string {
  const params = new URLSearchParams();
  params.set('q', query);

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => params.append(key, v));
      } else {
        params.set(key, value);
      }
    });
  }

  return `/search?${params.toString()}`;
}

// Parse search URL
export function parseSearchUrl(url: string): {
  query: string;
  filters: Record<string, any>;
} {
  const urlObj = new URL(url, 'http://localhost');
  const params = urlObj.searchParams;

  const query = params.get('q') || '';
  const filters: Record<string, any> = {};

  params.forEach((value, key) => {
    if (key !== 'q') {
      if (filters[key]) {
        if (Array.isArray(filters[key])) {
          filters[key].push(value);
        } else {
          filters[key] = [filters[key], value];
        }
      } else {
        filters[key] = value;
      }
    }
  });

  return { query, filters };
}

// Format search query for display
export function formatSearchQuery(query: string): string {
  return query.trim().replace(/\s+/g, ' ');
}

// Get popular search terms (mock implementation)
export async function getPopularSearchTerms(): Promise<string[]> {
  // In a real implementation, this would fetch from analytics or a dedicated endpoint
  return [
    'laptop',
    'smartphone',
    'headphones',
    'camera',
    'watch',
    'shoes',
    'dress',
    'bag',
    'sunglasses',
    'jewelry',
  ];
}

// Search history management (client-side)
export function getSearchHistory(): string[] {
  if (typeof window === 'undefined') return [];

  try {
    const history = localStorage.getItem('search_history');
    return history ? JSON.parse(history) : [];
  } catch {
    return [];
  }
}

export function addToSearchHistory(query: string): void {
  if (typeof window === 'undefined') return;

  try {
    const history = getSearchHistory();
    const formattedQuery = formatSearchQuery(query);

    if (formattedQuery && !history.includes(formattedQuery)) {
      const newHistory = [formattedQuery, ...history.slice(0, 9)]; // Keep last 10
      localStorage.setItem('search_history', JSON.stringify(newHistory));
    }
  } catch (error) {
    console.error('Error saving search history:', error);
  }
}

export function clearSearchHistory(): void {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem('search_history');
  } catch (error) {
    console.error('Error clearing search history:', error);
  }
}
