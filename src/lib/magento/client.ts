// Magento GraphQL Client with native fetch
interface GraphQLResponse<T> {
  data: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: Array<string | number>;
    extensions?: Record<string, any>;
  }>;
}

interface GraphQLRequest {
  query: string;
  variables?: Record<string, any>;
  operationName?: string;
}

class MagentoGraphQLError extends Error {
  constructor(
    message: string,
    public errors: GraphQLResponse<any>['errors'] = [],
    public response?: Response
  ) {
    super(message);
    this.name = 'MagentoGraphQLError';
  }
}

class MagentoGraphQLClient {
  private baseUrl: string;
  private headers: Record<string, string>;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'http://magento2.local/graphql/';
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add admin token if available (for server-side requests)
    if (typeof window === 'undefined' && process.env.MAGENTO_ADMIN_TOKEN) {
      this.headers['Authorization'] = `Bearer ${process.env.MAGENTO_ADMIN_TOKEN}`;
    }
  }

  // Set customer token for authenticated requests
  setCustomerToken(token: string) {
    this.headers['Authorization'] = `Bearer ${token}`;
  }

  // Remove customer token
  removeCustomerToken() {
    delete this.headers['Authorization'];
  }

  // Generate cache key
  private getCacheKey(query: string, variables?: Record<string, any>): string {
    return `${query}:${JSON.stringify(variables || {})}`;
  }

  // Check if cache is valid
  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  // Get from cache
  private getFromCache(cacheKey: string): any | null {
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }
    return null;
  }

  // Set cache
  private setCache(cacheKey: string, data: any): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
  }

  // Main fetch method
  async fetch<T>(
    query: string,
    variables?: Record<string, any>,
    options: {
      cache?: boolean;
      revalidate?: number;
      headers?: Record<string, string>;
    } = {}
  ): Promise<T> {
    const { cache = true, headers: customHeaders = {} } = options;
    
    // Check cache first (only for queries, not mutations)
    const isQuery = query.trim().startsWith('query');
    const cacheKey = this.getCacheKey(query, variables);
    
    if (cache && isQuery) {
      const cachedData = this.getFromCache(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    const requestBody: GraphQLRequest = {
      query,
      variables,
    };

    const requestHeaders = {
      ...this.headers,
      ...customHeaders,
    };

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestBody),
        // Add Next.js specific options
        ...(options.revalidate && { next: { revalidate: options.revalidate } }),
      });

      if (!response.ok) {
        throw new MagentoGraphQLError(
          `HTTP Error: ${response.status} ${response.statusText}`,
          [],
          response
        );
      }

      const result: GraphQLResponse<T> = await response.json();

      // Handle GraphQL errors
      if (result.errors && result.errors.length > 0) {
        const errorMessage = result.errors.map(error => error.message).join(', ');
        throw new MagentoGraphQLError(
          `GraphQL Error: ${errorMessage}`,
          result.errors,
          response
        );
      }

      // Cache successful queries
      if (cache && isQuery && result.data) {
        this.setCache(cacheKey, result.data);
      }

      return result.data;
    } catch (error) {
      if (error instanceof MagentoGraphQLError) {
        throw error;
      }
      
      // Handle network errors
      throw new MagentoGraphQLError(
        `Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        []
      );
    }
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(query: string, variables?: Record<string, any>): void {
    const cacheKey = this.getCacheKey(query, variables);
    this.cache.delete(cacheKey);
  }
}

// Create singleton instance
const magentoClient = new MagentoGraphQLClient();

// Export the client and error class
export { magentoClient, MagentoGraphQLError };
export type { GraphQLResponse, GraphQLRequest };
