// GraphQL Queries as constants (for browser compatibility)

// Store Configuration Queries
export const GET_STORE_CONFIG_BASIC = `
  query GetStoreConfigBasic {
    storeConfig {
      id
      code
      store_name
      locale
      base_currency_code
      default_display_currency_code
      timezone
      base_url
      base_media_url
      cms_home_page
      cms_no_route
      default_title
      default_description
      default_keywords
      header_logo_src
      logo_alt
      welcome
      copyright
    }
  }
`;

export const GET_HOMEPAGE_IDENTIFIER = `
  query GetHomepageIdentifier {
    storeConfig {
      cms_home_page
      store_name
      default_title
    }
  }
`;

export const GET_STORE_CONFIG = `
  query GetStoreConfig {
    storeConfig {
      id
      code
      website_id
      locale
      base_currency_code
      default_display_currency_code
      timezone
      weight_unit
      base_url
      base_link_url
      base_media_url
      base_static_url
      secure_base_url
      secure_base_link_url
      secure_base_media_url
      secure_base_static_url
      store_name
      store_sort_order
      is_default_store
      is_default_store_group
      default_title
      title_prefix
      title_suffix
      default_description
      default_keywords
      default_display_currency_code
      locale
      timezone
      weight_unit
      cms_home_page
      cms_no_route
      cms_no_cookies
      show_cms_breadcrumbs
      catalog_default_sort_by
      catalog_frontend_list_mode
      catalog_frontend_grid_per_page_values
      catalog_frontend_list_per_page_values
      catalog_frontend_grid_per_page
      catalog_frontend_list_per_page
      catalog_price_scope
      catalog_layered_navigation_price_step
      root_category_id
      root_category_uid
      category_url_suffix
      product_url_suffix
      use_store_in_url
      header_logo_src
      logo_width
      logo_height
      logo_alt
      welcome
      copyright
      absolute_footer
      head_shortcut_icon
      head_includes
      configurable_thumbnail_source
      image_placeholder
      grid_per_page
      list_per_page
      grid_per_page_values
      list_per_page_values
    }
  }
`;

export const GET_STORE_SEO_CONFIG = `
  query GetStoreSeoConfig {
    storeConfig {
      default_title
      title_prefix
      title_suffix
      default_description
      default_keywords
      head_shortcut_icon
      head_includes
    }
  }
`;

export const GET_STORE_BRANDING_CONFIG = `
  query GetStoreBrandingConfig {
    storeConfig {
      store_name
      header_logo_src
      logo_width
      logo_height
      logo_alt
      welcome
      copyright
      absolute_footer
    }
  }
`;

export const GET_STORE_CATALOG_CONFIG = `
  query GetStoreCatalogConfig {
    storeConfig {
      root_category_id
      root_category_uid
      category_url_suffix
      product_url_suffix
      catalog_default_sort_by
      grid_per_page
      list_per_page
      grid_per_page_values
      list_per_page_values
      configurable_thumbnail_source
    }
  }
`;

export const GET_STORE_CHECKOUT_CONFIG = `
  query GetStoreCheckoutConfig {
    storeConfig {
      is_guest_checkout_enabled
      is_one_page_checkout_enabled
      max_items_in_order_summary
      minicart_display
      minicart_max_items
      shopping_cart_display_full_summary
      shopping_cart_display_zero_tax
      payment_payflowpro_cc_vault_active
    }
  }
`;

// Category Queries
export const GET_CATEGORIES_FOR_MENU = `
  query GetCategoriesForMenu {
    categoryList(filters: { include_in_menu: { eq: true } }) {
      uid
      id
      name
      url_key
      url_path
      position
      level
      include_in_menu
      children_count
      image
      children {
        uid
        id
        name
        url_key
        url_path
        position
        level
        include_in_menu
        children_count
        image
        children {
          uid
          id
          name
          url_key
          url_path
          position
          level
          include_in_menu
          children_count
          image
        }
      }
    }
  }
`;

export const GET_CATEGORY_BY_URL_KEY = `
  query GetCategoryByUrlKey($urlKey: String!) {
    categoryList(filters: { url_key: { eq: $urlKey } }) {
      uid
      id
      name
      url_key
      url_path
      url_suffix
      description
      meta_title
      meta_keywords
      meta_description
      image
      path
      path_in_store
      position
      level
      children_count
      include_in_menu
      is_anchor
      default_sort_by
      available_sort_by
      landing_page
      custom_layout_update_file
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
        category_url_path
      }
    }
  }
`;

export const GET_CATEGORY_BY_ID = `
  query GetCategoryById($id: String!) {
    categoryList(filters: { ids: { eq: $id } }) {
      uid
      id
      name
      url_key
      url_path
      url_suffix
      description
      meta_title
      meta_keywords
      meta_description
      image
      path
      path_in_store
      position
      level
      children_count
      include_in_menu
      is_anchor
      default_sort_by
      available_sort_by
      landing_page
      custom_layout_update_file
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
        category_url_path
      }
    }
  }
`;

// Product Search Query
export const SEARCH_PRODUCTS = `
  query SearchProducts(
    $search: String!
    $pageSize: Int = 20
    $currentPage: Int = 1
    $sort: ProductAttributeSortInput
    $filter: ProductAttributeFilterInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      sort: $sort
      filter: $filter
    ) {
      items {
        uid
        id
        name
        sku
        url_key
        image {
          url
          label
        }
        small_image {
          url
          label
        }
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
            discount {
              amount_off
              percent_off
            }
          }
        }
        rating_summary
        review_count
        stock_status
      }
      page_info {
        page_size
        current_page
        total_pages
      }
      total_count
      aggregations {
        label
        count
        attribute_code
        options {
          label
          value
          count
        }
      }
      sort_fields {
        default
        options {
          label
          value
        }
      }
    }
  }
`;

// CMS Queries
export const GET_CMS_PAGE = `
  query GetCmsPage($identifier: String!) {
    cmsPage(identifier: $identifier) {
      identifier
      url_key
      title
      content
      content_heading
      page_layout
      meta_title
      meta_description
      meta_keywords
    }
  }
`;

export const GET_CMS_BLOCK = `
  query GetCmsBlock($identifier: String!) {
    cmsBlocks(identifiers: [$identifier]) {
      items {
        identifier
        title
        content
      }
    }
  }
`;

// Cart Queries
export const GET_CART = `
  query GetCart($cartId: String!) {
    cart(cart_id: $cartId) {
      id
      email
      is_virtual
      applied_coupons {
        code
      }
      itemsV2 {
        items {
          uid
          product {
            uid
            name
            sku
            url_key
            thumbnail {
              url
              label
            }
            price_range {
              minimum_price {
                regular_price {
                  value
                  currency
                }
                final_price {
                  value
                  currency
                }
              }
            }
            stock_status
          }
          prices {
            price {
              value
              currency
            }
            row_total {
              value
              currency
            }
            row_total_including_tax {
              value
              currency
            }
            total_item_discount {
              value
              currency
            }
          }
          quantity
        }
        page_info {
          page_size
          current_page
          total_pages
        }
        total_count
      }
      prices {
        grand_total {
          value
          currency
        }
        subtotal_excluding_tax {
          value
          currency
        }
        subtotal_including_tax {
          value
          currency
        }
        applied_taxes {
          label
          amount {
            value
            currency
          }
        }
        discounts {
          amount {
            value
            currency
          }
          label
        }
      }
      total_quantity
    }
  }
`;

// Cart Mutations
export const CREATE_EMPTY_CART = `
  mutation CreateEmptyCart {
    createEmptyCart
  }
`;

export const ADD_SIMPLE_PRODUCTS_TO_CART = `
  mutation AddSimpleProductsToCart($cartId: String!, $cartItems: [SimpleProductCartItemInput!]!) {
    addSimpleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        id
        total_quantity
        itemsV2 {
          total_count
        }
      }
    }
  }
`;
