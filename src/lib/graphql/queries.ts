// GraphQL Queries as constants (for browser compatibility)

// Store Configuration Queries
export const GET_STORE_CONFIG_BASIC = `
  query GetStoreConfigBasic {
    storeConfig {
      id
      code
      store_name
      locale
      base_currency_code
      default_display_currency_code
      timezone
      base_url
      base_media_url
      cms_home_page
      cms_no_route
      default_title
      default_description
      default_keywords
      header_logo_src
      logo_alt
      welcome
      copyright
    }
  }
`;

export const GET_HOMEPAGE_IDENTIFIER = `
  query GetHomepageIdentifier {
    storeConfig {
      cms_home_page
      store_name
      default_title
    }
  }
`;

// Category Queries
export const GET_CATEGORIES_FOR_MENU = `
  query GetCategoriesForMenu {
    categoryList(filters: { include_in_menu: { eq: true } }) {
      uid
      id
      name
      url_key
      url_path
      position
      level
      include_in_menu
      children_count
      image
      children {
        uid
        id
        name
        url_key
        url_path
        position
        level
        include_in_menu
        children_count
        image
        children {
          uid
          id
          name
          url_key
          url_path
          position
          level
          include_in_menu
          children_count
          image
        }
      }
    }
  }
`;

export const GET_CATEGORY_BY_URL_KEY = `
  query GetCategoryByUrlKey($urlKey: String!) {
    categoryList(filters: { url_key: { eq: $urlKey } }) {
      uid
      id
      name
      url_key
      url_path
      url_suffix
      description
      meta_title
      meta_keywords
      meta_description
      image
      path
      path_in_store
      position
      level
      children_count
      include_in_menu
      is_anchor
      default_sort_by
      available_sort_by
      landing_page
      custom_layout_update_file
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
        category_url_path
      }
    }
  }
`;

export const GET_CATEGORY_BY_ID = `
  query GetCategoryById($id: String!) {
    categoryList(filters: { ids: { eq: $id } }) {
      uid
      id
      name
      url_key
      url_path
      url_suffix
      description
      meta_title
      meta_keywords
      meta_description
      image
      path
      path_in_store
      position
      level
      children_count
      include_in_menu
      is_anchor
      default_sort_by
      available_sort_by
      landing_page
      custom_layout_update_file
      breadcrumbs {
        category_id
        category_name
        category_level
        category_url_key
        category_url_path
      }
    }
  }
`;

// Product Search Query
export const SEARCH_PRODUCTS = `
  query SearchProducts(
    $search: String!
    $pageSize: Int = 20
    $currentPage: Int = 1
    $sort: ProductAttributeSortInput
    $filter: ProductAttributeFilterInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      sort: $sort
      filter: $filter
    ) {
      items {
        uid
        id
        name
        sku
        url_key
        image {
          url
          label
        }
        small_image {
          url
          label
        }
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
            discount {
              amount_off
              percent_off
            }
          }
        }
        rating_summary
        review_count
        stock_status
      }
      page_info {
        page_size
        current_page
        total_pages
      }
      total_count
      aggregations {
        label
        count
        attribute_code
        options {
          label
          value
          count
        }
      }
      sort_fields {
        default
        options {
          label
          value
        }
      }
    }
  }
`;

// CMS Queries
export const GET_CMS_PAGE = `
  query GetCmsPage($identifier: String!) {
    cmsPage(identifier: $identifier) {
      identifier
      url_key
      title
      content
      content_heading
      page_layout
      meta_title
      meta_description
      meta_keywords
      created_at
      updated_at
    }
  }
`;

export const GET_CMS_BLOCK = `
  query GetCmsBlock($identifier: String!) {
    cmsBlocks(identifiers: [$identifier]) {
      items {
        identifier
        title
        content
        created_at
        updated_at
      }
    }
  }
`;

// Cart Queries
export const GET_CART = `
  query GetCart($cartId: String!) {
    cart(cart_id: $cartId) {
      id
      email
      is_virtual
      applied_coupons {
        code
      }
      itemsV2 {
        items {
          uid
          product {
            uid
            name
            sku
            url_key
            thumbnail {
              url
              label
            }
            price_range {
              minimum_price {
                regular_price {
                  value
                  currency
                }
                final_price {
                  value
                  currency
                }
              }
            }
            stock_status
          }
          prices {
            price {
              value
              currency
            }
            row_total {
              value
              currency
            }
            row_total_including_tax {
              value
              currency
            }
            total_item_discount {
              value
              currency
            }
          }
          quantity
        }
        page_info {
          page_size
          current_page
          total_pages
        }
        total_count
      }
      prices {
        grand_total {
          value
          currency
        }
        subtotal_excluding_tax {
          value
          currency
        }
        subtotal_including_tax {
          value
          currency
        }
        applied_taxes {
          label
          amount {
            value
            currency
          }
        }
        discounts {
          amount {
            value
            currency
          }
          label
        }
      }
      total_quantity
    }
  }
`;

// Cart Mutations
export const CREATE_EMPTY_CART = `
  mutation CreateEmptyCart {
    createEmptyCart
  }
`;

export const ADD_SIMPLE_PRODUCTS_TO_CART = `
  mutation AddSimpleProductsToCart($cartId: String!, $cartItems: [SimpleProductCartItemInput!]!) {
    addSimpleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        id
        total_quantity
        itemsV2 {
          total_count
        }
      }
    }
  }
`;
