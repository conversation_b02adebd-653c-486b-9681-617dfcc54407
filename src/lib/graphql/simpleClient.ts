// Simple GraphQL Client for browser and server

// GraphQL Client Configuration
interface GraphQLClientConfig {
  endpoint: string;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// GraphQL Response Interface
interface GraphQLResponse<T = any> {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: Array<string | number>;
    extensions?: Record<string, any>;
  }>;
  extensions?: Record<string, any>;
}

// GraphQL Variables Interface
interface GraphQLVariables {
  [key: string]: any;
}

// Simple GraphQL Client Class
export class SimpleGraphQLClient {
  private config: GraphQLClientConfig;

  constructor(config: GraphQLClientConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config,
    };
  }

  // Execute GraphQL query/mutation
  async execute<T = any>(
    query: string,
    variables?: GraphQLVariables,
    options?: {
      headers?: Record<string, string>;
      timeout?: number;
      cache?: RequestCache;
    }
  ): Promise<T> {
    const headers = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...options?.headers,
    };

    const body = JSON.stringify({
      query,
      variables: variables || {},
    });

    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body,
      cache: options?.cache || 'default',
    };

    // Add timeout if specified
    const timeout = options?.timeout || this.config.timeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    requestOptions.signal = controller.signal;

    try {
      const response = await this.executeWithRetry(requestOptions);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: GraphQLResponse<T> = await response.json();

      if (result.errors && result.errors.length > 0) {
        const errorMessage = result.errors.map(error => error.message).join(', ');
        console.error('GraphQL Errors:', result.errors);
        throw new Error(`GraphQL Error: ${errorMessage}`);
      }

      if (!result.data) {
        throw new Error('No data returned from GraphQL query');
      }

      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`Request timeout after ${timeout}ms`);
        }
        throw error;
      }
      
      throw new Error('Unknown error occurred during GraphQL request');
    }
  }

  // Execute request with retry logic
  private async executeWithRetry(options: RequestInit, attempt = 1): Promise<Response> {
    try {
      return await fetch(this.config.endpoint, options);
    } catch (error) {
      if (attempt < this.config.retries!) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.executeWithRetry(options, attempt + 1);
      }
      throw error;
    }
  }

  // Update client configuration
  updateConfig(newConfig: Partial<GraphQLClientConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Get current configuration
  getConfig(): GraphQLClientConfig {
    return { ...this.config };
  }
}

// Default GraphQL client instance
export const graphqlClient = new SimpleGraphQLClient({
  endpoint: process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'https://your-magento-store.com/graphql',
  headers: {
    'Store': process.env.NEXT_PUBLIC_MAGENTO_STORE_CODE || 'default',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Convenience function for making GraphQL requests
export async function gql<T = any>(
  query: string,
  variables?: GraphQLVariables,
  options?: {
    headers?: Record<string, string>;
    timeout?: number;
    cache?: RequestCache;
  }
): Promise<T> {
  return graphqlClient.execute<T>(query, variables, options);
}

// Server-side GraphQL request with caching
export async function serverGraphQL<T = any>(
  query: string,
  variables?: GraphQLVariables,
  revalidate?: number
): Promise<T> {
  const response = await fetch(graphqlClient.getConfig().endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...graphqlClient.getConfig().headers,
    },
    body: JSON.stringify({
      query,
      variables: variables || {},
    }),
    next: revalidate ? { revalidate } : undefined,
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const result: GraphQLResponse<T> = await response.json();

  if (result.errors && result.errors.length > 0) {
    const errorMessage = result.errors.map(error => error.message).join(', ');
    console.error('GraphQL Errors:', result.errors);
    throw new Error(`GraphQL Error: ${errorMessage}`);
  }

  if (!result.data) {
    throw new Error('No data returned from GraphQL query');
  }

  return result.data;
}

export default graphqlClient;
