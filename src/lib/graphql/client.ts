// Enhanced GraphQL Client with .gql file support

import { readFileSync } from 'fs';
import { join } from 'path';

// GraphQL Client Configuration
interface GraphQLClientConfig {
  endpoint: string;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// GraphQL Response Interface
interface GraphQLResponse<T = any> {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: Array<string | number>;
    extensions?: Record<string, any>;
  }>;
  extensions?: Record<string, any>;
}

// GraphQL Variables Interface
interface GraphQLVariables {
  [key: string]: any;
}

// Cache for loaded GraphQL documents
const documentCache = new Map<string, string>();

// Load GraphQL document from .gql file
function loadGraphQLDocument(filePath: string): string {
  if (documentCache.has(filePath)) {
    return documentCache.get(filePath)!;
  }

  try {
    const fullPath = join(process.cwd(), 'src/graphql', filePath);
    const content = readFileSync(fullPath, 'utf-8');
    
    // Process imports
    const processedContent = processImports(content, filePath);
    
    documentCache.set(filePath, processedContent);
    return processedContent;
  } catch (error) {
    console.error(`Failed to load GraphQL document: ${filePath}`, error);
    throw new Error(`GraphQL document not found: ${filePath}`);
  }
}

// Process #import statements in GraphQL files
function processImports(content: string, currentFile: string): string {
  const importRegex = /#import\s+"([^"]+)"/g;
  let processedContent = content;
  const imports = new Set<string>();

  let match;
  while ((match = importRegex.exec(content)) !== null) {
    const importPath = match[1];
    
    if (!imports.has(importPath)) {
      imports.add(importPath);
      
      try {
        const importedContent = loadGraphQLDocument(importPath);
        // Remove the import statement and prepend the imported content
        processedContent = processedContent.replace(match[0], '');
        processedContent = importedContent + '\n' + processedContent;
      } catch (error) {
        console.warn(`Failed to import GraphQL document: ${importPath} in ${currentFile}`);
      }
    }
  }

  return processedContent.trim();
}

// Enhanced GraphQL Client Class
export class GraphQLClient {
  private config: GraphQLClientConfig;

  constructor(config: GraphQLClientConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config,
    };
  }

  // Execute GraphQL query/mutation with .gql file
  async request<T = any>(
    documentPath: string,
    variables?: GraphQLVariables,
    options?: {
      headers?: Record<string, string>;
      timeout?: number;
      cache?: RequestCache;
    }
  ): Promise<T> {
    const query = loadGraphQLDocument(documentPath);
    return this.execute<T>(query, variables, options);
  }

  // Execute GraphQL query/mutation with string
  async execute<T = any>(
    query: string,
    variables?: GraphQLVariables,
    options?: {
      headers?: Record<string, string>;
      timeout?: number;
      cache?: RequestCache;
    }
  ): Promise<T> {
    const headers = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...options?.headers,
    };

    const body = JSON.stringify({
      query,
      variables: variables || {},
    });

    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body,
      cache: options?.cache || 'default',
    };

    // Add timeout if specified
    const timeout = options?.timeout || this.config.timeout;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    requestOptions.signal = controller.signal;

    try {
      const response = await this.executeWithRetry(requestOptions);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: GraphQLResponse<T> = await response.json();

      if (result.errors && result.errors.length > 0) {
        const errorMessage = result.errors.map(error => error.message).join(', ');
        throw new Error(`GraphQL Error: ${errorMessage}`);
      }

      if (!result.data) {
        throw new Error('No data returned from GraphQL query');
      }

      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`Request timeout after ${timeout}ms`);
        }
        throw error;
      }
      
      throw new Error('Unknown error occurred during GraphQL request');
    }
  }

  // Execute request with retry logic
  private async executeWithRetry(options: RequestInit, attempt = 1): Promise<Response> {
    try {
      return await fetch(this.config.endpoint, options);
    } catch (error) {
      if (attempt < this.config.retries!) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.executeWithRetry(options, attempt + 1);
      }
      throw error;
    }
  }

  // Update client configuration
  updateConfig(newConfig: Partial<GraphQLClientConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Get current configuration
  getConfig(): GraphQLClientConfig {
    return { ...this.config };
  }
}

// Default GraphQL client instance
export const graphqlClient = new GraphQLClient({
  endpoint: process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'https://your-magento-store.com/graphql',
  headers: {
    'Store': process.env.NEXT_PUBLIC_MAGENTO_STORE_CODE || 'default',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Convenience function for making GraphQL requests
export async function gql<T = any>(
  documentPath: string,
  variables?: GraphQLVariables,
  options?: {
    headers?: Record<string, string>;
    timeout?: number;
    cache?: RequestCache;
  }
): Promise<T> {
  return graphqlClient.request<T>(documentPath, variables, options);
}

// Convenience function for executing GraphQL strings
export async function executeGraphQL<T = any>(
  query: string,
  variables?: GraphQLVariables,
  options?: {
    headers?: Record<string, string>;
    timeout?: number;
    cache?: RequestCache;
  }
): Promise<T> {
  return graphqlClient.execute<T>(query, variables, options);
}

// Server-side GraphQL request with caching
export async function serverGraphQL<T = any>(
  documentPath: string,
  variables?: GraphQLVariables,
  revalidate?: number
): Promise<T> {
  const query = loadGraphQLDocument(documentPath);
  
  const response = await fetch(graphqlClient.getConfig().endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...graphqlClient.getConfig().headers,
    },
    body: JSON.stringify({
      query,
      variables: variables || {},
    }),
    next: revalidate ? { revalidate } : undefined,
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const result: GraphQLResponse<T> = await response.json();

  if (result.errors && result.errors.length > 0) {
    const errorMessage = result.errors.map(error => error.message).join(', ');
    throw new Error(`GraphQL Error: ${errorMessage}`);
  }

  if (!result.data) {
    throw new Error('No data returned from GraphQL query');
  }

  return result.data;
}

export default graphqlClient;
