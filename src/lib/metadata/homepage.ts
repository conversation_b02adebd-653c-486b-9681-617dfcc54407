// Homepage Metadata Generation

import { Metadata } from 'next';
import { 
  getHomepageConfig, 
  getBasicStoreConfig, 
  getStoreSeoConfig,
  formatStoreTitle,
  getDefaultMetaDescription,
  getDefaultMetaKeywords,
  getMediaUrl
} from '@/lib/magento/api/storeConfig';
import { getCmsPageWithPageBuilder } from '@/lib/magento/api/cms';

// Generate metadata for dynamic homepage
export async function generateHomepageMetadata(): Promise<Metadata> {
  try {
    // Fetch configuration data in parallel
    const [homepageConfig, storeConfig, seoConfig] = await Promise.all([
      getHomepageConfig(),
      getBasicStoreConfig(),
      getStoreSeoConfig(),
    ]);

    // Get homepage CMS page for additional metadata
    let cmsPage = null;
    if (homepageConfig?.cms_home_page) {
      cmsPage = await getCmsPageWithPageBuilder(homepageConfig.cms_home_page);
    }

    // Determine title
    let title = 'Home';
    if (cmsPage?.title) {
      title = cmsPage.title;
    } else if (homepageConfig?.default_title) {
      title = homepageConfig.default_title;
    } else if (storeConfig?.store_name) {
      title = storeConfig.store_name;
    }

    // Format title with prefix/suffix
    const formattedTitle = formatStoreTitle(title, seoConfig);

    // Determine description
    let description = getDefaultMetaDescription(seoConfig);
    if (cmsPage?.meta_description) {
      description = cmsPage.meta_description;
    }

    // Determine keywords
    let keywords = getDefaultMetaKeywords(seoConfig);
    if (cmsPage?.meta_keywords) {
      keywords = cmsPage.meta_keywords;
    }

    // Get favicon
    let favicon = '/favicon.ico';
    if (seoConfig?.head_shortcut_icon) {
      favicon = getMediaUrl(seoConfig.head_shortcut_icon, storeConfig);
    }

    // Get logo for Open Graph
    let logo = null;
    if (storeConfig?.header_logo_src) {
      logo = getMediaUrl(storeConfig.header_logo_src, storeConfig);
    }

    // Build base URL
    const baseUrl = storeConfig?.base_url || process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000';
    const siteUrl = baseUrl.replace(/\/$/, '');

    // Generate metadata
    const metadata: Metadata = {
      title: formattedTitle,
      description,
      keywords,
      
      // Basic meta tags
      authors: [{ name: storeConfig?.store_name || 'Store' }],
      creator: storeConfig?.store_name || 'Store',
      publisher: storeConfig?.store_name || 'Store',
      
      // Favicon
      icons: {
        icon: favicon,
        shortcut: favicon,
        apple: favicon,
      },
      
      // Open Graph
      openGraph: {
        type: 'website',
        locale: storeConfig?.locale || 'en_US',
        url: siteUrl,
        siteName: storeConfig?.store_name || 'Store',
        title: formattedTitle,
        description,
        images: logo ? [
          {
            url: logo,
            width: storeConfig?.logo_width || 800,
            height: storeConfig?.logo_height || 600,
            alt: storeConfig?.logo_alt || storeConfig?.store_name || 'Store Logo',
          }
        ] : [],
      },
      
      // Twitter
      twitter: {
        card: 'summary_large_image',
        title: formattedTitle,
        description,
        images: logo ? [logo] : [],
        creator: `@${storeConfig?.store_name?.toLowerCase().replace(/\s+/g, '') || 'store'}`,
      },
      
      // Additional meta tags
      other: {
        'theme-color': '#2196f3',
        'msapplication-TileColor': '#2196f3',
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default',
        'format-detection': 'telephone=no',
      },
      
      // Robots
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      
      // Verification (if available in store config)
      verification: {
        google: seoConfig?.google_site_verification || undefined,
        yandex: seoConfig?.yandex_verification || undefined,
        yahoo: seoConfig?.yahoo_verification || undefined,
        other: {
          'msvalidate.01': seoConfig?.bing_verification || undefined,
        },
      },
    };

    // Add canonical URL
    metadata.alternates = {
      canonical: siteUrl,
    };

    // Add language alternates if available
    if (storeConfig?.locale) {
      metadata.alternates.languages = {
        [storeConfig.locale.replace('_', '-')]: siteUrl,
      };
    }

    return metadata;
  } catch (error) {
    console.error('Error generating homepage metadata:', error);
    
    // Return fallback metadata
    return {
      title: 'Magento E-commerce Store',
      description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
      keywords: 'ecommerce, online shopping, products, store',
      openGraph: {
        type: 'website',
        title: 'Magento E-commerce Store',
        description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
      },
      twitter: {
        card: 'summary_large_image',
        title: 'Magento E-commerce Store',
        description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
      },
    };
  }
}

// Generate JSON-LD structured data for homepage
export async function generateHomepageStructuredData(): Promise<object | null> {
  try {
    const [storeConfig, homepageConfig] = await Promise.all([
      getBasicStoreConfig(),
      getHomepageConfig(),
    ]);

    if (!storeConfig) return null;

    const baseUrl = storeConfig.base_url || process.env.NEXT_PUBLIC_SITE_URL || 'https://localhost:3000';
    const siteUrl = baseUrl.replace(/\/$/, '');
    
    const logo = storeConfig.header_logo_src 
      ? getMediaUrl(storeConfig.header_logo_src, storeConfig)
      : null;

    // Organization structured data
    const organization = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: storeConfig.store_name,
      url: siteUrl,
      logo: logo ? {
        '@type': 'ImageObject',
        url: logo,
        width: storeConfig.logo_width || 800,
        height: storeConfig.logo_height || 600,
      } : undefined,
      sameAs: [
        // Add social media URLs if available in store config
        // These would need to be added to store config queries
      ],
    };

    // Website structured data
    const website = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: storeConfig.store_name,
      url: siteUrl,
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${siteUrl}/search?q={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      },
    };

    return [organization, website];
  } catch (error) {
    console.error('Error generating homepage structured data:', error);
    return null;
  }
}

// Get homepage cache tags for revalidation
export function getHomepageCacheTags(): string[] {
  return [
    'homepage',
    'store-config',
    'cms-page',
    'page-builder',
  ];
}

// Get homepage revalidation time
export function getHomepageRevalidation(): number {
  // Revalidate every hour for homepage content
  return 3600;
}
