// CMS Components Export

export { default as CmsPageRenderer } from './CmsPageRenderer';
export { default as CmsBlockRenderer } from './CmsBlockRenderer';
export { default as CmsBlockLoader } from './CmsBlockLoader';

// Re-export CMS API functions for convenience
export {
  getCmsPage,
  getCmsPageByUrlKey,
  getCmsPages,
  getCmsBlock,
  getCmsBlocks,
  getAllCmsPages,
  searchCmsPages,
  getCmsPageWithPageBuilder,
  getCmsBlockWithPageBuilder,
  hasPageBuilderContent,
  extractPageBuilderContent,
  cleanCmsContent,
  getCmsPageUrl,
  getCmsPageBreadcrumbs,
} from '@/lib/magento/api/cms';

// Re-export types
export type { CmsPage, CmsBlock } from '@/lib/magento/api/cms';
