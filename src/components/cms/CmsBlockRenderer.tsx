'use client';

import React from 'react';
import { Box } from '@mui/material';
import { motion } from 'framer-motion';
import { PageBuilderRenderer, RawHtmlRenderer } from '@/components/pagebuilder';
import { CmsBlock, hasPageBuilderContent, extractPageBuilderContent } from '@/lib/magento/api/cms';

interface CmsBlockRendererProps {
  block: CmsBlock;
  className?: string;
  style?: React.CSSProperties;
  animate?: boolean;
}

export const CmsBlockRenderer: React.FC<CmsBlockRendererProps> = ({
  block,
  className,
  style,
  animate = true,
}) => {
  const hasPageBuilder = hasPageBuilderContent(block.content);
  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(block.content) : null;

  // Animation variants
  const blockVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const content = (
    <Box
      className={`cms-block cms-block-${block.identifier} ${className || ''}`}
      style={style}
    >
      {hasPageBuilder && pageBuilderContent ? (
        // Render with Page Builder
        <PageBuilderRenderer
          content={pageBuilderContent}
          config={{
            enableLazyLoading: true,
            imageOptimization: true,
          }}
          className="cms-block-content"
        />
      ) : (
        // Render as raw HTML
        <RawHtmlRenderer
          html={block.content}
          className="cms-block-content"
        />
      )}
    </Box>
  );

  if (animate) {
    return (
      <motion.div
        variants={blockVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
      >
        {content}
      </motion.div>
    );
  }

  return content;
};

export default CmsBlockRenderer;
