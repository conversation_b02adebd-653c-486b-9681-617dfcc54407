import React from 'react';
import { PageBuilderRenderer } from '../pagebuilder/PageBuilderRenderer';

interface FooterCmsRendererProps {
  content: string;
  className?: string;
}

export const FooterCmsRenderer: React.FC<FooterCmsRendererProps> = ({
  content,
  className,
}) => {
  // Debug: Log what we're receiving
  console.log('FooterCmsRenderer - Raw content:', content);
  console.log('FooterCmsRenderer - Content type:', typeof content);
  console.log('FooterCmsRenderer - Content length:', content?.length);

  // Test with simple HTML first
  const testHtml =
    '<p><strong>Test HTML</strong> - <a href="#">Test Link</a></p>';
  console.log('FooterCmsRenderer - Test HTML:', testHtml);

  return (
    <div className={`footer-cms-content ${className || ''}`}>
      <div style={{ border: '1px solid red', margin: '10px', padding: '10px' }}>
        <h4>DEBUG: Original Content</h4>
        <div dangerouslySetInnerHTML={{ __html: content }} />
      </div>

      <div
        style={{ border: '1px solid blue', margin: '10px', padding: '10px' }}
      >
        <h4>DEBUG: Test HTML</h4>
        <div dangerouslySetInnerHTML={{ __html: testHtml }} />
      </div>

      <div
        style={{ border: '1px solid green', margin: '10px', padding: '10px' }}
      >
        <h4>DEBUG: Raw Content Display</h4>
        <pre>{content}</pre>
      </div>
    </div>
  );
};

export default FooterCmsRenderer;
