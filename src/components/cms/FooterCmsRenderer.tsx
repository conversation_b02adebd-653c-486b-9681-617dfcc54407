'use client';

import React from 'react';
import { Box } from '@mui/material';
import { PageBuilderRenderer } from '@/components/pagebuilder';

// Footer-specific CMS content renderer
interface FooterCmsRendererProps {
  content: string;
  className?: string;
}

export const FooterCmsRenderer: React.FC<FooterCmsRendererProps> = ({
  content,
  className,
}) => {
  // Check if content contains Page Builder elements
  const hasPageBuilder =
    content.includes('data-content-type') ||
    content.includes('pagebuilder-') ||
    content.includes('data-pb-style');

  if (hasPageBuilder) {
    // Use Page Builder renderer for Page Builder content
    return <PageBuilderRenderer content={content} className={className} />;
  }

  // For regular HTML content, apply footer-specific styling
  return (
    <Box
      className={`footer-cms-content ${className || ''}`}
      sx={{
        width: '100%',
        // Footer-specific styles for lists
        '& ul': {
          listStyle: 'none',
          padding: 0,
          margin: 0,
        },
        '& li': {
          marginBottom: 1,
        },
        '& a': {
          color: 'rgba(255, 255, 255, 0.8)',
          textDecoration: 'none',
          fontSize: '0.875rem',
          transition: 'color 0.2s ease',
          display: 'block',
          '&:hover': {
            color: 'primary.light',
          },
        },
        '& p': {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '0.875rem',
          lineHeight: 1.6,
          margin: 0,
          marginBottom: 1,
        },
        // Social media links styling
        '& .social-links': {
          display: 'flex',
          gap: 2,
          flexWrap: 'wrap',
          '& a': {
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40,
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: 'primary.main',
              transform: 'translateY(-2px)',
            },
          },
        },
        // Contact info styling
        '& .contact-info': {
          '& p': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            marginBottom: 1,
          },
        },
        // Navigation links styling
        '& .footer-links': {
          '& ul': {
            '& li': {
              '& a': {
                padding: '4px 0',
                borderBottom: '1px solid transparent',
                '&:hover': {
                  borderBottomColor: 'primary.light',
                },
              },
            },
          },
        },
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default FooterCmsRenderer;
