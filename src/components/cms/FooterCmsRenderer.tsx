import React from 'react';

interface FooterCmsRendererProps {
  content: string;
  className?: string;
}

export const FooterCmsRenderer: React.FC<FooterCmsRendererProps> = ({
  content,
  className,
}) => {
  // Add CSS to head for footer styling
  React.useEffect(() => {
    const styleId = 'footer-cms-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .footer-cms-content ul {
          list-style: none !important;
          padding: 0 !important;
          margin: 0 !important;
        }
        .footer-cms-content li {
          margin-bottom: 8px !important;
        }
        .footer-cms-content a {
          color: rgba(255, 255, 255, 0.8) !important;
          text-decoration: none !important;
          font-size: 14px !important;
          transition: color 0.2s ease !important;
          display: block !important;
        }
        .footer-cms-content a:hover {
          color: #90caf9 !important;
        }
        .footer-cms-content p {
          color: rgba(255, 255, 255, 0.8) !important;
          font-size: 14px !important;
          line-height: 1.6 !important;
          margin: 0 0 8px 0 !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  if (!content || content.trim() === '') {
    return null;
  }

  return (
    <div
      className={`footer-cms-content ${className || ''}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default FooterCmsRenderer;
