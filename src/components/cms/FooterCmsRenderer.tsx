import React from 'react';
import { PageBuilderRenderer } from '../pagebuilder/PageBuilderRenderer';

interface FooterCmsRendererProps {
  content: string;
  className?: string;
}

export const FooterCmsRenderer: React.FC<FooterCmsRendererProps> = ({
  content,
  className,
}) => {
  // Add CSS to head for footer styling
  React.useEffect(() => {
    const styleId = 'footer-cms-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .footer-cms-content ul {
          list-style: none !important;
          padding: 0 !important;
          margin: 0 !important;
        }
        .footer-cms-content li {
          margin-bottom: 8px !important;
        }
        .footer-cms-content a {
          color: rgba(255, 255, 255, 0.8) !important;
          text-decoration: none !important;
          font-size: 14px !important;
          transition: color 0.2s ease !important;
          display: block !important;
        }
        .footer-cms-content a:hover {
          color: #90caf9 !important;
        }
        .footer-cms-content p {
          color: rgba(255, 255, 255, 0.8) !important;
          font-size: 14px !important;
          line-height: 1.6 !important;
          margin: 0 0 8px 0 !important;
        }
        .footer-cms-content .pagebuilder-container {
          width: 100% !important;
        }
        .footer-cms-content .pagebuilder-element {
          color: rgba(255, 255, 255, 0.8) !important;
        }
        .footer-cms-content .pagebuilder-text {
          font-size: 14px !important;
        }
        .footer-cms-content .pagebuilder-heading {
          color: rgba(255, 255, 255, 0.9) !important;
          margin-bottom: 12px !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  if (!content || content.trim() === '') {
    return null;
  }

  // Check if content contains Page Builder elements (more specific detection)
  const hasPageBuilder =
    content.includes('data-content-type=') ||
    content.includes('data-pb-style=') ||
    content.includes('pagebuilder-column') ||
    content.includes('pagebuilder-row');

  if (hasPageBuilder) {
    // Use Page Builder renderer for Page Builder content
    return (
      <div className={`footer-cms-content ${className || ''}`}>
        <PageBuilderRenderer
          content={content}
          config={{
            enableImageOptimization: true,
            enableLazyLoading: true,
            sanitizeHtml: true,
            cssPrefix: 'footer-pagebuilder',
          }}
        />
      </div>
    );
  }

  // Fallback to raw HTML for regular content (this should be the normal case for footer)
  return (
    <div
      className={`footer-cms-content ${className || ''}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default FooterCmsRenderer;
