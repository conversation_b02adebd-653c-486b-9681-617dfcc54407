'use client';

import React from 'react';
import { Box, Typography, Container, Breadcrumbs, Link } from '@mui/material';
import { motion } from 'framer-motion';
import NextLink from 'next/link';
import { PageBuilderRenderer, RawHtmlRenderer } from '@/components/pagebuilder';
import { CmsPage, hasPageBuilderContent, extractPageBuilderContent } from '@/lib/magento/api/cms';

interface CmsPageRendererProps {
  page: CmsPage;
  showBreadcrumbs?: boolean;
  showTitle?: boolean;
  showContentHeading?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const CmsPageRenderer: React.FC<CmsPageRendererProps> = ({
  page,
  showBreadcrumbs = true,
  showTitle = true,
  showContentHeading = true,
  className,
  style,
}) => {
  const hasPageBuilder = hasPageBuilderContent(page.content);
  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(page.content) : null;

  // Animation variants
  const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8,
        delay: 0.2,
        ease: 'easeOut'
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 40 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8,
        delay: 0.4,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      className={`cms-page ${className || ''}`}
      style={style}
      variants={pageVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Breadcrumbs */}
      {showBreadcrumbs && (
        <Container maxWidth="lg" sx={{ py: 2 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link component={NextLink} href="/" color="inherit">
              Home
            </Link>
            <Typography color="text.primary">{page.title}</Typography>
          </Breadcrumbs>
        </Container>
      )}

      {/* Page Title */}
      {showTitle && (
        <Container maxWidth="lg" sx={{ py: 3 }}>
          <motion.div variants={titleVariants}>
            <Typography
              variant="h1"
              component="h1"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem', lg: '3rem' },
                fontWeight: 700,
                mb: 2,
                textAlign: 'center',
                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {page.title}
            </Typography>
          </motion.div>
        </Container>
      )}

      {/* Content Heading */}
      {showContentHeading && page.content_heading && (
        <Container maxWidth="lg" sx={{ pb: 2 }}>
          <motion.div variants={titleVariants}>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                fontSize: { xs: '1.5rem', md: '2rem' },
                fontWeight: 600,
                mb: 3,
                textAlign: 'center',
                color: 'text.secondary',
              }}
            >
              {page.content_heading}
            </Typography>
          </motion.div>
        </Container>
      )}

      {/* Page Content */}
      <motion.div variants={contentVariants}>
        {hasPageBuilder && pageBuilderContent ? (
          // Render with Page Builder
          <PageBuilderRenderer
            content={pageBuilderContent}
            config={{
              enableLazyLoading: true,
              imageOptimization: true,
            }}
            className="cms-page-content"
          />
        ) : (
          // Render as raw HTML
          <Container maxWidth="lg" sx={{ py: 3 }}>
            <RawHtmlRenderer
              html={page.content}
              className="cms-page-content"
            />
          </Container>
        )}
      </motion.div>

      {/* SEO Meta Information (hidden, for reference) */}
      <Box sx={{ display: 'none' }}>
        {page.meta_title && <meta name="title" content={page.meta_title} />}
        {page.meta_description && <meta name="description" content={page.meta_description} />}
        {page.meta_keywords && <meta name="keywords" content={page.meta_keywords} />}
      </Box>
    </motion.div>
  );
};

export default CmsPageRenderer;
