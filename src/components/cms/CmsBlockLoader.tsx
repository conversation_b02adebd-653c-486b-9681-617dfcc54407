'use client';

import React, { useEffect, useState } from 'react';
import { Box, Skeleton } from '@mui/material';
import { getCmsBlockWithPageBuilder, CmsBlock } from '@/lib/magento/api/cms';
import CmsBlockRenderer from './CmsBlockRenderer';

interface CmsBlockLoaderProps {
  identifier: string;
  fallback?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  animate?: boolean;
}

export const CmsBlockLoader: React.FC<CmsBlockLoaderProps> = ({
  identifier,
  fallback,
  className,
  style,
  animate = true,
}) => {
  const [block, setBlock] = useState<CmsBlock | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlock = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const blockData = await getCmsBlockWithPageBuilder(identifier);
        setBlock(blockData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load CMS block');
        console.error('Error loading CMS block:', err);
      } finally {
        setLoading(false);
      }
    };

    loadBlock();
  }, [identifier]);

  if (loading) {
    return (
      <Box className={className} style={style}>
        {fallback || (
          <Skeleton
            variant="rectangular"
            height={200}
            sx={{ borderRadius: 2 }}
          />
        )}
      </Box>
    );
  }

  if (error || !block) {
    if (process.env.NODE_ENV === 'development') {
      return (
        <Box
          className={className}
          style={style}
          sx={{
            p: 2,
            border: '1px dashed #f44336',
            borderRadius: 1,
            backgroundColor: '#ffebee',
            color: '#c62828',
            textAlign: 'center',
          }}
        >
          CMS Block Error: {error || `Block "${identifier}" not found`}
        </Box>
      );
    }
    return null;
  }

  return (
    <CmsBlockRenderer
      block={block}
      className={className}
      style={style}
      animate={animate}
    />
  );
};

export default CmsBlockLoader;
