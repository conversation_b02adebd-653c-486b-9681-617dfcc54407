'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  Rating,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  CircularProgress,
  Alert,
  Skeleton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { searchProductsClient } from '@/lib/magento/api/search';
import { ProductImage } from '@/components/ui/MagentoImage';
import type { SearchProduct, SearchResults as SearchResultsType } from '@/lib/magento/api/search';

// Styled components
const ProductCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const ProductMedia = styled(Box)(({ theme }) => ({
  position: 'relative',
  paddingTop: '100%', // 1:1 Aspect Ratio
  overflow: 'hidden',
  backgroundColor: theme.palette.grey[100],
}));

const PriceBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginTop: theme.spacing(1),
}));

const OriginalPrice = styled(Typography)(({ theme }) => ({
  textDecoration: 'line-through',
  color: theme.palette.text.secondary,
  fontSize: '0.875rem',
}));

const SalePrice = styled(Typography)(({ theme }) => ({
  color: theme.palette.error.main,
  fontWeight: 600,
  fontSize: '1.125rem',
}));

const RegularPrice = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  fontSize: '1.125rem',
  color: theme.palette.text.primary,
}));

// Search Results Props
interface SearchResultsProps {
  query: string;
  currentPage: number;
  sortBy: string;
  filters: Record<string, any>;
}

// Sort options
const sortOptions = [
  { value: 'relevance', label: 'Relevance' },
  { value: 'name', label: 'Name A-Z' },
  { value: 'name_desc', label: 'Name Z-A' },
  { value: 'price', label: 'Price Low to High' },
  { value: 'price_desc', label: 'Price High to Low' },
  { value: 'created_at', label: 'Newest First' },
  { value: 'rating', label: 'Highest Rated' },
];

export default function SearchResults({
  query,
  currentPage,
  sortBy,
  filters,
}: SearchResultsProps) {
  const [results, setResults] = useState<SearchResultsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load search results
  useEffect(() => {
    const loadResults = async () => {
      if (!query.trim()) {
        setResults(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Convert sort option to GraphQL format
        const sortConfig = getSortConfig(sortBy);
        
        // Convert filters to GraphQL format
        const filterConfig = getFilterConfig(filters);

        const searchResults = await searchProductsClient({
          search: query,
          pageSize: 20,
          currentPage,
          sort: sortConfig,
          filter: filterConfig,
        });

        setResults(searchResults);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load search results');
        console.error('Search error:', err);
      } finally {
        setLoading(false);
      }
    };

    loadResults();
  }, [query, currentPage, sortBy, filters]);

  // Convert sort option to GraphQL format
  const getSortConfig = (sort: string) => {
    switch (sort) {
      case 'name':
        return { name: 'ASC' };
      case 'name_desc':
        return { name: 'DESC' };
      case 'price':
        return { price: 'ASC' };
      case 'price_desc':
        return { price: 'DESC' };
      case 'created_at':
        return { created_at: 'DESC' };
      case 'rating':
        return { rating_summary: 'DESC' };
      default:
        return { relevance: 'DESC' };
    }
  };

  // Convert filters to GraphQL format
  const getFilterConfig = (filters: Record<string, any>) => {
    const config: any = {};

    if (filters.category) {
      config.category_id = { eq: filters.category };
    }

    if (filters.price_min !== undefined || filters.price_max !== undefined) {
      config.price = {};
      if (filters.price_min !== undefined) {
        config.price.from = filters.price_min.toString();
      }
      if (filters.price_max !== undefined) {
        config.price.to = filters.price_max.toString();
      }
    }

    return Object.keys(config).length > 0 ? config : undefined;
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());
    window.location.href = url.toString();
  };

  // Handle sort change
  const handleSortChange = (event: any) => {
    const url = new URL(window.location.href);
    url.searchParams.set('sort', event.target.value);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
  };

  // Format price
  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(price);
  };

  // Render product card
  const renderProductCard = (product: SearchProduct) => {
    const hasDiscount = product.price_range.minimum_price.discount?.amount_off > 0;
    const finalPrice = product.price_range.minimum_price.final_price.value;
    const regularPrice = product.price_range.minimum_price.regular_price.value;

    return (
      <Grid item xs={12} sm={6} md={4} lg={3} key={product.uid}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <ProductCard component={Link} href={`/product/${product.url_key}`}>
            <ProductMedia>
              {product.image ? (
                <ProductImage
                  src={product.image.url}
                  alt={product.image.label || product.name}
                  width={300}
                  height={300}
                  variant="small_image"
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                  }}
                />
              ) : (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'grey.100',
                    color: 'text.secondary',
                  }}
                >
                  No Image
                </Box>
              )}
              
              {hasDiscount && (
                <Chip
                  label={`-${Math.round(product.price_range.minimum_price.discount!.percent_off)}%`}
                  color="error"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    fontWeight: 600,
                  }}
                />
              )}
              
              {product.stock_status === 'OUT_OF_STOCK' && (
                <Chip
                  label="Out of Stock"
                  color="default"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                  }}
                />
              )}
            </ProductMedia>

            <CardContent sx={{ flexGrow: 1, p: 2 }}>
              <Typography
                variant="h6"
                component="h3"
                sx={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  lineHeight: 1.3,
                  mb: 1,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                }}
              >
                {product.name}
              </Typography>

              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mb: 1, fontSize: '0.875rem' }}
              >
                SKU: {product.sku}
              </Typography>

              {product.rating_summary > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Rating
                    value={product.rating_summary / 20} // Convert from 0-100 to 0-5
                    readOnly
                    size="small"
                    precision={0.1}
                  />
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    ({product.review_count})
                  </Typography>
                </Box>
              )}

              <PriceBox>
                {hasDiscount ? (
                  <>
                    <SalePrice>
                      {formatPrice(finalPrice, product.price_range.minimum_price.final_price.currency)}
                    </SalePrice>
                    <OriginalPrice>
                      {formatPrice(regularPrice, product.price_range.minimum_price.regular_price.currency)}
                    </OriginalPrice>
                  </>
                ) : (
                  <RegularPrice>
                    {formatPrice(finalPrice, product.price_range.minimum_price.final_price.currency)}
                  </RegularPrice>
                )}
              </PriceBox>
            </CardContent>
          </ProductCard>
        </motion.div>
      </Grid>
    );
  };

  // Loading skeleton
  const renderSkeleton = () => (
    <Grid container spacing={3}>
      {Array.from({ length: 8 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Card>
            <Skeleton variant="rectangular" height={300} />
            <CardContent>
              <Skeleton variant="text" height={24} />
              <Skeleton variant="text" height={20} width="60%" />
              <Skeleton variant="text" height={28} width="40%" />
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  if (!query.trim()) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h5" gutterBottom>
          Enter a search term to find products
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Use the search bar above to search our product catalog
        </Typography>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Skeleton variant="text" width={200} height={32} />
          <Skeleton variant="rectangular" width={200} height={40} />
        </Box>
        {renderSkeleton()}
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Search Error
        </Typography>
        <Typography variant="body2">
          {error}
        </Typography>
      </Alert>
    );
  }

  if (!results || results.total_count === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h5" gutterBottom>
          No products found for "{query}"
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Try adjusting your search terms or filters
        </Typography>
        <Button variant="contained" href="/search">
          Clear Search
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {/* Results Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          {results.total_count} result{results.total_count !== 1 ? 's' : ''} for "{query}"
        </Typography>
        
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Sort by</InputLabel>
          <Select
            value={sortBy}
            label="Sort by"
            onChange={handleSortChange}
          >
            {sortOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Product Grid */}
      <AnimatePresence>
        <Grid container spacing={3}>
          {results.items.map(renderProductCard)}
        </Grid>
      </AnimatePresence>

      {/* Pagination */}
      {results.page_info.total_pages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 6 }}>
          <Pagination
            count={results.page_info.total_pages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}
    </Box>
  );
}
