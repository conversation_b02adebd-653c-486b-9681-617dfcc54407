'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Slider,
  TextField,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Skeleton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { getCategoriesForMenuClient } from '@/lib/magento/api/categories';
import type { Category } from '@/lib/magento/api/categories';

// Styled components
const FilterPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  position: 'sticky',
  top: theme.spacing(2),
  maxHeight: 'calc(100vh - 100px)',
  overflowY: 'auto',
}));

const FilterHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(2),
}));

const PriceInputBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(1),
  alignItems: 'center',
  marginTop: theme.spacing(2),
}));

// Search Filters Props
interface SearchFiltersProps {
  query: string;
  currentFilters: Record<string, any>;
  currentSort: string;
}

// Price range interface
interface PriceRange {
  min: number;
  max: number;
}

export default function SearchFilters({
  query,
  currentFilters,
  currentSort,
}: SearchFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [priceRange, setPriceRange] = useState<PriceRange>({ min: 0, max: 1000 });
  const [tempPriceRange, setTempPriceRange] = useState<PriceRange>({ min: 0, max: 1000 });
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>(['categories', 'price']);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await getCategoriesForMenuClient();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading categories for filters:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Initialize filters from URL params
  useEffect(() => {
    if (currentFilters.category) {
      setSelectedCategories([currentFilters.category]);
    }
    
    if (currentFilters.price_min !== undefined || currentFilters.price_max !== undefined) {
      const min = currentFilters.price_min || 0;
      const max = currentFilters.price_max || 1000;
      setPriceRange({ min, max });
      setTempPriceRange({ min, max });
    }
  }, [currentFilters]);

  // Handle accordion expansion
  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedAccordions(prev => 
      isExpanded 
        ? [...prev, panel]
        : prev.filter(p => p !== panel)
    );
  };

  // Handle category selection
  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newSelected = checked
      ? [...selectedCategories, categoryId]
      : selectedCategories.filter(id => id !== categoryId);
    
    setSelectedCategories(newSelected);
    applyFilters({ categories: newSelected });
  };

  // Handle price range change
  const handlePriceRangeChange = (event: Event, newValue: number | number[]) => {
    const [min, max] = newValue as number[];
    setTempPriceRange({ min, max });
  };

  // Apply price filter
  const applyPriceFilter = () => {
    setPriceRange(tempPriceRange);
    applyFilters({ priceRange: tempPriceRange });
  };

  // Apply filters to URL
  const applyFilters = (updates: {
    categories?: string[];
    priceRange?: PriceRange;
  }) => {
    const url = new URL(window.location.href);
    
    // Update categories
    if (updates.categories !== undefined) {
      url.searchParams.delete('category');
      if (updates.categories.length > 0) {
        url.searchParams.set('category', updates.categories[0]); // For simplicity, use first category
      }
    }
    
    // Update price range
    if (updates.priceRange) {
      url.searchParams.delete('price_min');
      url.searchParams.delete('price_max');
      
      if (updates.priceRange.min > 0) {
        url.searchParams.set('price_min', updates.priceRange.min.toString());
      }
      if (updates.priceRange.max < 1000) {
        url.searchParams.set('price_max', updates.priceRange.max.toString());
      }
    }
    
    // Reset to first page when filters change
    url.searchParams.delete('page');
    
    window.location.href = url.toString();
  };

  // Clear all filters
  const clearAllFilters = () => {
    const url = new URL(window.location.href);
    url.searchParams.delete('category');
    url.searchParams.delete('price_min');
    url.searchParams.delete('price_max');
    url.searchParams.delete('page');
    
    window.location.href = url.toString();
  };

  // Get active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    if (selectedCategories.length > 0) count++;
    if (priceRange.min > 0 || priceRange.max < 1000) count++;
    return count;
  };

  // Render category tree
  const renderCategoryTree = (categoryList: Category[], level = 0) => {
    return categoryList.map(category => (
      <Box key={category.uid} sx={{ ml: level * 2 }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={selectedCategories.includes(category.id.toString())}
              onChange={(e) => handleCategoryChange(category.id.toString(), e.target.checked)}
              size="small"
            />
          }
          label={
            <Typography variant="body2" sx={{ fontSize: level === 0 ? '0.875rem' : '0.8125rem' }}>
              {category.name}
            </Typography>
          }
        />
        {category.children && category.children.length > 0 && (
          <Box sx={{ ml: 1 }}>
            {renderCategoryTree(category.children, level + 1)}
          </Box>
        )}
      </Box>
    ));
  };

  return (
    <FilterPaper elevation={2}>
      <FilterHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FilterIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Filters
          </Typography>
          {getActiveFiltersCount() > 0 && (
            <Chip
              label={getActiveFiltersCount()}
              size="small"
              color="primary"
              sx={{ ml: 1 }}
            />
          )}
        </Box>
        
        {getActiveFiltersCount() > 0 && (
          <Button
            size="small"
            startIcon={<ClearIcon />}
            onClick={clearAllFilters}
            sx={{ textTransform: 'none' }}
          >
            Clear All
          </Button>
        )}
      </FilterHeader>

      <Divider sx={{ mb: 2 }} />

      {/* Categories Filter */}
      <Accordion
        expanded={expandedAccordions.includes('categories')}
        onChange={handleAccordionChange('categories')}
        elevation={0}
        sx={{ '&:before': { display: 'none' } }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Categories
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 0 }}>
          {loading ? (
            <Box>
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton key={index} variant="text" height={32} sx={{ mb: 1 }} />
              ))}
            </Box>
          ) : (
            <FormGroup>
              {renderCategoryTree(categories.slice(0, 10))} {/* Limit for performance */}
            </FormGroup>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Price Range Filter */}
      <Accordion
        expanded={expandedAccordions.includes('price')}
        onChange={handleAccordionChange('price')}
        elevation={0}
        sx={{ '&:before': { display: 'none' } }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Price Range
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 0 }}>
          <Box sx={{ px: 1 }}>
            <Slider
              value={[tempPriceRange.min, tempPriceRange.max]}
              onChange={handlePriceRangeChange}
              valueLabelDisplay="auto"
              min={0}
              max={1000}
              step={10}
              valueLabelFormat={(value) => `$${value}`}
              sx={{ mb: 2 }}
            />
            
            <PriceInputBox>
              <TextField
                label="Min"
                type="number"
                size="small"
                value={tempPriceRange.min}
                onChange={(e) => setTempPriceRange(prev => ({ 
                  ...prev, 
                  min: Math.max(0, parseInt(e.target.value) || 0) 
                }))}
                InputProps={{ startAdornment: '$' }}
                sx={{ flex: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                to
              </Typography>
              <TextField
                label="Max"
                type="number"
                size="small"
                value={tempPriceRange.max}
                onChange={(e) => setTempPriceRange(prev => ({ 
                  ...prev, 
                  max: Math.min(1000, parseInt(e.target.value) || 1000) 
                }))}
                InputProps={{ startAdornment: '$' }}
                sx={{ flex: 1 }}
              />
            </PriceInputBox>
            
            <Button
              variant="outlined"
              size="small"
              fullWidth
              onClick={applyPriceFilter}
              sx={{ mt: 2, textTransform: 'none' }}
            >
              Apply Price Filter
            </Button>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Stock Status Filter */}
      <Accordion
        expanded={expandedAccordions.includes('stock')}
        onChange={handleAccordionChange('stock')}
        elevation={0}
        sx={{ '&:before': { display: 'none' } }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Availability
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 0 }}>
          <FormGroup>
            <FormControlLabel
              control={<Checkbox size="small" />}
              label={
                <Typography variant="body2">
                  In Stock
                </Typography>
              }
            />
            <FormControlLabel
              control={<Checkbox size="small" />}
              label={
                <Typography variant="body2">
                  On Sale
                </Typography>
              }
            />
            <FormControlLabel
              control={<Checkbox size="small" />}
              label={
                <Typography variant="body2">
                  New Arrivals
                </Typography>
              }
            />
          </FormGroup>
        </AccordionDetails>
      </Accordion>

      {/* Rating Filter */}
      <Accordion
        expanded={expandedAccordions.includes('rating')}
        onChange={handleAccordionChange('rating')}
        elevation={0}
        sx={{ '&:before': { display: 'none' } }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Customer Rating
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 0 }}>
          <FormGroup>
            {[4, 3, 2, 1].map(rating => (
              <FormControlLabel
                key={rating}
                control={<Checkbox size="small" />}
                label={
                  <Typography variant="body2">
                    {rating}+ Stars
                  </Typography>
                }
              />
            ))}
          </FormGroup>
        </AccordionDetails>
      </Accordion>
    </FilterPaper>
  );
}
