'use client';

import React from 'react';
import { Box, Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ButtonElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface ButtonProps extends PageBuilderElementProps {
  element: ButtonElement;
}

export const Button: React.FC<ButtonProps> = ({ element, className, style }) => {
  const { content, attributes, styles } = element;

  // Build container styles
  const containerStyles: React.CSSProperties = {
    display: 'flex',
    ...styles,
    ...style,
  };

  // Text alignment for container
  if (attributes.textAlign) {
    switch (attributes.textAlign) {
      case 'left':
        containerStyles.justifyContent = 'flex-start';
        break;
      case 'center':
        containerStyles.justifyContent = 'center';
        break;
      case 'right':
        containerStyles.justifyContent = 'flex-end';
        break;
    }
  }

  // Build button styles
  const buttonStyles: React.CSSProperties = {
    textTransform: 'none',
    fontWeight: 500,
    borderRadius: '8px',
    padding: '12px 24px',
    fontSize: '1rem',
    transition: 'all 0.3s ease',
  };

  // Button type styling
  let variant: 'contained' | 'outlined' | 'text' = 'contained';
  let color: 'primary' | 'secondary' | 'inherit' = 'primary';

  switch (attributes.buttonType) {
    case 'primary':
      variant = 'contained';
      color = 'primary';
      break;
    case 'secondary':
      variant = 'outlined';
      color = 'primary';
      break;
    case 'link':
      variant = 'text';
      color = 'primary';
      break;
  }

  // Custom colors
  if (attributes.backgroundColor) {
    buttonStyles.backgroundColor = attributes.backgroundColor;
  }
  if (attributes.textColor) {
    buttonStyles.color = attributes.textColor;
  }

  // Border styles
  if (attributes.border) {
    buttonStyles.border = attributes.border;
  }
  if (attributes.borderColor) {
    buttonStyles.borderColor = attributes.borderColor;
  }
  if (attributes.borderWidth) {
    buttonStyles.borderWidth = attributes.borderWidth;
  }
  if (attributes.borderRadius) {
    buttonStyles.borderRadius = attributes.borderRadius;
  }

  // Animation variants
  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 }
    }
  };

  const ButtonComponent = (
    <motion.div
      variants={buttonVariants}
      initial="hidden"
      whileInView="visible"
      whileHover="hover"
      whileTap="tap"
      viewport={{ once: true, amount: 0.1 }}
    >
      <MuiButton
        variant={variant}
        color={color}
        sx={{
          ...buttonStyles,
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          },
        }}
      >
        {content || attributes.buttonText || 'Button'}
      </MuiButton>
    </motion.div>
  );

  return (
    <Box
      className={`pagebuilder-button ${className || ''}`}
      sx={containerStyles}
    >
      {attributes.link ? (
        <Link
          href={attributes.link}
          target={attributes.linkTarget || '_self'}
          style={{ textDecoration: 'none' }}
        >
          {ButtonComponent}
        </Link>
      ) : (
        ButtonComponent
      )}
    </Box>
  );
};

export default Button;
