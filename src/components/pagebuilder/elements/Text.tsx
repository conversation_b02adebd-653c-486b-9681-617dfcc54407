'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import { motion } from 'framer-motion';
import { TextElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface TextProps extends PageBuilderElementProps {
  element: TextElement;
}

export const Text: React.FC<TextProps> = ({ element, className, style }) => {
  const { content, attributes, styles } = element;

  // Build styles
  const textStyles: React.CSSProperties = {
    wordWrap: 'break-word',
    lineHeight: 1.6,
    ...styles,
    ...style,
  };

  // Text alignment
  if (attributes.textAlign) {
    textStyles.textAlign = attributes.textAlign as any;
  }

  // Border styles
  if (attributes.border) {
    textStyles.border = attributes.border;
  }
  if (attributes.borderColor) {
    textStyles.borderColor = attributes.borderColor;
  }
  if (attributes.borderWidth) {
    textStyles.borderWidth = attributes.borderWidth;
  }
  if (attributes.borderRadius) {
    textStyles.borderRadius = attributes.borderRadius;
  }

  // Animation variants
  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  // Check if content contains HTML
  const isHtml = content && (content.includes('<') || content.includes('&'));

  return (
    <motion.div
      className={`pagebuilder-text ${className || ''}`}
      variants={textVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {isHtml ? (
        <Box
          component="div"
          sx={textStyles}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      ) : (
        <Typography
          component="div"
          sx={textStyles}
        >
          {content}
        </Typography>
      )}
    </motion.div>
  );
};

export default Text;
