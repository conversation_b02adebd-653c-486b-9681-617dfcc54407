'use client';

import React from 'react';
import { Typography } from '@mui/material';
import { motion } from 'framer-motion';
import { HeadingElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface HeadingProps extends PageBuilderElementProps {
  element: HeadingElement;
}

export const Heading: React.FC<HeadingProps> = ({ element, className, style }) => {
  const { content, attributes, styles } = element;

  // Determine heading variant
  const variant = attributes.headingType || 'h2';

  // Build styles
  const headingStyles: React.CSSProperties = {
    wordWrap: 'break-word',
    marginBottom: '1rem',
    ...styles,
    ...style,
  };

  // Text alignment
  if (attributes.textAlign) {
    headingStyles.textAlign = attributes.textAlign as any;
  }

  // Border styles
  if (attributes.border) {
    headingStyles.border = attributes.border;
  }
  if (attributes.borderColor) {
    headingStyles.borderColor = attributes.borderColor;
  }
  if (attributes.borderWidth) {
    headingStyles.borderWidth = attributes.borderWidth;
  }
  if (attributes.borderRadius) {
    headingStyles.borderRadius = attributes.borderRadius;
  }

  // Animation variants
  const headingVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  };

  // Check if content contains HTML
  const isHtml = content && (content.includes('<') || content.includes('&'));

  return (
    <motion.div
      className={`pagebuilder-heading ${className || ''}`}
      variants={headingVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {isHtml ? (
        <Typography
          variant={variant as any}
          component={variant}
          sx={headingStyles}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      ) : (
        <Typography
          variant={variant as any}
          component={variant}
          sx={headingStyles}
        >
          {content}
        </Typography>
      )}
    </motion.div>
  );
};

export default Heading;
