'use client';

import React from 'react';
import { Box } from '@mui/material';
import { motion } from 'framer-motion';
import { HtmlElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface HtmlProps extends PageBuilderElementProps {
  element: HtmlElement;
}

export const Html: React.FC<HtmlProps> = ({ element, className, style }) => {
  const { content, styles } = element;

  // Build styles
  const htmlStyles: React.CSSProperties = {
    width: '100%',
    ...styles,
    ...style,
  };

  // Animation variants
  const htmlVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      className={`pagebuilder-html ${className || ''}`}
      variants={htmlVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      <Box
        component="div"
        sx={htmlStyles}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </motion.div>
  );
};

export default Html;
