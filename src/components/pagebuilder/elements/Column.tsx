'use client';

import React from 'react';
import { Box } from '@mui/material';
import { motion } from 'framer-motion';
import { ColumnElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';
import { convertColumnWidth } from '@/lib/pagebuilder/utils';

interface ColumnProps extends PageBuilderElementProps {
  element: ColumnElement;
}

export const Column: React.FC<ColumnProps> = ({ element, children, className, style }) => {
  const { attributes, styles } = element;

  // Build styles
  const columnStyles: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    boxSizing: 'border-box',
    flex: '1 1 auto',
    ...styles,
    ...style,
  };

  // Width handling
  if (attributes.width) {
    const width = convertColumnWidth(attributes.width);
    columnStyles.width = width;
    columnStyles.flexBasis = width;
    columnStyles.flexGrow = 0;
    columnStyles.flexShrink = 0;
  }

  // Background styles
  if (attributes.backgroundColor) {
    columnStyles.backgroundColor = attributes.backgroundColor;
  }

  if (attributes.backgroundImage) {
    columnStyles.backgroundImage = `url(${attributes.backgroundImage})`;
    columnStyles.backgroundSize = attributes.backgroundSize || 'cover';
    columnStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
    columnStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
    columnStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
  }

  // Height handling
  if (attributes.appearance === 'full-height') {
    columnStyles.height = '100%';
  } else if (attributes.minHeight) {
    columnStyles.minHeight = attributes.minHeight;
  }

  // Vertical alignment
  if (attributes.verticalAlignment) {
    switch (attributes.verticalAlignment) {
      case 'top':
        columnStyles.justifyContent = 'flex-start';
        break;
      case 'middle':
        columnStyles.justifyContent = 'center';
        break;
      case 'bottom':
        columnStyles.justifyContent = 'flex-end';
        break;
    }
  }

  // Animation variants
  const columnVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      className={`pagebuilder-column ${className || ''}`}
      style={columnStyles}
      variants={columnVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {/* Background overlay for better content readability */}
      {attributes.backgroundImage && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            zIndex: 0,
          }}
        />
      )}
      
      <Box
        sx={{
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          height: '100%',
          padding: { xs: 1, sm: 2 },
        }}
      >
        {children}
      </Box>
    </motion.div>
  );
};

export default Column;
