'use client';

import React from 'react';
import { Box, Container } from '@mui/material';
import { motion } from 'framer-motion';
import { RowElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface RowProps extends PageBuilderElementProps {
  element: RowElement;
}

export const Row: React.FC<RowProps> = ({ element, children, className, style }) => {
  const { attributes, styles } = element;

  // Determine container type based on appearance
  const getContainerComponent = () => {
    switch (attributes.appearance) {
      case 'contained':
        return Container;
      case 'full-width':
      case 'full-bleed':
      default:
        return Box;
    }
  };

  const ContainerComponent = getContainerComponent();

  // Build styles
  const rowStyles: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    boxSizing: 'border-box',
    ...styles,
    ...style,
  };

  // Background styles
  if (attributes.backgroundColor) {
    rowStyles.backgroundColor = attributes.backgroundColor;
  }

  if (attributes.backgroundImage) {
    rowStyles.backgroundImage = `url(${attributes.backgroundImage})`;
    rowStyles.backgroundSize = attributes.backgroundSize || 'cover';
    rowStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
    rowStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
    rowStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
  }

  if (attributes.minHeight) {
    rowStyles.minHeight = attributes.minHeight;
  }

  // Vertical alignment
  if (attributes.verticalAlignment) {
    switch (attributes.verticalAlignment) {
      case 'top':
        rowStyles.justifyContent = 'flex-start';
        break;
      case 'middle':
        rowStyles.justifyContent = 'center';
        break;
      case 'bottom':
        rowStyles.justifyContent = 'flex-end';
        break;
    }
  }

  // Parallax effect (if enabled and in browser)
  const parallaxProps = attributes.enableParallax && typeof window !== 'undefined' ? {
    initial: { y: 0 },
    whileInView: { y: -20 },
    transition: { 
      duration: 0.6,
      ease: 'easeOut'
    },
    viewport: { once: false, amount: 0.3 }
  } : {};

  const containerProps = attributes.appearance === 'contained' ? {
    maxWidth: 'lg' as const,
    sx: { px: { xs: 2, sm: 3 } }
  } : {
    sx: { 
      width: '100%',
      maxWidth: 'none',
      px: attributes.appearance === 'full-bleed' ? 0 : { xs: 2, sm: 3 }
    }
  };

  return (
    <motion.div
      className={`pagebuilder-row ${className || ''}`}
      style={rowStyles}
      {...parallaxProps}
    >
      {/* Background overlay for better text readability */}
      {attributes.backgroundImage && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            zIndex: 0,
          }}
        />
      )}
      
      <ContainerComponent
        {...containerProps}
        sx={{
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          height: '100%',
          ...containerProps.sx,
        }}
      >
        {children}
      </ContainerComponent>
    </motion.div>
  );
};

export default Row;
