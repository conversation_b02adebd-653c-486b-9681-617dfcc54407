'use client';

import React from 'react';
import { Box, Divider as MuiDivider } from '@mui/material';
import { motion } from 'framer-motion';
import { DividerElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';

interface DividerProps extends PageBuilderElementProps {
  element: DividerElement;
}

export const Divider: React.FC<DividerProps> = ({ element, className, style }) => {
  const { attributes, styles } = element;

  // Build styles
  const dividerStyles: React.CSSProperties = {
    width: attributes.lineWidth || '100%',
    height: attributes.lineThickness || '1px',
    backgroundColor: attributes.lineColor || '#e0e0e0',
    border: 'none',
    margin: '2rem 0',
    ...styles,
    ...style,
  };

  // Animation variants
  const dividerVariants = {
    hidden: { opacity: 0, scaleX: 0 },
    visible: { 
      opacity: 1, 
      scaleX: 1,
      transition: { 
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  };

  return (
    <Box
      className={`pagebuilder-divider ${className || ''}`}
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        my: 2,
      }}
    >
      <motion.div
        variants={dividerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        style={{ width: '100%' }}
      >
        <MuiDivider
          sx={{
            ...dividerStyles,
            '&::before, &::after': {
              borderColor: attributes.lineColor || 'divider',
            },
          }}
        />
      </motion.div>
    </Box>
  );
};

export default Divider;
