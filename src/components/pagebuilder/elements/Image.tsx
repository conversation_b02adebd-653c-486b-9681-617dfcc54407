'use client';

import React from 'react';
import { Box } from '@mui/material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { ImageElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';
import { optimizeImageUrl } from '@/lib/pagebuilder/utils';

interface ImageProps extends PageBuilderElementProps {
  element: ImageElement;
}

export const PageBuilderImage: React.FC<ImageProps> = ({ element, className, style }) => {
  const { attributes, styles } = element;

  // Build styles
  const imageContainerStyles: React.CSSProperties = {
    position: 'relative',
    display: 'inline-block',
    maxWidth: '100%',
    ...styles,
    ...style,
  };

  // Alignment
  if (attributes.alignment) {
    switch (attributes.alignment) {
      case 'left':
        imageContainerStyles.textAlign = 'left';
        break;
      case 'center':
        imageContainerStyles.textAlign = 'center';
        imageContainerStyles.margin = '0 auto';
        break;
      case 'right':
        imageContainerStyles.textAlign = 'right';
        imageContainerStyles.marginLeft = 'auto';
        break;
    }
  }

  // Border styles
  const imageStyles: React.CSSProperties = {
    maxWidth: '100%',
    height: 'auto',
  };

  if (attributes.border) {
    imageStyles.border = attributes.border;
  }
  if (attributes.borderColor) {
    imageStyles.borderColor = attributes.borderColor;
  }
  if (attributes.borderWidth) {
    imageStyles.borderWidth = attributes.borderWidth;
  }
  if (attributes.borderRadius) {
    imageStyles.borderRadius = attributes.borderRadius;
  }

  // Animation variants
  const imageVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  // Optimize image URL
  const optimizedSrc = optimizeImageUrl(attributes.src);

  // Image component
  const ImageComponent = (
    <motion.div
      variants={imageVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        component="div"
        sx={{
          position: 'relative',
          display: 'inline-block',
          ...imageStyles,
        }}
      >
        <Image
          src={optimizedSrc}
          alt={attributes.alt || ''}
          title={attributes.title}
          width={800} // Default width, will be responsive
          height={600} // Default height, will be responsive
          style={{
            width: '100%',
            height: 'auto',
            ...imageStyles,
          }}
          loading={attributes.lazyLoading !== false ? 'lazy' : 'eager'}
          quality={85}
        />
        
        {/* Caption */}
        {attributes.caption && (
          <Box
            component="figcaption"
            sx={{
              mt: 1,
              fontSize: '0.875rem',
              color: 'text.secondary',
              textAlign: attributes.alignment || 'center',
              fontStyle: 'italic',
            }}
          >
            {attributes.caption}
          </Box>
        )}
      </Box>
    </motion.div>
  );

  return (
    <Box
      className={`pagebuilder-image ${className || ''}`}
      component="figure"
      sx={{
        margin: 0,
        padding: 0,
        ...imageContainerStyles,
      }}
    >
      {attributes.link ? (
        <Link
          href={attributes.link}
          target={attributes.linkTarget || '_self'}
          style={{ textDecoration: 'none' }}
        >
          {ImageComponent}
        </Link>
      ) : (
        ImageComponent
      )}
    </Box>
  );
};

export default PageBuilderImage;
