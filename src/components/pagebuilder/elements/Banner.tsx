'use client';

import React from 'react';
import { Box, Typography, But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { BannerElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';
import { optimizeImageUrl } from '@/lib/pagebuilder/utils';

interface BannerProps extends PageBuilderElementProps {
  element: BannerElement;
}

export const Banner: React.FC<BannerProps> = ({ element, children, className, style }) => {
  const { attributes, styles } = element;

  // Build container styles
  const bannerStyles: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    minHeight: attributes.minHeight || '400px',
    ...styles,
    ...style,
  };

  // Background styles
  if (attributes.backgroundColor) {
    bannerStyles.backgroundColor = attributes.backgroundColor;
  }

  // Content placement
  let contentAlignment = 'center';
  if (attributes.contentPlacement) {
    contentAlignment = attributes.contentPlacement;
  }

  // Animation variants
  const bannerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8,
        delay: 0.3,
        ease: 'easeOut'
      }
    }
  };

  return (
    <motion.div
      className={`pagebuilder-banner ${className || ''}`}
      style={bannerStyles}
      variants={bannerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
    >
      {/* Background Image */}
      {attributes.backgroundImage && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
          }}
        >
          <Image
            src={optimizeImageUrl(attributes.backgroundImage)}
            alt=""
            fill
            style={{
              objectFit: attributes.backgroundSize === 'contain' ? 'contain' : 'cover',
              objectPosition: attributes.backgroundPosition || 'center center',
            }}
            quality={85}
            priority
          />
        </Box>
      )}

      {/* Overlay */}
      {attributes.showOverlay && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: attributes.overlayColor || 'rgba(0, 0, 0, 0.4)',
            zIndex: 1,
          }}
        />
      )}

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          width: '100%',
          maxWidth: '800px',
          padding: { xs: 3, md: 6 },
          textAlign: contentAlignment,
          color: 'white',
        }}
      >
        <motion.div
          variants={contentVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* Banner Content */}
          {attributes.content && (
            <Box sx={{ mb: 3 }}>
              {attributes.content.includes('<') ? (
                <div dangerouslySetInnerHTML={{ __html: attributes.content }} />
              ) : (
                <Typography
                  variant="h3"
                  component="h2"
                  sx={{
                    fontWeight: 700,
                    mb: 2,
                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                    fontSize: { xs: '2rem', md: '3rem' },
                  }}
                >
                  {attributes.content}
                </Typography>
              )}
            </Box>
          )}

          {/* Children content */}
          {children && (
            <Box sx={{ mb: 3 }}>
              {children}
            </Box>
          )}

          {/* Button */}
          {attributes.showButton && attributes.buttonText && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {attributes.buttonLink ? (
                <Link
                  href={attributes.buttonLink}
                  target={attributes.buttonTarget || '_self'}
                  style={{ textDecoration: 'none' }}
                >
                  <MuiButton
                    variant={attributes.buttonType === 'secondary' ? 'outlined' : 'contained'}
                    size="large"
                    sx={{
                      fontSize: '1.125rem',
                      fontWeight: 600,
                      px: 4,
                      py: 1.5,
                      borderRadius: 2,
                      textTransform: 'none',
                      boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                      color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                      borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                      '&:hover': {
                        boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease-in-out',
                    }}
                  >
                    {attributes.buttonText}
                  </MuiButton>
                </Link>
              ) : (
                <MuiButton
                  variant={attributes.buttonType === 'secondary' ? 'outlined' : 'contained'}
                  size="large"
                  sx={{
                    fontSize: '1.125rem',
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    borderRadius: 2,
                    textTransform: 'none',
                    boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                    color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                    borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                    '&:hover': {
                      boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease-in-out',
                  }}
                >
                  {attributes.buttonText}
                </MuiButton>
              )}
            </motion.div>
          )}
        </motion.div>
      </Box>
    </motion.div>
  );
};

export default Banner;
