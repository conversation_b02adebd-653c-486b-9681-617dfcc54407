'use client';

import React, { useState } from 'react';
import { Box, IconButton } from '@mui/material';
import { PlayArrow, Pause } from '@mui/icons-material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { VideoElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';
import { optimizeImageUrl } from '@/lib/pagebuilder/utils';

interface VideoProps extends PageBuilderElementProps {
  element: VideoElement;
}

export const Video: React.FC<VideoProps> = ({ element, className, style }) => {
  const { attributes, styles } = element;
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(false);

  // Build container styles
  const videoContainerStyles: React.CSSProperties = {
    position: 'relative',
    width: '100%',
    maxWidth: attributes.maxWidth || '100%',
    margin: '0 auto',
    ...styles,
    ...style,
  };

  // Get video embed URL
  const getEmbedUrl = () => {
    const { videoType, videoUrl, videoId } = attributes;

    if (videoType === 'youtube') {
      const id = videoId || extractYouTubeId(videoUrl);
      return `https://www.youtube.com/embed/${id}?autoplay=${attributes.autoplay ? 1 : 0}&mute=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
    }

    if (videoType === 'vimeo') {
      const id = videoId || extractVimeoId(videoUrl);
      return `https://player.vimeo.com/video/${id}?autoplay=${attributes.autoplay ? 1 : 0}&muted=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
    }

    return videoUrl;
  };

  // Extract YouTube video ID
  const extractYouTubeId = (url?: string): string => {
    if (!url) return '';
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    return match ? match[1] : '';
  };

  // Extract Vimeo video ID
  const extractVimeoId = (url?: string): string => {
    if (!url) return '';
    const match = url.match(/vimeo\.com\/(\d+)/);
    return match ? match[1] : '';
  };

  // Handle play/pause
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // Animation variants
  const videoVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  return (
    <motion.div
      className={`pagebuilder-video ${className || ''}`}
      style={videoContainerStyles}
      variants={videoVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          paddingBottom: '56.25%', // 16:9 aspect ratio
          height: 0,
          overflow: 'hidden',
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        }}
      >
        {attributes.videoType === 'mp4' ? (
          // Native HTML5 video
          <video
            src={attributes.videoUrl}
            autoPlay={attributes.autoplay}
            muted={attributes.muted}
            loop={attributes.loop}
            controls={attributes.controls}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
            poster={attributes.fallbackImage ? optimizeImageUrl(attributes.fallbackImage) : undefined}
          />
        ) : (
          // Embedded video (YouTube/Vimeo)
          <>
            {!isPlaying && attributes.fallbackImage ? (
              // Fallback image with play button
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  cursor: 'pointer',
                }}
                onClick={handlePlayPause}
              >
                <Image
                  src={optimizeImageUrl(attributes.fallbackImage)}
                  alt="Video thumbnail"
                  fill
                  style={{ objectFit: 'cover' }}
                  quality={85}
                />
                
                {/* Play button overlay */}
                <motion.div
                  variants={overlayVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                  }}
                >
                  <IconButton
                    sx={{
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      width: 80,
                      height: 80,
                      '&:hover': {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        transform: 'scale(1.1)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    <PlayArrow sx={{ fontSize: 40 }} />
                  </IconButton>
                </motion.div>
              </Box>
            ) : (
              // Embedded iframe
              <iframe
                src={getEmbedUrl()}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  border: 'none',
                }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                loading={attributes.lazyLoading !== false ? 'lazy' : 'eager'}
              />
            )}
          </>
        )}

        {/* Custom controls overlay */}
        {attributes.videoType === 'mp4' && showControls && (
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'absolute',
              bottom: 16,
              left: 16,
            }}
          >
            <IconButton
              onClick={handlePlayPause}
              sx={{
                backgroundColor: 'rgba(0,0,0,0.7)',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(0,0,0,0.8)',
                },
              }}
            >
              {isPlaying ? <Pause /> : <PlayArrow />}
            </IconButton>
          </motion.div>
        )}
      </Box>
    </motion.div>
  );
};

export default Video;
