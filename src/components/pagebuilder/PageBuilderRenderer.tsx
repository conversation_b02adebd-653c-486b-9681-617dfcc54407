'use client';

import React from 'react';
import {
  PageBuilderRenderer as NextPage<PERSON><PERSON>er<PERSON><PERSON><PERSON>,
  PageBuilderRendererSSR,
  usePageBuilderContext,
  usePageBuilderConfig,
  usePageBuilderEditing,
  usePageBuilderDeviceType,
  PageBuilderRendererProps,
} from '@magento/nextjs-pagebuilder';

// Import the CSS styles (commented out until package is built)
// import '@magento/nextjs-pagebuilder/dist/styles/pagebuilder.css';

// Configuration for our Magento 2 integration
const DEFAULT_CONFIG = {
  enableServerSideRendering: true,
  enableImageOptimization: true,
  enableLazyLoading: true,
  sanitizeHtml: true,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  defaultImageQuality: 75,
  cssPrefix: 'pagebuilder',
};

// Main Page Builder Renderer component for our app
export const PageBuilderRenderer: React.FC<PageBuilderRendererProps> = ({
  content,
  config = {},
  className,
  style,
  isEditing = false,
  deviceType = 'desktop',
}) => {
  // Merge our default config with user config
  const mergedConfig = {
    ...DEFAULT_CONFIG,
    ...config,
  };

  return (
    <NextPageBuilderRenderer
      content={content}
      config={mergedConfig}
      className={className}
      style={style}
      isEditing={isEditing}
      deviceType={deviceType}
    />
  );
};

// Server-side renderer
export const PageBuilderRendererSSRWrapper: React.FC<
  PageBuilderRendererProps
> = props => {
  const mergedConfig = {
    ...DEFAULT_CONFIG,
    ...props.config,
  };

  return <PageBuilderRendererSSR {...props} config={mergedConfig} />;
};

// Raw HTML renderer for backward compatibility
export const RawHtmlRenderer: React.FC<{
  html: string;
  className?: string;
}> = ({ html, className }) => {
  return (
    <div className={className} dangerouslySetInnerHTML={{ __html: html }} />
  );
};

// Re-export hooks for convenience
export {
  usePageBuilderContext,
  usePageBuilderConfig,
  usePageBuilderEditing,
  usePageBuilderDeviceType,
};

// Default export
export default PageBuilderRenderer;
