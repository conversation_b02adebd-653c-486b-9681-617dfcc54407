'use client';

import React, { createContext, useContext } from 'react';
import { Box } from '@mui/material';
import {
  PageBuilderContent,
  PageBuilderElement,
  PageBuilderElementType,
  PageBuilderContext,
  PageBuilderParserConfig,
} from '@/lib/pagebuilder/types';
import { mergeConfig } from '@/lib/pagebuilder/utils';

// Import Page Builder components
import {
  Row,
  Column,
  Text,
  Heading,
  PageBuilderImage,
  Button,
  Banner,
  Video,
  Html,
  Divider,
} from './elements';

// Page Builder Context
const PageBuilderContextProvider = createContext<PageBuilderContext | null>(
  null
);

// Hook to use Page Builder context
export const usePageBuilderContext = (): PageBuilderContext => {
  const context = useContext(PageBuilderContextProvider);
  if (!context) {
    throw new Error(
      'usePageBuilderContext must be used within a PageBuilderRenderer'
    );
  }
  return context;
};

// Component mapping
const COMPONENT_MAP = {
  [PageBuilderElementType.ROW]: Row,
  [PageBuilderElementType.COLUMN]: Column,
  [PageBuilderElementType.TEXT]: Text,
  [PageBuilderElementType.HEADING]: Heading,
  [PageBuilderElementType.IMAGE]: PageBuilderImage,
  [PageBuilderElementType.BUTTON]: Button,
  [PageBuilderElementType.BANNER]: Banner,
  [PageBuilderElementType.VIDEO]: Video,
  [PageBuilderElementType.HTML]: Html,
  [PageBuilderElementType.DIVIDER]: Divider,
  // Add more components as needed
};

// Props for PageBuilderRenderer
interface PageBuilderRendererProps {
  content: PageBuilderContent | string;
  config?: Partial<PageBuilderParserConfig>;
  className?: string;
  style?: React.CSSProperties;
  isEditing?: boolean;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}

// Props for ElementRenderer
interface ElementRendererProps {
  element: PageBuilderElement;
  config: PageBuilderParserConfig;
}

// Individual element renderer
const ElementRenderer: React.FC<ElementRendererProps> = ({
  element,
  config,
}) => {
  // Get component from mapping or custom overrides
  const Component =
    config.componentOverrides?.[element.type] || COMPONENT_MAP[element.type];

  if (!Component) {
    // Fallback for unknown element types
    console.warn(`Unknown Page Builder element type: ${element.type}`);
    return (
      <Box
        sx={{
          p: 2,
          border: '1px dashed #ccc',
          borderRadius: 1,
          backgroundColor: '#f5f5f5',
          textAlign: 'center',
          color: 'text.secondary',
        }}
      >
        Unknown element: {element.type}
      </Box>
    );
  }

  // Render children if they exist
  const children = element.children?.map((child, index) => (
    <ElementRenderer key={child.id || index} element={child} config={config} />
  ));

  return (
    <Component
      element={element}
      className={`pagebuilder-element pagebuilder-${element.type}`}
    >
      {children}
    </Component>
  );
};

// Main Page Builder Renderer component
export const PageBuilderRenderer: React.FC<PageBuilderRendererProps> = ({
  content,
  config = {},
  className,
  style,
  isEditing = false,
  deviceType = 'desktop',
}) => {
  // Merge configuration
  const mergedConfig = mergeConfig(config);

  // Parse content if it's a string
  let pageBuilderContent: PageBuilderContent;

  if (typeof content === 'string') {
    // Import parser dynamically to avoid SSR issues
    const { parsePageBuilderContent } = require('./parser/PageBuilderParser');
    pageBuilderContent = parsePageBuilderContent(content, mergedConfig);
  } else {
    pageBuilderContent = content;
  }

  // Create context value
  const contextValue: PageBuilderContext = {
    config: mergedConfig,
    isEditing,
    deviceType,
  };

  // If no Page Builder elements found, render as raw HTML
  if (
    !pageBuilderContent.elements ||
    pageBuilderContent.elements.length === 0
  ) {
    // Check if we have raw HTML content to render
    if (pageBuilderContent.rawHtml && pageBuilderContent.rawHtml.trim()) {
      return (
        <Box
          className={`pagebuilder-raw-html ${className || ''}`}
          sx={{
            width: '100%',
            '& ul': {
              listStyle: 'none',
              padding: 0,
              margin: 0,
            },
            '& li': {
              marginBottom: 1,
            },
            '& a': {
              color: 'rgba(255, 255, 255, 0.8)',
              textDecoration: 'none',
              fontSize: '0.875rem',
              transition: 'color 0.2s ease',
              '&:hover': {
                color: 'primary.light',
              },
            },
            '& p': {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: '0.875rem',
              lineHeight: 1.6,
              margin: 0,
              marginBottom: 1,
            },
            ...style,
          }}
          dangerouslySetInnerHTML={{ __html: pageBuilderContent.rawHtml }}
        />
      );
    }
    return null;
  }

  return (
    <PageBuilderContextProvider.Provider value={contextValue}>
      <Box
        className={`pagebuilder-content ${className || ''}`}
        sx={{
          width: '100%',
          ...style,
        }}
      >
        {pageBuilderContent.elements.map((element, index) => (
          <ElementRenderer
            key={element.id || index}
            element={element}
            config={mergedConfig}
          />
        ))}
      </Box>
    </PageBuilderContextProvider.Provider>
  );
};

// Server-side safe renderer (for SSR)
export const PageBuilderRendererSSR: React.FC<
  PageBuilderRendererProps
> = props => {
  // For SSR, we need to handle the parsing differently
  if (typeof window === 'undefined' && typeof props.content === 'string') {
    // Server-side: parse with regex-based parser
    const { parsePageBuilderContent } = require('./parser/PageBuilderParser');
    const parsedContent = parsePageBuilderContent(props.content, props.config);

    return <PageBuilderRenderer {...props} content={parsedContent} />;
  }

  // Client-side: use normal renderer
  return <PageBuilderRenderer {...props} />;
};

// Utility component for rendering raw HTML (fallback)
export const RawHtmlRenderer: React.FC<{
  html: string;
  className?: string;
}> = ({ html, className }) => {
  return (
    <Box
      className={`pagebuilder-raw-html ${className || ''}`}
      dangerouslySetInnerHTML={{ __html: html }}
      sx={{
        '& .pagebuilder-row': {
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
        },
        '& .pagebuilder-column-group': {
          display: 'flex',
          flexWrap: 'wrap',
          width: '100%',
        },
        '& .pagebuilder-column': {
          flex: '1 1 auto',
          minWidth: 0,
        },
        // Add more CSS for Page Builder elements
      }}
    />
  );
};

// Hook for accessing Page Builder configuration
export const usePageBuilderConfig = (): PageBuilderParserConfig => {
  const context = usePageBuilderContext();
  return context.config;
};

// Hook for checking if in editing mode
export const usePageBuilderEditing = (): boolean => {
  const context = usePageBuilderContext();
  return context.isEditing || false;
};

// Hook for getting device type
export const usePageBuilderDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  const context = usePageBuilderContext();
  return context.deviceType || 'desktop';
};

export default PageBuilderRenderer;
