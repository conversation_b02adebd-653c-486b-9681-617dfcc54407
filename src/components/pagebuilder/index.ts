// Page Builder Module Exports

// Main renderer components
export {
  PageBuilderRenderer,
  PageBuilderRendererSSR,
  RawHtmlRenderer,
  usePageBuilderContext,
  usePageBuilderConfig,
  usePageBuilderEditing,
  usePageBuilderDeviceType,
} from './PageBuilderRenderer';

// Parser classes and functions
export {
  PageBuilderParser,
  defaultParser,
  parsePageBuilderContent,
  validatePageBuilderContent,
  optimizePageBuilderContent,
} from './parser/PageBuilderParser';

export { ElementParser } from './parser/ElementParser';
export { StyleParser } from './parser/StyleParser';

// Individual element components
export {
  Row,
  Column,
  Text,
  Heading,
  PageBuilderImage,
  Button,
  Banner,
  Video,
  Html,
  Divider,
} from './elements';

// Types
export type {
  PageBuilderContent,
  PageBuilderElement,
  PageBuilderElementType,
  PageBuilderElementProps,
  PageBuilderParserConfig,
  PageBuilderContext,
  RowElement,
  ColumnElement,
  TextElement,
  HeadingElement,
  ImageElement,
  ButtonElement,
  BannerElement,
  VideoElement,
  HtmlElement,
  DividerElement,
} from '@/lib/pagebuilder/types';

// Constants and utilities
export {
  PAGE_BUILDER_CLASSES,
  PAGE_BUILDER_ATTRIBUTES,
  ELEMENT_TYPE_MAPPING,
  CONTENT_TYPE_MAPPING,
  DEFAULT_STYLES,
  BREAKPOINTS,
  DEFAULT_CONFIG,
} from '@/lib/pagebuilder/constants';

export {
  generateElementId,
  getElementType,
  extractAttributes,
  extractStyles,
  parseBackgroundImages,
  cssValueToNumber,
  numberToCssValue,
  isPageBuilderElement,
  getElementContent,
  cleanHtmlContent,
  mergeConfig,
  optimizeImageUrl,
  validateElement,
  getBreakpoint,
  convertColumnWidth,
  safeJsonParse,
  debounce,
  isBrowser,
  logParsingError,
} from '@/lib/pagebuilder/utils';
