// Page Builder Element Parser

import {
  PageBuilderElement,
  PageBuilderElementType,
  RowElement,
  ColumnElement,
  TextElement,
  HeadingElement,
  ImageElement,
  ButtonElement,
  BannerElement,
  VideoElement,
  HtmlElement,
  DividerElement,
  PageBuilderParserConfig,
} from '@/lib/pagebuilder/types';
import {
  generateElementId,
  getElementType,
  extractAttributes,
  extractStyles,
  getElementContent,
  parseBackgroundImages,
  logParsingError,
} from '@/lib/pagebuilder/utils';

export class ElementParser {
  private config: PageBuilderParserConfig;

  constructor(config: PageBuilderParserConfig) {
    this.config = config;
  }

  // Parse a DOM element into a PageBuilderElement
  parseElement(element: Element): PageBuilderElement | null {
    try {
      const elementType = getElementType(element);
      if (!elementType) {
        return null;
      }

      // Check for custom parser
      if (this.config.customElementParsers?.[elementType]) {
        return this.config.customElementParsers[elementType](element);
      }

      // Use built-in parser
      return this.parseByType(element, elementType);
    } catch (error) {
      logParsingError(error as Error, element);
      return null;
    }
  }

  // Parse element by type
  private parseByType(element: Element, type: PageBuilderElementType): PageBuilderElement | null {
    const baseElement = this.createBaseElement(element, type);

    switch (type) {
      case PageBuilderElementType.ROW:
        return this.parseRow(element, baseElement);
      case PageBuilderElementType.COLUMN:
        return this.parseColumn(element, baseElement);
      case PageBuilderElementType.TEXT:
        return this.parseText(element, baseElement);
      case PageBuilderElementType.HEADING:
        return this.parseHeading(element, baseElement);
      case PageBuilderElementType.IMAGE:
        return this.parseImage(element, baseElement);
      case PageBuilderElementType.BUTTON:
        return this.parseButton(element, baseElement);
      case PageBuilderElementType.BANNER:
        return this.parseBanner(element, baseElement);
      case PageBuilderElementType.VIDEO:
        return this.parseVideo(element, baseElement);
      case PageBuilderElementType.HTML:
        return this.parseHtml(element, baseElement);
      case PageBuilderElementType.DIVIDER:
        return this.parseDivider(element, baseElement);
      default:
        return baseElement;
    }
  }

  // Create base element with common properties
  private createBaseElement(element: Element, type: PageBuilderElementType): PageBuilderElement {
    return {
      type,
      id: generateElementId(),
      attributes: extractAttributes(element),
      styles: extractStyles(element),
      rawHtml: element.outerHTML,
    };
  }

  // Parse Row element
  private parseRow(element: Element, baseElement: PageBuilderElement): RowElement {
    const attributes = baseElement.attributes;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.ROW,
      attributes: {
        appearance: attributes.appearance || 'contained',
        enableParallax: attributes.enableParallax === 'true',
        parallaxSpeed: parseFloat(attributes.parallaxSpeed) || 0.5,
        backgroundColor: attributes.backgroundColor,
        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
        backgroundSize: attributes.backgroundSize || 'cover',
        backgroundPosition: attributes.backgroundPosition || 'center center',
        backgroundAttachment: attributes.backgroundAttachment || 'scroll',
        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
        minHeight: attributes.minHeight,
        verticalAlignment: attributes.verticalAlignment || 'top',
      },
    } as RowElement;
  }

  // Parse Column element
  private parseColumn(element: Element, baseElement: PageBuilderElement): ColumnElement {
    const attributes = baseElement.attributes;
    const styles = baseElement.styles;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.COLUMN,
      attributes: {
        width: this.extractColumnWidth(element, styles),
        appearance: attributes.appearance || 'minimum-height',
        backgroundColor: attributes.backgroundColor,
        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
        backgroundSize: attributes.backgroundSize || 'cover',
        backgroundPosition: attributes.backgroundPosition || 'center center',
        backgroundAttachment: attributes.backgroundAttachment || 'scroll',
        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
        minHeight: attributes.minHeight,
        verticalAlignment: attributes.verticalAlignment || 'top',
      },
    } as ColumnElement;
  }

  // Parse Text element
  private parseText(element: Element, baseElement: PageBuilderElement): TextElement {
    const attributes = baseElement.attributes;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.TEXT,
      content: getElementContent(element, true),
      attributes: {
        textAlign: attributes.textAlign || 'left',
        border: attributes.border,
        borderColor: attributes.borderColor,
        borderWidth: attributes.borderWidth,
        borderRadius: attributes.borderRadius,
      },
    } as TextElement;
  }

  // Parse Heading element
  private parseHeading(element: Element, baseElement: PageBuilderElement): HeadingElement {
    const attributes = baseElement.attributes;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.HEADING,
      content: getElementContent(element, true),
      attributes: {
        headingType: this.extractHeadingType(element),
        textAlign: attributes.textAlign || 'left',
        border: attributes.border,
        borderColor: attributes.borderColor,
        borderWidth: attributes.borderWidth,
        borderRadius: attributes.borderRadius,
      },
    } as HeadingElement;
  }

  // Parse Image element
  private parseImage(element: Element, baseElement: PageBuilderElement): ImageElement {
    const attributes = baseElement.attributes;
    const img = element.tagName === 'IMG' ? element : element.querySelector('img');
    
    return {
      ...baseElement,
      type: PageBuilderElementType.IMAGE,
      attributes: {
        src: img?.getAttribute('src') || attributes.src || '',
        alt: img?.getAttribute('alt') || attributes.alt || '',
        title: img?.getAttribute('title') || attributes.title,
        caption: this.extractImageCaption(element),
        link: this.extractImageLink(element),
        linkTarget: attributes.linkTarget || '_self',
        alignment: attributes.alignment || 'center',
        border: attributes.border,
        borderColor: attributes.borderColor,
        borderWidth: attributes.borderWidth,
        borderRadius: attributes.borderRadius,
        lazyLoading: this.config.enableLazyLoading !== false,
      },
    } as ImageElement;
  }

  // Parse Button element
  private parseButton(element: Element, baseElement: PageBuilderElement): ButtonElement {
    const attributes = baseElement.attributes;
    const link = element.tagName === 'A' ? element : element.querySelector('a');
    
    return {
      ...baseElement,
      type: PageBuilderElementType.BUTTON,
      content: getElementContent(element, false),
      attributes: {
        buttonType: this.extractButtonType(element),
        link: link?.getAttribute('href') || attributes.link,
        linkTarget: link?.getAttribute('target') || attributes.linkTarget || '_self',
        textAlign: attributes.textAlign || 'left',
        border: attributes.border,
        borderColor: attributes.borderColor,
        borderWidth: attributes.borderWidth,
        borderRadius: attributes.borderRadius,
        backgroundColor: attributes.backgroundColor,
        textColor: attributes.textColor,
      },
    } as ButtonElement;
  }

  // Parse Banner element
  private parseBanner(element: Element, baseElement: PageBuilderElement): BannerElement {
    const attributes = baseElement.attributes;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.BANNER,
      attributes: {
        appearance: attributes.appearance || 'poster',
        minHeight: attributes.minHeight || '300px',
        backgroundColor: attributes.backgroundColor,
        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
        backgroundSize: attributes.backgroundSize || 'cover',
        backgroundPosition: attributes.backgroundPosition || 'center center',
        backgroundAttachment: attributes.backgroundAttachment || 'scroll',
        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
        showButton: attributes.showButton === 'true',
        buttonText: this.extractBannerButtonText(element),
        buttonType: attributes.buttonType || 'primary',
        buttonLink: this.extractBannerButtonLink(element),
        buttonTarget: attributes.buttonTarget || '_self',
        showOverlay: attributes.showOverlay === 'true',
        overlayColor: attributes.overlayColor,
        content: this.extractBannerContent(element),
        contentPlacement: attributes.contentPlacement || 'center',
      },
    } as BannerElement;
  }

  // Parse Video element
  private parseVideo(element: Element, baseElement: PageBuilderElement): VideoElement {
    const attributes = baseElement.attributes;
    const video = element.querySelector('video');
    const iframe = element.querySelector('iframe');
    
    return {
      ...baseElement,
      type: PageBuilderElementType.VIDEO,
      attributes: {
        videoType: this.extractVideoType(element),
        videoUrl: video?.getAttribute('src') || iframe?.getAttribute('src') || attributes.videoUrl,
        videoId: attributes.videoId,
        maxWidth: attributes.maxWidth || '100%',
        autoplay: attributes.autoplay === 'true' || video?.hasAttribute('autoplay'),
        muted: attributes.muted === 'true' || video?.hasAttribute('muted'),
        loop: attributes.loop === 'true' || video?.hasAttribute('loop'),
        controls: attributes.controls === 'true' || video?.hasAttribute('controls'),
        lazyLoading: this.config.enableLazyLoading !== false,
        fallbackImage: this.extractVideoFallbackImage(element),
      },
    } as VideoElement;
  }

  // Parse HTML element
  private parseHtml(element: Element, baseElement: PageBuilderElement): HtmlElement {
    return {
      ...baseElement,
      type: PageBuilderElementType.HTML,
      content: element.innerHTML,
    } as HtmlElement;
  }

  // Parse Divider element
  private parseDivider(element: Element, baseElement: PageBuilderElement): DividerElement {
    const attributes = baseElement.attributes;
    
    return {
      ...baseElement,
      type: PageBuilderElementType.DIVIDER,
      attributes: {
        lineColor: attributes.lineColor || '#e0e0e0',
        lineThickness: attributes.lineThickness || '1px',
        lineWidth: attributes.lineWidth || '100%',
      },
    } as DividerElement;
  }

  // Helper methods
  private parseBackgroundImage(backgroundImagesData?: string): string | undefined {
    if (!backgroundImagesData) return undefined;
    const images = parseBackgroundImages(backgroundImagesData);
    return images[0]; // Use first image (desktop)
  }

  private extractColumnWidth(element: Element, styles: Record<string, any>): string {
    // Try to get width from styles or data attributes
    if (styles.width) return styles.width;
    if (styles.flexBasis) return styles.flexBasis;
    
    // Check for Magento column classes
    const classList = Array.from(element.classList);
    for (const className of classList) {
      if (className.includes('col-')) {
        const match = className.match(/col-(\d+)/);
        if (match) {
          const cols = parseInt(match[1]);
          return `${(cols / 12) * 100}%`;
        }
      }
    }
    
    return '100%';
  }

  private extractHeadingType(element: Element): 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' {
    const tagName = element.tagName.toLowerCase();
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      return tagName as any;
    }
    return 'h2'; // Default
  }

  private extractButtonType(element: Element): 'primary' | 'secondary' | 'link' {
    const classList = Array.from(element.classList);
    if (classList.includes('pagebuilder-button-secondary')) return 'secondary';
    if (classList.includes('pagebuilder-button-link')) return 'link';
    return 'primary';
  }

  private extractImageCaption(element: Element): string | undefined {
    const caption = element.querySelector('figcaption');
    return caption?.textContent || undefined;
  }

  private extractImageLink(element: Element): string | undefined {
    const link = element.querySelector('a');
    return link?.getAttribute('href') || undefined;
  }

  private extractBannerContent(element: Element): string | undefined {
    const content = element.querySelector('.pagebuilder-banner-content');
    return content?.innerHTML || undefined;
  }

  private extractBannerButtonText(element: Element): string | undefined {
    const button = element.querySelector('.pagebuilder-banner-button');
    return button?.textContent || undefined;
  }

  private extractBannerButtonLink(element: Element): string | undefined {
    const button = element.querySelector('.pagebuilder-banner-button a');
    return button?.getAttribute('href') || undefined;
  }

  private extractVideoType(element: Element): 'youtube' | 'vimeo' | 'mp4' {
    const iframe = element.querySelector('iframe');
    if (iframe?.src.includes('youtube.com')) return 'youtube';
    if (iframe?.src.includes('vimeo.com')) return 'vimeo';
    return 'mp4';
  }

  private extractVideoFallbackImage(element: Element): string | undefined {
    const img = element.querySelector('img');
    return img?.getAttribute('src') || undefined;
  }
}
