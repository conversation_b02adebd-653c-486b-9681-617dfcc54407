// Page Builder Style Parser

import { CSS_PROPERTY_MAPPING } from '@/lib/pagebuilder/constants';

export class StyleParser {
  // Parse inline styles from style attribute
  static parseInlineStyles(styleString: string): Record<string, any> {
    const styles: Record<string, any> = {};
    
    if (!styleString) return styles;
    
    // Split by semicolon and process each declaration
    const declarations = styleString.split(';').filter(decl => decl.trim());
    
    for (const declaration of declarations) {
      const [property, value] = declaration.split(':').map(s => s.trim());
      
      if (property && value) {
        const jsProperty = this.cssPropertyToJs(property);
        styles[jsProperty] = this.parseValue(value);
      }
    }
    
    return styles;
  }

  // Parse CSS from data-pb-style attribute (Magento specific)
  static parsePbStyles(pbStyleString: string): Record<string, any> {
    const styles: Record<string, any> = {};
    
    if (!pbStyleString) return styles;
    
    try {
      // Magento stores styles as encoded CSS
      const decodedStyles = decodeURIComponent(pbStyleString);
      return this.parseInlineStyles(decodedStyles);
    } catch (error) {
      console.warn('Failed to parse pb-style:', error);
      return styles;
    }
  }

  // Parse background images from Magento's data-background-images
  static parseBackgroundImages(backgroundImagesData: string): {
    desktop?: string;
    mobile?: string;
    tablet?: string;
  } {
    try {
      const data = JSON.parse(backgroundImagesData);
      return {
        desktop: data.desktop_image,
        mobile: data.mobile_image,
        tablet: data.tablet_image,
      };
    } catch (error) {
      return {};
    }
  }

  // Convert CSS property name to JavaScript property name
  private static cssPropertyToJs(cssProperty: string): string {
    // Check if we have a mapping
    if (CSS_PROPERTY_MAPPING[cssProperty as keyof typeof CSS_PROPERTY_MAPPING]) {
      return CSS_PROPERTY_MAPPING[cssProperty as keyof typeof CSS_PROPERTY_MAPPING];
    }
    
    // Convert kebab-case to camelCase
    return cssProperty.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // Parse CSS value and convert to appropriate type
  private static parseValue(value: string): any {
    const trimmedValue = value.trim();
    
    // Handle numeric values
    if (/^-?\d+(\.\d+)?(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax)?$/.test(trimmedValue)) {
      return trimmedValue;
    }
    
    // Handle color values
    if (this.isColor(trimmedValue)) {
      return trimmedValue;
    }
    
    // Handle URLs
    if (trimmedValue.startsWith('url(')) {
      return trimmedValue;
    }
    
    // Handle keywords and other values
    return trimmedValue;
  }

  // Check if value is a color
  private static isColor(value: string): boolean {
    // Hex colors
    if (/^#([0-9A-F]{3}){1,2}$/i.test(value)) {
      return true;
    }
    
    // RGB/RGBA
    if (/^rgba?\(/.test(value)) {
      return true;
    }
    
    // HSL/HSLA
    if (/^hsla?\(/.test(value)) {
      return true;
    }
    
    // Named colors (basic check)
    const namedColors = [
      'transparent', 'black', 'white', 'red', 'green', 'blue',
      'yellow', 'orange', 'purple', 'pink', 'gray', 'grey'
    ];
    
    return namedColors.includes(value.toLowerCase());
  }

  // Generate responsive styles based on breakpoints
  static generateResponsiveStyles(
    styles: Record<string, any>,
    breakpoint: 'mobile' | 'tablet' | 'desktop' = 'desktop'
  ): Record<string, any> {
    const responsiveStyles: Record<string, any> = { ...styles };
    
    // Apply breakpoint-specific adjustments
    switch (breakpoint) {
      case 'mobile':
        // Adjust font sizes for mobile
        if (responsiveStyles.fontSize) {
          responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.8);
        }
        
        // Adjust padding/margins for mobile
        if (responsiveStyles.padding) {
          responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.7);
        }
        if (responsiveStyles.margin) {
          responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.7);
        }
        break;
        
      case 'tablet':
        // Adjust font sizes for tablet
        if (responsiveStyles.fontSize) {
          responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.9);
        }
        
        // Adjust padding/margins for tablet
        if (responsiveStyles.padding) {
          responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.85);
        }
        if (responsiveStyles.margin) {
          responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.85);
        }
        break;
    }
    
    return responsiveStyles;
  }

  // Scale font size by factor
  private static scaleFontSize(fontSize: string, factor: number): string {
    const match = fontSize.match(/^(\d+(?:\.\d+)?)(px|em|rem|%)$/);
    if (match) {
      const value = parseFloat(match[1]);
      const unit = match[2];
      return `${(value * factor).toFixed(2)}${unit}`;
    }
    return fontSize;
  }

  // Scale spacing (padding/margin) by factor
  private static scaleSpacing(spacing: string, factor: number): string {
    // Handle multiple values (e.g., "10px 20px")
    const values = spacing.split(' ');
    const scaledValues = values.map(value => {
      const match = value.match(/^(\d+(?:\.\d+)?)(px|em|rem|%)$/);
      if (match) {
        const num = parseFloat(match[1]);
        const unit = match[2];
        return `${(num * factor).toFixed(2)}${unit}`;
      }
      return value;
    });
    
    return scaledValues.join(' ');
  }

  // Convert Magento styles to Material-UI sx prop format
  static toMuiSx(styles: Record<string, any>): Record<string, any> {
    const muiStyles: Record<string, any> = {};
    
    Object.entries(styles).forEach(([key, value]) => {
      // Convert specific properties for MUI
      switch (key) {
        case 'backgroundColor':
          muiStyles.bgcolor = value;
          break;
        case 'textAlign':
          muiStyles.textAlign = value;
          break;
        case 'fontSize':
          muiStyles.fontSize = value;
          break;
        case 'fontWeight':
          muiStyles.fontWeight = value;
          break;
        case 'color':
          muiStyles.color = value;
          break;
        case 'padding':
          muiStyles.p = this.convertSpacingToMui(value);
          break;
        case 'margin':
          muiStyles.m = this.convertSpacingToMui(value);
          break;
        case 'paddingTop':
          muiStyles.pt = this.convertSpacingToMui(value);
          break;
        case 'paddingRight':
          muiStyles.pr = this.convertSpacingToMui(value);
          break;
        case 'paddingBottom':
          muiStyles.pb = this.convertSpacingToMui(value);
          break;
        case 'paddingLeft':
          muiStyles.pl = this.convertSpacingToMui(value);
          break;
        case 'marginTop':
          muiStyles.mt = this.convertSpacingToMui(value);
          break;
        case 'marginRight':
          muiStyles.mr = this.convertSpacingToMui(value);
          break;
        case 'marginBottom':
          muiStyles.mb = this.convertSpacingToMui(value);
          break;
        case 'marginLeft':
          muiStyles.ml = this.convertSpacingToMui(value);
          break;
        case 'borderRadius':
          muiStyles.borderRadius = value;
          break;
        case 'border':
          muiStyles.border = value;
          break;
        case 'borderColor':
          muiStyles.borderColor = value;
          break;
        case 'borderWidth':
          muiStyles.borderWidth = value;
          break;
        default:
          // Keep other properties as-is
          muiStyles[key] = value;
      }
    });
    
    return muiStyles;
  }

  // Convert spacing value to MUI spacing units
  private static convertSpacingToMui(value: string): string | number {
    // If it's a pixel value, convert to MUI spacing units (8px = 1 unit)
    const pxMatch = value.match(/^(\d+)px$/);
    if (pxMatch) {
      const pixels = parseInt(pxMatch[1]);
      return Math.round(pixels / 8);
    }
    
    // Return as-is for other units
    return value;
  }

  // Clean styles by removing empty or invalid values
  static cleanStyles(styles: Record<string, any>): Record<string, any> {
    const cleanedStyles: Record<string, any> = {};
    
    Object.entries(styles).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        cleanedStyles[key] = value;
      }
    });
    
    return cleanedStyles;
  }
}
