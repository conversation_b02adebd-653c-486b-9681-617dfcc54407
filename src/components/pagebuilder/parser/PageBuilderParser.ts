// Main Page Builder Parser

import {
  Page<PERSON><PERSON>erContent,
  PageBuilderElement,
  PageBuilderParserConfig,
} from '@/lib/pagebuilder/types';
import { mergeConfig, isPageBuilderElement, logParsingError } from '@/lib/pagebuilder/utils';
import { ElementParser } from './ElementParser';

export class PageBuilderParser {
  private config: PageBuilderParserConfig;
  private elementParser: ElementParser;

  constructor(config: Partial<PageBuilderParserConfig> = {}) {
    this.config = mergeConfig(config);
    this.elementParser = new ElementParser(this.config);
  }

  // Parse HTML string into PageBuilderContent
  parse(html: string): PageBuilderContent {
    try {
      // Create a temporary DOM to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Extract Page Builder elements
      const elements = this.parseElements(doc.body);
      
      return {
        elements,
        rawHtml: html,
        version: this.extractPageBuilderVersion(html),
      };
    } catch (error) {
      logParsingError(error as Error);
      return {
        elements: [],
        rawHtml: html,
      };
    }
  }

  // Parse elements from a DOM node
  private parseElements(container: Element): PageBuilderElement[] {
    const elements: PageBuilderElement[] = [];
    
    // Get direct children that are Page Builder elements
    const children = Array.from(container.children);
    
    for (const child of children) {
      if (isPageBuilderElement(child)) {
        const element = this.elementParser.parseElement(child);
        if (element) {
          // Parse children recursively
          element.children = this.parseElements(child);
          elements.push(element);
        }
      } else {
        // Check if this element contains Page Builder elements
        const nestedElements = this.parseElements(child);
        elements.push(...nestedElements);
      }
    }
    
    return elements;
  }

  // Extract Page Builder version from HTML
  private extractPageBuilderVersion(html: string): string | undefined {
    const versionMatch = html.match(/data-pb-version="([^"]+)"/);
    return versionMatch ? versionMatch[1] : undefined;
  }

  // Parse HTML from server-side (Node.js environment)
  static parseServerSide(html: string, config: Partial<PageBuilderParserConfig> = {}): PageBuilderContent {
    // For server-side parsing, we need to use a different approach
    // since DOMParser is not available in Node.js
    
    if (typeof window === 'undefined') {
      // Server-side parsing using regex and string manipulation
      return PageBuilderParser.parseWithRegex(html, config);
    }
    
    // Client-side parsing
    const parser = new PageBuilderParser(config);
    return parser.parse(html);
  }

  // Fallback regex-based parsing for server-side
  private static parseWithRegex(html: string, config: Partial<PageBuilderParserConfig> = {}): PageBuilderContent {
    const elements: PageBuilderElement[] = [];
    
    try {
      // Extract Page Builder elements using regex patterns
      const rowPattern = /<div[^>]*data-content-type="row"[^>]*>(.*?)<\/div>/gs;
      const matches = html.matchAll(rowPattern);
      
      for (const match of matches) {
        const element = PageBuilderParser.parseElementWithRegex(match[0]);
        if (element) {
          elements.push(element);
        }
      }
    } catch (error) {
      logParsingError(error as Error);
    }
    
    return {
      elements,
      rawHtml: html,
    };
  }

  // Parse single element with regex (server-side fallback)
  private static parseElementWithRegex(elementHtml: string): PageBuilderElement | null {
    try {
      // Extract data-content-type
      const contentTypeMatch = elementHtml.match(/data-content-type="([^"]+)"/);
      if (!contentTypeMatch) return null;
      
      const contentType = contentTypeMatch[1];
      
      // Basic element structure
      const element: PageBuilderElement = {
        type: contentType as any,
        id: `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        attributes: PageBuilderParser.extractAttributesWithRegex(elementHtml),
        styles: {},
        rawHtml: elementHtml,
      };
      
      // Extract content for text/heading elements
      if (contentType === 'text' || contentType === 'heading') {
        const contentMatch = elementHtml.match(/>([^<]+)</);
        element.content = contentMatch ? contentMatch[1].trim() : '';
      }
      
      return element;
    } catch (error) {
      logParsingError(error as Error);
      return null;
    }
  }

  // Extract attributes with regex (server-side fallback)
  private static extractAttributesWithRegex(html: string): Record<string, any> {
    const attributes: Record<string, any> = {};
    
    // Extract data attributes
    const dataAttrPattern = /data-([^=]+)="([^"]+)"/g;
    let match;
    
    while ((match = dataAttrPattern.exec(html)) !== null) {
      const key = match[1].replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      attributes[key] = match[2];
    }
    
    return attributes;
  }

  // Validate parsed content
  validateContent(content: PageBuilderContent): boolean {
    try {
      // Check if content has valid structure
      if (!content.elements || !Array.isArray(content.elements)) {
        return false;
      }
      
      // Validate each element
      for (const element of content.elements) {
        if (!this.validateElement(element)) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      logParsingError(error as Error);
      return false;
    }
  }

  // Validate single element
  private validateElement(element: PageBuilderElement): boolean {
    // Check required properties
    if (!element.type || !element.id) {
      return false;
    }
    
    // Validate children recursively
    if (element.children) {
      for (const child of element.children) {
        if (!this.validateElement(child)) {
          return false;
        }
      }
    }
    
    return true;
  }

  // Clean and optimize parsed content
  optimizeContent(content: PageBuilderContent): PageBuilderContent {
    return {
      ...content,
      elements: this.optimizeElements(content.elements),
    };
  }

  // Optimize elements (remove empty elements, merge similar elements, etc.)
  private optimizeElements(elements: PageBuilderElement[]): PageBuilderElement[] {
    return elements
      .filter(element => this.shouldKeepElement(element))
      .map(element => ({
        ...element,
        children: element.children ? this.optimizeElements(element.children) : undefined,
      }));
  }

  // Determine if element should be kept during optimization
  private shouldKeepElement(element: PageBuilderElement): boolean {
    // Remove empty text elements
    if (element.type === 'text' && (!element.content || element.content.trim() === '')) {
      return false;
    }
    
    // Remove empty HTML elements
    if (element.type === 'html' && (!element.content || element.content.trim() === '')) {
      return false;
    }
    
    // Keep all other elements
    return true;
  }

  // Get configuration
  getConfig(): PageBuilderParserConfig {
    return this.config;
  }

  // Update configuration
  updateConfig(newConfig: Partial<PageBuilderParserConfig>): void {
    this.config = mergeConfig({ ...this.config, ...newConfig });
    this.elementParser = new ElementParser(this.config);
  }
}

// Export default instance
export const defaultParser = new PageBuilderParser();

// Export static methods for convenience
export const parsePageBuilderContent = (html: string, config?: Partial<PageBuilderParserConfig>) => {
  return PageBuilderParser.parseServerSide(html, config);
};

export const validatePageBuilderContent = (content: PageBuilderContent) => {
  return defaultParser.validateContent(content);
};

export const optimizePageBuilderContent = (content: PageBuilderContent) => {
  return defaultParser.optimizeContent(content);
};
