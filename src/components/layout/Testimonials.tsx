'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper/modules';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Rating,
  Container,
  useTheme,
  alpha,
} from '@mui/material';
import { motion } from 'framer-motion';
import { FormatQuote } from '@mui/icons-material';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

// Testimonial interface
interface Testimonial {
  id: number;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  location?: string;
  date: string;
}

// Individual testimonial card component
const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({
  testimonial,
}) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        borderRadius: 3,
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
          transform: 'translateY(-4px)',
        },
      }}
    >
      {/* Quote Icon */}
      <Box
        sx={{
          position: 'absolute',
          top: -10,
          left: 20,
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: theme.palette.primary.main,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        }}
      >
        <FormatQuote sx={{ color: 'white', fontSize: 20 }} />
      </Box>

      <CardContent sx={{ pt: 4, pb: 3 }}>
        {/* Rating */}
        <Box sx={{ mb: 2 }}>
          <Rating
            value={testimonial.rating}
            readOnly
            size="small"
            sx={{
              color: theme.palette.warning.main,
            }}
          />
        </Box>

        {/* Comment */}
        <Typography
          variant="body1"
          sx={{
            mb: 3,
            lineHeight: 1.7,
            color: theme.palette.text.primary,
            fontStyle: 'italic',
            fontSize: '1.1rem',
          }}
        >
          "{testimonial.comment}"
        </Typography>

        {/* Customer Info */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
          <Avatar
            src={testimonial.avatar}
            alt={testimonial.name}
            sx={{
              width: 50,
              height: 50,
              mr: 2,
              border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            }}
          >
            {testimonial.name.charAt(0)}
          </Avatar>
          
          <Box>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
              }}
            >
              {testimonial.name}
            </Typography>
            
            {testimonial.location && (
              <Typography
                variant="caption"
                sx={{
                  color: theme.palette.text.secondary,
                  display: 'block',
                }}
              >
                {testimonial.location}
              </Typography>
            )}
            
            <Typography
              variant="caption"
              sx={{
                color: theme.palette.text.secondary,
                display: 'block',
              }}
            >
              {new Date(testimonial.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Testimonials props
interface TestimonialsProps {
  testimonials: Testimonial[];
  title?: string;
  subtitle?: string;
  autoplay?: boolean;
  autoplayDelay?: number;
  slidesPerView?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

// Main testimonials component
export const Testimonials: React.FC<TestimonialsProps> = ({
  testimonials,
  title = "What Our Customers Say",
  subtitle = "Don't just take our word for it - hear from our satisfied customers",
  autoplay = true,
  autoplayDelay = 4000,
  slidesPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
  },
}) => {
  const theme = useTheme();

  if (!testimonials || testimonials.length === 0) {
    return null;
  }

  return (
    <Box
      sx={{
        py: 8,
        backgroundColor: alpha(theme.palette.primary.main, 0.02),
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Background Pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `radial-gradient(circle at 20% 80%, ${alpha(
            theme.palette.primary.main,
            0.05
          )} 0%, transparent 50%), radial-gradient(circle at 80% 20%, ${alpha(
            theme.palette.secondary.main,
            0.05
          )} 0%, transparent 50%)`,
          zIndex: 0,
        }}
      />

      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {title}
            </Typography>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h6"
              sx={{
                color: 'text.secondary',
                maxWidth: 600,
                mx: 'auto',
                lineHeight: 1.6,
              }}
            >
              {subtitle}
            </Typography>
          </motion.div>
        </Box>

        {/* Testimonials Slider */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Swiper
            modules={[Autoplay, Pagination]}
            spaceBetween={24}
            slidesPerView={slidesPerView.mobile}
            autoplay={
              autoplay
                ? {
                    delay: autoplayDelay,
                    disableOnInteraction: false,
                  }
                : false
            }
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            breakpoints={{
              600: {
                slidesPerView: slidesPerView.tablet,
              },
              900: {
                slidesPerView: slidesPerView.desktop,
              },
            }}
            className="testimonials-slider"
            style={{
              '--swiper-pagination-color': theme.palette.primary.main,
              paddingBottom: '50px',
            } as React.CSSProperties}
          >
            {testimonials.map((testimonial) => (
              <SwiperSlide key={testimonial.id}>
                <TestimonialCard testimonial={testimonial} />
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>

        {/* Custom styles for Swiper */}
        <style jsx global>{`
          .testimonials-slider .swiper-pagination {
            bottom: 10px;
          }
          
          .testimonials-slider .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: ${alpha(theme.palette.primary.main, 0.3)};
            opacity: 1;
            transition: all 0.3s ease;
          }
          
          .testimonials-slider .swiper-pagination-bullet-active {
            background: ${theme.palette.primary.main};
            transform: scale(1.2);
          }
        `}</style>
      </Container>
    </Box>
  );
};

export default Testimonials;
