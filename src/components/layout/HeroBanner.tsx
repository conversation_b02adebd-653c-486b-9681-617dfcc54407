'use client';

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

// Hero banner props
interface HeroBannerProps {
  title: string;
  subtitle: string;
  description?: string;
  cta: {
    text: string;
    link: string;
  };
  backgroundImage: string;
  mobileBackgroundImage?: string;
  height?: number | string;
  overlay?: boolean;
  overlayOpacity?: number;
}

// Hero banner component
export const HeroBanner: React.FC<HeroBannerProps> = ({
  title,
  subtitle,
  description,
  cta,
  backgroundImage,
  mobileBackgroundImage,
  height = 600,
  overlay = true,
  overlayOpacity = 0.4,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const currentBackgroundImage = isMobile && mobileBackgroundImage 
    ? mobileBackgroundImage 
    : backgroundImage;

  return (
    <Box
      sx={{
        position: 'relative',
        height: typeof height === 'number' ? `${height}px` : height,
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
        color: 'white',
      }}
    >
      {/* Background Image */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -2,
        }}
      >
        <Image
          src={currentBackgroundImage}
          alt="Hero background"
          fill
          style={{
            objectFit: 'cover',
            objectPosition: 'center',
          }}
          priority
          sizes="100vw"
        />
      </Box>

      {/* Overlay */}
      {overlay && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: `rgba(0, 0, 0, ${overlayOpacity})`,
            zIndex: -1,
          }}
        />
      )}

      {/* Content */}
      <Container maxWidth="lg">
        <Box
          sx={{
            maxWidth: { xs: '100%', md: '60%' },
            textAlign: { xs: 'center', md: 'left' },
          }}
        >
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Typography
              variant="h1"
              component="h1"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                fontWeight: 700,
                mb: 2,
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              {title}
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Typography
              variant="h4"
              component="h2"
              sx={{
                fontSize: { xs: '1.25rem', md: '1.5rem', lg: '1.75rem' },
                fontWeight: 400,
                mb: description ? 2 : 4,
                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                opacity: 0.9,
              }}
            >
              {subtitle}
            </Typography>
          </motion.div>

          {description && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontSize: { xs: '1rem', md: '1.125rem' },
                  mb: 4,
                  textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                  opacity: 0.8,
                  maxWidth: '500px',
                }}
              >
                {description}
              </Typography>
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Button
              component={Link}
              href={cta.link}
              variant="contained"
              size="large"
              sx={{
                fontSize: '1.125rem',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: 2,
                textTransform: 'none',
                boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1976d2 30%, #1cb5e0 90%)',
                  boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease-in-out',
              }}
            >
              {cta.text}
            </Button>
          </motion.div>
        </Box>
      </Container>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        style={{
          position: 'absolute',
          bottom: 30,
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      >
        <Box
          sx={{
            width: 2,
            height: 30,
            backgroundColor: 'rgba(255,255,255,0.7)',
            borderRadius: 1,
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '30%',
              backgroundColor: 'white',
              borderRadius: 1,
              animation: 'scroll 2s infinite',
            },
            '@keyframes scroll': {
              '0%': {
                transform: 'translateY(0)',
                opacity: 0,
              },
              '50%': {
                opacity: 1,
              },
              '100%': {
                transform: 'translateY(200%)',
                opacity: 0,
              },
            },
          }}
        />
      </motion.div>
    </Box>
  );
};

export default HeroBanner;
