'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  useTheme,
  alpha,
} from '@mui/material';
import { Send as SendIcon } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import { getBasicStoreConfig } from '@/lib/magento/api/storeConfig';
import { gql } from '@/lib/graphql/simpleClient';
import { GET_CMS_BLOCK } from '@/lib/graphql/queries';
import { LogoImage } from '@/components/ui/MagentoImage';
import { FooterCmsRenderer } from '@/components/cms/FooterCmsRenderer';

// Newsletter Section (Top)
const NewsletterSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.common.white,
  padding: theme.spacing(6, 0),
}));

// Main Footer Section (Middle)
const MainFooterSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[900],
  color: theme.palette.common.white,
  padding: theme.spacing(6, 0),
}));

// Copyright Section (Bottom)
const CopyrightSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[800],
  color: theme.palette.common.white,
  padding: theme.spacing(3, 0),
  borderTop: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
}));

const NewsletterBox = styled(Box)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.common.white, 0.1),
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(4),
  border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
  maxWidth: 600,
  margin: '0 auto',
}));

const FooterColumn = styled(Box)(({ theme }) => ({
  '& h6': {
    marginBottom: theme.spacing(3),
    fontWeight: 600,
    color: theme.palette.common.white,
  },
  '& a': {
    color: alpha(theme.palette.common.white, 0.8),
    textDecoration: 'none',
    display: 'block',
    marginBottom: theme.spacing(1),
    fontSize: '0.875rem',
    transition: 'color 0.2s ease',
    '&:hover': {
      color: theme.palette.primary.light,
    },
  },
  '& p': {
    color: alpha(theme.palette.common.white, 0.8),
    fontSize: '0.875rem',
    lineHeight: 1.6,
    margin: 0,
    marginBottom: theme.spacing(1),
  },
}));

// Interface for store configuration
interface StoreConfig {
  store_name: string;
  copyright?: string;
  base_url: string;
  base_media_url: string;
  header_logo_src?: string;
  logo_alt?: string;
}

// Interface for CMS blocks
interface CmsBlock {
  identifier: string;
  title: string;
  content: string;
}

// Dynamic Footer Props
interface DynamicFooterProps {
  className?: string;
}

export default function DynamicFooter({ className }: DynamicFooterProps) {
  const theme = useTheme();

  // State
  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);
  const [footerBlocks, setFooterBlocks] = useState<{
    topCategories: CmsBlock | null;
    customerService: CmsBlock | null;
    followUs: CmsBlock | null;
    copyright: CmsBlock | null;
  }>({
    topCategories: null,
    customerService: null,
    followUs: null,
    copyright: null,
  });
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterLoading, setNewsletterLoading] = useState(false);
  const [newsletterMessage, setNewsletterMessage] = useState('');

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [configData, footerBlocksData] = await Promise.all([
          getBasicStoreConfig(),
          loadFooterBlocks(),
        ]);

        setStoreConfig(configData);
        setFooterBlocks(footerBlocksData);
      } catch (error) {
        console.error('Error loading footer data:', error);
      }
    };

    loadData();
  }, []);

  // Load footer CMS blocks
  const loadFooterBlocks = async () => {
    try {
      // Load the 4 specific footer column blocks
      const blockIdentifiers = [
        'footer-column-top-categories',
        'footer-column-customer-service',
        'footer-column-follow-us',
        'footer-column-copyright',
      ];

      const blocks = {
        topCategories: null as CmsBlock | null,
        customerService: null as CmsBlock | null,
        followUs: null as CmsBlock | null,
        copyright: null as CmsBlock | null,
      };

      // Load each block individually with graceful fallback
      for (const identifier of blockIdentifiers) {
        try {
          const response = await gql<{
            cmsBlocks: { items: CmsBlock[] };
          }>(GET_CMS_BLOCK, { identifier });

          if (response.cmsBlocks.items.length > 0) {
            const block = response.cmsBlocks.items[0];

            switch (identifier) {
              case 'footer-column-top-categories':
                blocks.topCategories = block;
                break;
              case 'footer-column-customer-service':
                blocks.customerService = block;
                break;
              case 'footer-column-follow-us':
                blocks.followUs = block;
                break;
              case 'footer-column-copyright':
                blocks.copyright = block;
                break;
            }
          }
        } catch (error) {
          // Block doesn't exist, continue with graceful fallback
          console.warn(`Footer block ${identifier} not found`);
        }
      }

      return blocks;
    } catch (error) {
      console.error('Error loading footer blocks:', error);
      return {
        topCategories: null,
        customerService: null,
        followUs: null,
        copyright: null,
      };
    }
  };

  // Newsletter subscription
  const handleNewsletterSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!newsletterEmail.trim()) return;

    setNewsletterLoading(true);
    setNewsletterMessage('');

    try {
      // In a real implementation, this would call a newsletter subscription API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setNewsletterMessage('Thank you for subscribing to our newsletter!');
      setNewsletterEmail('');
    } catch (error) {
      setNewsletterMessage('Failed to subscribe. Please try again.');
    } finally {
      setNewsletterLoading(false);
    }
  };

  return (
    <Box className={className} component="footer">
      {/* Newsletter Section (Top) */}
      <NewsletterSection>
        <Container maxWidth="xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <NewsletterBox>
              <Typography
                variant="h4"
                gutterBottom
                sx={{ fontWeight: 700, textAlign: 'center' }}
              >
                Stay Updated
              </Typography>
              <Typography
                variant="body1"
                sx={{ mb: 4, opacity: 0.9, textAlign: 'center' }}
              >
                Subscribe to our newsletter for the latest updates, exclusive
                offers, and new arrivals.
              </Typography>

              <Box component="form" onSubmit={handleNewsletterSubmit}>
                <Box
                  sx={{ display: 'flex', gap: 2, maxWidth: 400, mx: 'auto' }}
                >
                  <TextField
                    fullWidth
                    size="medium"
                    placeholder="Enter your email"
                    value={newsletterEmail}
                    onChange={e => setNewsletterEmail(e.target.value)}
                    type="email"
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: alpha(
                          theme.palette.common.white,
                          0.15
                        ),
                        color: 'white',
                        '& fieldset': {
                          borderColor: alpha(theme.palette.common.white, 0.3),
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.palette.common.white, 0.5),
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.common.white,
                        },
                      },
                      '& .MuiInputBase-input::placeholder': {
                        color: alpha(theme.palette.common.white, 0.7),
                        opacity: 1,
                      },
                    }}
                  />
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={newsletterLoading}
                    size="large"
                    sx={{
                      minWidth: 'auto',
                      px: 3,
                      backgroundColor: alpha(theme.palette.common.white, 0.2),
                      color: 'white',
                      border: `1px solid ${alpha(theme.palette.common.white, 0.3)}`,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.common.white, 0.3),
                      },
                    }}
                  >
                    <SendIcon />
                  </Button>
                </Box>

                {newsletterMessage && (
                  <Typography
                    variant="body2"
                    sx={{
                      display: 'block',
                      mt: 2,
                      textAlign: 'center',
                      color: newsletterMessage.includes('Thank you')
                        ? theme.palette.success.light
                        : theme.palette.error.light,
                    }}
                  >
                    {newsletterMessage}
                  </Typography>
                )}
              </Box>
            </NewsletterBox>
          </motion.div>
        </Container>
      </NewsletterSection>

      {/* Main Footer Section (Middle) - 4 Columns */}
      <MainFooterSection>
        <Container maxWidth="xl">
          <Grid container spacing={4}>
            {/* Column 1: Contact Details with Store Logo */}
            <Grid item xs={12} sm={6} md={3}>
              <FooterColumn>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  {storeConfig?.header_logo_src ? (
                    <Box sx={{ mb: 3 }}>
                      <LogoImage
                        src={storeConfig.header_logo_src}
                        alt={
                          storeConfig?.logo_alt ||
                          storeConfig?.store_name ||
                          'Store Logo'
                        }
                        width={120}
                        height={40}
                        storeConfig={storeConfig}
                        style={{ objectFit: 'contain' }}
                      />
                    </Box>
                  ) : (
                    <Typography variant="h6" sx={{ mb: 3, fontWeight: 700 }}>
                      {storeConfig?.store_name || 'Store'}
                    </Typography>
                  )}

                  <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
                    123 Commerce Street
                    <br />
                    City, State 12345
                    <br />
                    United States
                  </Typography>

                  <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
                    Phone: +****************
                  </Typography>

                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Email: support@
                    {storeConfig?.store_name
                      ?.toLowerCase()
                      .replace(/\s+/g, '') || 'store'}
                    .com
                  </Typography>
                </motion.div>
              </FooterColumn>
            </Grid>

            {/* Column 2: Top Categories */}
            <Grid item xs={12} sm={6} md={3}>
              <FooterColumn>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                >
                  <Typography variant="h6">Top Categories</Typography>
                  {footerBlocks.topCategories?.content ? (
                    <FooterCmsRenderer
                      content={footerBlocks.topCategories.content}
                    />
                  ) : (
                    <Typography
                      variant="body2"
                      sx={{ opacity: 0.6, fontStyle: 'italic' }}
                    >
                      CMS block 'footer-column-top-categories' is missing
                    </Typography>
                  )}
                </motion.div>
              </FooterColumn>
            </Grid>

            {/* Column 3: Customer Service */}
            <Grid item xs={12} sm={6} md={3}>
              <FooterColumn>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <Typography variant="h6">Customer Service</Typography>
                  {/* TEMPORARY: Test with known HTML content */}
                  <FooterCmsRenderer
                    content={`<ul class="footer-links">
                      <li><a href="/about-us">About Us</a></li>
                      <li><a href="/contact-us">Contact Us</a></li>
                      <li><a href="/customer-service">Customer Service</a></li>
                      <li><a href="/privacy-policy">Privacy Policy</a></li>
                    </ul>`}
                  />
                </motion.div>
              </FooterColumn>
            </Grid>

            {/* Column 4: Follow Us */}
            <Grid item xs={12} sm={6} md={3}>
              <FooterColumn>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <Typography variant="h6">Follow Us</Typography>
                  {footerBlocks.followUs?.content ? (
                    <FooterCmsRenderer
                      content={footerBlocks.followUs.content}
                    />
                  ) : (
                    <Typography
                      variant="body2"
                      sx={{ opacity: 0.6, fontStyle: 'italic' }}
                    >
                      CMS block 'footer-column-follow-us' is missing
                    </Typography>
                  )}
                </motion.div>
              </FooterColumn>
            </Grid>
          </Grid>
        </Container>
      </MainFooterSection>

      {/* Copyright Section (Bottom) */}
      <CopyrightSection>
        <Container maxWidth="xl">
          <Box sx={{ textAlign: 'center' }}>
            {footerBlocks.copyright?.content ? (
              <FooterCmsRenderer content={footerBlocks.copyright.content} />
            ) : (
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                {storeConfig?.copyright ||
                  `© ${new Date().getFullYear()} ${storeConfig?.store_name || 'E-commerce Store'}. All rights reserved.`}
              </Typography>
            )}
          </Box>
        </Container>
      </CopyrightSection>
    </Box>
  );
}
