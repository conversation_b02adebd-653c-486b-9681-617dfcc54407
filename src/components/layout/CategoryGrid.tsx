'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  useTheme,
  alpha,
} from '@mui/material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { CategoryCardSkeleton } from '@/components/ui/LoadingSkeleton';

// Category interface
interface Category {
  id: number;
  name: string;
  description: string;
  image: string;
  link: string;
  productCount: number;
}

// Category card component
const CategoryCard: React.FC<{ category: Category; index: number }> = ({
  category,
  index,
}) => {
  const theme = useTheme();

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -8 }}
    >
      <Card
        component={Link}
        href={category.link}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          borderRadius: 3,
          overflow: 'hidden',
          textDecoration: 'none',
          color: 'inherit',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 30px rgba(0,0,0,0.15)',
            transform: 'translateY(-4px)',
            '& .category-image': {
              transform: 'scale(1.05)',
            },
            '& .category-overlay': {
              backgroundColor: alpha(theme.palette.primary.main, 0.8),
            },
            '& .category-content': {
              transform: 'translateY(-10px)',
            },
          },
        }}
      >
        {/* Category Image */}
        <Box
          sx={{
            position: 'relative',
            height: 200,
            overflow: 'hidden',
          }}
        >
          <Image
            src={category.image}
            alt={category.name}
            fill
            style={{
              objectFit: 'cover',
              transition: 'transform 0.3s ease',
            }}
            className="category-image"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />

          {/* Overlay */}
          <Box
            className="category-overlay"
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: alpha(theme.palette.common.black, 0.4),
              transition: 'background-color 0.3s ease',
            }}
          />

          {/* Product Count Badge */}
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
              borderRadius: 2,
              px: 1.5,
              py: 0.5,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
              }}
            >
              {category.productCount} items
            </Typography>
          </Box>
        </Box>

        {/* Category Content */}
        <CardContent
          className="category-content"
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            textAlign: 'center',
            p: 3,
            transition: 'transform 0.3s ease',
          }}
        >
          <Typography
            variant="h5"
            component="h3"
            sx={{
              fontWeight: 600,
              mb: 1,
              color: theme.palette.text.primary,
            }}
          >
            {category.name}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              lineHeight: 1.6,
            }}
          >
            {category.description}
          </Typography>
        </CardContent>

        {/* Hover Effect Arrow */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            right: 16,
            width: 40,
            height: 40,
            borderRadius: '50%',
            backgroundColor: theme.palette.primary.main,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transform: 'scale(0.8)',
            transition: 'all 0.3s ease',
            '.MuiCard-root:hover &': {
              opacity: 1,
              transform: 'scale(1)',
            },
          }}
        >
          <Typography
            sx={{
              color: 'white',
              fontSize: '1.2rem',
              fontWeight: 'bold',
            }}
          >
            →
          </Typography>
        </Box>
      </Card>
    </motion.div>
  );
};

// Category grid props
interface CategoryGridProps {
  categories: Category[];
  title?: string;
  subtitle?: string;
  columns?: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
  };
  spacing?: number;
  loading?: boolean;
  skeletonCount?: number;
}

// Main category grid component
export const CategoryGrid: React.FC<CategoryGridProps> = ({
  categories,
  title,
  subtitle,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 3,
  },
  spacing = 3,
  loading = false,
  skeletonCount = 6,
}) => {
  // Show loading skeletons
  if (loading) {
    return (
      <Box>
        {/* Header */}
        {(title || subtitle) && (
          <Box sx={{ textAlign: 'center', mb: 5 }}>
            {title && (
              <Typography
                variant="h3"
                component="h2"
                sx={{
                  fontWeight: 700,
                  mb: subtitle ? 2 : 0,
                  background:
                    'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {title}
              </Typography>
            )}

            {subtitle && (
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600,
                  mx: 'auto',
                  lineHeight: 1.6,
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        )}

        {/* Skeleton Grid */}
        <Grid container spacing={spacing}>
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <Grid
              item
              xs={columns.xs}
              sm={columns.sm}
              md={columns.md}
              lg={columns.lg}
              key={index}
            >
              <CategoryCardSkeleton animationType="shimmer" />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <Box>
      {/* Header */}
      {(title || subtitle) && (
        <Box sx={{ textAlign: 'center', mb: 5 }}>
          {title && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h3"
                component="h2"
                sx={{
                  fontWeight: 700,
                  mb: subtitle ? 2 : 0,
                  background:
                    'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {title}
              </Typography>
            </motion.div>
          )}

          {subtitle && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600,
                  mx: 'auto',
                  lineHeight: 1.6,
                }}
              >
                {subtitle}
              </Typography>
            </motion.div>
          )}
        </Box>
      )}

      {/* Category Grid */}
      <Grid container spacing={spacing}>
        {categories.map((category, index) => (
          <Grid
            item
            xs={columns.xs}
            sm={columns.sm}
            md={columns.md}
            lg={columns.lg}
            key={category.id}
          >
            <CategoryCard category={category} index={index} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CategoryGrid;
