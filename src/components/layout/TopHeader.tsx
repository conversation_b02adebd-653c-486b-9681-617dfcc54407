'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Skeleton,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { gql } from '@/lib/graphql/simpleClient';
import { GET_CMS_BLOCK } from '@/lib/graphql/queries';
import { PageBuilderRenderer } from '@/components/pagebuilder';

// Styled components
const TopHeaderContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.dark,
  color: theme.palette.common.white,
  padding: theme.spacing(1, 0),
  fontSize: '0.875rem',
  borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
}));

const TopHeaderContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  textAlign: 'center',
  minHeight: 32,
  '& a': {
    color: 'inherit',
    textDecoration: 'underline',
    '&:hover': {
      textDecoration: 'none',
    },
  },
  '& p': {
    margin: 0,
    lineHeight: 1.4,
  },
  '& strong': {
    fontWeight: 600,
  },
}));

// CMS Block interface
interface CmsBlock {
  identifier: string;
  title: string;
  content: string;
}

// Top Header Props
interface TopHeaderProps {
  className?: string;
}

export default function TopHeader({ className }: TopHeaderProps) {
  const theme = useTheme();
  const [cmsBlock, setCmsBlock] = useState<CmsBlock | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load top header CMS block
  useEffect(() => {
    const loadTopHeaderBlock = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await gql<{
          cmsBlocks: { items: CmsBlock[] };
        }>(GET_CMS_BLOCK, { identifier: 'top-header' });

        if (response.cmsBlocks.items.length > 0) {
          setCmsBlock(response.cmsBlocks.items[0]);
        } else {
          // CMS block doesn't exist - this is not an error, just no content to show
          setCmsBlock(null);
        }
      } catch (err) {
        console.warn('Top header CMS block not found or error loading:', err);
        // Don't set error state for missing CMS blocks - graceful fallback
        setCmsBlock(null);
      } finally {
        setLoading(false);
      }
    };

    loadTopHeaderBlock();
  }, []);

  // Show loading skeleton
  if (loading) {
    return (
      <TopHeaderContainer className={className}>
        <Container maxWidth="xl">
          <TopHeaderContent>
            <Skeleton
              variant="text"
              width={300}
              height={20}
              sx={{ backgroundColor: alpha(theme.palette.common.white, 0.2) }}
            />
          </TopHeaderContent>
        </Container>
      </TopHeaderContainer>
    );
  }

  // Don't render anything if no CMS block content (graceful fallback)
  if (!cmsBlock || !cmsBlock.content.trim()) {
    return null;
  }

  return (
    <TopHeaderContainer className={className}>
      <Container maxWidth="xl">
        <TopHeaderContent>
          <PageBuilderRenderer content={cmsBlock.content} />
        </TopHeaderContent>
      </Container>
    </TopHeaderContainer>
  );
}
