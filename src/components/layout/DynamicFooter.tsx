'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Link,
  TextField,
  Button,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  YouTube as YouTubeIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import NextLink from 'next/link';
import { motion } from 'framer-motion';
import { getBasicStoreConfig } from '@/lib/magento/api/storeConfig';
import { gql } from '@/lib/graphql/simpleClient';
import { GET_CMS_BLOCK } from '@/lib/graphql/queries';
import { LogoImage } from '@/components/ui/MagentoImage';

// Styled components
const StyledFooter = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[900],
  color: theme.palette.common.white,
  marginTop: 'auto',
}));

// Newsletter Section (Top)
const NewsletterSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.common.white,
  padding: theme.spacing(6, 0),
}));

// Main Footer Section (Middle)
const MainFooterSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[900],
  color: theme.palette.common.white,
  padding: theme.spacing(6, 0),
}));

// Copyright Section (Bottom)
const CopyrightSection = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[800],
  color: theme.palette.common.white,
  padding: theme.spacing(3, 0),
  borderTop: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
}));

const SocialIconButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.common.white,
  backgroundColor: alpha(theme.palette.common.white, 0.1),
  margin: theme.spacing(0, 1),
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    transform: 'translateY(-2px)',
  },
}));

const NewsletterBox = styled(Box)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.common.white, 0.1),
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(4),
  border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
  maxWidth: 600,
  margin: '0 auto',
}));

const FooterColumn = styled(Box)(({ theme }) => ({
  '& h6': {
    marginBottom: theme.spacing(3),
    fontWeight: 600,
    color: theme.palette.common.white,
  },
  '& a': {
    color: alpha(theme.palette.common.white, 0.8),
    textDecoration: 'none',
    display: 'block',
    marginBottom: theme.spacing(1),
    fontSize: '0.875rem',
    transition: 'color 0.2s ease',
    '&:hover': {
      color: theme.palette.primary.light,
    },
  },
  '& p': {
    color: alpha(theme.palette.common.white, 0.8),
    fontSize: '0.875rem',
    lineHeight: 1.6,
    margin: 0,
    marginBottom: theme.spacing(1),
  },
}));

// Interface for store configuration
interface StoreConfig {
  store_name: string;
  copyright?: string;
  base_url: string;
  base_media_url: string;
  header_logo_src?: string;
  logo_alt?: string;
}

// Interface for CMS blocks
interface CmsBlock {
  identifier: string;
  title: string;
  content: string;
}

// Footer link interface
interface FooterLink {
  label: string;
  href: string;
  external?: boolean;
}

// Footer section interface
interface FooterSection {
  title: string;
  links: FooterLink[];
}

// Dynamic Footer Props
interface DynamicFooterProps {
  className?: string;
}

export default function DynamicFooter({ className }: DynamicFooterProps) {
  const theme = useTheme();

  // State
  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);
  const [footerBlocks, setFooterBlocks] = useState<{
    topCategories: CmsBlock | null;
    customerService: CmsBlock | null;
    followUs: CmsBlock | null;
    copyright: CmsBlock | null;
  }>({
    topCategories: null,
    customerService: null,
    followUs: null,
    copyright: null,
  });
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterLoading, setNewsletterLoading] = useState(false);
  const [newsletterMessage, setNewsletterMessage] = useState('');

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [configData, footerBlocksData] = await Promise.all([
          getBasicStoreConfig(),
          loadFooterBlocks(),
        ]);

        setStoreConfig(configData);
        setFooterBlocks(footerBlocksData);
      } catch (error) {
        console.error('Error loading footer data:', error);
      }
    };

    loadData();
  }, []);

  // Load footer CMS blocks
  const loadFooterBlocks = async () => {
    try {
      // Load the 4 specific footer column blocks
      const blockIdentifiers = [
        'footer-column-top-categories',
        'footer-column-customer-service',
        'footer-column-follow-us',
        'footer-column-copyright',
      ];

      const blocks = {
        topCategories: null as CmsBlock | null,
        customerService: null as CmsBlock | null,
        followUs: null as CmsBlock | null,
        copyright: null as CmsBlock | null,
      };

      // Load each block individually with graceful fallback
      for (const identifier of blockIdentifiers) {
        try {
          const response = await gql<{
            cmsBlocks: { items: CmsBlock[] };
          }>(GET_CMS_BLOCK, { identifier });

          if (response.cmsBlocks.items.length > 0) {
            const block = response.cmsBlocks.items[0];

            switch (identifier) {
              case 'footer-column-top-categories':
                blocks.topCategories = block;
                break;
              case 'footer-column-customer-service':
                blocks.customerService = block;
                break;
              case 'footer-column-follow-us':
                blocks.followUs = block;
                break;
              case 'footer-column-copyright':
                blocks.copyright = block;
                break;
            }
          }
        } catch (error) {
          // Block doesn't exist, continue with graceful fallback
          console.warn(`Footer block ${identifier} not found`);
        }
      }

      return blocks;
    } catch (error) {
      console.error('Error loading footer blocks:', error);
      return {
        topCategories: null,
        customerService: null,
        followUs: null,
        copyright: null,
      };
    }
  };

  // Newsletter subscription
  const handleNewsletterSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!newsletterEmail.trim()) return;

    setNewsletterLoading(true);
    setNewsletterMessage('');

    try {
      // In a real implementation, this would call a newsletter subscription API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setNewsletterMessage('Thank you for subscribing to our newsletter!');
      setNewsletterEmail('');
    } catch (error) {
      setNewsletterMessage('Failed to subscribe. Please try again.');
    } finally {
      setNewsletterLoading(false);
    }
  };

  return (
    <StyledFooter className={className} component="footer">
      <FooterSection>
        <Container maxWidth="xl">
          <Grid container spacing={4}>
            {/* Newsletter Section */}
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <NewsletterBox>
                  <Typography
                    variant="h6"
                    gutterBottom
                    sx={{ fontWeight: 600 }}
                  >
                    Stay Updated
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, opacity: 0.8 }}>
                    Subscribe to our newsletter for the latest updates,
                    exclusive offers, and new arrivals.
                  </Typography>

                  <Box component="form" onSubmit={handleNewsletterSubmit}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        fullWidth
                        size="small"
                        placeholder="Enter your email"
                        value={newsletterEmail}
                        onChange={e => setNewsletterEmail(e.target.value)}
                        type="email"
                        required
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: alpha(
                              theme.palette.common.white,
                              0.1
                            ),
                            color: 'white',
                            '& fieldset': {
                              borderColor: alpha(
                                theme.palette.common.white,
                                0.3
                              ),
                            },
                            '&:hover fieldset': {
                              borderColor: alpha(
                                theme.palette.common.white,
                                0.5
                              ),
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: theme.palette.primary.main,
                            },
                          },
                          '& .MuiInputBase-input::placeholder': {
                            color: alpha(theme.palette.common.white, 0.7),
                            opacity: 1,
                          },
                        }}
                      />
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={newsletterLoading}
                        sx={{
                          minWidth: 'auto',
                          px: 2,
                          backgroundColor: theme.palette.primary.main,
                          '&:hover': {
                            backgroundColor: theme.palette.primary.dark,
                          },
                        }}
                      >
                        <SendIcon />
                      </Button>
                    </Box>

                    {newsletterMessage && (
                      <Typography
                        variant="caption"
                        sx={{
                          display: 'block',
                          mt: 1,
                          color: newsletterMessage.includes('Thank you')
                            ? theme.palette.success.light
                            : theme.palette.error.light,
                        }}
                      >
                        {newsletterMessage}
                      </Typography>
                    )}
                  </Box>
                </NewsletterBox>
              </motion.div>
            </Grid>

            {/* Footer Links */}
            {defaultFooterSections.map((section, index) => (
              <Grid item xs={6} sm={3} md={2} key={section.title}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Typography
                    variant="h6"
                    gutterBottom
                    sx={{ fontWeight: 600, mb: 2 }}
                  >
                    {section.title}
                  </Typography>
                  <List dense sx={{ p: 0 }}>
                    {section.links.map(link => (
                      <ListItem key={link.href} sx={{ p: 0, mb: 1 }}>
                        <Link
                          component={link.external ? 'a' : NextLink}
                          href={link.href}
                          target={link.external ? '_blank' : undefined}
                          rel={
                            link.external ? 'noopener noreferrer' : undefined
                          }
                          sx={{
                            color: alpha(theme.palette.common.white, 0.8),
                            textDecoration: 'none',
                            fontSize: '0.875rem',
                            transition: 'color 0.2s ease',
                            '&:hover': {
                              color: theme.palette.primary.light,
                            },
                          }}
                        >
                          {link.label}
                        </Link>
                      </ListItem>
                    ))}
                  </List>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Social Media & Contact */}
          <Box
            sx={{
              mt: 6,
              pt: 4,
              borderTop: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
            }}
          >
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Follow Us
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {socialLinks.map(social => (
                    <SocialIconButton
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label={social.label}
                    >
                      <social.icon />
                    </SocialIconButton>
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                  Contact Info
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmailIcon fontSize="small" sx={{ opacity: 0.7 }} />
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      support@
                      {storeConfig?.store_name
                        ?.toLowerCase()
                        .replace(/\s+/g, '') || 'store'}
                      .com
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PhoneIcon fontSize="small" sx={{ opacity: 0.7 }} />
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      +****************
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LocationIcon fontSize="small" sx={{ opacity: 0.7 }} />
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      123 Commerce St, City, State 12345
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </FooterSection>

      {/* Footer Bottom */}
      <FooterBottom>
        <Container maxWidth="xl">
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                {storeConfig?.copyright ||
                  `© ${new Date().getFullYear()} ${storeConfig?.store_name || 'E-commerce Store'}. All rights reserved.`}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: { xs: 'flex-start', md: 'flex-end' },
                  gap: 3,
                }}
              >
                <Link
                  component={NextLink}
                  href="/privacy-policy"
                  sx={{
                    color: alpha(theme.palette.common.white, 0.8),
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    '&:hover': {
                      color: theme.palette.primary.light,
                    },
                  }}
                >
                  Privacy
                </Link>
                <Link
                  component={NextLink}
                  href="/terms"
                  sx={{
                    color: alpha(theme.palette.common.white, 0.8),
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    '&:hover': {
                      color: theme.palette.primary.light,
                    },
                  }}
                >
                  Terms
                </Link>
                <Link
                  component={NextLink}
                  href="/cookies"
                  sx={{
                    color: alpha(theme.palette.common.white, 0.8),
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    '&:hover': {
                      color: theme.palette.primary.light,
                    },
                  }}
                >
                  Cookies
                </Link>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </FooterBottom>
    </StyledFooter>
  );
}
