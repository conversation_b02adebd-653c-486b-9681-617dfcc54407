'use client';

import React, { useState, useEffect } from 'react';
import {
  AppBar,
  Tool<PERSON>,
  Box,
  Container,
  Typography,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  InputBase,
  Button,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  useTheme,
  useMediaQuery,
  alpha,
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingCart as CartIcon,
  Person as PersonIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandLess,
  ExpandMore,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { LogoImage, ProductImage } from '@/components/ui/MagentoImage';
import { getCategoriesForMenuClient } from '@/lib/magento/api/categories';
import { getBasicStoreConfig } from '@/lib/magento/api/storeConfig';
import { searchProductsClient } from '@/lib/magento/api/search';
import type { Category } from '@/lib/magento/api/categories';

// Styled components
const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const SearchContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.shape.borderRadius * 2,
  backgroundColor: alpha(theme.palette.common.black, 0.05),
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.black, 0.08),
  },
  marginLeft: theme.spacing(2),
  marginRight: theme.spacing(2),
  width: '100%',
  maxWidth: 400,
  [theme.breakpoints.up('sm')]: {
    marginLeft: theme.spacing(3),
    width: 'auto',
  },
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: theme.palette.text.secondary,
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  width: '100%',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create('width'),
    [theme.breakpoints.up('sm')]: {
      width: '20ch',
      '&:focus': {
        width: '30ch',
      },
    },
  },
}));

const MegaMenuContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '100%',
  left: 0,
  right: 0,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[8],
  zIndex: theme.zIndex.modal,
  maxHeight: '70vh',
  overflowY: 'auto',
}));

// Interface for store configuration
interface StoreConfig {
  store_name: string;
  header_logo_src?: string;
  logo_alt?: string;
  base_media_url: string;
}

// Dynamic Header Props
interface DynamicHeaderProps {
  cartItemCount?: number;
  onCartClick?: () => void;
  onSearchSubmit?: (query: string) => void;
}

export default function DynamicHeader({
  cartItemCount = 0,
  onCartClick,
  onSearchSubmit,
}: DynamicHeaderProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [categories, setCategories] = useState<Category[]>([]);
  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [megaMenuOpen, setMegaMenuOpen] = useState<string | null>(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(
    null
  );
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set()
  );

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [categoriesData, configData] = await Promise.all([
          getCategoriesForMenuClient(),
          getBasicStoreConfig(),
        ]);

        setCategories(categoriesData);
        setStoreConfig(configData);
      } catch (error) {
        console.error('Error loading header data:', error);
      }
    };

    loadData();
  }, []);

  // Search functionality
  const handleSearchChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const query = event.target.value;
    setSearchQuery(query);

    if (query.length > 2) {
      try {
        const results = await searchProductsClient({
          search: query,
          pageSize: 5,
        });
        setSearchResults(results.items);
        setShowSearchResults(true);
      } catch (error) {
        console.error('Error searching products:', error);
        setSearchResults([]);
      }
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  };

  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (searchQuery.trim()) {
      setShowSearchResults(false);
      onSearchSubmit?.(searchQuery.trim());
      // Navigate to search results page
      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  // Menu handlers
  const handleMegaMenuOpen = (categoryId: string) => {
    setMegaMenuOpen(categoryId);
  };

  const handleMegaMenuClose = () => {
    setMegaMenuOpen(null);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const toggleMobileCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Render category menu items
  const renderCategoryMenuItem = (category: Category, level = 0) => (
    <MenuItem
      key={category.uid}
      component={Link}
      href={`/category/${category.url_key}`}
      sx={{
        pl: level * 2 + 2,
        py: 1,
        '&:hover': {
          backgroundColor: alpha(theme.palette.primary.main, 0.08),
        },
      }}
    >
      <Typography variant="body2" color="text.primary">
        {category.name}
      </Typography>
    </MenuItem>
  );

  // Render mobile category items
  const renderMobileCategoryItem = (category: Category, level = 0) => (
    <React.Fragment key={category.uid}>
      <ListItem
        button
        sx={{ pl: level * 2 + 2 }}
        onClick={() => {
          if (category.children && category.children.length > 0) {
            toggleMobileCategory(category.uid);
          } else {
            setMobileMenuOpen(false);
            window.location.href = `/category/${category.url_key}`;
          }
        }}
      >
        <ListItemIcon sx={{ minWidth: 32 }}>
          <CategoryIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary={category.name} />
        {category.children &&
          category.children.length > 0 &&
          (expandedCategories.has(category.uid) ? (
            <ExpandLess />
          ) : (
            <ExpandMore />
          ))}
      </ListItem>
      {category.children && category.children.length > 0 && (
        <Collapse
          in={expandedCategories.has(category.uid)}
          timeout="auto"
          unmountOnExit
        >
          <List component="div" disablePadding>
            {category.children.map(child =>
              renderMobileCategoryItem(child, level + 1)
            )}
          </List>
        </Collapse>
      )}
    </React.Fragment>
  );

  return (
    <>
      <StyledAppBar position="sticky">
        <Container maxWidth="xl">
          <Toolbar sx={{ px: { xs: 0, sm: 2 } }}>
            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                edge="start"
                color="inherit"
                aria-label="menu"
                onClick={() => setMobileMenuOpen(true)}
                sx={{ mr: 1 }}
              >
                <MenuIcon />
              </IconButton>
            )}

            {/* Logo */}
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
              <Link
                href="/"
                style={{ textDecoration: 'none', color: 'inherit' }}
              >
                {storeConfig?.header_logo_src ? (
                  <LogoImage
                    src={storeConfig.header_logo_src}
                    alt={
                      storeConfig?.logo_alt ||
                      storeConfig?.store_name ||
                      'Store Logo'
                    }
                    width={120}
                    height={40}
                    storeConfig={storeConfig}
                    style={{ objectFit: 'contain' }}
                  />
                ) : (
                  <Typography
                    variant="h6"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      background:
                        'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    {storeConfig?.store_name || 'Store'}
                  </Typography>
                )}
              </Link>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                {categories.slice(0, 6).map(category => (
                  <Button
                    key={category.uid}
                    color="inherit"
                    onMouseEnter={() => handleMegaMenuOpen(category.uid)}
                    onMouseLeave={handleMegaMenuClose}
                    component={Link}
                    href={`/category/${category.url_key}`}
                    sx={{
                      mx: 1,
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        backgroundColor: alpha(
                          theme.palette.primary.main,
                          0.08
                        ),
                      },
                    }}
                  >
                    {category.name}
                  </Button>
                ))}
              </Box>
            )}

            {/* Search */}
            <SearchContainer>
              <SearchIconWrapper>
                <SearchIcon />
              </SearchIconWrapper>
              <form onSubmit={handleSearchSubmit}>
                <StyledInputBase
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onFocus={() =>
                    searchResults.length > 0 && setShowSearchResults(true)
                  }
                  onBlur={() =>
                    setTimeout(() => setShowSearchResults(false), 200)
                  }
                />
              </form>

              {/* Search Results Dropdown */}
              <AnimatePresence>
                {showSearchResults && searchResults.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    style={{
                      position: 'absolute',
                      top: '100%',
                      left: 0,
                      right: 0,
                      backgroundColor: theme.palette.background.paper,
                      boxShadow: theme.shadows[8],
                      borderRadius: theme.shape.borderRadius,
                      zIndex: theme.zIndex.modal,
                      maxHeight: 300,
                      overflowY: 'auto',
                    }}
                  >
                    {searchResults.map(product => (
                      <MenuItem
                        key={product.uid}
                        component={Link}
                        href={`/product/${product.url_key}`}
                        sx={{ py: 1 }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                          }}
                        >
                          {product.image && (
                            <ProductImage
                              src={product.image.url}
                              alt={product.image.label || product.name}
                              width={40}
                              height={40}
                              variant="thumbnail"
                              showSkeleton={false}
                              style={{ objectFit: 'cover', marginRight: 12 }}
                            />
                          )}
                          <Box>
                            <Typography variant="body2" noWrap>
                              {product.name}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              $
                              {
                                product.price_range.minimum_price.final_price
                                  .value
                              }
                            </Typography>
                          </Box>
                        </Box>
                      </MenuItem>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </SearchContainer>

            <Box sx={{ flexGrow: 1 }} />

            {/* Cart */}
            <IconButton color="inherit" onClick={onCartClick} sx={{ mr: 1 }}>
              <Badge badgeContent={cartItemCount} color="primary">
                <CartIcon />
              </Badge>
            </IconButton>

            {/* User Menu */}
            <IconButton color="inherit" onClick={handleUserMenuOpen}>
              <PersonIcon />
            </IconButton>
          </Toolbar>
        </Container>

        {/* Mega Menu */}
        <AnimatePresence>
          {megaMenuOpen && !isMobile && (
            <MegaMenuContainer
              onMouseEnter={() => setMegaMenuOpen(megaMenuOpen)}
              onMouseLeave={handleMegaMenuClose}
            >
              <Container maxWidth="xl">
                <Box sx={{ py: 2 }}>
                  {categories
                    .find(cat => cat.uid === megaMenuOpen)
                    ?.children?.map(category => (
                      <Box key={category.uid} sx={{ mb: 2 }}>
                        <Typography
                          variant="subtitle1"
                          component={Link}
                          href={`/category/${category.url_key}`}
                          sx={{
                            fontWeight: 600,
                            color: 'primary.main',
                            textDecoration: 'none',
                            '&:hover': { textDecoration: 'underline' },
                          }}
                        >
                          {category.name}
                        </Typography>
                        {category.children && (
                          <Box sx={{ mt: 1, ml: 2 }}>
                            {category.children.slice(0, 8).map(subCategory => (
                              <Typography
                                key={subCategory.uid}
                                variant="body2"
                                component={Link}
                                href={`/category/${subCategory.url_key}`}
                                sx={{
                                  display: 'block',
                                  py: 0.5,
                                  color: 'text.secondary',
                                  textDecoration: 'none',
                                  '&:hover': {
                                    color: 'primary.main',
                                    textDecoration: 'underline',
                                  },
                                }}
                              >
                                {subCategory.name}
                              </Typography>
                            ))}
                          </Box>
                        )}
                      </Box>
                    ))}
                </Box>
              </Container>
            </MegaMenuContainer>
          )}
        </AnimatePresence>
      </StyledAppBar>

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="left"
        open={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            maxWidth: '80vw',
          },
        }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography variant="h6">Menu</Typography>
            <IconButton onClick={() => setMobileMenuOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <List>
          {categories.map(category => renderMobileCategoryItem(category))}
        </List>
      </Drawer>

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          component={Link}
          href="/account/login"
          onClick={handleUserMenuClose}
        >
          Login
        </MenuItem>
        <MenuItem
          component={Link}
          href="/account/register"
          onClick={handleUserMenuClose}
        >
          Register
        </MenuItem>
        <MenuItem
          component={Link}
          href="/account/dashboard"
          onClick={handleUserMenuClose}
        >
          My Account
        </MenuItem>
        <MenuItem
          component={Link}
          href="/account/orders"
          onClick={handleUserMenuClose}
        >
          My Orders
        </MenuItem>
        <MenuItem
          component={Link}
          href="/wishlist"
          onClick={handleUserMenuClose}
        >
          Wishlist
        </MenuItem>
      </Menu>
    </>
  );
}
