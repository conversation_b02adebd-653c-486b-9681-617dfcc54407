'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  Rating,
  Pagination,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  CircularProgress,
  Alert,
  Skeleton,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  FavoriteBorder as FavoriteIcon,
  Favorite as FavoriteFilledIcon,
  ShoppingCart as CartIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { gql } from '@/lib/graphql/simpleClient';
import { ProductImage } from '@/components/ui/MagentoImage';

// GraphQL query for category products
const GET_CATEGORY_PRODUCTS = `
  query GetCategoryProducts(
    $categoryId: String!
    $pageSize: Int = 20
    $currentPage: Int = 1
    $sort: ProductAttributeSortInput
  ) {
    products(
      filter: { category_id: { eq: $categoryId } }
      pageSize: $pageSize
      currentPage: $currentPage
      sort: $sort
    ) {
      items {
        uid
        id
        name
        sku
        url_key
        image {
          url
          label
        }
        small_image {
          url
          label
        }
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
            discount {
              amount_off
              percent_off
            }
          }
        }
        rating_summary
        review_count
        stock_status
      }
      page_info {
        page_size
        current_page
        total_pages
      }
      total_count
      aggregations {
        label
        count
        attribute_code
        options {
          label
          value
          count
        }
      }
      sort_fields {
        default
        options {
          label
          value
        }
      }
    }
  }
`;

// Styled components
const ProductCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    '& .product-actions': {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
}));

const ProductMedia = styled(Box)(({ theme }) => ({
  position: 'relative',
  paddingTop: '100%', // 1:1 Aspect Ratio
  overflow: 'hidden',
  backgroundColor: theme.palette.grey[100],
}));

const ProductActions = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(0.5),
  opacity: 0,
  transform: 'translateY(-10px)',
  transition: 'all 0.3s ease',
}));

const ActionButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  boxShadow: theme.shadows[2],
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    transform: 'scale(1.1)',
  },
}));

const PriceBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginTop: theme.spacing(1),
}));

// Product interface
interface Product {
  uid: string;
  id: number;
  name: string;
  sku: string;
  url_key: string;
  image?: {
    url: string;
    label: string;
  };
  price_range: {
    minimum_price: {
      regular_price: {
        value: number;
        currency: string;
      };
      final_price: {
        value: number;
        currency: string;
      };
      discount?: {
        amount_off: number;
        percent_off: number;
      };
    };
  };
  rating_summary: number;
  review_count: number;
  stock_status: string;
}

// Category Products Props
interface CategoryProductsProps {
  categoryId: string;
  currentPage: number;
  sortBy: string;
  searchParams: Record<string, any>;
}

// Sort options
const sortOptions = [
  { value: 'position', label: 'Position' },
  { value: 'name', label: 'Name A-Z' },
  { value: 'name_desc', label: 'Name Z-A' },
  { value: 'price', label: 'Price Low to High' },
  { value: 'price_desc', label: 'Price High to Low' },
  { value: 'created_at', label: 'Newest First' },
  { value: 'rating', label: 'Highest Rated' },
];

export default function CategoryProducts({
  categoryId,
  currentPage,
  sortBy,
  searchParams,
}: CategoryProductsProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [pageInfo, setPageInfo] = useState<any>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [wishlist, setWishlist] = useState<Set<string>>(new Set());

  // Load products
  useEffect(() => {
    const loadProducts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Convert sort option to GraphQL format
        const sortConfig = getSortConfig(sortBy);

        const response = await gql<{
          products: {
            items: Product[];
            page_info: any;
            total_count: number;
          };
        }>(GET_CATEGORY_PRODUCTS, {
          categoryId,
          pageSize: 20,
          currentPage,
          sort: sortConfig,
        });

        setProducts(response.products.items);
        setPageInfo(response.products.page_info);
        setTotalCount(response.products.total_count);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products');
        console.error('Category products error:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, [categoryId, currentPage, sortBy]);

  // Convert sort option to GraphQL format
  const getSortConfig = (sort: string) => {
    switch (sort) {
      case 'name':
        return { name: 'ASC' };
      case 'name_desc':
        return { name: 'DESC' };
      case 'price':
        return { price: 'ASC' };
      case 'price_desc':
        return { price: 'DESC' };
      case 'created_at':
        return { created_at: 'DESC' };
      case 'rating':
        return { rating_summary: 'DESC' };
      default:
        return { position: 'ASC' };
    }
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());
    window.location.href = url.toString();
  };

  // Handle sort change
  const handleSortChange = (event: any) => {
    const url = new URL(window.location.href);
    url.searchParams.set('sort', event.target.value);
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
  };

  // Toggle wishlist
  const toggleWishlist = (productId: string) => {
    setWishlist(prev => {
      const newWishlist = new Set(prev);
      if (newWishlist.has(productId)) {
        newWishlist.delete(productId);
      } else {
        newWishlist.add(productId);
      }
      return newWishlist;
    });
  };

  // Format price
  const formatPrice = (price: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(price);
  };

  // Render product card
  const renderProductCard = (product: Product) => {
    const hasDiscount = product.price_range.minimum_price.discount?.amount_off > 0;
    const finalPrice = product.price_range.minimum_price.final_price.value;
    const regularPrice = product.price_range.minimum_price.regular_price.value;
    const isInWishlist = wishlist.has(product.uid);

    return (
      <Grid item xs={12} sm={6} md={4} lg={3} key={product.uid}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <ProductCard>
            <ProductMedia>
              <Link href={`/product/${product.url_key}`} style={{ display: 'block', height: '100%' }}>
                {product.image ? (
                  <ProductImage
                    src={product.image.url}
                    alt={product.image.label || product.name}
                    width={300}
                    height={300}
                    variant="small_image"
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'grey.100',
                      color: 'text.secondary',
                    }}
                  >
                    No Image
                  </Box>
                )}
              </Link>

              {/* Product Actions */}
              <ProductActions className="product-actions">
                <Tooltip title={isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}>
                  <ActionButton
                    size="small"
                    onClick={() => toggleWishlist(product.uid)}
                    color={isInWishlist ? 'error' : 'default'}
                  >
                    {isInWishlist ? <FavoriteFilledIcon /> : <FavoriteIcon />}
                  </ActionButton>
                </Tooltip>
                
                <Tooltip title="Quick View">
                  <ActionButton size="small">
                    <ViewIcon />
                  </ActionButton>
                </Tooltip>
                
                <Tooltip title="Add to Cart">
                  <ActionButton size="small" color="primary">
                    <CartIcon />
                  </ActionButton>
                </Tooltip>
              </ProductActions>

              {/* Discount Badge */}
              {hasDiscount && (
                <Chip
                  label={`-${Math.round(product.price_range.minimum_price.discount!.percent_off)}%`}
                  color="error"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    fontWeight: 600,
                  }}
                />
              )}

              {/* Stock Status */}
              {product.stock_status === 'OUT_OF_STOCK' && (
                <Chip
                  label="Out of Stock"
                  color="default"
                  size="small"
                  sx={{
                    position: 'absolute',
                    bottom: 8,
                    left: 8,
                    backgroundColor: 'rgba(0,0,0,0.7)',
                    color: 'white',
                  }}
                />
              )}
            </ProductMedia>

            <CardContent sx={{ flexGrow: 1, p: 2 }}>
              <Typography
                variant="h6"
                component={Link}
                href={`/product/${product.url_key}`}
                sx={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  lineHeight: 1.3,
                  mb: 1,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textDecoration: 'none',
                  color: 'text.primary',
                  '&:hover': {
                    color: 'primary.main',
                  },
                }}
              >
                {product.name}
              </Typography>

              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mb: 1, fontSize: '0.875rem' }}
              >
                SKU: {product.sku}
              </Typography>

              {product.rating_summary > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Rating
                    value={product.rating_summary / 20} // Convert from 0-100 to 0-5
                    readOnly
                    size="small"
                    precision={0.1}
                  />
                  <Typography variant="caption" sx={{ ml: 1 }}>
                    ({product.review_count})
                  </Typography>
                </Box>
              )}

              <PriceBox>
                {hasDiscount ? (
                  <>
                    <Typography
                      variant="h6"
                      sx={{
                        color: 'error.main',
                        fontWeight: 600,
                        fontSize: '1.125rem',
                      }}
                    >
                      {formatPrice(finalPrice, product.price_range.minimum_price.final_price.currency)}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.secondary',
                        fontSize: '0.875rem',
                      }}
                    >
                      {formatPrice(regularPrice, product.price_range.minimum_price.regular_price.currency)}
                    </Typography>
                  </>
                ) : (
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      fontSize: '1.125rem',
                      color: 'text.primary',
                    }}
                  >
                    {formatPrice(finalPrice, product.price_range.minimum_price.final_price.currency)}
                  </Typography>
                )}
              </PriceBox>
            </CardContent>
          </ProductCard>
        </motion.div>
      </Grid>
    );
  };

  // Loading skeleton
  const renderSkeleton = () => (
    <Grid container spacing={3}>
      {Array.from({ length: 8 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Card>
            <Skeleton variant="rectangular" height={300} />
            <CardContent>
              <Skeleton variant="text" height={24} />
              <Skeleton variant="text" height={20} width="60%" />
              <Skeleton variant="text" height={28} width="40%" />
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  if (loading) {
    return (
      <Box id="products">
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Skeleton variant="text" width={200} height={32} />
          <Skeleton variant="rectangular" width={200} height={40} />
        </Box>
        {renderSkeleton()}
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Error Loading Products
        </Typography>
        <Typography variant="body2">
          {error}
        </Typography>
      </Alert>
    );
  }

  if (products.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }} id="products">
        <Typography variant="h5" gutterBottom>
          No products found in this category
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Check back later for new products
        </Typography>
      </Box>
    );
  }

  return (
    <Box id="products">
      {/* Products Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          {totalCount} product{totalCount !== 1 ? 's' : ''}
        </Typography>
        
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Sort by</InputLabel>
          <Select
            value={sortBy}
            label="Sort by"
            onChange={handleSortChange}
          >
            {sortOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Product Grid */}
      <AnimatePresence>
        <Grid container spacing={3}>
          {products.map(renderProductCard)}
        </Grid>
      </AnimatePresence>

      {/* Pagination */}
      {pageInfo && pageInfo.total_pages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 6 }}>
          <Pagination
            count={pageInfo.total_pages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}
    </Box>
  );
}
