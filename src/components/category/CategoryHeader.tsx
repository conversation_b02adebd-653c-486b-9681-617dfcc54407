'use client';

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Container,
  useTheme,
  alpha,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { CategoryImage } from '@/components/ui/MagentoImage';

// Styled components
const CategoryHero = styled(Box)(({ theme }) => ({
  position: 'relative',
  minHeight: 300,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  marginBottom: theme.spacing(4),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
}));

const CategoryOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.6) 100%)',
  zIndex: 1,
}));

const CategoryContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 2,
  textAlign: 'center',
  color: 'white',
  padding: theme.spacing(4),
}));

const SubcategoryCard = styled(Card)(({ theme }) => ({
  height: '100%',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const SubcategoryMedia = styled(Box)(({ theme }) => ({
  position: 'relative',
  paddingTop: '75%', // 4:3 Aspect Ratio
  overflow: 'hidden',
  backgroundColor: theme.palette.grey[100],
}));

// Category interface
interface Category {
  uid: string;
  id: number;
  name: string;
  url_key: string;
  url_path: string;
  description?: string;
  image?: string;
}

// Subcategory interface
interface Subcategory {
  uid: string;
  id: number;
  name: string;
  url_key: string;
  url_path: string;
  image?: string;
}

// Category Header Props
interface CategoryHeaderProps {
  category: Category;
  subcategories: Subcategory[];
}

export default function CategoryHeader({
  category,
  subcategories,
}: CategoryHeaderProps) {
  const theme = useTheme();

  return (
    <Box>
      {/* Category Hero Section */}
      <CategoryHero>
        {category.image && (
          <CategoryImage
            src={category.image}
            alt={category.name}
            fill
            style={{
              objectFit: 'cover',
              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 0,
            }}
          />
        )}
        <CategoryOverlay />
        <CategoryContent>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Typography
              variant="h2"
              component="h1"
              sx={{
                fontWeight: 800,
                mb: 2,
                fontSize: { xs: '2rem', md: '3rem' },
                textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
              }}
            >
              {category.name}
            </Typography>
            
            {category.description && (
              <Typography
                variant="h6"
                sx={{
                  maxWidth: 600,
                  mx: 'auto',
                  mb: 3,
                  opacity: 0.9,
                  textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                  fontSize: { xs: '1rem', md: '1.25rem' },
                }}
                dangerouslySetInnerHTML={{ __html: category.description }}
              />
            )}
            
            <Button
              variant="contained"
              size="large"
              sx={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.3)',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  transform: 'translateY(-2px)',
                },
              }}
              href="#products"
            >
              Shop Now
            </Button>
          </motion.div>
        </CategoryContent>
      </CategoryHero>

      {/* Subcategories */}
      {subcategories && subcategories.length > 0 && (
        <Box sx={{ mb: 6 }}>
          <Typography
            variant="h4"
            component="h2"
            sx={{
              mb: 4,
              textAlign: 'center',
              fontWeight: 700,
              color: 'text.primary',
            }}
          >
            Shop by Category
          </Typography>
          
          <Grid container spacing={3}>
            {subcategories.slice(0, 8).map((subcategory, index) => (
              <Grid item xs={6} sm={4} md={3} key={subcategory.uid}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <SubcategoryCard
                    component={Link}
                    href={`/category/${subcategory.url_path}`}
                    sx={{ textDecoration: 'none' }}
                  >
                    <SubcategoryMedia>
                      {subcategory.image ? (
                        <CategoryImage
                          src={subcategory.image}
                          alt={subcategory.name}
                          fill
                          style={{
                            objectFit: 'cover',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                          }}
                        />
                      ) : (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.2)} 100%)`,
                            color: theme.palette.primary.main,
                            fontSize: '3rem',
                            fontWeight: 300,
                          }}
                        >
                          {subcategory.name.charAt(0)}
                        </Box>
                      )}
                      
                      {/* Overlay on hover */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%)',
                          opacity: 0,
                          transition: 'opacity 0.3s ease',
                          '.MuiCard-root:hover &': {
                            opacity: 1,
                          },
                        }}
                      />
                    </SubcategoryMedia>
                    
                    <CardContent sx={{ p: 2 }}>
                      <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                          fontWeight: 600,
                          fontSize: '1rem',
                          textAlign: 'center',
                          color: 'text.primary',
                          lineHeight: 1.3,
                        }}
                      >
                        {subcategory.name}
                      </Typography>
                    </CardContent>
                  </SubcategoryCard>
                </motion.div>
              </Grid>
            ))}
          </Grid>
          
          {subcategories.length > 8 && (
            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 4,
                }}
              >
                View All Categories
              </Button>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
}
