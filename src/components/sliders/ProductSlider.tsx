'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, FreeMode } from 'swiper/modules';
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Rating,
  Chip,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  FavoriteBorder,
  Favorite,
  ShoppingCart,
  Visibility,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { ProductCardSkeleton } from '@/components/ui/LoadingSkeleton';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/free-mode';

// Product interface (simplified for slider)
interface Product {
  id: number;
  name: string;
  sku: string;
  url_key: string;
  price: {
    regular: number;
    final: number;
    currency: string;
  };
  image: {
    url: string;
    alt: string;
  };
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  isOnSale?: boolean;
  stockStatus: 'IN_STOCK' | 'OUT_OF_STOCK';
}

// Product card component
const ProductCard: React.FC<{ product: Product }> = ({ product }) => {
  const [isFavorite, setIsFavorite] = React.useState(false);
  const theme = useTheme();

  const handleFavoriteToggle = () => {
    setIsFavorite(!isFavorite);
  };

  const discountPercentage =
    product.price.regular > product.price.final
      ? Math.round(
          ((product.price.regular - product.price.final) /
            product.price.regular) *
            100
        )
      : 0;

  return (
    <motion.div whileHover={{ y: -5 }} transition={{ duration: 0.3 }}>
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          borderRadius: 2,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          '&:hover': {
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          },
          transition: 'box-shadow 0.3s ease',
        }}
      >
        {/* Product Image */}
        <Box sx={{ position: 'relative', paddingTop: '75%' }}>
          <Image
            src={product.image.url}
            alt={product.image.alt}
            fill
            style={{
              objectFit: 'cover',
            }}
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          />

          {/* Badges */}
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              display: 'flex',
              flexDirection: 'column',
              gap: 0.5,
            }}
          >
            {product.isNew && (
              <Chip
                label="New"
                size="small"
                sx={{
                  backgroundColor: theme.palette.success.main,
                  color: 'white',
                  fontWeight: 600,
                }}
              />
            )}
            {discountPercentage > 0 && (
              <Chip
                label={`-${discountPercentage}%`}
                size="small"
                sx={{
                  backgroundColor: theme.palette.error.main,
                  color: 'white',
                  fontWeight: 600,
                }}
              />
            )}
          </Box>

          {/* Favorite Button */}
          <IconButton
            onClick={handleFavoriteToggle}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              backgroundColor: 'rgba(255,255,255,0.9)',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,1)',
              },
            }}
            size="small"
          >
            {isFavorite ? (
              <Favorite sx={{ color: theme.palette.error.main }} />
            ) : (
              <FavoriteBorder />
            )}
          </IconButton>

          {/* Quick Actions */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 8,
              right: 8,
              display: 'flex',
              gap: 0.5,
              opacity: 0,
              transition: 'opacity 0.3s ease',
              '.MuiCard-root:hover &': {
                opacity: 1,
              },
            }}
          >
            <IconButton
              size="small"
              sx={{
                backgroundColor: 'rgba(255,255,255,0.9)',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,1)',
                },
              }}
            >
              <Visibility />
            </IconButton>
          </Box>
        </Box>

        {/* Product Info */}
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          <Typography
            variant="h6"
            component={Link}
            href={`/products/${product.url_key}`}
            sx={{
              fontSize: '1rem',
              fontWeight: 500,
              mb: 1,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textDecoration: 'none',
              color: 'inherit',
              '&:hover': {
                color: theme.palette.primary.main,
              },
            }}
          >
            {product.name}
          </Typography>

          {/* Rating */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Rating
              value={product.rating}
              precision={0.5}
              size="small"
              readOnly
            />
            <Typography
              variant="caption"
              sx={{ ml: 0.5, color: 'text.secondary' }}
            >
              ({product.reviewCount})
            </Typography>
          </Box>

          {/* Price */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: theme.palette.primary.main,
              }}
            >
              {product.price.currency}
              {product.price.final.toFixed(2)}
            </Typography>
            {discountPercentage > 0 && (
              <Typography
                variant="body2"
                sx={{
                  textDecoration: 'line-through',
                  color: 'text.secondary',
                }}
              >
                {product.price.currency}
                {product.price.regular.toFixed(2)}
              </Typography>
            )}
          </Box>
        </CardContent>

        {/* Actions */}
        <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
          <Button
            variant="contained"
            startIcon={<ShoppingCart />}
            fullWidth
            disabled={product.stockStatus === 'OUT_OF_STOCK'}
            sx={{
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            {product.stockStatus === 'OUT_OF_STOCK'
              ? 'Out of Stock'
              : 'Add to Cart'}
          </Button>
        </CardActions>
      </Card>
    </motion.div>
  );
};

// Product slider props
interface ProductSliderProps {
  products: Product[];
  title?: string;
  showNavigation?: boolean;
  showPagination?: boolean;
  slidesPerView?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  spaceBetween?: number;
  loading?: boolean;
  skeletonCount?: number;
}

// Main product slider component
export const ProductSlider: React.FC<ProductSliderProps> = ({
  products,
  title,
  showNavigation = true,
  showPagination = false,
  slidesPerView = {
    mobile: 1.2,
    tablet: 2.5,
    desktop: 4,
  },
  spaceBetween = 16,
  loading = false,
  skeletonCount = 8,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Show loading skeletons
  if (loading) {
    return (
      <Box>
        {title && (
          <Typography
            variant="h4"
            component="h2"
            sx={{
              mb: 3,
              fontWeight: 600,
              textAlign: { xs: 'center', md: 'left' },
            }}
          >
            {title}
          </Typography>
        )}

        <Box sx={{ position: 'relative' }}>
          <Swiper
            modules={[Navigation, Pagination, FreeMode]}
            spaceBetween={spaceBetween}
            slidesPerView={slidesPerView.mobile}
            freeMode={true}
            navigation={false}
            pagination={false}
            breakpoints={{
              600: {
                slidesPerView: slidesPerView.tablet,
              },
              900: {
                slidesPerView: slidesPerView.desktop,
              },
            }}
            className="product-slider"
          >
            {Array.from({ length: skeletonCount }).map((_, index) => (
              <SwiperSlide key={index}>
                <ProductCardSkeleton animationType="shimmer" />
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
      </Box>
    );
  }

  if (!products || products.length === 0) {
    return null;
  }

  return (
    <Box>
      {title && (
        <Typography
          variant="h4"
          component="h2"
          sx={{
            mb: 3,
            fontWeight: 600,
            textAlign: { xs: 'center', md: 'left' },
          }}
        >
          {title}
        </Typography>
      )}

      <Box sx={{ position: 'relative' }}>
        <Swiper
          modules={[Navigation, Pagination, FreeMode]}
          spaceBetween={spaceBetween}
          slidesPerView={slidesPerView.mobile}
          freeMode={true}
          navigation={showNavigation && !isMobile}
          pagination={
            showPagination
              ? {
                  clickable: true,
                  dynamicBullets: true,
                }
              : false
          }
          breakpoints={{
            600: {
              slidesPerView: slidesPerView.tablet,
            },
            900: {
              slidesPerView: slidesPerView.desktop,
            },
          }}
          className="product-slider"
          style={
            {
              '--swiper-navigation-color': theme.palette.primary.main,
              '--swiper-pagination-color': theme.palette.primary.main,
            } as React.CSSProperties
          }
        >
          {products.map(product => (
            <SwiperSlide key={product.id}>
              <ProductCard product={product} />
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom styles for Swiper */}
        <style jsx global>{`
          .product-slider .swiper-button-next,
          .product-slider .swiper-button-prev {
            background: white;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            margin-top: -22px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
          }

          .product-slider .swiper-button-next:hover,
          .product-slider .swiper-button-prev:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
          }

          .product-slider .swiper-button-next::after,
          .product-slider .swiper-button-prev::after {
            font-size: 16px;
            font-weight: bold;
          }

          .product-slider .swiper-pagination {
            bottom: -40px;
          }
        `}</style>
      </Box>
    </Box>
  );
};

export default ProductSlider;
