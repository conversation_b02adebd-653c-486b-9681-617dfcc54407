'use client';

import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Navigation, Pagination, EffectFade } from 'swiper/modules';
import {
  Box,
  Typography,
  Button,
  Container,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { BannerSkeleton } from '@/components/ui/LoadingSkeleton';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

// Banner interface
interface Banner {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  mobileImage?: string;
  link: string;
  cta: string;
  backgroundColor?: string;
  textColor?: string;
}

// Banner slider props
interface BannerSliderProps {
  banners: Banner[];
  height?: number | string;
  autoplay?: boolean;
  autoplayDelay?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
  effect?: 'slide' | 'fade';
  loading?: boolean;
}

// Individual banner slide component
const BannerSlide: React.FC<{ banner: Banner; height: number | string }> = ({
  banner,
  height,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const currentImage =
    isMobile && banner.mobileImage ? banner.mobileImage : banner.image;

  return (
    <Box
      sx={{
        position: 'relative',
        height: typeof height === 'number' ? `${height}px` : height,
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
        backgroundColor: banner.backgroundColor || 'transparent',
        color: banner.textColor || 'white',
      }}
    >
      {/* Background Image */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -2,
        }}
      >
        <Image
          src={currentImage}
          alt={banner.title}
          fill
          style={{
            objectFit: 'cover',
            objectPosition: 'center',
          }}
          sizes="100vw"
        />
      </Box>

      {/* Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          zIndex: -1,
        }}
      />

      {/* Content */}
      <Container maxWidth="lg">
        <Box
          sx={{
            maxWidth: { xs: '100%', md: '50%' },
            textAlign: { xs: 'center', md: 'left' },
          }}
        >
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h6"
              component="p"
              sx={{
                fontSize: { xs: '0.875rem', md: '1rem' },
                fontWeight: 500,
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: 1,
                opacity: 0.9,
              }}
            >
              {banner.subtitle}
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h2"
              component="h3"
              sx={{
                fontSize: { xs: '2rem', md: '2.5rem', lg: '3rem' },
                fontWeight: 700,
                mb: 2,
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
              }}
            >
              {banner.title}
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="body1"
              sx={{
                fontSize: { xs: '1rem', md: '1.125rem' },
                mb: 3,
                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                opacity: 0.9,
                maxWidth: '400px',
              }}
            >
              {banner.description}
            </Typography>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <Button
              component={Link}
              href={banner.link}
              variant="contained"
              size="large"
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                px: 3,
                py: 1.5,
                borderRadius: 2,
                textTransform: 'none',
                boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                '&:hover': {
                  boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease-in-out',
              }}
            >
              {banner.cta}
            </Button>
          </motion.div>
        </Box>
      </Container>
    </Box>
  );
};

// Main banner slider component
export const BannerSlider: React.FC<BannerSliderProps> = ({
  banners,
  height = 400,
  autoplay = true,
  autoplayDelay = 5000,
  showNavigation = true,
  showPagination = true,
  effect = 'slide',
  loading = false,
}) => {
  // Show loading skeleton
  if (loading) {
    return (
      <BannerSkeleton
        height={typeof height === 'number' ? height : 400}
        animationType="shimmer"
      />
    );
  }

  if (!banners || banners.length === 0) {
    return null;
  }

  return (
    <Box sx={{ position: 'relative' }}>
      <Swiper
        modules={[Autoplay, Navigation, Pagination, EffectFade]}
        spaceBetween={0}
        slidesPerView={1}
        autoplay={
          autoplay
            ? {
                delay: autoplayDelay,
                disableOnInteraction: false,
              }
            : false
        }
        navigation={showNavigation}
        pagination={
          showPagination
            ? {
                clickable: true,
                dynamicBullets: true,
              }
            : false
        }
        effect={effect}
        fadeEffect={{
          crossFade: true,
        }}
        loop={banners.length > 1}
        className="banner-slider"
        style={
          {
            '--swiper-navigation-color': '#fff',
            '--swiper-pagination-color': '#fff',
            '--swiper-navigation-size': '24px',
          } as React.CSSProperties
        }
      >
        {banners.map(banner => (
          <SwiperSlide key={banner.id}>
            <BannerSlide banner={banner} height={height} />
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom styles for Swiper */}
      <style jsx global>{`
        .banner-slider .swiper-button-next,
        .banner-slider .swiper-button-prev {
          background: rgba(0, 0, 0, 0.3);
          border-radius: 50%;
          width: 50px;
          height: 50px;
          margin-top: -25px;
          transition: all 0.3s ease;
        }

        .banner-slider .swiper-button-next:hover,
        .banner-slider .swiper-button-prev:hover {
          background: rgba(0, 0, 0, 0.5);
          transform: scale(1.1);
        }

        .banner-slider .swiper-button-next::after,
        .banner-slider .swiper-button-prev::after {
          font-size: 18px;
          font-weight: bold;
        }

        .banner-slider .swiper-pagination {
          bottom: 20px;
        }

        .banner-slider .swiper-pagination-bullet {
          width: 12px;
          height: 12px;
          background: rgba(255, 255, 255, 0.5);
          opacity: 1;
          transition: all 0.3s ease;
        }

        .banner-slider .swiper-pagination-bullet-active {
          background: #fff;
          transform: scale(1.2);
        }

        @media (max-width: 768px) {
          .banner-slider .swiper-button-next,
          .banner-slider .swiper-button-prev {
            width: 40px;
            height: 40px;
            margin-top: -20px;
          }

          .banner-slider .swiper-button-next::after,
          .banner-slider .swiper-button-prev::after {
            font-size: 14px;
          }
        }
      `}</style>
    </Box>
  );
};

export default BannerSlider;
