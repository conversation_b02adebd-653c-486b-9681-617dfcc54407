'use client';

import React from 'react';
import {
  Skeleton,
  Box,
  Card,
  CardContent,
  Grid,
  keyframes,
} from '@mui/material';
import { motion } from 'framer-motion';

// Custom shimmer animation
const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

// Custom pulse animation
const pulse = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
`;

// Animation variants for motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

// Enhanced skeleton styles with animations
const getSkeletonSx = (
  animationType: 'shimmer' | 'pulse' | 'wave' = 'shimmer'
) => {
  const baseSx = {
    borderRadius: 2,
    transform: 'scale(1)',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'scale(1.02)',
    },
  };

  switch (animationType) {
    case 'shimmer':
      return {
        ...baseSx,
        background:
          'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
        backgroundSize: '200px 100%',
        animation: `${shimmer} 2s infinite linear`,
      };
    case 'pulse':
      return {
        ...baseSx,
        animation: `${pulse} 2s infinite ease-in-out`,
      };
    default:
      return baseSx;
  }
};

// Product card skeleton with enhanced animations
export const ProductCardSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div variants={itemVariants}>
      <Card
        sx={{
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
        }}
      >
        <Skeleton
          variant="rectangular"
          height={200}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
        <CardContent>
          <Skeleton
            variant="text"
            height={24}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="text"
            height={20}
            width="60%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mt: 1 }}
          />
          <Box sx={{ mt: 1 }}>
            <Skeleton
              variant="text"
              height={28}
              width="40%"
              animation={animationType === 'shimmer' ? false : animationType}
              sx={getSkeletonSx(animationType)}
            />
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Product grid skeleton with staggered animations
interface ProductGridSkeletonProps {
  count?: number;
  columns?: number;
  animationType?: 'shimmer' | 'pulse' | 'wave';
}

export const ProductGridSkeleton: React.FC<ProductGridSkeletonProps> = ({
  count = 12,
  columns = 4,
  animationType = 'shimmer',
}) => {
  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Grid container spacing={3}>
        {Array.from({ length: count }).map((_, index) => (
          <Grid item xs={12} sm={6} md={12 / columns} key={index}>
            <ProductCardSkeleton animationType={animationType} />
          </Grid>
        ))}
      </Grid>
    </motion.div>
  );
};

// Category card skeleton with enhanced animations
export const CategoryCardSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div variants={itemVariants}>
      <Card
        sx={{
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
        }}
      >
        <Skeleton
          variant="rectangular"
          height={150}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
        <CardContent>
          <Skeleton
            variant="text"
            height={24}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="text"
            height={16}
            width="80%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mt: 1 }}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Banner skeleton with enhanced animations
export const BannerSkeleton: React.FC<{
  height?: number;
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ height = 300, animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <Skeleton
        variant="rectangular"
        height={height}
        animation={animationType === 'shimmer' ? false : animationType}
        sx={{
          ...getSkeletonSx(animationType),
          borderRadius: 2,
        }}
      />
    </motion.div>
  );
};

// Text skeleton with multiple lines and enhanced animations
interface TextSkeletonProps {
  lines?: number;
  width?: string | number;
  animationType?: 'shimmer' | 'pulse' | 'wave';
}

export const TextSkeleton: React.FC<TextSkeletonProps> = ({
  lines = 3,
  width = '100%',
  animationType = 'shimmer',
}) => {
  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Box>
        {Array.from({ length: lines }).map((_, index) => (
          <motion.div key={index} variants={itemVariants}>
            <Skeleton
              variant="text"
              height={20}
              width={index === lines - 1 ? '60%' : width}
              animation={animationType === 'shimmer' ? false : animationType}
              sx={{
                ...getSkeletonSx(animationType),
                mb: 0.5,
              }}
            />
          </motion.div>
        ))}
      </Box>
    </motion.div>
  );
};

// Cart item skeleton with enhanced animations
export const CartItemSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', p: 2, gap: 2 }}>
        <Skeleton
          variant="rectangular"
          width={80}
          height={80}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
        <Box sx={{ flex: 1 }}>
          <Skeleton
            variant="text"
            height={24}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="text"
            height={20}
            width="60%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}
          />
          <Skeleton
            variant="text"
            height={20}
            width="40%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}
          />
        </Box>
        <Box
          sx={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}
        >
          <Skeleton
            variant="text"
            height={24}
            width={60}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="rectangular"
            width={40}
            height={32}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mt: 1 }}
          />
        </Box>
      </Box>
    </motion.div>
  );
};

// Order item skeleton with enhanced animations
export const OrderItemSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Box
        sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, mb: 2 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Skeleton
            variant="text"
            height={20}
            width={120}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="text"
            height={20}
            width={80}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
        </Box>
        <Skeleton
          variant="text"
          height={16}
          width="100%"
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
        <Skeleton
          variant="text"
          height={16}
          width="80%"
          animation={animationType === 'shimmer' ? false : animationType}
          sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}
        />
        <Box sx={{ mt: 1, display: 'flex', gap: 2 }}>
          <Skeleton
            variant="text"
            height={16}
            width={60}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
          <Skeleton
            variant="text"
            height={16}
            width={80}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={getSkeletonSx(animationType)}
          />
        </Box>
      </Box>
    </motion.div>
  );
};

// Page skeleton for full page loading with enhanced animations
export const PageSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <motion.div variants={itemVariants}>
          <Skeleton
            variant="text"
            height={40}
            width="30%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mb: 2 }}
          />
        </motion.div>

        {/* Breadcrumbs */}
        <motion.div variants={itemVariants}>
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            <Skeleton
              variant="text"
              height={16}
              width={60}
              animation={animationType === 'shimmer' ? false : animationType}
              sx={getSkeletonSx(animationType)}
            />
            <Skeleton
              variant="text"
              height={16}
              width={20}
              animation={animationType === 'shimmer' ? false : animationType}
              sx={getSkeletonSx(animationType)}
            />
            <Skeleton
              variant="text"
              height={16}
              width={80}
              animation={animationType === 'shimmer' ? false : animationType}
              sx={getSkeletonSx(animationType)}
            />
          </Box>
        </motion.div>

        {/* Content */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            {/* Sidebar */}
            <motion.div variants={itemVariants}>
              <Box sx={{ mb: 2 }}>
                <Skeleton
                  variant="text"
                  height={24}
                  width="60%"
                  animation={
                    animationType === 'shimmer' ? false : animationType
                  }
                  sx={getSkeletonSx(animationType)}
                />
                {Array.from({ length: 5 }).map((_, index) => (
                  <Skeleton
                    key={index}
                    variant="text"
                    height={20}
                    animation={
                      animationType === 'shimmer' ? false : animationType
                    }
                    sx={{ ...getSkeletonSx(animationType), mt: 1 }}
                  />
                ))}
              </Box>
            </motion.div>
          </Grid>
          <Grid item xs={12} md={9}>
            {/* Main content */}
            <ProductGridSkeleton
              count={9}
              columns={3}
              animationType={animationType}
            />
          </Grid>
        </Grid>
      </Box>
    </motion.div>
  );
};

// Generic list skeleton with enhanced animations
interface ListSkeletonProps {
  count?: number;
  height?: number;
  animationType?: 'shimmer' | 'pulse' | 'wave';
}

export const ListSkeleton: React.FC<ListSkeletonProps> = ({
  count = 5,
  height = 60,
  animationType = 'shimmer',
}) => {
  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Box>
        {Array.from({ length: count }).map((_, index) => (
          <motion.div key={index} variants={itemVariants}>
            <Box sx={{ mb: 1 }}>
              <Skeleton
                variant="rectangular"
                height={height}
                animation={animationType === 'shimmer' ? false : animationType}
                sx={getSkeletonSx(animationType)}
              />
            </Box>
          </motion.div>
        ))}
      </Box>
    </motion.div>
  );
};

// Page Builder specific skeletons
export const PageBuilderRowSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <Box sx={{ width: '100%', mb: 3 }}>
        <Skeleton
          variant="rectangular"
          height={60}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
      </Box>
    </motion.div>
  );
};

export const PageBuilderColumnSkeleton: React.FC<{
  width?: string;
  height?: number;
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ width = '100%', height = 200, animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Skeleton
        variant="rectangular"
        height={height}
        animation={animationType === 'shimmer' ? false : animationType}
        sx={{
          ...getSkeletonSx(animationType),
          width,
        }}
      />
    </motion.div>
  );
};

export const PageBuilderBannerSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, ease: 'easeOut' }}
    >
      <Box sx={{ position: 'relative', width: '100%', mb: 3 }}>
        <Skeleton
          variant="rectangular"
          height={400}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            width: '80%',
          }}
        >
          <Skeleton
            variant="text"
            height={48}
            width="70%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mb: 2, mx: 'auto' }}
          />
          <Skeleton
            variant="text"
            height={24}
            width="50%"
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mb: 3, mx: 'auto' }}
          />
          <Skeleton
            variant="rectangular"
            height={40}
            width={120}
            animation={animationType === 'shimmer' ? false : animationType}
            sx={{ ...getSkeletonSx(animationType), mx: 'auto' }}
          />
        </Box>
      </Box>
    </motion.div>
  );
};

export const CmsBlockSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <Box
        sx={{
          width: '100%',
          p: 2,
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          mb: 2,
        }}
      >
        <Skeleton
          variant="text"
          height={28}
          width="40%"
          animation={animationType === 'shimmer' ? false : animationType}
          sx={{ ...getSkeletonSx(animationType), mb: 2 }}
        />
        <Skeleton
          variant="rectangular"
          height={120}
          animation={animationType === 'shimmer' ? false : animationType}
          sx={getSkeletonSx(animationType)}
        />
      </Box>
    </motion.div>
  );
};

export const PageBuilderContentSkeleton: React.FC<{
  animationType?: 'shimmer' | 'pulse' | 'wave';
}> = ({ animationType = 'shimmer' }) => {
  return (
    <motion.div variants={containerVariants} initial="hidden" animate="visible">
      <Box sx={{ width: '100%' }}>
        {/* Row with columns */}
        <motion.div variants={itemVariants}>
          <PageBuilderRowSkeleton animationType={animationType} />
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <PageBuilderColumnSkeleton
              width="50%"
              animationType={animationType}
            />
            <PageBuilderColumnSkeleton
              width="50%"
              animationType={animationType}
            />
          </Box>
        </motion.div>

        {/* Banner */}
        <motion.div variants={itemVariants}>
          <PageBuilderBannerSkeleton animationType={animationType} />
        </motion.div>

        {/* Text content */}
        <motion.div variants={itemVariants}>
          <TextSkeleton lines={4} animationType={animationType} />
        </motion.div>
      </Box>
    </motion.div>
  );
};

export default {
  ProductCardSkeleton,
  ProductGridSkeleton,
  CategoryCardSkeleton,
  BannerSkeleton,
  TextSkeleton,
  CartItemSkeleton,
  OrderItemSkeleton,
  PageSkeleton,
  ListSkeleton,
  PageBuilderRowSkeleton,
  PageBuilderColumnSkeleton,
  PageBuilderBannerSkeleton,
  CmsBlockSkeleton,
  PageBuilderContentSkeleton,
};
