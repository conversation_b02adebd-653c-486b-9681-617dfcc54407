'use client';

import React from 'react';
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonProps as MuiButtonProps,
  CircularProgress,
  Box,
} from '@mui/material';

// Extended button props
export interface ButtonProps extends Omit<MuiButtonProps, 'loading'> {
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
}

// Custom button component
export const Button: React.FC<ButtonProps> = ({
  children,
  loading = false,
  loadingText,
  disabled,
  icon,
  iconPosition = 'start',
  startIcon,
  endIcon,
  ...props
}) => {
  const isDisabled = disabled || loading;
  
  const renderIcon = () => {
    if (loading) {
      return <CircularProgress size={16} color="inherit" />;
    }
    return icon;
  };

  const renderContent = () => {
    if (loading && loadingText) {
      return loadingText;
    }
    return children;
  };

  const getStartIcon = () => {
    if (loading) {
      return <CircularProgress size={16} color="inherit" />;
    }
    if (icon && iconPosition === 'start') {
      return icon;
    }
    return startIcon;
  };

  const getEndIcon = () => {
    if (icon && iconPosition === 'end' && !loading) {
      return icon;
    }
    return endIcon;
  };

  return (
    <MuiButton
      {...props}
      disabled={isDisabled}
      startIcon={getStartIcon()}
      endIcon={getEndIcon()}
    >
      {renderContent()}
    </MuiButton>
  );
};

// Specialized button variants
export const PrimaryButton: React.FC<ButtonProps> = (props) => (
  <Button variant="contained" color="primary" {...props} />
);

export const SecondaryButton: React.FC<ButtonProps> = (props) => (
  <Button variant="outlined" color="primary" {...props} />
);

export const DangerButton: React.FC<ButtonProps> = (props) => (
  <Button variant="contained" color="error" {...props} />
);

export const SuccessButton: React.FC<ButtonProps> = (props) => (
  <Button variant="contained" color="success" {...props} />
);

export const TextButton: React.FC<ButtonProps> = (props) => (
  <Button variant="text" {...props} />
);

// Icon button with loading state
interface IconButtonProps extends ButtonProps {
  'aria-label': string;
}

export const IconButton: React.FC<IconButtonProps> = ({
  children,
  loading,
  disabled,
  size = 'medium',
  ...props
}) => {
  return (
    <Button
      {...props}
      variant="text"
      disabled={disabled || loading}
      size={size}
      sx={{
        minWidth: 'auto',
        padding: size === 'small' ? 1 : size === 'large' ? 1.5 : 1.25,
        borderRadius: '50%',
        ...props.sx,
      }}
    >
      {loading ? <CircularProgress size={16} color="inherit" /> : children}
    </Button>
  );
};

// Floating action button
export const FloatingActionButton: React.FC<ButtonProps> = ({
  children,
  loading,
  disabled,
  size = 'large',
  ...props
}) => {
  return (
    <Button
      {...props}
      variant="contained"
      disabled={disabled || loading}
      size={size}
      sx={{
        borderRadius: '50%',
        minWidth: 'auto',
        width: size === 'small' ? 40 : size === 'large' ? 56 : 48,
        height: size === 'small' ? 40 : size === 'large' ? 56 : 48,
        boxShadow: 3,
        '&:hover': {
          boxShadow: 6,
        },
        ...props.sx,
      }}
    >
      {loading ? <CircularProgress size={20} color="inherit" /> : children}
    </Button>
  );
};

// Button group component
interface ButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: number;
  fullWidth?: boolean;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  orientation = 'horizontal',
  spacing = 1,
  fullWidth = false,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'column' : 'row',
        gap: spacing,
        width: fullWidth ? '100%' : 'auto',
        '& > *': {
          flex: fullWidth ? 1 : 'none',
        },
      }}
    >
      {children}
    </Box>
  );
};

export default Button;
