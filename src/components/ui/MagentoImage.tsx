'use client';

import React, { useState, useEffect } from 'react';
import Image, { ImageProps } from 'next/image';
import { Box, Skeleton } from '@mui/material';
import { 
  buildMediaUrl, 
  buildProductImageUrl, 
  buildCategoryImageUrl, 
  buildLogoUrl,
  buildCmsImageUrl,
  handleImageError,
  getImagePlaceholder,
  IMAGE_SIZES,
  type ImageSize,
  type ImageVariant
} from '@/lib/utils/imageUtils';

// Magento Image Props
interface MagentoImageProps extends Omit<ImageProps, 'src'> {
  src: string;
  alt: string;
  type?: 'product' | 'category' | 'logo' | 'cms' | 'banner';
  variant?: string;
  size?: ImageSize;
  storeConfig?: any;
  showSkeleton?: boolean;
  fallbackSrc?: string;
  onError?: (error: React.SyntheticEvent<HTMLImageElement>) => void;
}

// Product Image Component
export function ProductImage({
  src,
  alt,
  variant = 'image',
  storeConfig,
  showSkeleton = true,
  fallbackSrc,
  onError,
  ...props
}: Omit<MagentoImageProps, 'type'>) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const imageUrl = buildProductImageUrl(src, storeConfig, variant as any);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setError(true);
    setLoading(false);
    
    if (onError) {
      onError(event);
    } else {
      handleImageError(event, fallbackSrc);
    }
  };

  if (error && !fallbackSrc) {
    return (
      <Box
        sx={{
          width: props.width || 300,
          height: props.height || 300,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '14px',
        }}
      >
        Image not available
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width={props.width || 300}
          height={props.height || 300}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <Image
        {...props}
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleImageError}
        style={{
          ...props.style,
          opacity: loading ? 0 : 1,
          transition: 'opacity 0.3s ease',
        }}
      />
    </Box>
  );
}

// Category Image Component
export function CategoryImage({
  src,
  alt,
  storeConfig,
  showSkeleton = true,
  fallbackSrc,
  onError,
  ...props
}: Omit<MagentoImageProps, 'type' | 'variant'>) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const imageUrl = buildCategoryImageUrl(src, storeConfig);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setError(true);
    setLoading(false);
    
    if (onError) {
      onError(event);
    } else {
      handleImageError(event, fallbackSrc);
    }
  };

  if (error && !fallbackSrc) {
    return (
      <Box
        sx={{
          width: props.width || 300,
          height: props.height || 300,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '14px',
        }}
      >
        Category image not available
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width={props.width || 300}
          height={props.height || 300}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <Image
        {...props}
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleImageError}
        style={{
          ...props.style,
          opacity: loading ? 0 : 1,
          transition: 'opacity 0.3s ease',
        }}
      />
    </Box>
  );
}

// Logo Image Component
export function LogoImage({
  src,
  alt,
  storeConfig,
  showSkeleton = false,
  fallbackSrc,
  onError,
  ...props
}: Omit<MagentoImageProps, 'type' | 'variant'>) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const imageUrl = buildLogoUrl(src, storeConfig);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setError(true);
    setLoading(false);
    
    if (onError) {
      onError(event);
    } else {
      handleImageError(event, fallbackSrc);
    }
  };

  if (error && !fallbackSrc) {
    return null; // Don't show anything for logo errors
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width={props.width || 120}
          height={props.height || 40}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <Image
        {...props}
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleImageError}
        style={{
          ...props.style,
          opacity: loading ? 0 : 1,
          transition: 'opacity 0.3s ease',
        }}
      />
    </Box>
  );
}

// CMS Image Component
export function CmsImage({
  src,
  alt,
  storeConfig,
  showSkeleton = true,
  fallbackSrc,
  onError,
  ...props
}: Omit<MagentoImageProps, 'type' | 'variant'>) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const imageUrl = buildCmsImageUrl(src, storeConfig);

  const handleLoad = () => {
    setLoading(false);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setError(true);
    setLoading(false);
    
    if (onError) {
      onError(event);
    } else {
      handleImageError(event, fallbackSrc);
    }
  };

  if (error && !fallbackSrc) {
    return (
      <Box
        sx={{
          width: props.width || 300,
          height: props.height || 200,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '14px',
        }}
      >
        Image not available
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width={props.width || 300}
          height={props.height || 200}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <Image
        {...props}
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleImageError}
        style={{
          ...props.style,
          opacity: loading ? 0 : 1,
          transition: 'opacity 0.3s ease',
        }}
      />
    </Box>
  );
}

// Generic Magento Image Component
export default function MagentoImage({
  src,
  alt,
  type = 'product',
  variant,
  size,
  storeConfig,
  showSkeleton = true,
  fallbackSrc,
  onError,
  ...props
}: MagentoImageProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  let imageUrl: string;
  
  switch (type) {
    case 'product':
      imageUrl = buildProductImageUrl(src, storeConfig, variant as any);
      break;
    case 'category':
      imageUrl = buildCategoryImageUrl(src, storeConfig);
      break;
    case 'logo':
      imageUrl = buildLogoUrl(src, storeConfig);
      break;
    case 'cms':
      imageUrl = buildCmsImageUrl(src, storeConfig);
      break;
    default:
      imageUrl = buildMediaUrl(src, storeConfig, type);
  }

  const handleLoad = () => {
    setLoading(false);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setError(true);
    setLoading(false);
    
    if (onError) {
      onError(event);
    } else {
      handleImageError(event, fallbackSrc);
    }
  };

  if (error && !fallbackSrc) {
    return (
      <Box
        sx={{
          width: props.width || 300,
          height: props.height || 300,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '14px',
        }}
      >
        Image not available
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative' }}>
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width={props.width || 300}
          height={props.height || 300}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <Image
        {...props}
        src={imageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleImageError}
        style={{
          ...props.style,
          opacity: loading ? 0 : 1,
          transition: 'opacity 0.3s ease',
        }}
      />
    </Box>
  );
}
