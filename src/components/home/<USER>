'use client';

import React from 'react';
import { Box, Container, Typography, Alert } from '@mui/material';
import { motion } from 'framer-motion';
import { PageBuilderRenderer } from '@/components/pagebuilder';
import { CmsPageRenderer } from '@/components/cms';
import { PageBuilderContentSkeleton } from '@/components/ui/LoadingSkeleton';

// Dynamic Homepage Props
interface DynamicHomepageProps {
  fallbackContent?: React.ReactNode;
  showFallbackOnError?: boolean;
  className?: string;
}

// Homepage Data Interface
interface HomepageData {
  cmsPage: any;
  storeConfig: any;
  homepageConfig: any;
}

// API function to fetch homepage data (client-side)
async function fetchHomepageData(): Promise<HomepageData | null> {
  try {
    // In a real implementation, these would be API calls to your Next.js API routes
    // For now, we'll simulate the API calls
    const response = await fetch('/api/homepage');
    if (!response.ok) {
      throw new Error('Failed to fetch homepage data');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return null;
  }
}

// Client Component for Dynamic Homepage with Loading State
export function DynamicHomepage({
  fallbackContent,
  showFallbackOnError = true,
  className
}: DynamicHomepageProps) {
  const [homepageData, setHomepageData] = React.useState<HomepageData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadHomepageData = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await fetchHomepageData();
        setHomepageData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load homepage');
        console.error('Error fetching homepage data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadHomepageData();
  }, []);

  // Show loading skeleton
  if (loading) {
    return (
      <Box className={className}>
        <PageBuilderContentSkeleton animationType="shimmer" />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Error Loading Homepage
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
        {showFallbackOnError && fallbackContent && (
          <Box sx={{ mt: 4 }}>
            {fallbackContent}
          </Box>
        )}
      </Container>
    );
  }

  // Show fallback content if no data
  if (!homepageData && showFallbackOnError && fallbackContent) {
    return (
      <Box className={className}>
        {fallbackContent}
      </Box>
    );
  }

  // Show warning if no data and no fallback
  if (!homepageData) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Homepage Content Not Available
          </Typography>
          <Typography variant="body2">
            The homepage content could not be loaded from Magento. Please check your store configuration.
          </Typography>
        </Alert>
      </Container>
    );
  }

  const { cmsPage } = homepageData;

  // Render with Page Builder if available
  if (cmsPage?.hasPageBuilder && cmsPage?.pageBuilderContent) {
    return (
      <motion.div
        className={className}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <PageBuilderRenderer
          content={cmsPage.pageBuilderContent}
          config={{
            enableLazyLoading: true,
            imageOptimization: true,
          }}
        />
      </motion.div>
    );
  }

  // Render as regular CMS page
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <CmsPageRenderer
        page={cmsPage}
        showBreadcrumbs={false}
        showTitle={false}
        showContentHeading={!!cmsPage?.content_heading}
      />
    </motion.div>
  );
}

// Alias for backward compatibility
export const DynamicHomepageClient = DynamicHomepage;

// Hook for getting homepage metadata
export function useHomepageMetadata() {
  const [metadata, setMetadata] = React.useState<{
    title: string;
    description: string;
    keywords?: string;
  } | null>(null);

  React.useEffect(() => {
    const fetchMetadata = async () => {
      try {
        // In a real implementation, this would fetch from your API
        const response = await fetch('/api/homepage/metadata');
        if (response.ok) {
          const data = await response.json();
          setMetadata(data);
        }
      } catch (error) {
        console.error('Error fetching homepage metadata:', error);
      }
    };

    fetchMetadata();
  }, []);

  return metadata;
}

export default DynamicHomepage;

  // Otherwise, render as regular CMS page
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <CmsPageRenderer
        page={cmsPage}
        showBreadcrumbs={false} // Don't show breadcrumbs on homepage
        showTitle={false} // Don't show title on homepage (usually handled by hero)
        showContentHeading={!!cmsPage.content_heading}
      />
    </motion.div>
  );
}

// Client Component for Dynamic Homepage with Loading State
export function DynamicHomepageClient({
  fallbackContent,
  showFallbackOnError = true,
  className,
}: DynamicHomepageProps) {
  const [homepageData, setHomepageData] = React.useState<HomepageData | null>(
    null
  );
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchHomepageData = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await getHomepageData();
        setHomepageData(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to load homepage'
        );
        console.error('Error fetching homepage data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchHomepageData();
  }, []);

  // Show loading skeleton
  if (loading) {
    return (
      <Box className={className}>
        <PageBuilderContentSkeleton animationType="shimmer" />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Error Loading Homepage
          </Typography>
          <Typography variant="body2">{error}</Typography>
        </Alert>
        {showFallbackOnError && fallbackContent && (
          <Box sx={{ mt: 4 }}>{fallbackContent}</Box>
        )}
      </Container>
    );
  }

  // Show fallback content if no data
  if (!homepageData && showFallbackOnError && fallbackContent) {
    return <Box className={className}>{fallbackContent}</Box>;
  }

  // Show warning if no data and no fallback
  if (!homepageData) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Homepage Content Not Available
          </Typography>
          <Typography variant="body2">
            The homepage content could not be loaded from Magento. Please check
            your store configuration.
          </Typography>
        </Alert>
      </Container>
    );
  }

  const { cmsPage } = homepageData;

  // Render with Page Builder if available
  if (cmsPage.hasPageBuilder && cmsPage.pageBuilderContent) {
    return (
      <motion.div
        className={className}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <PageBuilderRenderer
          content={cmsPage.pageBuilderContent}
          config={{
            enableLazyLoading: true,
            imageOptimization: true,
          }}
        />
      </motion.div>
    );
  }

  // Render as regular CMS page
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <CmsPageRenderer
        page={cmsPage}
        showBreadcrumbs={false}
        showTitle={false}
        showContentHeading={!!cmsPage.content_heading}
      />
    </motion.div>
  );
}

// Hook for getting homepage metadata
export function useHomepageMetadata() {
  const [metadata, setMetadata] = React.useState<{
    title: string;
    description: string;
    keywords?: string;
  } | null>(null);

  React.useEffect(() => {
    const fetchMetadata = async () => {
      try {
        const [homepageConfig, storeConfig] = await Promise.all([
          getHomepageConfig(),
          getBasicStoreConfig(),
        ]);

        if (homepageConfig && storeConfig) {
          const title = formatStoreTitle(
            homepageConfig.default_title || homepageConfig.store_name,
            storeConfig
          );
          const description = getDefaultMetaDescription(storeConfig);

          setMetadata({
            title,
            description,
            keywords: storeConfig.default_keywords,
          });
        }
      } catch (error) {
        console.error('Error fetching homepage metadata:', error);
      }
    };

    fetchMetadata();
  }, []);

  return metadata;
}

export default DynamicHomepage;
