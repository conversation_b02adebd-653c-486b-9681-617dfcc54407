import React from 'react';
import { Box, Container, Typography, Alert } from '@mui/material';
import { PageBuilderRenderer } from '@/components/pagebuilder';
import { CmsPageRenderer } from '@/components/cms';
import {
  getHomepageIdentifier,
  getHomepageConfig,
  getBasicStoreConfig,
  formatStoreTitle,
  getDefaultMetaDescription,
} from '@/lib/magento/api/storeConfig';
import { getCmsPageWithPageBuilder } from '@/lib/magento/api/cmsNew';

// Dynamic Homepage Props
interface DynamicHomepageServerProps {
  fallbackContent?: React.ReactNode;
  showFallbackOnError?: boolean;
  className?: string;
}

// Homepage Data Interface
interface HomepageData {
  cmsPage: any;
  storeConfig: any;
  homepageConfig: any;
}

// Fetch homepage data
async function getHomepageData(): Promise<HomepageData | null> {
  try {
    // Get homepage identifier from store configuration
    const homepageIdentifier = await getHomepageIdentifier();

    if (!homepageIdentifier) {
      console.warn('No homepage identifier found in store configuration');
      return null;
    }

    // Fetch homepage CMS page, store config, and homepage config in parallel
    const [cmsPage, storeConfig, homepageConfig] = await Promise.all([
      getCmsPageWithPageBuilder(homepageIdentifier),
      getBasicStoreConfig(),
      getHomepageConfig(),
    ]);

    if (!cmsPage) {
      console.warn(`Homepage CMS page not found: ${homepageIdentifier}`);
      return null;
    }

    return {
      cmsPage,
      storeConfig,
      homepageConfig,
    };
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return null;
  }
}

// Server Component for Dynamic Homepage
export async function DynamicHomepageServer({
  fallbackContent,
  showFallbackOnError = true,
  className,
}: DynamicHomepageServerProps) {
  const homepageData = await getHomepageData();

  // Show fallback content if no data and fallback is enabled
  if (!homepageData && showFallbackOnError && fallbackContent) {
    return <Box className={className}>{fallbackContent}</Box>;
  }

  // Show error message if no data and no fallback
  if (!homepageData) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Homepage Content Not Available
          </Typography>
          <Typography variant="body2">
            The homepage content could not be loaded from Magento. Please check
            your store configuration.
          </Typography>
        </Alert>
      </Container>
    );
  }

  const { cmsPage, storeConfig, homepageConfig } = homepageData;

  // If the CMS page has Page Builder content, render with Page Builder
  if (cmsPage.hasPageBuilder && cmsPage.pageBuilderContent) {
    return (
      <Box className={className}>
        <PageBuilderRenderer
          content={cmsPage.pageBuilderContent}
          config={{
            enableLazyLoading: true,
            imageOptimization: true,
          }}
        />
      </Box>
    );
  }

  // Otherwise, render as regular CMS page
  return (
    <Box className={className}>
      <CmsPageRenderer
        page={cmsPage}
        showBreadcrumbs={false} // Don't show breadcrumbs on homepage
        showTitle={false} // Don't show title on homepage (usually handled by hero)
        showContentHeading={!!cmsPage.content_heading}
      />
    </Box>
  );
}

export default DynamicHomepageServer;
