// Home Components Export

export {
  DynamicHomepage,
  DynamicHomepageClient,
  useHomepageMetadata,
} from './DynamicHomepage';

export { DynamicHomepageServer } from './DynamicHomepageServer';

// Re-export store config functions for convenience
export {
  getHomepageIdentifier,
  getHomepageConfig,
  getBasicStoreConfig,
  formatStoreTitle,
  getDefaultMetaDescription,
  getDefaultMetaKeywords,
  getMediaUrl,
} from '@/lib/magento/api/storeConfig';

// Re-export metadata functions
export {
  generateHomepageMetadata,
  generateHomepageStructuredData,
  getHomepageCacheTags,
  getHomepageRevalidation,
} from '@/lib/metadata/homepage';

// Re-export types
export type {
  StoreConfig,
  BasicStoreConfig,
  HomepageConfig,
} from '@/lib/magento/api/storeConfig';
