#import "../fragments/cms.gql"

query GetCmsPage($identifier: String!) {
  cmsPage(identifier: $identifier) {
    ...CmsPageFragment
  }
}

query GetCmsPageByUrlKey($urlKey: String!) {
  cmsPage(url_key: $urlKey) {
    ...CmsPageFragment
  }
}

query GetCmsPages($identifiers: [String!]) {
  cmsPages(identifiers: $identifiers) {
    items {
      ...CmsPageFragment
    }
  }
}

query GetCmsBlock($identifier: String!) {
  cmsBlocks(identifiers: [$identifier]) {
    items {
      ...CmsBlockFragment
    }
  }
}

query GetCmsBlocks($identifiers: [String!]!) {
  cmsBlocks(identifiers: $identifiers) {
    items {
      ...CmsBlockFragment
    }
  }
}

query GetAllCmsPages {
  cmsPages {
    items {
      identifier
      url_key
      title
      meta_title
      meta_description
    }
  }
}

query SearchCmsPages($search: String!) {
  cmsPages(search: $search) {
    items {
      ...CmsPageFragment
    }
  }
}
