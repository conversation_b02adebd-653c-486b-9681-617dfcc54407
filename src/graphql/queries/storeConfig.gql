#import "../fragments/storeConfig.gql"

query GetStoreConfig {
  storeConfig {
    ...StoreConfigFragment
  }
}

query GetStoreConfigBasic {
  storeConfig {
    id
    code
    store_name
    locale
    base_currency_code
    default_display_currency_code
    timezone
    base_url
    base_media_url
    cms_home_page
    cms_no_route
    default_title
    default_description
    default_keywords
    header_logo_src
    logo_alt
    welcome
    copyright
  }
}

query GetHomepageIdentifier {
  storeConfig {
    cms_home_page
    store_name
    default_title
  }
}

query GetStoreSeoConfig {
  storeConfig {
    default_title
    title_prefix
    title_suffix
    default_description
    default_keywords
    head_shortcut_icon
    head_includes
  }
}

query GetStoreBrandingConfig {
  storeConfig {
    store_name
    header_logo_src
    logo_width
    logo_height
    logo_alt
    welcome
    copyright
    absolute_footer
  }
}

query GetStoreCatalogConfig {
  storeConfig {
    root_category_id
    root_category_uid
    category_url_suffix
    product_url_suffix
    catalog_default_sort_by
    grid_per_page
    list_per_page
    grid_per_page_values
    list_per_page_values
    configurable_thumbnail_source
  }
}
