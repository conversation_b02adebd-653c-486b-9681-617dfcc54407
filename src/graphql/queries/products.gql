#import "../fragments/product.gql"

query GetProducts(
  $search: String
  $filter: ProductAttributeFilterInput
  $sort: ProductAttributeSortInput
  $pageSize: Int = 20
  $currentPage: Int = 1
) {
  products(
    search: $search
    filter: $filter
    sort: $sort
    pageSize: $pageSize
    currentPage: $currentPage
  ) {
    items {
      ...ProductFragment
      ... on SimpleProduct {
        ...SimpleProductFragment
      }
      ... on ConfigurableProduct {
        ...ConfigurableProductFragment
      }
      ... on GroupedProduct {
        ...GroupedProductFragment
      }
      ... on BundleProduct {
        ...BundleProductFragment
      }
    }
    page_info {
      page_size
      current_page
      total_pages
    }
    total_count
    aggregations {
      label
      count
      attribute_code
      options {
        label
        value
        count
      }
    }
    sort_fields {
      default
      options {
        label
        value
      }
    }
  }
}

query GetProductByUrlKey($urlKey: String!) {
  products(filter: { url_key: { eq: $urlKey } }) {
    items {
      ...ProductFragment
      ... on SimpleProduct {
        ...SimpleProductFragment
      }
      ... on ConfigurableProduct {
        ...ConfigurableProductFragment
      }
      ... on GroupedProduct {
        ...GroupedProductFragment
      }
      ... on BundleProduct {
        ...BundleProductFragment
      }
    }
  }
}

query GetProductBySku($sku: String!) {
  products(filter: { sku: { eq: $sku } }) {
    items {
      ...ProductFragment
      ... on SimpleProduct {
        ...SimpleProductFragment
      }
      ... on ConfigurableProduct {
        ...ConfigurableProductFragment
      }
      ... on GroupedProduct {
        ...GroupedProductFragment
      }
      ... on BundleProduct {
        ...BundleProductFragment
      }
    }
  }
}

query GetProductsByCategory(
  $categoryId: String!
  $pageSize: Int = 20
  $currentPage: Int = 1
  $sort: ProductAttributeSortInput
) {
  products(
    filter: { category_id: { eq: $categoryId } }
    pageSize: $pageSize
    currentPage: $currentPage
    sort: $sort
  ) {
    items {
      ...ProductFragment
    }
    page_info {
      page_size
      current_page
      total_pages
    }
    total_count
    aggregations {
      label
      count
      attribute_code
      options {
        label
        value
        count
      }
    }
    sort_fields {
      default
      options {
        label
        value
      }
    }
  }
}

query SearchProducts(
  $search: String!
  $pageSize: Int = 20
  $currentPage: Int = 1
  $sort: ProductAttributeSortInput
  $filter: ProductAttributeFilterInput
) {
  products(
    search: $search
    pageSize: $pageSize
    currentPage: $currentPage
    sort: $sort
    filter: $filter
  ) {
    items {
      ...ProductFragment
    }
    page_info {
      page_size
      current_page
      total_pages
    }
    total_count
    aggregations {
      label
      count
      attribute_code
      options {
        label
        value
        count
      }
    }
    sort_fields {
      default
      options {
        label
        value
      }
    }
  }
}

query GetFeaturedProducts($pageSize: Int = 12) {
  products(
    filter: { featured: { eq: "1" } }
    pageSize: $pageSize
    currentPage: 1
  ) {
    items {
      uid
      id
      name
      sku
      url_key
      image {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
          discount {
            amount_off
            percent_off
          }
        }
      }
      rating_summary
      review_count
      stock_status
    }
    total_count
  }
}

query GetNewProducts($pageSize: Int = 12) {
  products(
    filter: { news_from_date: { gteq: "2024-01-01" } }
    pageSize: $pageSize
    currentPage: 1
    sort: { created_at: DESC }
  ) {
    items {
      uid
      id
      name
      sku
      url_key
      image {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
          discount {
            amount_off
            percent_off
          }
        }
      }
      rating_summary
      review_count
      stock_status
      new_from_date
      new_to_date
    }
    total_count
  }
}

query GetBestSellingProducts($pageSize: Int = 12) {
  products(
    pageSize: $pageSize
    currentPage: 1
    sort: { position: ASC }
  ) {
    items {
      uid
      id
      name
      sku
      url_key
      image {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
          discount {
            amount_off
            percent_off
          }
        }
      }
      rating_summary
      review_count
      stock_status
    }
    total_count
  }
}
