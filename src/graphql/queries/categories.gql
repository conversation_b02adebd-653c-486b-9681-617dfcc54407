#import "../fragments/category.gql"

query GetCategoryTree($id: String) {
  categoryList(filters: { ids: { eq: $id } }) {
    ...CategoryTreeFragment
  }
}

query GetRootCategories {
  categoryList(filters: { parent_id: { eq: "2" } }) {
    ...CategoryTreeFragment
  }
}

query GetCategoryById($id: String!) {
  categoryList(filters: { ids: { eq: $id } }) {
    ...CategoryFragment
  }
}

query GetCategoryByUrlKey($urlKey: String!) {
  categoryList(filters: { url_key: { eq: $urlKey } }) {
    ...CategoryFragment
  }
}

query GetCategoriesForMenu {
  categoryList(filters: { include_in_menu: { eq: true } }) {
    uid
    id
    name
    url_key
    url_path
    position
    level
    include_in_menu
    children_count
    children {
      uid
      id
      name
      url_key
      url_path
      position
      level
      include_in_menu
      children_count
      children {
        uid
        id
        name
        url_key
        url_path
        position
        level
        include_in_menu
        children_count
      }
    }
  }
}
