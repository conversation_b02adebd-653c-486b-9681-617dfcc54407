#import "../fragments/cart.gql"

mutation CreateEmptyCart {
  createEmptyCart
}

mutation AddSimpleProductsToCart($cartId: String!, $cartItems: [SimpleProductCartItemInput!]!) {
  addSimpleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
    cart {
      ...CartFragment
    }
  }
}

mutation AddConfigurableProductsToCart($cartId: String!, $cartItems: [ConfigurableProductCartItemInput!]!) {
  addConfigurableProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
    cart {
      ...CartFragment
    }
  }
}

mutation AddBundleProductsToCart($cartId: String!, $cartItems: [BundleProductCartItemInput!]!) {
  addBundleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {
    cart {
      ...CartFragment
    }
  }
}

mutation UpdateCartItems($cartId: String!, $cartItems: [CartItemUpdateInput!]!) {
  updateCartItems(input: { cart_id: $cartId, cart_items: $cartItems }) {
    cart {
      ...CartFragment
    }
  }
}

mutation RemoveItemFromCart($cartId: String!, $cartItemUid: String!) {
  removeItemFromCart(input: { cart_id: $cartId, cart_item_uid: $cartItemUid }) {
    cart {
      ...CartFragment
    }
  }
}

mutation ApplyCouponToCart($cartId: String!, $couponCode: String!) {
  applyCouponToCart(input: { cart_id: $cartId, coupon_code: $couponCode }) {
    cart {
      ...CartFragment
    }
  }
}

mutation RemoveCouponFromCart($cartId: String!) {
  removeCouponFromCart(input: { cart_id: $cartId }) {
    cart {
      ...CartFragment
    }
  }
}

mutation SetShippingAddressesOnCart($cartId: String!, $shippingAddresses: [ShippingAddressInput!]!) {
  setShippingAddressesOnCart(input: { cart_id: $cartId, shipping_addresses: $shippingAddresses }) {
    cart {
      ...CartFragment
    }
  }
}

mutation SetBillingAddressOnCart($cartId: String!, $billingAddress: BillingAddressInput!) {
  setBillingAddressOnCart(input: { cart_id: $cartId, billing_address: $billingAddress }) {
    cart {
      ...CartFragment
    }
  }
}

mutation SetShippingMethodsOnCart($cartId: String!, $shippingMethods: [ShippingMethodInput!]!) {
  setShippingMethodsOnCart(input: { cart_id: $cartId, shipping_methods: $shippingMethods }) {
    cart {
      ...CartFragment
    }
  }
}

mutation SetPaymentMethodOnCart($cartId: String!, $paymentMethod: PaymentMethodInput!) {
  setPaymentMethodOnCart(input: { cart_id: $cartId, payment_method: $paymentMethod }) {
    cart {
      ...CartFragment
    }
  }
}

mutation PlaceOrder($cartId: String!) {
  placeOrder(input: { cart_id: $cartId }) {
    order {
      order_number
      order_id
    }
  }
}
