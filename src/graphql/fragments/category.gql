fragment CategoryFragment on CategoryTree {
  uid
  id
  name
  url_key
  url_path
  url_suffix
  description
  meta_title
  meta_keywords
  meta_description
  image
  path
  path_in_store
  position
  level
  children_count
  include_in_menu
  is_anchor
  default_sort_by
  available_sort_by
  landing_page
  custom_layout_update_file
  breadcrumbs {
    category_id
    category_name
    category_level
    category_url_key
    category_url_path
  }
}

fragment CategoryTreeFragment on CategoryTree {
  uid
  id
  name
  url_key
  url_path
  position
  level
  include_in_menu
  children_count
  children {
    uid
    id
    name
    url_key
    url_path
    position
    level
    include_in_menu
    children_count
    children {
      uid
      id
      name
      url_key
      url_path
      position
      level
      include_in_menu
      children_count
    }
  }
}
