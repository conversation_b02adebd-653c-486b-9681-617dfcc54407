fragment ProductFragment on ProductInterface {
  __typename
  uid
  id
  name
  sku
  url_key
  url_suffix
  canonical_url
  meta_title
  meta_keyword
  meta_description
  description {
    html
  }
  short_description {
    html
  }
  image {
    url
    label
  }
  small_image {
    url
    label
  }
  thumbnail {
    url
    label
  }
  media_gallery {
    url
    label
    position
    disabled
  }
  price_range {
    minimum_price {
      regular_price {
        value
        currency
      }
      final_price {
        value
        currency
      }
      discount {
        amount_off
        percent_off
      }
    }
    maximum_price {
      regular_price {
        value
        currency
      }
      final_price {
        value
        currency
      }
      discount {
        amount_off
        percent_off
      }
    }
  }
  special_price
  special_from_date
  special_to_date
  new_from_date
  new_to_date
  tier_prices {
    customer_group_id
    qty
    value
    percentage_value
    website_id
  }
  stock_status
  only_x_left_in_stock
  rating_summary
  review_count
  reviews {
    items {
      average_rating
      ratings_breakdown {
        name
        value
      }
      summary
      text
      created_at
      nickname
    }
  }
  categories {
    uid
    id
    name
    url_key
    url_path
    breadcrumbs {
      category_id
      category_name
      category_level
      category_url_key
    }
  }
  related_products {
    uid
    id
    name
    sku
    url_key
    image {
      url
      label
    }
    price_range {
      minimum_price {
        regular_price {
          value
          currency
        }
        final_price {
          value
          currency
        }
      }
    }
  }
  upsell_products {
    uid
    id
    name
    sku
    url_key
    image {
      url
      label
    }
    price_range {
      minimum_price {
        regular_price {
          value
          currency
        }
        final_price {
          value
          currency
        }
      }
    }
  }
  crosssell_products {
    uid
    id
    name
    sku
    url_key
    image {
      url
      label
    }
    price_range {
      minimum_price {
        regular_price {
          value
          currency
        }
        final_price {
          value
          currency
        }
      }
    }
  }
}

fragment SimpleProductFragment on SimpleProduct {
  ...ProductFragment
}

fragment ConfigurableProductFragment on ConfigurableProduct {
  ...ProductFragment
  configurable_options {
    id
    attribute_id
    label
    position
    use_default
    attribute_code
    values {
      value_index
      label
      store_label
      default_label
      use_default_value
    }
    product_id
  }
  variants {
    product {
      uid
      id
      name
      sku
      image {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
        }
      }
      stock_status
    }
    attributes {
      label
      code
      value_index
    }
  }
}

fragment GroupedProductFragment on GroupedProduct {
  ...ProductFragment
  items {
    qty
    position
    product {
      uid
      id
      name
      sku
      url_key
      image {
        url
        label
      }
      price_range {
        minimum_price {
          regular_price {
            value
            currency
          }
          final_price {
            value
            currency
          }
        }
      }
      stock_status
    }
  }
}

fragment BundleProductFragment on BundleProduct {
  ...ProductFragment
  dynamic_price
  dynamic_sku
  dynamic_weight
  price_view
  ship_bundle_items
  items {
    option_id
    title
    required
    type
    position
    sku
    options {
      id
      qty
      position
      is_default
      price
      price_type
      can_change_quantity
      product {
        uid
        id
        name
        sku
        url_key
        image {
          url
          label
        }
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
          }
        }
      }
    }
  }
}
