fragment CartFragment on Cart {
  id
  email
  is_virtual
  applied_coupons {
    code
  }
  itemsV2 {
    items {
      uid
      product {
        uid
        name
        sku
        url_key
        thumbnail {
          url
          label
        }
        price_range {
          minimum_price {
            regular_price {
              value
              currency
            }
            final_price {
              value
              currency
            }
          }
        }
        stock_status
      }
      prices {
        price {
          value
          currency
        }
        row_total {
          value
          currency
        }
        row_total_including_tax {
          value
          currency
        }
        total_item_discount {
          value
          currency
        }
      }
      quantity
      ... on SimpleCartItem {
        customizable_options {
          label
          values {
            label
            value
          }
        }
      }
      ... on ConfigurableCartItem {
        configurable_options {
          id
          option_label
          value_id
          value_label
        }
      }
      ... on BundleCartItem {
        bundle_options {
          id
          label
          type
          values {
            id
            label
            price
            quantity
          }
        }
      }
    }
    page_info {
      page_size
      current_page
      total_pages
    }
    total_count
  }
  prices {
    grand_total {
      value
      currency
    }
    subtotal_excluding_tax {
      value
      currency
    }
    subtotal_including_tax {
      value
      currency
    }
    applied_taxes {
      label
      amount {
        value
        currency
      }
    }
    discounts {
      amount {
        value
        currency
      }
      label
    }
  }
  total_quantity
  shipping_addresses {
    firstname
    lastname
    company
    street
    city
    region {
      code
      label
      region_id
    }
    postcode
    country {
      code
      label
    }
    telephone
    available_shipping_methods {
      carrier_code
      carrier_title
      method_code
      method_title
      amount {
        value
        currency
      }
    }
    selected_shipping_method {
      carrier_code
      carrier_title
      method_code
      method_title
      amount {
        value
        currency
      }
    }
  }
  billing_address {
    firstname
    lastname
    company
    street
    city
    region {
      code
      label
      region_id
    }
    postcode
    country {
      code
      label
    }
    telephone
  }
  available_payment_methods {
    code
    title
  }
  selected_payment_method {
    code
    title
  }
}
