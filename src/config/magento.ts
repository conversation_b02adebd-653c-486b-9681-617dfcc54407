// Magento 2 Configuration

// Magento GraphQL endpoint configuration
export const MAGENTO_CONFIG = {
  // GraphQL endpoint
  graphqlEndpoint: process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'https://your-magento-store.com/graphql',
  
  // Base URLs
  baseUrl: process.env.NEXT_PUBLIC_MAGENTO_BASE_URL || 'https://your-magento-store.com',
  baseMediaUrl: process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL || 'https://your-magento-store.com/media',
  baseStaticUrl: process.env.NEXT_PUBLIC_MAGENTO_BASE_STATIC_URL || 'https://your-magento-store.com/static',
  
  // Store configuration
  storeCode: process.env.NEXT_PUBLIC_MAGENTO_STORE_CODE || 'default',
  websiteCode: process.env.NEXT_PUBLIC_MAGENTO_WEBSITE_CODE || 'base',
  
  // Cache configuration
  defaultCacheTime: parseInt(process.env.NEXT_PUBLIC_DEFAULT_CACHE_TIME || '3600'), // 1 hour
  
  // API configuration
  timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'), // 30 seconds
  retries: parseInt(process.env.NEXT_PUBLIC_API_RETRIES || '3'),
  
  // Feature flags
  features: {
    pageBuilder: process.env.NEXT_PUBLIC_ENABLE_PAGE_BUILDER !== 'false',
    dynamicHomepage: process.env.NEXT_PUBLIC_ENABLE_DYNAMIC_HOMEPAGE !== 'false',
    cmsPages: process.env.NEXT_PUBLIC_ENABLE_CMS_PAGES !== 'false',
    productSearch: process.env.NEXT_PUBLIC_ENABLE_PRODUCT_SEARCH !== 'false',
    customerAccount: process.env.NEXT_PUBLIC_ENABLE_CUSTOMER_ACCOUNT !== 'false',
    shoppingCart: process.env.NEXT_PUBLIC_ENABLE_SHOPPING_CART !== 'false',
    checkout: process.env.NEXT_PUBLIC_ENABLE_CHECKOUT !== 'false',
    wishlist: process.env.NEXT_PUBLIC_ENABLE_WISHLIST !== 'false',
    reviews: process.env.NEXT_PUBLIC_ENABLE_REVIEWS !== 'false',
  },
  
  // Development configuration
  development: {
    enableMockData: process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_ENABLE_MOCK_DATA === 'true',
    showGraphQLErrors: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_ENABLE_DEBUG_LOGS === 'true',
  },
} as const;

// Default homepage identifier (fallback)
export const DEFAULT_HOMEPAGE_IDENTIFIER = 'home';

// Default store configuration (fallback)
export const DEFAULT_STORE_CONFIG = {
  store_name: 'Magento E-commerce Store',
  default_title: 'Magento E-commerce Store',
  default_description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
  default_keywords: 'ecommerce, online shopping, products, magento, nextjs',
  locale: 'en_US',
  base_currency_code: 'USD',
  default_display_currency_code: 'USD',
  timezone: 'America/New_York',
  cms_home_page: DEFAULT_HOMEPAGE_IDENTIFIER,
} as const;

// Cache keys for different data types
export const CACHE_KEYS = {
  storeConfig: 'store-config',
  homepageConfig: 'homepage-config',
  cmsPage: (identifier: string) => `cms-page-${identifier}`,
  cmsBlock: (identifier: string) => `cms-block-${identifier}`,
  product: (id: string) => `product-${id}`,
  category: (id: string) => `category-${id}`,
  productSearch: (query: string) => `product-search-${query}`,
} as const;

// Cache tags for revalidation
export const CACHE_TAGS = {
  storeConfig: ['store-config'],
  homepage: ['homepage', 'store-config', 'cms-page'],
  cmsPage: ['cms-page'],
  cmsBlock: ['cms-block'],
  product: ['product'],
  category: ['category'],
  pageBuilder: ['page-builder'],
} as const;

// GraphQL headers
export const getGraphQLHeaders = () => ({
  'Content-Type': 'application/json',
  'Store': MAGENTO_CONFIG.storeCode,
  'X-Requested-With': 'XMLHttpRequest',
  ...(process.env.MAGENTO_ACCESS_TOKEN && {
    'Authorization': `Bearer ${process.env.MAGENTO_ACCESS_TOKEN}`,
  }),
});

// Utility function to check if Magento is configured
export function isMagentoConfigured(): boolean {
  return (
    MAGENTO_CONFIG.graphqlEndpoint !== 'https://your-magento-store.com/graphql' &&
    MAGENTO_CONFIG.baseUrl !== 'https://your-magento-store.com'
  );
}

// Utility function to get environment-specific configuration
export function getEnvironmentConfig() {
  const env = process.env.NODE_ENV;
  
  return {
    isDevelopment: env === 'development',
    isProduction: env === 'production',
    isTest: env === 'test',
    enableMockData: MAGENTO_CONFIG.development.enableMockData,
    showErrors: MAGENTO_CONFIG.development.showGraphQLErrors,
    enableDebugLogs: MAGENTO_CONFIG.development.enableDebugLogs,
  };
}

// Utility function to build media URL
export function buildMediaUrl(path: string): string {
  if (!path) return '';
  
  // If path is already a full URL, return as is
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // Remove leading slash and build URL
  const cleanPath = path.replace(/^\//, '');
  return `${MAGENTO_CONFIG.baseMediaUrl.replace(/\/$/, '')}/${cleanPath}`;
}

// Utility function to build store URL
export function buildStoreUrl(path: string = ''): string {
  const cleanPath = path.replace(/^\//, '');
  return `${MAGENTO_CONFIG.baseUrl.replace(/\/$/, '')}/${cleanPath}`;
}

// Error messages
export const ERROR_MESSAGES = {
  MAGENTO_NOT_CONFIGURED: 'Magento 2 is not properly configured. Please check your environment variables.',
  GRAPHQL_ENDPOINT_ERROR: 'Failed to connect to Magento GraphQL endpoint.',
  STORE_CONFIG_ERROR: 'Failed to fetch store configuration from Magento.',
  HOMEPAGE_NOT_FOUND: 'Homepage content not found in Magento CMS.',
  CMS_PAGE_NOT_FOUND: 'CMS page not found.',
  CMS_BLOCK_NOT_FOUND: 'CMS block not found.',
  PRODUCT_NOT_FOUND: 'Product not found.',
  CATEGORY_NOT_FOUND: 'Category not found.',
  NETWORK_ERROR: 'Network error occurred while connecting to Magento.',
  TIMEOUT_ERROR: 'Request timeout while connecting to Magento.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  MAGENTO_CONNECTED: 'Successfully connected to Magento 2.',
  STORE_CONFIG_LOADED: 'Store configuration loaded successfully.',
  HOMEPAGE_LOADED: 'Homepage content loaded successfully.',
  CMS_PAGE_LOADED: 'CMS page loaded successfully.',
  CMS_BLOCK_LOADED: 'CMS block loaded successfully.',
} as const;

// Development helpers
export const DEV_HELPERS = {
  // Log configuration in development
  logConfig: () => {
    if (getEnvironmentConfig().isDevelopment) {
      console.group('🔧 Magento Configuration');
      console.log('GraphQL Endpoint:', MAGENTO_CONFIG.graphqlEndpoint);
      console.log('Base URL:', MAGENTO_CONFIG.baseUrl);
      console.log('Store Code:', MAGENTO_CONFIG.storeCode);
      console.log('Features:', MAGENTO_CONFIG.features);
      console.log('Is Configured:', isMagentoConfigured());
      console.groupEnd();
    }
  },
  
  // Test GraphQL connection
  testConnection: async () => {
    if (!getEnvironmentConfig().isDevelopment) return;
    
    try {
      const response = await fetch(MAGENTO_CONFIG.graphqlEndpoint, {
        method: 'POST',
        headers: getGraphQLHeaders(),
        body: JSON.stringify({
          query: '{ __schema { queryType { name } } }',
        }),
      });
      
      if (response.ok) {
        console.log('✅ Magento GraphQL connection successful');
      } else {
        console.error('❌ Magento GraphQL connection failed:', response.status);
      }
    } catch (error) {
      console.error('❌ Magento GraphQL connection error:', error);
    }
  },
};

export default MAGENTO_CONFIG;
