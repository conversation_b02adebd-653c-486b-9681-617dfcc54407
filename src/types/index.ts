// Main TypeScript type definitions for Magento 2 GraphQL

// Common types
export interface Money {
  value: number;
  currency: string;
}

export interface Image {
  url: string;
  label: string;
}

export interface MediaGalleryEntry {
  url: string;
  label: string;
  position: number;
  disabled: boolean;
}

export interface Breadcrumb {
  category_id: number;
  category_name: string;
  category_level: number;
  category_url_key: string;
}

export interface PageInfo {
  page_size: number;
  current_page: number;
  total_pages: number;
}

export interface Aggregation {
  attribute_code: string;
  count: number;
  label: string;
  options: AggregationOption[];
}

export interface AggregationOption {
  count: number;
  label: string;
  value: string;
}

// Re-export all specific types
export * from './product';
export * from './category';
export * from './cart';
export * from './customer';
export * from './checkout';

// API Response types
export interface GraphQLResponse<T> {
  data: T;
  errors?: GraphQLError[];
}

export interface GraphQLError {
  message: string;
  locations?: Array<{ line: number; column: number }>;
  path?: Array<string | number>;
  extensions?: Record<string, any>;
}

// Filter and Sort types
export interface ProductAttributeFilterInput {
  category_id?: FilterEqualTypeInput;
  category_uid?: FilterEqualTypeInput;
  name?: FilterMatchTypeInput;
  sku?: FilterEqualTypeInput;
  url_key?: FilterEqualTypeInput;
  price?: FilterRangeTypeInput;
  special_price?: FilterRangeTypeInput;
  created_at?: FilterRangeTypeInput;
  updated_at?: FilterRangeTypeInput;
  news_from_date?: FilterRangeTypeInput;
  news_to_date?: FilterRangeTypeInput;
  special_from_date?: FilterRangeTypeInput;
  special_to_date?: FilterRangeTypeInput;
  custom_layout_update_file?: FilterEqualTypeInput;
  gift_message_available?: FilterEqualTypeInput;
  has_options?: FilterEqualTypeInput;
  image?: FilterEqualTypeInput;
  image_label?: FilterEqualTypeInput;
  is_returnable?: FilterEqualTypeInput;
  manufacturer?: FilterEqualTypeInput;
  max_price?: FilterEqualTypeInput;
  meta_description?: FilterMatchTypeInput;
  meta_keyword?: FilterMatchTypeInput;
  meta_title?: FilterMatchTypeInput;
  min_price?: FilterEqualTypeInput;
  options_container?: FilterEqualTypeInput;
  required_options?: FilterEqualTypeInput;
  short_description?: FilterMatchTypeInput;
  small_image?: FilterEqualTypeInput;
  small_image_label?: FilterEqualTypeInput;
  special_price_from_date?: FilterRangeTypeInput;
  special_price_to_date?: FilterRangeTypeInput;
  swatch_image?: FilterEqualTypeInput;
  thumbnail?: FilterEqualTypeInput;
  thumbnail_label?: FilterEqualTypeInput;
  tier_price?: FilterEqualTypeInput;
  updated_at_legacy?: FilterRangeTypeInput;
  weight?: FilterRangeTypeInput;
  or?: ProductAttributeFilterInput;
}

export interface FilterEqualTypeInput {
  eq?: string;
  in?: string[];
  neq?: string;
  nin?: string[];
}

export interface FilterMatchTypeInput {
  match?: string;
}

export interface FilterRangeTypeInput {
  from?: string;
  to?: string;
  gteq?: string;
  lteq?: string;
  gt?: string;
  lt?: string;
}

export interface ProductAttributeSortInput {
  country_of_manufacture?: SortEnum;
  created_at?: SortEnum;
  custom_layout?: SortEnum;
  custom_layout_update_file?: SortEnum;
  description?: SortEnum;
  gift_message_available?: SortEnum;
  has_options?: SortEnum;
  image?: SortEnum;
  image_label?: SortEnum;
  manufacturer?: SortEnum;
  meta_description?: SortEnum;
  meta_keyword?: SortEnum;
  meta_title?: SortEnum;
  name?: SortEnum;
  news_from_date?: SortEnum;
  news_to_date?: SortEnum;
  options_container?: SortEnum;
  price?: SortEnum;
  required_options?: SortEnum;
  short_description?: SortEnum;
  sku?: SortEnum;
  small_image?: SortEnum;
  small_image_label?: SortEnum;
  special_from_date?: SortEnum;
  special_price?: SortEnum;
  special_to_date?: SortEnum;
  swatch_image?: SortEnum;
  thumbnail?: SortEnum;
  thumbnail_label?: SortEnum;
  tier_price?: SortEnum;
  updated_at?: SortEnum;
  url_key?: SortEnum;
  url_path?: SortEnum;
  weight?: SortEnum;
  position?: SortEnum;
  relevance?: SortEnum;
}

export enum SortEnum {
  ASC = 'ASC',
  DESC = 'DESC',
}

// Stock status enum
export enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
}

// Product type enum
export enum ProductType {
  SIMPLE = 'simple',
  CONFIGURABLE = 'configurable',
  GROUPED = 'grouped',
  VIRTUAL = 'virtual',
  BUNDLE = 'bundle',
  DOWNLOADABLE = 'downloadable',
  GIFT_CARD = 'giftcard',
}

// Utility types
export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
