// Category TypeScript type definitions

import { Breadcrumb, Maybe } from './index';
import { Products } from './product';

// Main category interface
export interface CategoryTree {
  id: number;
  uid: string;
  name: string;
  url_key: string;
  url_path: string;
  url_suffix?: Maybe<string>;
  level: number;
  position: number;
  include_in_menu: boolean;
  is_anchor: boolean;
  product_count: number;
  image?: Maybe<string>;
  description?: Maybe<string>;
  meta_title?: Maybe<string>;
  meta_keywords?: Maybe<string>;
  meta_description?: Maybe<string>;
  created_at?: Maybe<string>;
  updated_at?: Maybe<string>;
  children?: CategoryTree[];
  breadcrumbs?: Breadcrumb[];
  products?: Products;
}

// Category list response
export interface CategoryList {
  items: CategoryTree[];
}

// Category filter input
export interface CategoryFilterInput {
  ids?: FilterEqualTypeInput;
  name?: FilterMatchTypeInput;
  url_key?: FilterEqualTypeInput;
  url_path?: FilterMatchTypeInput;
  parent_id?: FilterEqualTypeInput;
  parent_category_uid?: FilterEqualTypeInput;
  level?: FilterEqualTypeInput;
  include_in_menu?: FilterEqualTypeInput;
  is_anchor?: FilterEqualTypeInput;
}

// Filter types (imported from main types)
interface FilterEqualTypeInput {
  eq?: string;
  in?: string[];
  neq?: string;
  nin?: string[];
}

interface FilterMatchTypeInput {
  match?: string;
}

// Category navigation interface
export interface CategoryNavigation {
  categories: CategoryTree[];
}

// Category with products interface
export interface CategoryWithProducts extends CategoryTree {
  products: Products;
}

// Category breadcrumb interface (extended)
export interface CategoryBreadcrumb extends Breadcrumb {
  category_uid: string;
}

// Category sort input
export interface CategorySortInput {
  name?: SortEnum;
  position?: SortEnum;
  level?: SortEnum;
  created_at?: SortEnum;
  updated_at?: SortEnum;
}

enum SortEnum {
  ASC = 'ASC',
  DESC = 'DESC',
}

// Category display mode enum
export enum CategoryDisplayModeEnum {
  PRODUCTS = 'PRODUCTS',
  PAGE = 'PAGE',
  PRODUCTS_AND_PAGE = 'PRODUCTS_AND_PAGE',
}

// Category CMS block interface
export interface CategoryCmsBlock {
  content: string;
  identifier: string;
  title: string;
}

// Category with CMS content
export interface CategoryWithCms extends CategoryTree {
  cms_block?: CategoryCmsBlock;
  landing_page?: number;
  display_mode?: CategoryDisplayModeEnum;
}

// Category attribute interface
export interface CategoryAttribute {
  attribute_code: string;
  entity_type: string;
  attribute_type: string;
  input_type: string;
  label: string;
  value: string;
}

// Category custom attributes
export interface CategoryCustomAttributes {
  attributes: CategoryAttribute[];
}

// Category URL rewrite interface
export interface CategoryUrlRewrite {
  url: string;
  parameters: CategoryUrlRewriteParameter[];
}

export interface CategoryUrlRewriteParameter {
  name: string;
  value: string;
}

// Category aggregation for layered navigation
export interface CategoryAggregation {
  attribute_code: string;
  count: number;
  label: string;
  options: CategoryAggregationOption[];
}

export interface CategoryAggregationOption {
  count: number;
  label: string;
  value: string;
}

// Category filter state for frontend
export interface CategoryFilterState {
  [attributeCode: string]: string[] | string;
}

// Category sort options for frontend
export interface CategorySortOption {
  label: string;
  value: string;
  field: string;
  direction: SortEnum;
}

// Default category sort options
export const DEFAULT_CATEGORY_SORT_OPTIONS: CategorySortOption[] = [
  {
    label: 'Position',
    value: 'position_asc',
    field: 'position',
    direction: SortEnum.ASC,
  },
  {
    label: 'Name: A to Z',
    value: 'name_asc',
    field: 'name',
    direction: SortEnum.ASC,
  },
  {
    label: 'Name: Z to A',
    value: 'name_desc',
    field: 'name',
    direction: SortEnum.DESC,
  },
  {
    label: 'Price: Low to High',
    value: 'price_asc',
    field: 'price',
    direction: SortEnum.ASC,
  },
  {
    label: 'Price: High to Low',
    value: 'price_desc',
    field: 'price',
    direction: SortEnum.DESC,
  },
  {
    label: 'Newest First',
    value: 'created_at_desc',
    field: 'created_at',
    direction: SortEnum.DESC,
  },
];

// Category page size options
export const CATEGORY_PAGE_SIZE_OPTIONS = [12, 24, 36, 48];

// Default category page size
export const DEFAULT_CATEGORY_PAGE_SIZE = 12;
