// Checkout TypeScript type definitions

import { Money, Maybe } from './index';
import { Cart, CartAddressInput, ShippingMethodInput, PaymentMethodInput } from './cart';

// Checkout step enum
export enum CheckoutStep {
  SHIPPING_ADDRESS = 'shipping_address',
  SHIPPING_METHOD = 'shipping_method',
  PAYMENT_METHOD = 'payment_method',
  REVIEW_ORDER = 'review_order',
  ORDER_CONFIRMATION = 'order_confirmation',
}

// Checkout state interface
export interface CheckoutState {
  currentStep: CheckoutStep;
  cart?: Cart;
  shippingAddress?: CartAddressInput;
  billingAddress?: CartAddressInput;
  shippingMethod?: ShippingMethodInput;
  paymentMethod?: PaymentMethodInput;
  sameAsShipping: boolean;
  loading: boolean;
  error?: string;
  orderNumber?: string;
  orderId?: string;
}

// Checkout form data
export interface CheckoutFormData {
  shippingAddress: ShippingAddressFormData;
  billingAddress: BillingAddressFormData;
  shippingMethod: string;
  paymentMethod: string;
  sameAsShipping: boolean;
  saveAddresses: boolean;
  subscribeNewsletter: boolean;
  orderComments?: string;
}

export interface ShippingAddressFormData {
  firstname: string;
  lastname: string;
  company?: string;
  street: string[];
  city: string;
  region?: string;
  region_id?: number;
  postcode?: string;
  country_code: string;
  telephone?: string;
  save_in_address_book?: boolean;
}

export interface BillingAddressFormData extends ShippingAddressFormData {
  same_as_shipping?: boolean;
}

// Order placement response
export interface PlaceOrderResponse {
  order: {
    order_number: string;
    order_id: string;
  };
}

// Order confirmation data
export interface OrderConfirmation {
  order_number: string;
  order_id: string;
  email: string;
  grand_total: Money;
  items: OrderConfirmationItem[];
  shipping_address: OrderConfirmationAddress;
  billing_address: OrderConfirmationAddress;
  shipping_method: string;
  payment_method: string;
  order_date: string;
}

export interface OrderConfirmationItem {
  name: string;
  sku: string;
  quantity: number;
  price: Money;
  total: Money;
}

export interface OrderConfirmationAddress {
  firstname: string;
  lastname: string;
  company?: string;
  street: string[];
  city: string;
  region?: string;
  postcode?: string;
  country: string;
  telephone?: string;
}

// Checkout validation
export interface CheckoutValidation {
  shippingAddress: AddressValidation;
  billingAddress: AddressValidation;
  shippingMethod: boolean;
  paymentMethod: boolean;
}

export interface AddressValidation {
  firstname: boolean;
  lastname: boolean;
  street: boolean;
  city: boolean;
  region: boolean;
  postcode: boolean;
  country_code: boolean;
  telephone: boolean;
}

// Checkout actions for state management
export enum CheckoutActionType {
  SET_STEP = 'SET_STEP',
  SET_CART = 'SET_CART',
  SET_SHIPPING_ADDRESS = 'SET_SHIPPING_ADDRESS',
  SET_BILLING_ADDRESS = 'SET_BILLING_ADDRESS',
  SET_SHIPPING_METHOD = 'SET_SHIPPING_METHOD',
  SET_PAYMENT_METHOD = 'SET_PAYMENT_METHOD',
  SET_SAME_AS_SHIPPING = 'SET_SAME_AS_SHIPPING',
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  SET_ORDER_CONFIRMATION = 'SET_ORDER_CONFIRMATION',
  RESET_CHECKOUT = 'RESET_CHECKOUT',
}

export interface CheckoutAction {
  type: CheckoutActionType;
  payload?: any;
}

// Payment method types
export enum PaymentMethodType {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  PAYPAL = 'paypal',
  BANK_TRANSFER = 'bank_transfer',
  CASH_ON_DELIVERY = 'cash_on_delivery',
  CHECK_MONEY_ORDER = 'check_money_order',
}

// Credit card interface
export interface CreditCardData {
  number: string;
  expiry_month: string;
  expiry_year: string;
  cvv: string;
  holder_name: string;
}

// PayPal data interface
export interface PayPalData {
  payer_id: string;
  payment_id: string;
}

// Bank transfer data interface
export interface BankTransferData {
  account_number: string;
  routing_number: string;
  account_holder_name: string;
}

// Checkout step validation rules
export interface CheckoutStepValidation {
  [CheckoutStep.SHIPPING_ADDRESS]: (data: ShippingAddressFormData) => boolean;
  [CheckoutStep.SHIPPING_METHOD]: (method: string) => boolean;
  [CheckoutStep.PAYMENT_METHOD]: (method: string) => boolean;
  [CheckoutStep.REVIEW_ORDER]: (state: CheckoutState) => boolean;
}

// Checkout progress interface
export interface CheckoutProgress {
  steps: CheckoutProgressStep[];
  currentStepIndex: number;
  completedSteps: CheckoutStep[];
}

export interface CheckoutProgressStep {
  step: CheckoutStep;
  label: string;
  completed: boolean;
  active: boolean;
}

// Default checkout progress steps
export const DEFAULT_CHECKOUT_STEPS: CheckoutProgressStep[] = [
  {
    step: CheckoutStep.SHIPPING_ADDRESS,
    label: 'Shipping Address',
    completed: false,
    active: true,
  },
  {
    step: CheckoutStep.SHIPPING_METHOD,
    label: 'Shipping Method',
    completed: false,
    active: false,
  },
  {
    step: CheckoutStep.PAYMENT_METHOD,
    label: 'Payment Method',
    completed: false,
    active: false,
  },
  {
    step: CheckoutStep.REVIEW_ORDER,
    label: 'Review Order',
    completed: false,
    active: false,
  },
];

// Checkout error types
export enum CheckoutErrorType {
  VALIDATION_ERROR = 'validation_error',
  NETWORK_ERROR = 'network_error',
  PAYMENT_ERROR = 'payment_error',
  INVENTORY_ERROR = 'inventory_error',
  GENERAL_ERROR = 'general_error',
}

export interface CheckoutError {
  type: CheckoutErrorType;
  message: string;
  field?: string;
  code?: string;
}

// Guest checkout data
export interface GuestCheckoutData {
  email: string;
  firstname: string;
  lastname: string;
  create_account?: boolean;
  password?: string;
}

// Checkout totals for review step
export interface CheckoutTotals {
  subtotal: Money;
  shipping: Money;
  tax: Money;
  discount: Money;
  grand_total: Money;
  applied_coupons?: string[];
}

// Checkout configuration
export interface CheckoutConfig {
  allow_guest_checkout: boolean;
  require_billing_address: boolean;
  require_shipping_address: boolean;
  default_country: string;
  available_payment_methods: string[];
  available_shipping_methods: string[];
  min_order_amount?: Money;
  max_order_amount?: Money;
}
