// Cart TypeScript type definitions

import { Money, Maybe } from './index';
import { ProductInterface } from './product';

// Main cart interface
export interface Cart {
  id: string;
  email?: Maybe<string>;
  is_virtual: boolean;
  applied_coupons?: AppliedCoupon[];
  items: CartItemInterface[];
  available_payment_methods: AvailablePaymentMethod[];
  selected_payment_method?: SelectedPaymentMethod;
  shipping_addresses: ShippingCartAddress[];
  billing_address?: BillingCartAddress;
  prices: CartPrices;
}

// Cart item interfaces
export interface CartItemInterface {
  id: string;
  uid: string;
  quantity: number;
  prices: CartItemPrices;
  product: ProductInterface;
}

export interface SimpleCartItem extends CartItemInterface {
  customizable_options?: SelectedCustomizableOption[];
}

export interface ConfigurableCartItem extends CartItemInterface {
  configurable_options: SelectedConfigurableOption[];
}

export interface BundleCartItem extends CartItemInterface {
  bundle_options: SelectedBundleOption[];
}

export interface GroupedCartItem extends CartItemInterface {
  // Grouped products don't have specific cart item properties
}

export interface VirtualCartItem extends CartItemInterface {
  // Virtual products don't have specific cart item properties
}

export interface DownloadableCartItem extends CartItemInterface {
  links: DownloadableProductLinks[];
}

// Cart item prices
export interface CartItemPrices {
  price: Money;
  row_total: Money;
  row_total_including_tax: Money;
  total_item_discount?: Money;
}

// Selected options interfaces
export interface SelectedCustomizableOption {
  id: number;
  label: string;
  type: string;
  values: SelectedCustomizableOptionValue[];
}

export interface SelectedCustomizableOptionValue {
  id: number;
  label: string;
  value: string;
}

export interface SelectedConfigurableOption {
  id: number;
  option_label: string;
  value_id: number;
  value_label: string;
}

export interface SelectedBundleOption {
  id: number;
  label: string;
  type: string;
  values: SelectedBundleOptionValue[];
}

export interface SelectedBundleOptionValue {
  id: number;
  label: string;
  price: number;
  quantity: number;
}

export interface DownloadableProductLinks {
  id: number;
  uid: string;
  title: string;
  sort_order: number;
  is_shareable: boolean;
  price: number;
  number_of_downloads: number;
  link_type: DownloadableFileTypeEnum;
  sample_type: DownloadableFileTypeEnum;
  sample_file: string;
  sample_url: string;
}

export enum DownloadableFileTypeEnum {
  FILE = 'FILE',
  URL = 'URL',
}

// Cart addresses
export interface CartAddressInterface {
  firstname: string;
  lastname: string;
  company?: Maybe<string>;
  street: string[];
  city: string;
  region?: CartAddressRegion;
  postcode?: Maybe<string>;
  country: CartAddressCountry;
  telephone?: Maybe<string>;
}

export interface ShippingCartAddress extends CartAddressInterface {
  available_shipping_methods?: AvailableShippingMethod[];
  selected_shipping_method?: SelectedShippingMethod;
}

export interface BillingCartAddress extends CartAddressInterface {
  // Billing address doesn't have additional properties
}

export interface CartAddressRegion {
  code?: Maybe<string>;
  label?: Maybe<string>;
  region_id?: Maybe<number>;
}

export interface CartAddressCountry {
  code: string;
  label: string;
}

// Shipping methods
export interface AvailableShippingMethod {
  carrier_code: string;
  carrier_title: string;
  method_code: string;
  method_title: string;
  amount: Money;
  price_excl_tax: Money;
  price_incl_tax: Money;
}

export interface SelectedShippingMethod {
  carrier_code: string;
  carrier_title: string;
  method_code: string;
  method_title: string;
  amount: Money;
}

// Payment methods
export interface AvailablePaymentMethod {
  code: string;
  title: string;
}

export interface SelectedPaymentMethod {
  code: string;
  title: string;
}

// Cart prices
export interface CartPrices {
  grand_total: Money;
  subtotal_excluding_tax: Money;
  subtotal_including_tax: Money;
  applied_taxes?: CartTax[];
  discounts?: CartDiscount[];
}

export interface CartTax {
  label: string;
  amount: Money;
}

export interface CartDiscount {
  amount: Money;
  label: string;
}

// Applied coupon
export interface AppliedCoupon {
  code: string;
}

// Cart input types for mutations
export interface CartItemInput {
  quantity: number;
  sku: string;
}

export interface SimpleProductCartItemInput extends CartItemInput {
  customizable_options?: CustomizableOptionInput[];
}

export interface ConfigurableProductCartItemInput extends CartItemInput {
  variant_sku?: string;
  parent_sku?: string;
  selected_options?: string[];
}

export interface BundleProductCartItemInput extends CartItemInput {
  bundle_options: BundleOptionInput[];
}

export interface CustomizableOptionInput {
  id: number;
  value_string?: string;
  value_id?: number;
}

export interface BundleOptionInput {
  id: number;
  quantity: number;
  value: string[];
}

// Cart item update input
export interface CartItemUpdateInput {
  cart_item_id: number;
  quantity: number;
  customizable_options?: CustomizableOptionInput[];
}

// Address input types
export interface CartAddressInput {
  firstname: string;
  lastname: string;
  company?: string;
  street: string[];
  city: string;
  region?: string;
  region_id?: number;
  postcode?: string;
  country_code: string;
  telephone?: string;
  save_in_address_book?: boolean;
}

export interface ShippingAddressInput extends CartAddressInput {
  // Shipping address doesn't have additional input properties
}

export interface BillingAddressInput extends CartAddressInput {
  same_as_shipping?: boolean;
}

// Shipping method input
export interface ShippingMethodInput {
  carrier_code: string;
  method_code: string;
}

// Payment method input
export interface PaymentMethodInput {
  code: string;
  purchase_order_number?: string;
}

// Cart totals summary for display
export interface CartTotalsSummary {
  subtotal: Money;
  shipping: Money;
  tax: Money;
  discount: Money;
  grand_total: Money;
  items_count: number;
  items_qty: number;
}

// Cart state for frontend management
export interface CartState {
  cart?: Cart;
  loading: boolean;
  error?: string;
  isOpen: boolean;
}

// Cart actions for state management
export enum CartActionType {
  SET_CART = 'SET_CART',
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  TOGGLE_CART = 'TOGGLE_CART',
  CLEAR_CART = 'CLEAR_CART',
}

export interface CartAction {
  type: CartActionType;
  payload?: any;
}
