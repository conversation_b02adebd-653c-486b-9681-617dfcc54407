// Product TypeScript type definitions

import { Money, Image, MediaGalleryEntry, PageInfo, Aggregation, StockStatus, ProductType, Maybe } from './index';

// Product interfaces
export interface ProductInterface {
  id: number;
  uid: string;
  name: string;
  sku: string;
  url_key: string;
  url_suffix?: Maybe<string>;
  type_id: ProductType;
  stock_status: StockStatus;
  only_x_left_in_stock?: Maybe<number>;
  rating_summary: number;
  review_count: number;
  price_range: PriceRange;
  image?: Maybe<Image>;
  small_image?: Maybe<Image>;
  thumbnail?: Maybe<Image>;
  short_description?: Maybe<ComplexTextValue>;
  description?: Maybe<ComplexTextValue>;
  meta_title?: Maybe<string>;
  meta_description?: Maybe<string>;
  meta_keyword?: Maybe<string>;
  created_at?: Maybe<string>;
  updated_at?: Maybe<string>;
  media_gallery?: MediaGalleryEntry[];
  categories?: CategoryInterface[];
  attributes?: ProductAttribute[];
}

export interface SimpleProduct extends ProductInterface {
  weight?: Maybe<number>;
}

export interface ConfigurableProduct extends ProductInterface {
  configurable_options: ConfigurableProductOptions[];
  variants: ConfigurableVariant[];
}

export interface GroupedProduct extends ProductInterface {
  items: GroupedProductItem[];
}

export interface BundleProduct extends ProductInterface {
  items: BundleItem[];
}

export interface VirtualProduct extends ProductInterface {
  // Virtual products don't have additional specific properties
}

export interface DownloadableProduct extends ProductInterface {
  downloadable_product_links: DownloadableProductLinks[];
  downloadable_product_samples: DownloadableProductSamples[];
}

// Product price interfaces
export interface PriceRange {
  minimum_price: ProductPrice;
  maximum_price: ProductPrice;
}

export interface ProductPrice {
  regular_price: Money;
  final_price: Money;
  discount?: ProductDiscount;
}

export interface ProductDiscount {
  amount_off: number;
  percent_off: number;
}

// Product attribute interfaces
export interface ProductAttribute {
  attribute_code: string;
  label: string;
  value: string;
}

export interface ComplexTextValue {
  html: string;
}

// Configurable product interfaces
export interface ConfigurableProductOptions {
  id: number;
  attribute_id: number;
  label: string;
  position: number;
  use_default: boolean;
  attribute_code: string;
  values: ConfigurableProductOptionsValues[];
  product_id: number;
}

export interface ConfigurableProductOptionsValues {
  value_index: number;
  label: string;
  store_label: string;
  default_label: string;
  use_default_value: boolean;
}

export interface ConfigurableVariant {
  product: SimpleProduct;
  attributes: ConfigurableAttributeOption[];
}

export interface ConfigurableAttributeOption {
  code: string;
  value_index: number;
  label: string;
}

// Grouped product interfaces
export interface GroupedProductItem {
  position: number;
  qty: number;
  product: ProductInterface;
}

// Bundle product interfaces
export interface BundleItem {
  option_id: number;
  title: string;
  required: boolean;
  type: string;
  position: number;
  sku: string;
  options: BundleItemOption[];
}

export interface BundleItemOption {
  id: number;
  uid: string;
  label: string;
  quantity: number;
  position: number;
  is_default: boolean;
  price: number;
  price_type: BundlePriceType;
  can_change_quantity: boolean;
  product: ProductInterface;
}

export enum BundlePriceType {
  FIXED = 'FIXED',
  PERCENT = 'PERCENT',
}

// Downloadable product interfaces
export interface DownloadableProductLinks {
  id: number;
  uid: string;
  title: string;
  sort_order: number;
  is_shareable: boolean;
  price: number;
  number_of_downloads: number;
  link_type: DownloadableFileTypeEnum;
  sample_type: DownloadableFileTypeEnum;
  sample_file: string;
  sample_url: string;
}

export interface DownloadableProductSamples {
  id: number;
  uid: string;
  title: string;
  sort_order: number;
  sample_type: DownloadableFileTypeEnum;
  sample_file: string;
  sample_url: string;
}

export enum DownloadableFileTypeEnum {
  FILE = 'FILE',
  URL = 'URL',
}

// Product list interfaces
export interface Products {
  total_count: number;
  page_info: PageInfo;
  items: ProductInterface[];
  aggregations?: Aggregation[];
  suggestions?: SearchSuggestion[];
}

export interface SearchSuggestion {
  search: string;
}

// Product reviews interfaces
export interface ProductReviews {
  items: ProductReview[];
  page_info: PageInfo;
}

export interface ProductReview {
  average_rating: number;
  ratings_breakdown: ProductReviewRating[];
  nickname: string;
  summary: string;
  text: string;
  created_at: string;
}

export interface ProductReviewRating {
  name: string;
  value: string;
}

// Related products
export interface RelatedProducts {
  related_products: ProductInterface[];
  upsell_products: ProductInterface[];
  crosssell_products: ProductInterface[];
}

// Category interface (simplified for product context)
export interface CategoryInterface {
  id: number;
  uid: string;
  name: string;
  url_key: string;
  url_path: string;
  level: number;
  breadcrumbs?: Breadcrumb[];
}

// Custom options interfaces
export interface CustomizableOptionInterface {
  title: string;
  required: boolean;
  sort_order: number;
  option_id: number;
}

export interface CustomizableAreaOption extends CustomizableOptionInterface {
  value: CustomizableAreaValue;
}

export interface CustomizableAreaValue {
  uid: string;
  max_characters: number;
}

export interface CustomizableFieldOption extends CustomizableOptionInterface {
  value: CustomizableFieldValue;
}

export interface CustomizableFieldValue {
  uid: string;
  max_characters: number;
}

export interface CustomizableFileOption extends CustomizableOptionInterface {
  value: CustomizableFileValue;
}

export interface CustomizableFileValue {
  uid: string;
  price: number;
  price_type: PriceTypeEnum;
  sku: string;
  file_extension: string;
  image_size_x: number;
  image_size_y: number;
}

export enum PriceTypeEnum {
  FIXED = 'FIXED',
  PERCENT = 'PERCENT',
}

// Import from index to avoid circular dependency
import { Breadcrumb } from './index';
