// Customer TypeScript type definitions

import { Money, PageInfo, Maybe } from './index';

// Main customer interface
export interface Customer {
  id: number;
  firstname: string;
  lastname: string;
  email: string;
  is_subscribed: boolean;
  date_of_birth?: Maybe<string>;
  gender?: Maybe<number>;
  created_at: string;
  updated_at: string;
  addresses?: CustomerAddress[];
}

// Customer address interface
export interface CustomerAddress {
  id: number;
  firstname: string;
  lastname: string;
  company?: Maybe<string>;
  street: string[];
  city: string;
  region?: CustomerAddressRegion;
  postcode?: Maybe<string>;
  country_code: string;
  telephone?: Maybe<string>;
  default_shipping: boolean;
  default_billing: boolean;
}

export interface CustomerAddressRegion {
  region_code?: Maybe<string>;
  region?: Maybe<string>;
  region_id?: Maybe<number>;
}

// Customer input types
export interface CustomerInput {
  firstname: string;
  lastname: string;
  email: string;
  password?: string;
  date_of_birth?: string;
  gender?: number;
  is_subscribed?: boolean;
}

export interface CustomerAddressInput {
  firstname: string;
  lastname: string;
  company?: string;
  street: string[];
  city: string;
  region?: CustomerAddressRegionInput;
  postcode?: string;
  country_code: string;
  telephone?: string;
  default_shipping?: boolean;
  default_billing?: boolean;
}

export interface CustomerAddressRegionInput {
  region?: string;
  region_id?: number;
  region_code?: string;
}

// Customer authentication
export interface CustomerToken {
  token: string;
}

export interface CustomerCreateResponse {
  customer: Customer;
}

export interface CustomerUpdateResponse {
  customer: Customer;
}

// Customer orders
export interface CustomerOrders {
  total_count: number;
  page_info: PageInfo;
  items: CustomerOrder[];
}

export interface CustomerOrder {
  id: string;
  order_date: string;
  status: string;
  number: string;
  grand_total: Money;
  items: OrderItem[];
  shipping_address?: OrderAddress;
  billing_address?: OrderAddress;
  payment_methods?: PaymentMethod[];
  shipping_method?: string;
  shipments?: Shipment[];
  invoices?: Invoice[];
}

export interface OrderItem {
  id: string;
  product_name: string;
  product_sku: string;
  product_url_key?: Maybe<string>;
  product_sale_price: Money;
  quantity_ordered: number;
  quantity_shipped: number;
  quantity_invoiced: number;
  quantity_refunded: number;
  quantity_canceled: number;
  quantity_returned: number;
}

export interface OrderAddress {
  firstname: string;
  lastname: string;
  company?: Maybe<string>;
  street: string[];
  city: string;
  region?: Maybe<string>;
  postcode?: Maybe<string>;
  country_code: string;
  telephone?: Maybe<string>;
}

export interface PaymentMethod {
  name: string;
  type: string;
}

export interface Shipment {
  id: string;
  number: string;
  tracking?: ShipmentTracking[];
  items: ShipmentItem[];
}

export interface ShipmentTracking {
  title: string;
  number: string;
  carrier: string;
}

export interface ShipmentItem {
  id: string;
  product_name: string;
  product_sku: string;
  quantity_shipped: number;
}

export interface Invoice {
  id: string;
  number: string;
  grand_total: Money;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  product_name: string;
  product_sku: string;
  quantity_invoiced: number;
}

// Customer orders filter
export interface CustomerOrdersFilterInput {
  number?: FilterMatchTypeInput;
  created_date?: FilterRangeTypeInput;
}

interface FilterMatchTypeInput {
  match?: string;
}

interface FilterRangeTypeInput {
  from?: string;
  to?: string;
  gteq?: string;
  lteq?: string;
  gt?: string;
  lt?: string;
}

// Newsletter subscription
export interface NewsletterSubscriptionStatus {
  status: SubscriptionStatusesEnum;
}

export enum SubscriptionStatusesEnum {
  SUBSCRIBED = 'SUBSCRIBED',
  UNSUBSCRIBED = 'UNSUBSCRIBED',
  NOT_ACTIVE = 'NOT_ACTIVE',
  UNCONFIRMED = 'UNCONFIRMED',
}

// Countries and regions for address forms
export interface Country {
  id: string;
  two_letter_abbreviation: string;
  three_letter_abbreviation: string;
  full_name_locale: string;
  full_name_english: string;
  available_regions?: Region[];
}

export interface Region {
  id: number;
  code: string;
  name: string;
}

// Customer wishlist (if needed)
export interface Wishlist {
  id: string;
  items_count: number;
  sharing_code?: Maybe<string>;
  updated_at: string;
  items?: WishlistItem[];
}

export interface WishlistItem {
  id: number;
  quantity: number;
  description?: Maybe<string>;
  added_at: string;
  product: WishlistProduct;
}

export interface WishlistProduct {
  id: number;
  uid: string;
  name: string;
  sku: string;
  url_key: string;
  thumbnail?: {
    url: string;
    label: string;
  };
  price_range: {
    minimum_price: {
      regular_price: Money;
      final_price: Money;
    };
  };
}

// Customer state for frontend
export interface CustomerState {
  customer?: Customer;
  isAuthenticated: boolean;
  token?: string;
  loading: boolean;
  error?: string;
}

// Customer actions for state management
export enum CustomerActionType {
  SET_CUSTOMER = 'SET_CUSTOMER',
  SET_TOKEN = 'SET_TOKEN',
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
  LOGOUT = 'LOGOUT',
}

export interface CustomerAction {
  type: CustomerActionType;
  payload?: any;
}

// Gender options
export const GENDER_OPTIONS = [
  { value: 1, label: 'Male' },
  { value: 2, label: 'Female' },
  { value: 3, label: 'Not Specified' },
];

// Customer validation rules
export interface CustomerValidationRules {
  firstname: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  lastname: {
    required: boolean;
    minLength: number;
    maxLength: number;
  };
  email: {
    required: boolean;
    pattern: RegExp;
  };
  password: {
    required: boolean;
    minLength: number;
    pattern: RegExp;
  };
  telephone: {
    required: boolean;
    pattern: RegExp;
  };
}

export const DEFAULT_CUSTOMER_VALIDATION_RULES: CustomerValidationRules = {
  firstname: {
    required: true,
    minLength: 1,
    maxLength: 255,
  },
  lastname: {
    required: true,
    minLength: 1,
    maxLength: 255,
  },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  telephone: {
    required: false,
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
  },
};
