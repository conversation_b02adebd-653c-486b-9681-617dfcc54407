import { NextRequest, NextResponse } from 'next/server';
import { 
  getHomepageIdentifier, 
  getHomepageConfig,
  getBasicStoreConfig 
} from '@/lib/magento/api/storeConfig';
import { getCmsPageWithPageBuilder } from '@/lib/magento/api/cms';

// GET /api/homepage - Fetch homepage data
export async function GET(request: NextRequest) {
  try {
    // Get homepage identifier from store configuration
    const homepageIdentifier = await getHomepageIdentifier();
    
    if (!homepageIdentifier) {
      return NextResponse.json(
        { error: 'No homepage identifier found in store configuration' },
        { status: 404 }
      );
    }

    // Fetch homepage CMS page, store config, and homepage config in parallel
    const [cmsPage, storeConfig, homepageConfig] = await Promise.all([
      getCmsPageWithPageBuilder(homepageIdentifier),
      getBasicStoreConfig(),
      getHomepageConfig(),
    ]);

    if (!cmsPage) {
      return NextResponse.json(
        { error: `Homepage CMS page not found: ${homepageIdentifier}` },
        { status: 404 }
      );
    }

    const homepageData = {
      cmsPage,
      storeConfig,
      homepageConfig,
    };

    // Set cache headers
    const response = NextResponse.json(homepageData);
    response.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch homepage data' },
      { status: 500 }
    );
  }
}

// Handle other HTTP methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
