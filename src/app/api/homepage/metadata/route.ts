import { NextRequest, NextResponse } from 'next/server';
import { 
  getHomepageConfig, 
  getBasicStoreConfig, 
  getStoreSeoConfig,
  formatStoreTitle,
  getDefaultMetaDescription,
  getDefaultMetaKeywords 
} from '@/lib/magento/api/storeConfig';
import { getCmsPageWithPageBuilder } from '@/lib/magento/api/cms';

// GET /api/homepage/metadata - Fetch homepage metadata
export async function GET(request: NextRequest) {
  try {
    // Fetch configuration data in parallel
    const [homepageConfig, storeConfig, seoConfig] = await Promise.all([
      getHomepageConfig(),
      getBasicStoreConfig(),
      getStoreSeoConfig(),
    ]);

    // Get homepage CMS page for additional metadata
    let cmsPage = null;
    if (homepageConfig?.cms_home_page) {
      cmsPage = await getCmsPageWithPageBuilder(homepageConfig.cms_home_page);
    }

    // Determine title
    let title = 'Home';
    if (cmsPage?.title) {
      title = cmsPage.title;
    } else if (homepageConfig?.default_title) {
      title = homepageConfig.default_title;
    } else if (storeConfig?.store_name) {
      title = storeConfig.store_name;
    }

    // Format title with prefix/suffix
    const formattedTitle = formatStoreTitle(title, seoConfig);

    // Determine description
    let description = getDefaultMetaDescription(seoConfig);
    if (cmsPage?.meta_description) {
      description = cmsPage.meta_description;
    }

    // Determine keywords
    let keywords = getDefaultMetaKeywords(seoConfig);
    if (cmsPage?.meta_keywords) {
      keywords = cmsPage.meta_keywords;
    }

    const metadata = {
      title: formattedTitle,
      description,
      keywords,
    };

    // Set cache headers
    const response = NextResponse.json(metadata);
    response.headers.set('Cache-Control', 'public, s-maxage=3600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error('Error fetching homepage metadata:', error);
    
    // Return fallback metadata
    const fallbackMetadata = {
      title: 'Magento E-commerce Store',
      description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
      keywords: 'ecommerce, online shopping, products, store',
    };
    
    return NextResponse.json(fallbackMetadata);
  }
}

// Handle other HTTP methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
