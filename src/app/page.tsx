import { <PERSON>adata } from 'next';
import { Container, Box } from '@mui/material';
import { DynamicHomepageServer } from '@/components/home/<USER>';
import { generateHomepageMetadata } from '@/lib/metadata/homepage';
import HeroBanner from '@/components/layout/HeroBanner';
import BannerSlider from '@/components/sliders/BannerSlider';
import ProductSlider from '@/components/sliders/ProductSlider';
import CategoryGrid from '@/components/layout/CategoryGrid';
import Testimonials from '@/components/layout/Testimonials';
import homepageData from '@/data/homepage.json';

// Generate dynamic metadata from Magento store configuration
export async function generateMetadata(): Promise<Metadata> {
  return await generateHomepageMetadata();
}

// Mock product data for demonstration
const mockProducts = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    sku: 'WH-001',
    url_key: 'premium-wireless-headphones',
    price: {
      regular: 199.99,
      final: 149.99,
      currency: '$',
    },
    image: {
      url: '/images/products/headphones.jpg',
      alt: 'Premium Wireless Headphones',
    },
    rating: 4.5,
    reviewCount: 128,
    isNew: true,
    isOnSale: true,
    stockStatus: 'IN_STOCK' as const,
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    sku: 'SW-002',
    url_key: 'smart-fitness-watch',
    price: {
      regular: 299.99,
      final: 299.99,
      currency: '$',
    },
    image: {
      url: '/images/products/smartwatch.jpg',
      alt: 'Smart Fitness Watch',
    },
    rating: 4.8,
    reviewCount: 89,
    isNew: false,
    isOnSale: false,
    stockStatus: 'IN_STOCK' as const,
  },
  // Add more mock products as needed
];

// Fallback homepage content (used when Magento CMS is not available)
function FallbackHomepage() {
  return (
    <main>
      {/* Hero Banner */}
      <HeroBanner
        title={homepageData.hero.title}
        subtitle={homepageData.hero.subtitle}
        description={homepageData.hero.description}
        cta={homepageData.hero.cta}
        backgroundImage={homepageData.hero.backgroundImage}
        mobileBackgroundImage={homepageData.hero.mobileBackgroundImage}
        height={600}
      />

      {/* Banner Slider */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <BannerSlider
          banners={homepageData.banners}
          height={400}
          autoplay={true}
          autoplayDelay={5000}
        />
      </Container>

      {/* Featured Products */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <ProductSlider
          products={mockProducts}
          title="Featured Products"
          showNavigation={true}
        />
      </Container>

      {/* Category Grid */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <CategoryGrid
          categories={homepageData.featuredCategories}
          title="Shop by Category"
          subtitle="Explore our wide range of product categories"
        />
      </Container>

      {/* Testimonials */}
      <Testimonials
        testimonials={homepageData.testimonials}
        title="What Our Customers Say"
        subtitle="Don't just take our word for it - hear from our satisfied customers"
      />

      {/* Additional spacing */}
      <Box sx={{ py: 4 }} />
    </main>
  );
}

// Main homepage component with dynamic content from Magento
export default async function HomePage() {
  return (
    <DynamicHomepageServer
      fallbackContent={<FallbackHomepage />}
      showFallbackOnError={true}
    />
  );
}
