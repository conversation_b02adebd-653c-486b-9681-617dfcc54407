import type { Metadata } from 'next';
import './globals.css';
import { ThemeProvider } from '@/components/ui/ThemeProvider';
import TopHeader from '@/components/layout/TopHeader';
import DynamicHeader from '@/components/layout/DynamicHeader';
import DynamicFooter from '@/components/layout/DynamicFooterNew';

export const metadata: Metadata = {
  title: 'Magento E-commerce Store',
  description:
    'Modern e-commerce store built with Next.js and Magento 2 GraphQL',
  keywords: 'ecommerce, magento, nextjs, shopping, online store',
  authors: [{ name: 'Your Store Name' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL,
    siteName: 'Magento E-commerce Store',
    title: 'Magento E-commerce Store',
    description:
      'Modern e-commerce store built with Next.js and Magento 2 GraphQL',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Magento E-commerce Store',
    description:
      'Modern e-commerce store built with Next.js and Magento 2 GraphQL',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              minHeight: '100vh',
            }}
          >
            <TopHeader />
            <DynamicHeader />
            <main style={{ flex: 1 }}>{children}</main>
            <DynamicFooter />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
