import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Home | Magento E-commerce Store',
  description: 'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.',
  openGraph: {
    title: 'Home | Magento E-commerce Store',
    description: 'Discover amazing products at our online store.',
    type: 'website',
  },
};

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-center mb-8">
          Welcome to Our Store
        </h1>
        <p className="text-center text-gray-600 mb-8">
          This is the homepage placeholder. Components will be added here.
        </p>
        
        {/* Hero Section Placeholder */}
        <section className="mb-12">
          <div className="bg-gray-100 h-96 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Hero Banner Component</p>
          </div>
        </section>
        
        {/* Banner Slider Placeholder */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Featured Banners</h2>
          <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Banner Slider Component</p>
          </div>
        </section>
        
        {/* Product Slider Placeholder */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Featured Products</h2>
          <div className="bg-gray-100 h-80 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Product Slider Component</p>
          </div>
        </section>
        
        {/* Category Grid Placeholder */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Shop by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((item) => (
              <div key={item} className="bg-gray-100 h-48 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Category {item}</p>
              </div>
            ))}
          </div>
        </section>
      </div>
    </main>
  );
}
