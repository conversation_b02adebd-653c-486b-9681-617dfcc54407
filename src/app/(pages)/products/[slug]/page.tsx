import { Metadata } from 'next';

interface ProductPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  // TODO: Fetch product data from Magento
  const productName = params.slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `${productName} | Magento E-commerce Store`,
    description: `Shop ${productName} with fast shipping and great customer service.`,
    openGraph: {
      title: productName,
      description: `Shop ${productName} with fast shipping and great customer service.`,
      type: 'product',
    },
  };
}

export default function ProductDetailPage({ params }: ProductPageProps) {
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="bg-gray-100 h-96 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Main Product Image</p>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="bg-gray-100 h-20 rounded-lg flex items-center justify-center">
                  <p className="text-xs text-gray-500">{item}</p>
                </div>
              ))}
            </div>
          </div>
          
          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">Product: {params.slug}</h1>
              <p className="text-2xl font-semibold text-green-600 mb-4">$99.99</p>
              <p className="text-gray-600 mb-6">
                This is a placeholder product description. The actual product details will be fetched from Magento GraphQL API.
              </p>
            </div>
            
            {/* Product Options */}
            <div className="space-y-4">
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-gray-500">Size/Color Options Component</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-gray-500">Quantity Selector Component</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-gray-500">Add to Cart Button Component</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Product Tabs */}
        <div className="mb-12">
          <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Product Tabs (Description, Reviews, etc.)</p>
          </div>
        </div>
        
        {/* Related Products */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Related Products</h2>
          <div className="bg-gray-100 h-80 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Related Products Slider</p>
          </div>
        </div>
      </div>
    </main>
  );
}
