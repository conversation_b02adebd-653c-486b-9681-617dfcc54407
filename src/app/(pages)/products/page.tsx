import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Products | Magento E-commerce Store',
  description: 'Browse our complete collection of products with filters and sorting options.',
};

export default function ProductsPage() {
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">All Products</h1>
        
        {/* Filters and Sorting */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="bg-gray-100 p-4 rounded-lg flex-1">
            <p className="text-gray-500">Filters Component</p>
          </div>
          <div className="bg-gray-100 p-4 rounded-lg w-full md:w-64">
            <p className="text-gray-500">Sort Component</p>
          </div>
        </div>
        
        {/* Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="bg-gray-100 h-80 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Product {index + 1}</p>
            </div>
          ))}
        </div>
        
        {/* Pagination */}
        <div className="bg-gray-100 p-4 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Pagination Component</p>
        </div>
      </div>
    </main>
  );
}
