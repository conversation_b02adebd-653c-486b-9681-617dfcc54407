import { Metadata } from 'next';
import { Container, Typography, Box, Paper, Alert, Button, Grid } from '@mui/material';
import { DynamicHomepageClient } from '@/components/home/<USER>';
import { PageBuilderContentSkeleton } from '@/components/ui/LoadingSkeleton';

export const metadata: Metadata = {
  title: 'Dynamic Homepage Demo | Magento E-commerce Store',
  description: 'Demonstration of dynamic homepage loading from Magento 2 CMS with Page Builder support',
};

// Fallback content for demo
function DemoFallbackContent() {
  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Demo Fallback Content
        </Typography>
        <Typography variant="body2">
          This is the fallback content that would be displayed if the Magento CMS homepage 
          cannot be loaded. In a real application, this could be your static homepage design.
        </Typography>
      </Alert>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom>
              Static Hero Section
            </Typography>
            <Typography variant="body1" paragraph>
              This would be your fallback hero content when Magento CMS is not available.
            </Typography>
            <Button variant="contained" size="large">
              Shop Now
            </Button>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom>
              Featured Products
            </Typography>
            <Typography variant="body1" paragraph>
              Your featured products section would be displayed here as a fallback.
            </Typography>
            <Button variant="outlined" size="large">
              View Products
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}

export default function DynamicHomepageDemo() {
  return (
    <main>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography
          variant="h2"
          component="h1"
          sx={{
            mb: 4,
            textAlign: 'center',
            fontWeight: 700,
            background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Dynamic Homepage Demo
        </Typography>

        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            About Dynamic Homepage
          </Typography>
          <Typography variant="body1" paragraph>
            This page demonstrates the dynamic homepage functionality that:
          </Typography>
          <Box component="ul" sx={{ pl: 3, mb: 3 }}>
            <li>Fetches the homepage identifier from Magento store configuration</li>
            <li>Loads the corresponding CMS page content</li>
            <li>Automatically detects and renders Page Builder content</li>
            <li>Falls back to static content if Magento is unavailable</li>
            <li>Generates dynamic metadata from store configuration</li>
          </Box>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Note:</strong> Since this is a demo environment without a real Magento backend, 
              you'll see the fallback content below. In a production environment with Magento 2 
              connected, this would display the actual CMS homepage with Page Builder content.
            </Typography>
          </Alert>

          <Typography variant="h6" gutterBottom>
            How It Works:
          </Typography>
          <Box component="ol" sx={{ pl: 3 }}>
            <li>Query Magento GraphQL for store configuration</li>
            <li>Extract the <code>cms_home_page</code> identifier</li>
            <li>Fetch the CMS page content using the identifier</li>
            <li>Check if the content contains Page Builder elements</li>
            <li>Render with Page Builder parser or standard CMS renderer</li>
            <li>Show fallback content if any step fails</li>
          </Box>
        </Paper>

        <Typography variant="h4" gutterBottom sx={{ mt: 4, mb: 2 }}>
          Live Demo
        </Typography>
        
        <Typography variant="body1" paragraph sx={{ mb: 4 }}>
          The component below will attempt to load dynamic content from Magento. 
          Since we're in demo mode, it will show the fallback content:
        </Typography>
      </Container>

      {/* Dynamic Homepage Demo */}
      <DynamicHomepageClient
        fallbackContent={<DemoFallbackContent />}
        showFallbackOnError={true}
      />

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
          <Typography variant="h5" gutterBottom>
            Implementation Details
          </Typography>
          
          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Store Configuration Query:
          </Typography>
          <Box 
            component="pre" 
            sx={{ 
              backgroundColor: '#f5f5f5', 
              p: 2, 
              borderRadius: 1, 
              overflow: 'auto',
              fontSize: '0.875rem',
            }}
          >
{`query GetHomepageIdentifier {
  storeConfig {
    cms_home_page
    store_name
    default_title
  }
}`}
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            CMS Page Query:
          </Typography>
          <Box 
            component="pre" 
            sx={{ 
              backgroundColor: '#f5f5f5', 
              p: 2, 
              borderRadius: 1, 
              overflow: 'auto',
              fontSize: '0.875rem',
            }}
          >
{`query GetCmsPage($identifier: String!) {
  cmsPage(identifier: $identifier) {
    identifier
    title
    content
    meta_title
    meta_description
    meta_keywords
  }
}`}
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Usage Example:
          </Typography>
          <Box 
            component="pre" 
            sx={{ 
              backgroundColor: '#f5f5f5', 
              p: 2, 
              borderRadius: 1, 
              overflow: 'auto',
              fontSize: '0.875rem',
            }}
          >
{`import { DynamicHomepage } from '@/components/home/<USER>';

export default async function HomePage() {
  return (
    <DynamicHomepage
      fallbackContent={<StaticHomepage />}
      showFallbackOnError={true}
    />
  );
}`}
          </Box>
        </Paper>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button
            variant="contained"
            size="large"
            href="/pagebuilder-demo"
            sx={{ mr: 2 }}
          >
            View Page Builder Demo
          </Button>
          <Button
            variant="outlined"
            size="large"
            href="/"
          >
            Back to Homepage
          </Button>
        </Box>
      </Container>
    </main>
  );
}
