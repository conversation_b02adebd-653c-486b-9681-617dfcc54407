import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCmsPageByUrlKey, getCmsPageWithPageBuilder } from '@/lib/magento/api/cms';
import CmsPageRenderer from '@/components/cms/CmsPageRenderer';

interface CmsPageProps {
  params: {
    slug: string[];
  };
}

// Generate metadata for CMS pages
export async function generateMetadata({ params }: CmsPageProps): Promise<Metadata> {
  const urlKey = params.slug.join('/');
  const page = await getCmsPageByUrlKey(urlKey);

  if (!page) {
    return {
      title: 'Page Not Found',
      description: 'The requested page could not be found.',
    };
  }

  return {
    title: page.meta_title || page.title,
    description: page.meta_description || `${page.title} - Learn more about our services and products.`,
    keywords: page.meta_keywords,
    openGraph: {
      title: page.meta_title || page.title,
      description: page.meta_description || `${page.title} - Learn more about our services and products.`,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: page.meta_title || page.title,
      description: page.meta_description || `${page.title} - Learn more about our services and products.`,
    },
  };
}

// CMS Page component
export default async function CmsPage({ params }: CmsPageProps) {
  const urlKey = params.slug.join('/');
  
  // Get CMS page with Page Builder content
  const page = await getCmsPageWithPageBuilder(urlKey);

  if (!page) {
    notFound();
  }

  return (
    <main className="cms-page-container">
      <CmsPageRenderer
        page={page}
        showBreadcrumbs={true}
        showTitle={true}
        showContentHeading={true}
      />
    </main>
  );
}

// Generate static params for known CMS pages (optional)
export async function generateStaticParams() {
  // You can implement this to pre-generate static pages for known CMS pages
  // For now, we'll use dynamic rendering
  return [];
}
