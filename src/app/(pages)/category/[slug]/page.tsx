import { Metadata } from 'next';

interface CategoryPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  // TODO: Fetch category data from Magento
  const categoryName = params.slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `${categoryName} | Magento E-commerce Store`,
    description: `Shop ${categoryName} products with fast shipping and great customer service.`,
    openGraph: {
      title: `${categoryName} Category`,
      description: `Shop ${categoryName} products with fast shipping and great customer service.`,
      type: 'website',
    },
  };
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const categoryName = params.slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumbs */}
        <nav className="mb-6">
          <div className="bg-gray-100 p-3 rounded-lg">
            <p className="text-gray-500">Breadcrumbs Component</p>
          </div>
        </nav>
        
        {/* Category Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">{categoryName}</h1>
          <div className="bg-gray-100 h-32 rounded-lg flex items-center justify-center">
            <p className="text-gray-500">Category Banner/Description</p>
          </div>
        </div>
        
        {/* Filters and Sorting */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <aside className="w-full lg:w-64">
            <div className="bg-gray-100 p-4 rounded-lg h-96">
              <p className="text-gray-500">Category Filters Sidebar</p>
            </div>
          </aside>
          
          {/* Products Grid */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="flex justify-between items-center mb-6">
              <p className="text-gray-600">Showing 1-12 of 48 products</p>
              <div className="bg-gray-100 p-2 rounded-lg">
                <p className="text-gray-500">Sort Dropdown</p>
              </div>
            </div>
            
            {/* Products Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {Array.from({ length: 12 }).map((_, index) => (
                <div key={index} className="bg-gray-100 h-80 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Product {index + 1}</p>
                </div>
              ))}
            </div>
            
            {/* Pagination */}
            <div className="bg-gray-100 p-4 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Pagination Component</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
