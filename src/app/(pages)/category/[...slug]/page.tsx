import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Container, Box, Typography, Breadcrumbs, Link } from '@mui/material';
import NextLink from 'next/link';
import CategoryProducts from '@/components/category/CategoryProducts';
import CategoryHeader from '@/components/category/CategoryHeader';
import { getCategoryByUrlKey, getCategoryBreadcrumbs } from '@/lib/magento/api/categories';
import { serverGraphQL } from '@/lib/graphql/simpleClient';
import { GET_CATEGORY_BY_URL_KEY } from '@/lib/graphql/queries';

// Generate metadata for category page
export async function generateMetadata({
  params,
}: {
  params: { slug: string[] };
}): Promise<Metadata> {
  const categoryUrlKey = params.slug[params.slug.length - 1];
  
  try {
    const response = await serverGraphQL<{
      categoryList: Array<{
        name: string;
        meta_title?: string;
        meta_description?: string;
        meta_keywords?: string;
        url_path: string;
      }>;
    }>(GET_CATEGORY_BY_URL_KEY, { urlKey: categoryUrlKey });

    const category = response.categoryList[0];
    
    if (!category) {
      return {
        title: 'Category Not Found | Magento E-commerce Store',
        description: 'The requested category could not be found.',
      };
    }

    return {
      title: category.meta_title || `${category.name} | Magento E-commerce Store`,
      description: category.meta_description || `Shop ${category.name} products. Find the best deals and latest arrivals in our ${category.name} collection.`,
      keywords: category.meta_keywords,
      openGraph: {
        title: category.meta_title || category.name,
        description: category.meta_description || `Shop ${category.name} products`,
        type: 'website',
        url: `/category/${category.url_path}`,
      },
    };
  } catch (error) {
    console.error('Error generating category metadata:', error);
    return {
      title: 'Category | Magento E-commerce Store',
      description: 'Browse our product categories and find exactly what you\'re looking for.',
    };
  }
}

// Category page component
export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: { slug: string[] };
  searchParams: {
    page?: string;
    sort?: string;
    filter?: string;
    [key: string]: string | undefined;
  };
}) {
  const categoryUrlKey = params.slug[params.slug.length - 1];
  const currentPage = parseInt(searchParams.page || '1', 10);
  const sortBy = searchParams.sort || 'position';

  // Fetch category data
  let category;
  try {
    const response = await serverGraphQL<{
      categoryList: Array<{
        uid: string;
        id: number;
        name: string;
        url_key: string;
        url_path: string;
        description?: string;
        meta_title?: string;
        meta_description?: string;
        image?: string;
        breadcrumbs?: Array<{
          category_id: number;
          category_name: string;
          category_level: number;
          category_url_key: string;
          category_url_path: string;
        }>;
        children?: Array<{
          uid: string;
          id: number;
          name: string;
          url_key: string;
          url_path: string;
          image?: string;
        }>;
      }>;
    }>(GET_CATEGORY_BY_URL_KEY, { urlKey: categoryUrlKey });

    category = response.categoryList[0];
  } catch (error) {
    console.error('Error fetching category:', error);
    notFound();
  }

  if (!category) {
    notFound();
  }

  // Build breadcrumbs
  const breadcrumbs = [
    { name: 'Home', url: '/' },
    ...(category.breadcrumbs?.map(breadcrumb => ({
      name: breadcrumb.category_name,
      url: `/category/${breadcrumb.category_url_path}`,
    })) || []),
    { name: category.name, url: `/category/${category.url_path}` },
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
        separator="›"
      >
        {breadcrumbs.map((breadcrumb, index) => {
          const isLast = index === breadcrumbs.length - 1;
          
          return isLast ? (
            <Typography key={breadcrumb.url} color="text.primary" sx={{ fontWeight: 500 }}>
              {breadcrumb.name}
            </Typography>
          ) : (
            <Link
              key={breadcrumb.url}
              component={NextLink}
              href={breadcrumb.url}
              color="inherit"
              sx={{
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {breadcrumb.name}
            </Link>
          );
        })}
      </Breadcrumbs>

      {/* Category Header */}
      <CategoryHeader
        category={category}
        subcategories={category.children || []}
      />

      {/* Category Products */}
      <Box sx={{ mt: 4 }}>
        <CategoryProducts
          categoryId={category.id.toString()}
          currentPage={currentPage}
          sortBy={sortBy}
          searchParams={searchParams}
        />
      </Box>
    </Container>
  );
}
