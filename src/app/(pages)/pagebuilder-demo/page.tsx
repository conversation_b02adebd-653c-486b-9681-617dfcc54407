import { Metadata } from 'next';
import { Container, Typography, Box, Paper } from '@mui/material';
import { PageBuilderRenderer } from '@/components/pagebuilder';

export const metadata: Metadata = {
  title: 'Page Builder Demo | Magento E-commerce Store',
  description: 'Demonstration of Magento 2 Page Builder integration with Next.js',
};

// Sample Page Builder HTML content (as it would come from Magento)
const samplePageBuilderContent = `
<div data-content-type="row" data-appearance="contained" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 10px;">
  <div data-content-type="column-group" data-grid-size="12" data-element="main" style="display: flex; width: 100%; box-sizing: border-box; flex-direction: row; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px; padding: 0px;">
    <div data-content-type="column" data-appearance="full-height" data-background-images="{}" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; width: 100%; margin: 0px; padding: 0px; align-self: stretch;">
      <div data-content-type="heading" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <h1 style="text-align: center;">Welcome to Page Builder Demo</h1>
      </div>
      <div data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <p style="text-align: center;">This is a demonstration of how Magento 2 Page Builder content is rendered in Next.js using our custom parser and components.</p>
      </div>
    </div>
  </div>
</div>

<div data-content-type="row" data-appearance="full-width" data-enable-parallax="0" data-parallax-speed="0.5" data-background-images="{&quot;desktop_image&quot;:&quot;/images/hero-bg.jpg&quot;,&quot;mobile_image&quot;:&quot;/images/hero-bg-mobile.jpg&quot;}" data-background-type="image" data-video-loop="true" data-video-play-only-visible="true" data-video-lazy-load="true" data-video-fallback-src="" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: center center; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; min-height: 400px; margin: 0px 0px 10px; padding: 40px;">
  <div data-content-type="banner" data-appearance="poster" data-show-button="always" data-show-overlay="always" data-overlay-color="rgba(0, 0, 0, 0.4)" data-element="main" style="text-align: center; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
    <div class="pagebuilder-banner-wrapper" data-background-images="{}" data-background-type="image" data-video-loop="true" data-video-play-only-visible="true" data-video-lazy-load="true" data-video-fallback-src="" data-element="wrapper" style="background-position: center center; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; min-height: 300px; padding: 40px;">
      <div class="pagebuilder-overlay pagebuilder-poster-overlay" data-overlay-color="rgba(0, 0, 0, 0.4)" data-element="overlay" style="padding: 0px; background-color: rgba(0, 0, 0, 0.4);">
        <div class="pagebuilder-banner-content">
          <h2 style="color: white; margin-bottom: 20px;">Page Builder Banner</h2>
          <p style="color: white; margin-bottom: 30px;">This banner was created using Magento 2 Page Builder and is now rendered as a React component in Next.js.</p>
          <div class="pagebuilder-banner-button">
            <a href="/products" class="pagebuilder-button-primary" style="background-color: #2196f3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">Shop Now</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div data-content-type="row" data-appearance="contained" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 10px;">
  <div data-content-type="column-group" data-grid-size="12" data-element="main" style="display: flex; width: 100%; box-sizing: border-box; flex-direction: row; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px; padding: 0px;">
    <div data-content-type="column" data-appearance="full-height" data-background-images="{}" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; width: 50%; margin: 0px; padding: 10px; align-self: stretch;">
      <div data-content-type="heading" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <h3>Column 1</h3>
      </div>
      <div data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <p>This is the first column in a two-column layout created with Page Builder.</p>
      </div>
    </div>
    <div data-content-type="column" data-appearance="full-height" data-background-images="{}" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; width: 50%; margin: 0px; padding: 10px; align-self: stretch;">
      <div data-content-type="heading" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <h3>Column 2</h3>
      </div>
      <div data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
        <p>This is the second column. Both columns are responsive and will stack on mobile devices.</p>
      </div>
    </div>
  </div>
</div>

<div data-content-type="row" data-appearance="contained" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 10px;">
  <div data-content-type="divider" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 10px;">
    <hr style="width: 100%; border-color: #e0e0e0; border-width: 1px;">
  </div>
  <div data-content-type="html" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px; margin: 0px 0px 10px; padding: 0px;">
    <div style="text-align: center; padding: 20px; background-color: #f5f5f5; border-radius: 8px;">
      <h4>Custom HTML Block</h4>
      <p>This content was added using the HTML content type in Page Builder and is rendered as-is in the Next.js application.</p>
    </div>
  </div>
</div>
`;

export default function PageBuilderDemo() {
  return (
    <main>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography
          variant="h2"
          component="h1"
          sx={{
            mb: 4,
            textAlign: 'center',
            fontWeight: 700,
            background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Magento 2 Page Builder Demo
        </Typography>

        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            About This Demo
          </Typography>
          <Typography variant="body1" paragraph>
            This page demonstrates how Magento 2 Page Builder content is parsed and rendered 
            in a Next.js application. The content below was created using Magento's Page Builder 
            and is now displayed using our custom React components.
          </Typography>
          <Typography variant="body1" paragraph>
            Features demonstrated:
          </Typography>
          <Box component="ul" sx={{ pl: 3 }}>
            <li>Row and column layouts</li>
            <li>Text and heading elements</li>
            <li>Banner with background images and overlays</li>
            <li>Responsive design</li>
            <li>Custom HTML content</li>
            <li>Dividers and spacing</li>
          </Box>
        </Paper>

        {/* Render Page Builder Content */}
        <PageBuilderRenderer
          content={samplePageBuilderContent}
          config={{
            enableLazyLoading: true,
            imageOptimization: true,
          }}
        />
      </Container>
    </main>
  );
}
