import { Metadata } from 'next';
import { Container, Typography, Box, Paper, Grid, Button } from '@mui/material';
import {
  ProductCardSkeleton,
  ProductGridSkeleton,
  CategoryCardSkeleton,
  BannerSkeleton,
  TextSkeleton,
  CartItemSkeleton,
  OrderItemSkeleton,
  PageSkeleton,
  ListSkeleton,
  PageBuilderRowSkeleton,
  PageBuilderColumnSkeleton,
  PageBuilderBannerSkeleton,
  CmsBlockSkeleton,
  PageBuilderContentSkeleton,
} from '@/components/ui/LoadingSkeleton';

export const metadata: Metadata = {
  title: 'Animated Skeleton Demo | Magento E-commerce Store',
  description: 'Demonstration of animated loading skeletons with shimmer, pulse, and wave effects',
};

export default function SkeletonDemo() {
  return (
    <main>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography
          variant="h2"
          component="h1"
          sx={{
            mb: 4,
            textAlign: 'center',
            fontWeight: 700,
            background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Animated Skeleton Demo
        </Typography>

        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            About Animated Skeletons
          </Typography>
          <Typography variant="body1" paragraph>
            This page demonstrates various animated loading skeletons used throughout the application.
            Each skeleton includes smooth animations and hover effects to provide an engaging loading experience.
          </Typography>
          <Typography variant="body1" paragraph>
            Animation types available:
          </Typography>
          <Box component="ul" sx={{ pl: 3 }}>
            <li><strong>Shimmer:</strong> A smooth shimmer effect that moves across the skeleton</li>
            <li><strong>Pulse:</strong> A gentle pulsing opacity animation</li>
            <li><strong>Wave:</strong> Material-UI's default wave animation</li>
          </Box>
        </Paper>

        {/* Product Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Product Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Single Product Card (Shimmer)
          </Typography>
          <Box sx={{ maxWidth: 300 }}>
            <ProductCardSkeleton animationType="shimmer" />
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Product Grid (Pulse Animation)
          </Typography>
          <ProductGridSkeleton count={4} columns={4} animationType="pulse" />
        </Paper>

        {/* Category Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Category Skeletons
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="h6" gutterBottom>
                Shimmer Effect
              </Typography>
              <CategoryCardSkeleton animationType="shimmer" />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="h6" gutterBottom>
                Pulse Effect
              </Typography>
              <CategoryCardSkeleton animationType="pulse" />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Typography variant="h6" gutterBottom>
                Wave Effect
              </Typography>
              <CategoryCardSkeleton animationType="wave" />
            </Grid>
          </Grid>
        </Paper>

        {/* Banner Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Banner Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Hero Banner Skeleton
          </Typography>
          <BannerSkeleton height={300} animationType="shimmer" />
        </Paper>

        {/* Page Builder Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Page Builder Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Page Builder Row
          </Typography>
          <PageBuilderRowSkeleton animationType="shimmer" />

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Page Builder Columns
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <PageBuilderColumnSkeleton width="50%" animationType="shimmer" />
            <PageBuilderColumnSkeleton width="50%" animationType="shimmer" />
          </Box>

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Page Builder Banner
          </Typography>
          <PageBuilderBannerSkeleton animationType="shimmer" />

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Complete Page Builder Content
          </Typography>
          <PageBuilderContentSkeleton animationType="shimmer" />
        </Paper>

        {/* CMS Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            CMS Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            CMS Block Skeleton
          </Typography>
          <CmsBlockSkeleton animationType="shimmer" />
        </Paper>

        {/* E-commerce Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            E-commerce Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Cart Item Skeleton
          </Typography>
          <CartItemSkeleton animationType="shimmer" />

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            Order Item Skeleton
          </Typography>
          <OrderItemSkeleton animationType="shimmer" />
        </Paper>

        {/* Text and List Skeletons */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Text and List Skeletons
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Text Skeleton (Multiple Lines)
          </Typography>
          <TextSkeleton lines={5} animationType="shimmer" />

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
            List Skeleton
          </Typography>
          <ListSkeleton count={3} height={80} animationType="shimmer" />
        </Paper>

        {/* Full Page Skeleton */}
        <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Full Page Skeleton
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
            This skeleton represents a complete page layout with header, breadcrumbs, sidebar, and content area.
          </Typography>
          <Box sx={{ height: 600, overflow: 'hidden', border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <PageSkeleton animationType="shimmer" />
          </Box>
        </Paper>

        {/* Interactive Demo */}
        <Paper elevation={2} sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Try It Yourself
          </Typography>
          <Typography variant="body1" paragraph>
            These animated skeletons are used throughout the application to provide 
            smooth loading experiences. They automatically appear when content is loading 
            and disappear when real content is ready.
          </Typography>
          <Button
            variant="contained"
            size="large"
            href="/pagebuilder-demo"
            sx={{ mr: 2 }}
          >
            View Page Builder Demo
          </Button>
          <Button
            variant="outlined"
            size="large"
            href="/"
          >
            Back to Homepage
          </Button>
        </Paper>
      </Container>
    </main>
  );
}
