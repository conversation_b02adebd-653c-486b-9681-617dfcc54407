import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Shopping Cart | Magento E-commerce Store',
  description: 'Review your cart items and proceed to checkout.',
};

export default function CartPage() {
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Shopping Cart</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="space-y-4">
              {/* Cart Item */}
              {[1, 2, 3].map((item) => (
                <div key={item} className="bg-white border rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <div className="bg-gray-100 w-20 h-20 rounded-lg flex items-center justify-center">
                      <p className="text-xs text-gray-500">IMG</p>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold">Product Name {item}</h3>
                      <p className="text-gray-600">Size: M, Color: Blue</p>
                      <p className="text-green-600 font-semibold">$29.99</p>
                    </div>
                    <div className="bg-gray-100 p-2 rounded-lg">
                      <p className="text-gray-500">Qty: 1</p>
                    </div>
                    <div className="bg-gray-100 p-2 rounded-lg">
                      <p className="text-gray-500">Remove</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Continue Shopping */}
            <div className="mt-6">
              <div className="bg-gray-100 p-4 rounded-lg">
                <p className="text-gray-500">Continue Shopping Button</p>
              </div>
            </div>
          </div>
          
          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white border rounded-lg p-6 sticky top-4">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              
              <div className="space-y-3 mb-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>$89.97</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>$9.99</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>$8.00</span>
                </div>
                <hr />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>$107.96</span>
                </div>
              </div>
              
              {/* Coupon Code */}
              <div className="mb-4">
                <div className="bg-gray-100 p-3 rounded-lg">
                  <p className="text-gray-500">Coupon Code Input</p>
                </div>
              </div>
              
              {/* Checkout Button */}
              <div className="bg-blue-600 text-white p-4 rounded-lg text-center">
                <p>Proceed to Checkout</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
