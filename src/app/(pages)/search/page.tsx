import { Metadata } from 'next';
import { Suspense } from 'react';
import SearchResults from '@/components/search/SearchResults';
import SearchFilters from '@/components/search/SearchFilters';
import { Container, Grid, Box, Typography } from '@mui/material';

// Generate metadata for search page
export async function generateMetadata({
  searchParams,
}: {
  searchParams: { q?: string };
}): Promise<Metadata> {
  const query = searchParams.q || '';
  
  return {
    title: query ? `Search results for "${query}" | Magento E-commerce Store` : 'Search | Magento E-commerce Store',
    description: query 
      ? `Find products matching "${query}". Browse our extensive catalog with advanced search and filtering options.`
      : 'Search our extensive product catalog. Find exactly what you\'re looking for with advanced search and filtering options.',
    openGraph: {
      title: query ? `Search results for "${query}"` : 'Product Search',
      description: query 
        ? `Find products matching "${query}"`
        : 'Search our extensive product catalog',
      type: 'website',
    },
  };
}

// Search page component
export default function SearchPage({
  searchParams,
}: {
  searchParams: { 
    q?: string;
    page?: string;
    sort?: string;
    category?: string;
    price_min?: string;
    price_max?: string;
    [key: string]: string | undefined;
  };
}) {
  const query = searchParams.q || '';
  const currentPage = parseInt(searchParams.page || '1', 10);
  const sortBy = searchParams.sort || 'relevance';
  
  // Build filters from search params
  const filters = {
    category: searchParams.category,
    price_min: searchParams.price_min ? parseFloat(searchParams.price_min) : undefined,
    price_max: searchParams.price_max ? parseFloat(searchParams.price_max) : undefined,
    // Add more filters as needed
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {query ? `Search Results for "${query}"` : 'Product Search'}
        </Typography>
        {query && (
          <Typography variant="body1" color="text.secondary">
            Find products matching your search criteria
          </Typography>
        )}
      </Box>

      <Grid container spacing={4}>
        {/* Filters Sidebar */}
        <Grid item xs={12} md={3}>
          <Suspense fallback={<div>Loading filters...</div>}>
            <SearchFilters
              query={query}
              currentFilters={filters}
              currentSort={sortBy}
            />
          </Suspense>
        </Grid>

        {/* Search Results */}
        <Grid item xs={12} md={9}>
          <Suspense fallback={<div>Loading results...</div>}>
            <SearchResults
              query={query}
              currentPage={currentPage}
              sortBy={sortBy}
              filters={filters}
            />
          </Suspense>
        </Grid>
      </Grid>
    </Container>
  );
}
