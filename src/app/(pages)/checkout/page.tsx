import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Checkout | Magento E-commerce Store',
  description: 'Complete your purchase securely and quickly.',
};

export default function CheckoutPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Checkout</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Checkout Form */}
          <div className="space-y-6">
            {/* Step 1: Shipping Information */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">1. Shipping Information</h2>
              <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Shipping Address Form</p>
              </div>
            </div>
            
            {/* Step 2: Shipping Method */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">2. Shipping Method</h2>
              <div className="bg-gray-100 h-32 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Shipping Options</p>
              </div>
            </div>
            
            {/* Step 3: Payment Information */}
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">3. Payment Information</h2>
              <div className="bg-gray-100 h-48 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Payment Form</p>
              </div>
            </div>
          </div>
          
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 sticky top-4">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              
              {/* Order Items */}
              <div className="space-y-3 mb-6">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="flex items-center space-x-3">
                    <div className="bg-gray-100 w-12 h-12 rounded-lg flex items-center justify-center">
                      <p className="text-xs text-gray-500">IMG</p>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Product {item}</p>
                      <p className="text-sm text-gray-600">Qty: 1</p>
                    </div>
                    <p className="font-semibold">$29.99</p>
                  </div>
                ))}
              </div>
              
              {/* Pricing Breakdown */}
              <div className="space-y-2 mb-4 pt-4 border-t">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>$89.97</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>$9.99</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>$8.00</span>
                </div>
                <hr />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>$107.96</span>
                </div>
              </div>
              
              {/* Place Order Button */}
              <div className="bg-green-600 text-white p-4 rounded-lg text-center">
                <p className="font-semibold">Place Order</p>
              </div>
              
              {/* Security Notice */}
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  🔒 Your payment information is secure and encrypted
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
