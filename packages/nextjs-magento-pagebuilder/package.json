{"name": "@magento/nextjs-pagebuilder", "version": "1.0.0", "description": "Next.js server-side Page Builder package for Magento 2 PWA integration", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc && npm run build:css", "build:css": "postcss src/styles/*.css --dir dist/styles", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["magento", "pagebuilder", "nextjs", "react", "ssr", "pwa", "ecommerce"], "author": "Magento Community", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/magento/nextjs-pagebuilder.git"}, "bugs": {"url": "https://github.com/magento/nextjs-pagebuilder/issues"}, "homepage": "https://github.com/magento/nextjs-pagebuilder#readme", "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "next": ">=14.0.0"}, "dependencies": {"cheerio": "^1.0.0-rc.12", "classnames": "^2.3.2", "css-tree": "^2.3.1", "he": "^1.2.0", "lodash.merge": "^4.6.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/he": "^1.2.3", "@types/jest": "^29.5.8", "@types/lodash.merge": "^4.6.9", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "postcss": "^8.4.31", "postcss-cli": "^10.1.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}