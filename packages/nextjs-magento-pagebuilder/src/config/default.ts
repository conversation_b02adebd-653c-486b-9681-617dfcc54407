// Default configuration for Page Builder

import { PageBuilderParserConfig, PageBuilderElementType } from '../types';

export const DEFAULT_CONFIG: PageBuilderParserConfig = {
  enableServerSideRendering: true,
  enableImageOptimization: true,
  imageLoader: (src: string) => src,
  componentOverrides: {},
  customElementTypes: {},
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  defaultImageQuality: 75,
  enableLazyLoading: true,
  enableDebugMode: false,
  cssPrefix: 'pagebuilder',
  allowedHtmlTags: [
    'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'a', 'img', 'video', 'iframe', 'button', 'ul', 'ol', 'li',
    'table', 'thead', 'tbody', 'tr', 'td', 'th', 'strong', 'em',
    'br', 'hr', 'blockquote', 'pre', 'code', 'figure', 'figcaption'
  ],
  sanitizeHtml: true,
};

// Default element type mapping
export const DEFAULT_ELEMENT_TYPES = Object.values(PageBuilderElementType);

// CSS class mappings
export const CSS_CLASSES = {
  container: 'pagebuilder-container',
  element: 'pagebuilder-element',
  row: 'pagebuilder-row',
  column: 'pagebuilder-column',
  columnGroup: 'pagebuilder-column-group',
  text: 'pagebuilder-text',
  heading: 'pagebuilder-heading',
  image: 'pagebuilder-image',
  button: 'pagebuilder-button',
  banner: 'pagebuilder-banner',
  video: 'pagebuilder-video',
  divider: 'pagebuilder-divider',
  html: 'pagebuilder-html',
  tabs: 'pagebuilder-tabs',
  tabItem: 'pagebuilder-tab-item',
  slider: 'pagebuilder-slider',
  slide: 'pagebuilder-slide',
  products: 'pagebuilder-products',
  map: 'pagebuilder-map',
  block: 'pagebuilder-block',
  dynamicBlock: 'pagebuilder-dynamic-block',
} as const;

// Responsive breakpoint queries
export const MEDIA_QUERIES = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)',
} as const;

// Default spacing values
export const DEFAULT_SPACING = {
  none: '0',
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '3rem',
  '3xl': '4rem',
} as const;

// Default border radius values
export const DEFAULT_BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px',
} as const;

// Default shadow values
export const DEFAULT_SHADOWS = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
} as const;

// Animation durations
export const ANIMATION_DURATIONS = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
} as const;

// Z-index scale
export const Z_INDEX = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;
