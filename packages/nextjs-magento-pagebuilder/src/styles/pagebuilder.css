/* Page Builder Base Styles */

.pagebuilder-container {
  width: 100%;
  box-sizing: border-box;
}

.pagebuilder-element {
  box-sizing: border-box;
}

/* Row Styles */
.pagebuilder-row {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.pagebuilder-row--contained {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.pagebuilder-row--full-width {
  max-width: 100%;
  margin: 0;
}

.pagebuilder-row--full-bleed {
  width: 100vw;
  margin-left: 50%;
  transform: translateX(-50%);
}

.pagebuilder-row-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Column Styles */
.pagebuilder-column {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  min-width: 0;
  position: relative;
}

.pagebuilder-column-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
}

/* Text Styles */
.pagebuilder-text {
  width: 100%;
}

.pagebuilder-text--align-left {
  text-align: left;
}

.pagebuilder-text--align-center {
  text-align: center;
}

.pagebuilder-text--align-right {
  text-align: right;
}

.pagebuilder-text--align-justify {
  text-align: justify;
}

/* Heading Styles */
.pagebuilder-heading {
  width: 100%;
  margin: 0;
}

.pagebuilder-heading--align-left {
  text-align: left;
}

.pagebuilder-heading--align-center {
  text-align: center;
}

.pagebuilder-heading--align-right {
  text-align: right;
}

/* Image Styles */
.pagebuilder-image {
  display: block;
  width: 100%;
}

.pagebuilder-image img {
  width: 100%;
  height: auto;
  display: block;
}

.pagebuilder-image-link {
  display: block;
  text-decoration: none;
}

.pagebuilder-image-caption {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #666;
  text-align: center;
}

/* Button Styles */
.pagebuilder-button {
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
}

.pagebuilder-button--primary {
  background-color: #007bff;
  color: #ffffff;
}

.pagebuilder-button--primary:hover {
  background-color: #0056b3;
}

.pagebuilder-button--secondary {
  background-color: #6c757d;
  color: #ffffff;
}

.pagebuilder-button--secondary:hover {
  background-color: #545b62;
}

.pagebuilder-button--link {
  background-color: transparent;
  color: #007bff;
  text-decoration: underline;
}

.pagebuilder-button--link:hover {
  color: #0056b3;
}

.pagebuilder-button--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.pagebuilder-button--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.pagebuilder-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Banner Styles */
.pagebuilder-banner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  overflow: hidden;
}

.pagebuilder-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.pagebuilder-banner-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  padding: 2rem;
}

.pagebuilder-banner-message {
  margin-bottom: 1rem;
}

.pagebuilder-banner-button {
  margin-top: 1rem;
}

/* Video Styles */
.pagebuilder-video {
  width: 100%;
}

.pagebuilder-video video {
  width: 100%;
  height: auto;
}

/* HTML Styles */
.pagebuilder-html {
  width: 100%;
}

/* Divider Styles */
.pagebuilder-divider {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pagebuilder-divider hr {
  border: none;
  margin: 0;
}

/* Slider Styles */
.pagebuilder-slider {
  width: 100%;
  position: relative;
}

/* Tabs Styles */
.pagebuilder-tabs {
  width: 100%;
}

/* Products Styles */
.pagebuilder-products {
  width: 100%;
}

/* Block Styles */
.pagebuilder-block {
  width: 100%;
}

/* Responsive Styles */
@media (max-width: 767px) {
  .pagebuilder-row--contained {
    padding: 0 0.5rem;
  }
  
  .pagebuilder-column-group {
    flex-direction: column;
  }
  
  .pagebuilder-column {
    width: 100% !important;
    margin-bottom: 1rem;
  }
  
  .pagebuilder-banner-content {
    padding: 1rem;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .pagebuilder-row--contained {
    padding: 0 1rem;
  }
}

/* Utility Classes */
.pagebuilder-element--unknown {
  padding: 1rem;
  border: 1px dashed #ccc;
  border-radius: 4px;
  background-color: #f5f5f5;
  text-align: center;
  color: #666;
}
