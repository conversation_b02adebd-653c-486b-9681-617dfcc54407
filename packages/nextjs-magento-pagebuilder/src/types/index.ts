// Core Page Builder Types for Next.js

import { ReactNode, CSSProperties } from 'react';

// Base Page Builder element interface
export interface PageBuilderElement {
  type: PageBuilderElementType;
  id: string;
  attributes: Record<string, any>;
  styles: PageBuilderStyles;
  children?: PageBuilderElement[];
  content?: string;
  rawHtml?: string;
  metadata?: PageBuilderElementMetadata;
}

// Page Builder element metadata
export interface PageBuilderElementMetadata {
  appearance?: string;
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: 'cover' | 'contain' | 'auto';
  backgroundPosition?: string;
  backgroundAttachment?: 'scroll' | 'fixed';
  backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
  minHeight?: string;
  verticalAlignment?: 'top' | 'middle' | 'bottom';
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  border?: string;
  borderColor?: string;
  borderWidth?: string;
  borderRadius?: string;
  margins?: PageBuilderSpacing;
  padding?: PageBuilderSpacing;
}

// Page Builder spacing
export interface PageBuilderSpacing {
  top?: string;
  right?: string;
  bottom?: string;
  left?: string;
}

// Page Builder styles
export interface PageBuilderStyles {
  desktop?: CSSProperties;
  tablet?: CSSProperties;
  mobile?: CSSProperties;
  [key: string]: CSSProperties | undefined;
}

// Page Builder element types
export enum PageBuilderElementType {
  ROW = 'row',
  COLUMN = 'column',
  COLUMN_GROUP = 'column-group',
  TEXT = 'text',
  HEADING = 'heading',
  IMAGE = 'image',
  BUTTON = 'button',
  BUTTON_ITEM = 'button-item',
  BANNER = 'banner',
  SLIDER = 'slider',
  SLIDE = 'slide',
  PRODUCTS = 'products',
  VIDEO = 'video',
  MAP = 'map',
  BLOCK = 'block',
  DYNAMIC_BLOCK = 'dynamic_block',
  HTML = 'html',
  DIVIDER = 'divider',
  TABS = 'tabs',
  TAB_ITEM = 'tab-item',
}

// Page Builder content structure
export interface PageBuilderContent {
  elements: PageBuilderElement[];
  rawHtml?: string;
  version?: string;
  metadata?: {
    generatedAt?: string;
    source?: 'magento' | 'custom';
    [key: string]: any;
  };
}

// Parser configuration
export interface PageBuilderParserConfig {
  enableServerSideRendering: boolean;
  enableImageOptimization: boolean;
  imageLoader?: (src: string) => string;
  componentOverrides?: Record<PageBuilderElementType, React.ComponentType<any>>;
  customElementTypes?: Record<string, React.ComponentType<any>>;
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  defaultImageQuality: number;
  enableLazyLoading: boolean;
  enableDebugMode: boolean;
  cssPrefix: string;
  allowedHtmlTags: string[];
  sanitizeHtml: boolean;
}

// Context for Page Builder components
export interface PageBuilderContext {
  config: PageBuilderParserConfig;
  isEditing?: boolean;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
  parentElement?: PageBuilderElement;
}

// Component props interface
export interface PageBuilderComponentProps {
  element: PageBuilderElement;
  children?: ReactNode;
  className?: string;
  style?: CSSProperties;
}

// Renderer props
export interface PageBuilderRendererProps {
  content: PageBuilderContent | string;
  config?: Partial<PageBuilderParserConfig>;
  className?: string;
  style?: CSSProperties;
  isEditing?: boolean;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}

// Parser result
export interface ParseResult {
  success: boolean;
  content?: PageBuilderContent;
  error?: Error;
  warnings?: string[];
}

// Element parser interface
export interface ElementParser {
  canParse(element: Element): boolean;
  parse(element: Element, config: PageBuilderParserConfig): PageBuilderElement | null;
}

// Style parser interface
export interface StyleParser {
  parseInlineStyles(styleString: string): CSSProperties;
  parseResponsiveStyles(element: Element): PageBuilderStyles;
  extractBackgroundStyles(element: Element): Partial<PageBuilderElementMetadata>;
}

// Image configuration
export interface ImageConfig {
  src: string;
  alt?: string;
  title?: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

// Video configuration
export interface VideoConfig {
  src: string;
  poster?: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
}

// Button configuration
export interface ButtonConfig {
  text: string;
  link?: string;
  target?: '_self' | '_blank' | '_parent' | '_top';
  type?: 'primary' | 'secondary' | 'link';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
}

// Banner configuration
export interface BannerConfig {
  appearance?: 'poster' | 'collage-left' | 'collage-centered' | 'collage-right';
  minHeight?: string;
  showButton?: 'always' | 'hover' | 'never';
  showOverlay?: 'always' | 'hover' | 'never';
  overlayColor?: string;
  buttonText?: string;
  buttonType?: 'primary' | 'secondary' | 'link';
  link?: string;
  linkTarget?: '_self' | '_blank';
  message?: string;
}

// Export utility types
export type PageBuilderElementMap = Record<PageBuilderElementType, React.ComponentType<PageBuilderComponentProps>>;
export type CustomElementMap = Record<string, React.ComponentType<PageBuilderComponentProps>>;
export type ResponsiveValue<T> = T | { mobile?: T; tablet?: T; desktop?: T };
