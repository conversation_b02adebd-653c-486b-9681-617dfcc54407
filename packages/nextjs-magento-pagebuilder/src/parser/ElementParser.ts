// Element-specific parser for Page Builder elements

import * as cheerio from 'cheerio';
import { 
  PageBuilderElement, 
  PageBuilderParserConfig,
  PageBuilderElementType,
  ImageConfig,
  VideoConfig,
  ButtonConfig,
  BannerConfig 
} from '../types';
import { logParsingWarning, parseBackgroundImage, formatSpacing } from '../utils';

export class ElementParser {
  private config: PageBuilderParserConfig;

  constructor(config: PageBuilderParserConfig) {
    this.config = config;
  }

  /**
   * Parse element with type-specific logic
   */
  public parseElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement | null {
    switch (baseElement.type) {
      case PageBuilderElementType.ROW:
        return this.parseRowElement($, $element, baseElement);
      
      case PageBuilderElementType.COLUMN:
        return this.parseColumnElement($, $element, baseElement);
      
      case PageBuilderElementType.TEXT:
        return this.parseTextElement($, $element, baseElement);
      
      case PageBuilderElementType.HEADING:
        return this.parseHeadingElement($, $element, baseElement);
      
      case PageBuilderElementType.IMAGE:
        return this.parseImageElement($, $element, baseElement);
      
      case PageBuilderElementType.BUTTON:
        return this.parseButtonElement($, $element, baseElement);
      
      case PageBuilderElementType.BANNER:
        return this.parseBannerElement($, $element, baseElement);
      
      case PageBuilderElementType.VIDEO:
        return this.parseVideoElement($, $element, baseElement);
      
      case PageBuilderElementType.SLIDER:
        return this.parseSliderElement($, $element, baseElement);
      
      case PageBuilderElementType.PRODUCTS:
        return this.parseProductsElement($, $element, baseElement);
      
      case PageBuilderElementType.HTML:
        return this.parseHtmlElement($, $element, baseElement);
      
      case PageBuilderElementType.DIVIDER:
        return this.parseDividerElement($, $element, baseElement);
      
      case PageBuilderElementType.TABS:
        return this.parseTabsElement($, $element, baseElement);
      
      default:
        return baseElement;
    }
  }

  /**
   * Parse row element
   */
  private parseRowElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const appearance = $element.attr('data-appearance') || 'contained';
    const enableParallax = $element.attr('data-enable-parallax') === '1';
    const parallaxSpeed = parseFloat($element.attr('data-parallax-speed') || '0.5');
    const minHeight = $element.attr('data-min-height') || 'auto';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        appearance,
        enableParallax,
        parallaxSpeed,
        minHeight,
        verticalAlignment: $element.attr('data-vertical-alignment') as any,
      },
    };
  }

  /**
   * Parse column element
   */
  private parseColumnElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const width = $element.attr('data-width') || '100%';
    const appearance = $element.attr('data-appearance') || 'full-height';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        width,
        appearance,
        verticalAlignment: $element.attr('data-vertical-alignment') as any,
      },
    };
  }

  /**
   * Parse text element
   */
  private parseTextElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    return {
      ...baseElement,
      content: $element.html() || '',
      metadata: {
        ...baseElement.metadata,
        textAlign: $element.attr('data-text-align') as any,
      },
    };
  }

  /**
   * Parse heading element
   */
  private parseHeadingElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const headingType = $element.attr('data-heading-type') || 'h2';
    
    return {
      ...baseElement,
      content: $element.html() || '',
      metadata: {
        ...baseElement.metadata,
        headingType,
        textAlign: $element.attr('data-text-align') as any,
      },
    };
  }

  /**
   * Parse image element
   */
  private parseImageElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const $img = $element.find('img').first();
    
    const imageConfig: ImageConfig = {
      src: $img.attr('src') || $element.attr('data-src') || '',
      alt: $img.attr('alt') || '',
      title: $img.attr('title') || '',
      width: parseInt($img.attr('width') || '0') || undefined,
      height: parseInt($img.attr('height') || '0') || undefined,
      loading: $img.attr('loading') as any || (this.config.enableLazyLoading ? 'lazy' : 'eager'),
    };

    // Extract link if image is wrapped in anchor
    const $link = $element.find('a').first();
    const link = $link.length ? {
      href: $link.attr('href') || '',
      target: $link.attr('target') || '_self',
      title: $link.attr('title') || '',
    } : undefined;

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        image: imageConfig,
        link,
        caption: $element.find('figcaption').text() || undefined,
      },
    };
  }

  /**
   * Parse button element
   */
  private parseButtonElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const $button = $element.find('button, a').first();
    
    const buttonConfig: ButtonConfig = {
      text: $button.text() || '',
      link: $button.attr('href') || '',
      target: $button.attr('target') as any || '_self',
      type: $element.attr('data-button-type') as any || 'primary',
      size: $element.attr('data-button-size') as any || 'medium',
      disabled: $button.attr('disabled') !== undefined,
    };

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        button: buttonConfig,
      },
    };
  }

  /**
   * Parse banner element
   */
  private parseBannerElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const $content = $element.find('[data-element="content"]').first();
    const $button = $element.find('[data-element="button"]').first();
    
    const bannerConfig: BannerConfig = {
      appearance: $element.attr('data-appearance') as any || 'poster',
      minHeight: $element.attr('data-min-height') || '300px',
      showButton: $element.attr('data-show-button') as any || 'always',
      showOverlay: $element.attr('data-show-overlay') as any || 'never',
      overlayColor: $element.attr('data-overlay-color') || 'rgba(0,0,0,0.4)',
      buttonText: $button.text() || '',
      buttonType: $button.attr('data-button-type') as any || 'primary',
      link: $button.attr('href') || '',
      linkTarget: $button.attr('target') as any || '_self',
      message: $content.html() || '',
    };

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        banner: bannerConfig,
      },
    };
  }

  /**
   * Parse video element
   */
  private parseVideoElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const $video = $element.find('video').first();
    
    const videoConfig: VideoConfig = {
      src: $video.attr('src') || $element.attr('data-video-src') || '',
      poster: $video.attr('poster') || '',
      width: parseInt($video.attr('width') || '0') || undefined,
      height: parseInt($video.attr('height') || '0') || undefined,
      autoplay: $video.attr('autoplay') !== undefined,
      loop: $video.attr('loop') !== undefined,
      muted: $video.attr('muted') !== undefined,
      controls: $video.attr('controls') !== undefined,
      preload: $video.attr('preload') as any || 'metadata',
    };

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        video: videoConfig,
      },
    };
  }

  /**
   * Parse slider element
   */
  private parseSliderElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const autoplay = $element.attr('data-autoplay') === '1';
    const autoplaySpeed = parseInt($element.attr('data-autoplay-speed') || '4000');
    const fade = $element.attr('data-fade') === '1';
    const infinite = $element.attr('data-infinite-loop') === '1';
    const showArrows = $element.attr('data-show-arrows') === '1';
    const showDots = $element.attr('data-show-dots') === '1';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        slider: {
          autoplay,
          autoplaySpeed,
          fade,
          infinite,
          showArrows,
          showDots,
        },
      },
    };
  }

  /**
   * Parse products element
   */
  private parseProductsElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const appearance = $element.attr('data-appearance') || 'grid';
    const productsCount = parseInt($element.attr('data-products-count') || '5');
    const condition = $element.attr('data-condition-type') || 'all';
    const sortOrder = $element.attr('data-sort-order') || 'position';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        products: {
          appearance,
          productsCount,
          condition,
          sortOrder,
        },
      },
    };
  }

  /**
   * Parse HTML element
   */
  private parseHtmlElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    return {
      ...baseElement,
      content: $element.html() || '',
    };
  }

  /**
   * Parse divider element
   */
  private parseDividerElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const thickness = $element.attr('data-thickness') || '1px';
    const color = $element.attr('data-color') || '#e5e5e5';
    const width = $element.attr('data-width') || '100%';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        divider: {
          thickness,
          color,
          width,
        },
      },
    };
  }

  /**
   * Parse tabs element
   */
  private parseTabsElement(
    $: cheerio.CheerioAPI, 
    $element: cheerio.Cheerio<cheerio.Element>, 
    baseElement: PageBuilderElement
  ): PageBuilderElement {
    const defaultTab = parseInt($element.attr('data-default-tab') || '0');
    const tabNavigation = $element.attr('data-tab-navigation') || 'tabs';

    return {
      ...baseElement,
      metadata: {
        ...baseElement.metadata,
        tabs: {
          defaultTab,
          tabNavigation,
        },
      },
    };
  }
}
