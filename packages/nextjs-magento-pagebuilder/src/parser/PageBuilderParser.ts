// Main Page Builder Parser for Next.js

import * as cheerio from 'cheerio';
import {
  PageBuilderContent,
  PageBuilderElement,
  PageBuilderParserConfig,
  ParseResult,
  PageBuilderElementType,
} from '../types';
import {
  mergeConfig,
  generateElementId,
  isPageBuilderElement,
  getContentType,
  logParsingError,
  logParsingWarning,
  sanitizeHtml,
} from '../utils';
import { ElementParser } from './ElementParser';
import { StyleParser } from './StyleParser';

export class PageBuilderParser {
  private config: PageBuilderParserConfig;
  private elementParser: ElementParser;
  private styleParser: StyleParser;

  constructor(config: Partial<PageBuilderParserConfig> = {}) {
    this.config = mergeConfig(config);
    this.elementParser = new ElementParser(this.config);
    this.styleParser = new StyleParser(this.config);
  }

  /**
   * Parse HTML string into PageBuilderContent
   */
  public parse(html: string): ParseResult {
    try {
      // Sanitize HTML if enabled
      const sanitizedHtml = this.config.sanitizeHtml
        ? sanitizeHtml(html, this.config.allowedHtmlTags)
        : html;

      // Use cheerio for server-side parsing
      const $ = cheerio.load(sanitizedHtml, {
        xmlMode: false,
        decodeEntities: true,
      });

      // Extract Page Builder elements
      const elements = this.parseElements($, $('body'));

      const content: PageBuilderContent = {
        elements,
        rawHtml: sanitizedHtml,
        version: this.extractPageBuilderVersion(sanitizedHtml),
        metadata: {
          generatedAt: new Date().toISOString(),
          source: 'magento',
        },
      };

      return {
        success: true,
        content,
        warnings: [],
      };
    } catch (error) {
      logParsingError(error as Error, 'PageBuilderParser.parse');

      return {
        success: false,
        error: error as Error,
        content: {
          elements: [],
          rawHtml: html,
        },
      };
    }
  }

  /**
   * Parse elements from a cheerio selection
   */
  private parseElements(
    $: cheerio.CheerioAPI,
    container: cheerio.Cheerio<cheerio.Element>
  ): PageBuilderElement[] {
    const elements: PageBuilderElement[] = [];

    container.children().each((index, child) => {
      const $child = $(child);

      if (this.isPageBuilderElement($child)) {
        const element = this.parseElement($, $child);
        if (element) {
          // Parse children recursively
          element.children = this.parseElements($, $child);
          elements.push(element);
        }
      } else {
        // Check if this element contains Page Builder elements
        const nestedElements = this.parseElements($, $child);
        elements.push(...nestedElements);
      }
    });

    return elements;
  }

  /**
   * Parse a single Page Builder element
   */
  private parseElement(
    $: cheerio.CheerioAPI,
    $element: cheerio.Cheerio<cheerio.Element>
  ): PageBuilderElement | null {
    try {
      const contentType = this.getContentType($element);
      if (!contentType) {
        logParsingWarning('Element without content type found', 'parseElement');
        return null;
      }

      // Map content type to element type
      const elementType = this.mapContentTypeToElementType(contentType);

      // Extract basic element data
      const element: PageBuilderElement = {
        type: elementType,
        id: this.generateElementId($element),
        attributes: this.extractAttributes($element),
        styles: this.styleParser.parseResponsiveStyles($element),
        content: this.extractContent($, $element),
        rawHtml: $.html($element),
        metadata: this.styleParser.extractBackgroundStyles($element),
      };

      // Use specialized parser if available
      const parsedElement = this.elementParser.parseElement(
        $,
        $element,
        element
      );

      return parsedElement || element;
    } catch (error) {
      logParsingError(error as Error, 'parseElement');
      return null;
    }
  }

  /**
   * Check if element is a Page Builder element
   */
  private isPageBuilderElement(
    $element: cheerio.Cheerio<cheerio.Element>
  ): boolean {
    const element = $element.get(0);
    if (!element) return false;

    return (
      element.attribs['data-content-type'] !== undefined ||
      (element.attribs.class &&
        element.attribs.class.includes('pagebuilder-')) ||
      element.attribs['data-pb-style'] !== undefined
    );
  }

  /**
   * Get content type from element
   */
  private getContentType(
    $element: cheerio.Cheerio<cheerio.Element>
  ): string | null {
    return (
      $element.attr('data-content-type') ||
      $element.attr('data-element') ||
      null
    );
  }

  /**
   * Map Magento content type to our element type
   */
  private mapContentTypeToElementType(
    contentType: string
  ): PageBuilderElementType {
    const mapping: Record<string, PageBuilderElementType> = {
      row: PageBuilderElementType.ROW,
      column: PageBuilderElementType.COLUMN,
      'column-group': PageBuilderElementType.COLUMN_GROUP,
      text: PageBuilderElementType.TEXT,
      heading: PageBuilderElementType.HEADING,
      image: PageBuilderElementType.IMAGE,
      button: PageBuilderElementType.BUTTON,
      'button-item': PageBuilderElementType.BUTTON_ITEM,
      banner: PageBuilderElementType.BANNER,
      slider: PageBuilderElementType.SLIDER,
      slide: PageBuilderElementType.SLIDE,
      products: PageBuilderElementType.PRODUCTS,
      video: PageBuilderElementType.VIDEO,
      map: PageBuilderElementType.MAP,
      block: PageBuilderElementType.BLOCK,
      dynamic_block: PageBuilderElementType.DYNAMIC_BLOCK,
      html: PageBuilderElementType.HTML,
      divider: PageBuilderElementType.DIVIDER,
      tabs: PageBuilderElementType.TABS,
      'tab-item': PageBuilderElementType.TAB_ITEM,
    };

    return mapping[contentType] || PageBuilderElementType.HTML;
  }

  /**
   * Generate element ID
   */
  private generateElementId(
    $element: cheerio.Cheerio<cheerio.Element>
  ): string {
    const existingId = $element.attr('id');
    if (existingId) {
      return existingId;
    }

    const dataId = $element.attr('data-element-id');
    if (dataId) {
      return dataId;
    }

    return generateElementId();
  }

  /**
   * Extract all attributes from element
   */
  private extractAttributes(
    $element: cheerio.Cheerio<cheerio.Element>
  ): Record<string, any> {
    const attributes: Record<string, any> = {};
    const element = $element.get(0);

    if (element && element.attribs) {
      Object.entries(element.attribs).forEach(([key, value]) => {
        // Skip style and class attributes as they're handled separately
        if (key !== 'style' && key !== 'class') {
          attributes[key] = value;
        }
      });
    }

    return attributes;
  }

  /**
   * Extract text content from element
   */
  private extractContent(
    $: cheerio.CheerioAPI,
    $element: cheerio.Cheerio<cheerio.Element>
  ): string {
    // For text and heading elements, get the inner HTML
    const contentType = this.getContentType($element);

    if (contentType === 'text' || contentType === 'heading') {
      return $element.html() || '';
    }

    // For other elements, get text content
    return $element.text().trim();
  }

  /**
   * Extract Page Builder version from HTML
   */
  private extractPageBuilderVersion(html: string): string | undefined {
    const versionMatch = html.match(/data-pb-version="([^"]+)"/);
    return versionMatch ? versionMatch[1] : undefined;
  }

  /**
   * Static method for server-side parsing
   */
  public static parseServerSide(
    html: string,
    config: Partial<PageBuilderParserConfig> = {}
  ): ParseResult {
    const parser = new PageBuilderParser(config);
    return parser.parse(html);
  }

  /**
   * Static method for client-side parsing (fallback)
   */
  public static parseClientSide(
    html: string,
    config: Partial<PageBuilderParserConfig> = {}
  ): ParseResult {
    // For client-side, we can use the same parser since cheerio works in both environments
    return PageBuilderParser.parseServerSide(html, config);
  }

  /**
   * Parse content with fallback to raw HTML
   */
  public static parseWithFallback(
    html: string,
    config: Partial<PageBuilderParserConfig> = {}
  ): PageBuilderContent {
    const result = PageBuilderParser.parseServerSide(html, config);

    if (result.success && result.content) {
      return result.content;
    }

    // Fallback to raw HTML
    return {
      elements: [],
      rawHtml: html,
      metadata: {
        generatedAt: new Date().toISOString(),
        source: 'custom',
      },
    };
  }
}
