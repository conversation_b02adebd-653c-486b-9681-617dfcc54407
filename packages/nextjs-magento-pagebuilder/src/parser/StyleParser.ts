// Style parser for Page Builder elements

import * as cheerio from 'cheerio';
import { CSSProperties } from 'react';
import { 
  PageBuilderStyles, 
  PageBuilderParserConfig,
  PageBuilderElementMetadata,
  PageBuilderSpacing 
} from '../types';
import { 
  parseCssString, 
  parseBackgroundImage, 
  formatSpacing,
  logParsingWarning 
} from '../utils';

export class StyleParser {
  private config: PageBuilderParserConfig;

  constructor(config: PageBuilderParserConfig) {
    this.config = config;
  }

  /**
   * Parse inline styles from element
   */
  public parseInlineStyles(styleString: string): CSSProperties {
    if (!styleString) return {};
    
    try {
      return parseCssString(styleString);
    } catch (error) {
      logParsingWarning(`Failed to parse inline styles: ${styleString}`);
      return {};
    }
  }

  /**
   * Parse responsive styles from element
   */
  public parseResponsiveStyles($element: cheerio.Cheerio<cheerio.Element>): PageBuilderStyles {
    const styles: PageBuilderStyles = {};
    
    // Parse desktop styles (default)
    const desktopStyle = $element.attr('style');
    if (desktopStyle) {
      styles.desktop = this.parseInlineStyles(desktopStyle);
    }

    // Parse mobile styles
    const mobileStyle = $element.attr('data-mobile-style');
    if (mobileStyle) {
      styles.mobile = this.parseInlineStyles(mobileStyle);
    }

    // Parse tablet styles
    const tabletStyle = $element.attr('data-tablet-style');
    if (tabletStyle) {
      styles.tablet = this.parseInlineStyles(tabletStyle);
    }

    // Parse styles from data attributes
    this.parseDataAttributeStyles($element, styles);

    return styles;
  }

  /**
   * Extract background styles from element
   */
  public extractBackgroundStyles($element: cheerio.Cheerio<cheerio.Element>): Partial<PageBuilderElementMetadata> {
    const metadata: Partial<PageBuilderElementMetadata> = {};
    
    // Extract from style attribute
    const style = $element.attr('style');
    if (style) {
      const parsedStyles = this.parseInlineStyles(style);
      
      if (parsedStyles.backgroundColor) {
        metadata.backgroundColor = parsedStyles.backgroundColor as string;
      }
      
      if (parsedStyles.backgroundImage) {
        const imageUrl = parseBackgroundImage(parsedStyles.backgroundImage as string);
        if (imageUrl) {
          metadata.backgroundImage = imageUrl;
        }
      }
      
      if (parsedStyles.backgroundSize) {
        metadata.backgroundSize = parsedStyles.backgroundSize as any;
      }
      
      if (parsedStyles.backgroundPosition) {
        metadata.backgroundPosition = parsedStyles.backgroundPosition as string;
      }
      
      if (parsedStyles.backgroundAttachment) {
        metadata.backgroundAttachment = parsedStyles.backgroundAttachment as any;
      }
      
      if (parsedStyles.backgroundRepeat) {
        metadata.backgroundRepeat = parsedStyles.backgroundRepeat as any;
      }
      
      if (parsedStyles.minHeight) {
        metadata.minHeight = parsedStyles.minHeight as string;
      }
    }

    // Extract from data attributes
    const bgColor = $element.attr('data-background-color');
    if (bgColor) {
      metadata.backgroundColor = bgColor;
    }

    const bgImage = $element.attr('data-background-image');
    if (bgImage) {
      metadata.backgroundImage = bgImage;
    }

    const bgSize = $element.attr('data-background-size');
    if (bgSize) {
      metadata.backgroundSize = bgSize as any;
    }

    const bgPosition = $element.attr('data-background-position');
    if (bgPosition) {
      metadata.backgroundPosition = bgPosition;
    }

    const bgAttachment = $element.attr('data-background-attachment');
    if (bgAttachment) {
      metadata.backgroundAttachment = bgAttachment as any;
    }

    const bgRepeat = $element.attr('data-background-repeat');
    if (bgRepeat) {
      metadata.backgroundRepeat = bgRepeat as any;
    }

    const minHeight = $element.attr('data-min-height');
    if (minHeight) {
      metadata.minHeight = minHeight;
    }

    const verticalAlign = $element.attr('data-vertical-alignment');
    if (verticalAlign) {
      metadata.verticalAlignment = verticalAlign as any;
    }

    const textAlign = $element.attr('data-text-align');
    if (textAlign) {
      metadata.textAlign = textAlign as any;
    }

    // Extract spacing
    metadata.margins = this.extractSpacing($element, 'margin');
    metadata.padding = this.extractSpacing($element, 'padding');

    // Extract border styles
    this.extractBorderStyles($element, metadata);

    return metadata;
  }

  /**
   * Parse styles from data attributes
   */
  private parseDataAttributeStyles($element: cheerio.Cheerio<cheerio.Element>, styles: PageBuilderStyles): void {
    // Parse data-pb-style attribute (Magento's compressed styles)
    const pbStyle = $element.attr('data-pb-style');
    if (pbStyle) {
      try {
        // Decode and parse Page Builder styles
        const decodedStyles = this.decodePbStyles(pbStyle);
        Object.assign(styles, decodedStyles);
      } catch (error) {
        logParsingWarning(`Failed to parse data-pb-style: ${pbStyle}`);
      }
    }
  }

  /**
   * Decode Page Builder compressed styles
   */
  private decodePbStyles(pbStyle: string): PageBuilderStyles {
    const styles: PageBuilderStyles = {};
    
    try {
      // Page Builder styles are typically base64 encoded JSON
      const decoded = Buffer.from(pbStyle, 'base64').toString('utf-8');
      const styleData = JSON.parse(decoded);
      
      // Convert to our format
      if (styleData.desktop) {
        styles.desktop = this.convertPbStylesToCSS(styleData.desktop);
      }
      
      if (styleData.mobile) {
        styles.mobile = this.convertPbStylesToCSS(styleData.mobile);
      }
      
      if (styleData.tablet) {
        styles.tablet = this.convertPbStylesToCSS(styleData.tablet);
      }
    } catch (error) {
      // If decoding fails, try to parse as regular CSS
      styles.desktop = this.parseInlineStyles(pbStyle);
    }
    
    return styles;
  }

  /**
   * Convert Page Builder style object to CSS properties
   */
  private convertPbStylesToCSS(pbStyles: Record<string, any>): CSSProperties {
    const cssStyles: CSSProperties = {};
    
    Object.entries(pbStyles).forEach(([key, value]) => {
      // Convert Page Builder property names to CSS properties
      const cssProperty = this.mapPbPropertyToCss(key);
      if (cssProperty && value !== null && value !== undefined) {
        cssStyles[cssProperty as keyof CSSProperties] = value;
      }
    });
    
    return cssStyles;
  }

  /**
   * Map Page Builder property names to CSS properties
   */
  private mapPbPropertyToCss(pbProperty: string): string | null {
    const mapping: Record<string, string> = {
      'background_color': 'backgroundColor',
      'background_image': 'backgroundImage',
      'background_size': 'backgroundSize',
      'background_position': 'backgroundPosition',
      'background_attachment': 'backgroundAttachment',
      'background_repeat': 'backgroundRepeat',
      'min_height': 'minHeight',
      'text_align': 'textAlign',
      'vertical_align': 'verticalAlign',
      'margin_top': 'marginTop',
      'margin_right': 'marginRight',
      'margin_bottom': 'marginBottom',
      'margin_left': 'marginLeft',
      'padding_top': 'paddingTop',
      'padding_right': 'paddingRight',
      'padding_bottom': 'paddingBottom',
      'padding_left': 'paddingLeft',
      'border_color': 'borderColor',
      'border_width': 'borderWidth',
      'border_radius': 'borderRadius',
      'border_style': 'borderStyle',
    };

    return mapping[pbProperty] || null;
  }

  /**
   * Extract spacing (margin/padding) from element
   */
  private extractSpacing($element: cheerio.Cheerio<cheerio.Element>, type: 'margin' | 'padding'): PageBuilderSpacing {
    const spacing: PageBuilderSpacing = {};
    
    const top = $element.attr(`data-${type}-top`);
    if (top) spacing.top = formatSpacing(top);
    
    const right = $element.attr(`data-${type}-right`);
    if (right) spacing.right = formatSpacing(right);
    
    const bottom = $element.attr(`data-${type}-bottom`);
    if (bottom) spacing.bottom = formatSpacing(bottom);
    
    const left = $element.attr(`data-${type}-left`);
    if (left) spacing.left = formatSpacing(left);
    
    return spacing;
  }

  /**
   * Extract border styles from element
   */
  private extractBorderStyles($element: cheerio.Cheerio<cheerio.Element>, metadata: Partial<PageBuilderElementMetadata>): void {
    const borderColor = $element.attr('data-border-color');
    if (borderColor) {
      metadata.borderColor = borderColor;
    }

    const borderWidth = $element.attr('data-border-width');
    if (borderWidth) {
      metadata.borderWidth = formatSpacing(borderWidth);
    }

    const borderRadius = $element.attr('data-border-radius');
    if (borderRadius) {
      metadata.borderRadius = formatSpacing(borderRadius);
    }

    // Construct border shorthand if we have all components
    if (metadata.borderWidth && metadata.borderColor) {
      const borderStyle = $element.attr('data-border-style') || 'solid';
      metadata.border = `${metadata.borderWidth} ${borderStyle} ${metadata.borderColor}`;
    }
  }

  /**
   * Generate CSS string from styles object
   */
  public stylesToCssString(styles: CSSProperties): string {
    return Object.entries(styles)
      .filter(([_, value]) => value !== null && value !== undefined)
      .map(([property, value]) => {
        const kebabProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase();
        return `${kebabProperty}: ${value}`;
      })
      .join('; ');
  }

  /**
   * Generate responsive CSS rules
   */
  public generateResponsiveCss(styles: PageBuilderStyles, selector: string): string {
    let css = '';
    
    // Desktop styles (default)
    if (styles.desktop && Object.keys(styles.desktop).length > 0) {
      css += `${selector} { ${this.stylesToCssString(styles.desktop)} }\n`;
    }
    
    // Tablet styles
    if (styles.tablet && Object.keys(styles.tablet).length > 0) {
      css += `@media (max-width: ${this.config.breakpoints.desktop - 1}px) {\n`;
      css += `  ${selector} { ${this.stylesToCssString(styles.tablet)} }\n`;
      css += `}\n`;
    }
    
    // Mobile styles
    if (styles.mobile && Object.keys(styles.mobile).length > 0) {
      css += `@media (max-width: ${this.config.breakpoints.tablet - 1}px) {\n`;
      css += `  ${selector} { ${this.stylesToCssString(styles.mobile)} }\n`;
      css += `}\n`;
    }
    
    return css;
  }
}
