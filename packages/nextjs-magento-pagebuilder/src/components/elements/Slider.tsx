// Slider component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';

export const Slider: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Basic slider implementation - can be enhanced with a slider library
  return (
    <div
      className={`pagebuilder-slider ${className || ''}`}
      style={{ width: '100%', ...style }}
      data-content-type="slider"
      data-element-id={element.id}
    >
      {children}
    </div>
  );
};
