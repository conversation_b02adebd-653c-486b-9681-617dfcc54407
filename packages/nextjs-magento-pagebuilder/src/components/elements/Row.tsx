// Row component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { stylesToCss, getResponsiveClasses } from '../../utils';

export const Row: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { config, deviceType } = usePageBuilderContext();
  
  // Get appearance and other metadata
  const appearance = element.metadata?.appearance || 'contained';
  const enableParallax = element.metadata?.enableParallax || false;
  const parallaxSpeed = element.metadata?.parallaxSpeed || 0.5;
  const minHeight = element.metadata?.minHeight || 'auto';
  const verticalAlignment = element.metadata?.verticalAlignment || 'top';
  
  // Build CSS classes
  const cssClasses = [
    'pagebuilder-row',
    `pagebuilder-row--${appearance}`,
    `pagebuilder-row--valign-${verticalAlignment}`,
    enableParallax ? 'pagebuilder-row--parallax' : '',
    getResponsiveClasses('pagebuilder-row', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    minHeight: minHeight !== 'auto' ? minHeight : undefined,
    position: 'relative',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add background styles
  if (element.metadata?.backgroundColor) {
    inlineStyles.backgroundColor = element.metadata.backgroundColor;
  }

  if (element.metadata?.backgroundImage) {
    inlineStyles.backgroundImage = `url(${element.metadata.backgroundImage})`;
    inlineStyles.backgroundSize = element.metadata.backgroundSize || 'cover';
    inlineStyles.backgroundPosition = element.metadata.backgroundPosition || 'center center';
    inlineStyles.backgroundAttachment = element.metadata.backgroundAttachment || 'scroll';
    inlineStyles.backgroundRepeat = element.metadata.backgroundRepeat || 'no-repeat';
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) inlineStyles.marginTop = margins.top;
    if (margins.right) inlineStyles.marginRight = margins.right;
    if (margins.bottom) inlineStyles.marginBottom = margins.bottom;
    if (margins.left) inlineStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) inlineStyles.paddingTop = padding.top;
    if (padding.right) inlineStyles.paddingRight = padding.right;
    if (padding.bottom) inlineStyles.paddingBottom = padding.bottom;
    if (padding.left) inlineStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    inlineStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    inlineStyles.borderRadius = element.metadata.borderRadius;
  }

  // Handle different appearances
  const containerStyles: React.CSSProperties = {};
  
  switch (appearance) {
    case 'full-width':
      containerStyles.maxWidth = '100%';
      containerStyles.margin = '0';
      break;
    case 'full-bleed':
      containerStyles.width = '100vw';
      containerStyles.marginLeft = '50%';
      containerStyles.transform = 'translateX(-50%)';
      break;
    case 'contained':
    default:
      containerStyles.maxWidth = '1200px'; // Default container width
      containerStyles.margin = '0 auto';
      containerStyles.padding = '0 1rem';
      break;
  }

  // Handle vertical alignment
  const contentStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    minHeight: 'inherit',
  };

  switch (verticalAlignment) {
    case 'middle':
      contentStyles.justifyContent = 'center';
      break;
    case 'bottom':
      contentStyles.justifyContent = 'flex-end';
      break;
    case 'top':
    default:
      contentStyles.justifyContent = 'flex-start';
      break;
  }

  // Parallax effect (client-side only)
  React.useEffect(() => {
    if (!enableParallax || typeof window === 'undefined') return;

    const element = document.querySelector(`[data-element-id="${element.id}"]`);
    if (!element) return;

    const handleScroll = () => {
      const rect = element.getBoundingClientRect();
      const scrolled = window.pageYOffset;
      const rate = scrolled * -parallaxSpeed;
      
      (element as HTMLElement).style.transform = `translateY(${rate}px)`;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [enableParallax, parallaxSpeed, element.id]);

  return (
    <div
      className={cssClasses}
      style={{ ...inlineStyles, ...containerStyles }}
      data-content-type="row"
      data-appearance={appearance}
      data-element-id={element.id}
      data-enable-parallax={enableParallax}
      data-parallax-speed={parallaxSpeed}
    >
      <div 
        className="pagebuilder-row-content"
        style={contentStyles}
      >
        {children}
      </div>
    </div>
  );
};
