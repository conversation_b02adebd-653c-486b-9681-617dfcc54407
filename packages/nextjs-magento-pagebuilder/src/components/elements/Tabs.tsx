// Tabs component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';

export const Tabs: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Basic tabs implementation - can be enhanced with state management
  return (
    <div
      className={`pagebuilder-tabs ${className || ''}`}
      style={{ width: '100%', ...style }}
      data-content-type="tabs"
      data-element-id={element.id}
    >
      {children}
    </div>
  );
};
