// Banner component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

export const Banner: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Get banner metadata
  const bannerConfig = element.metadata?.banner;
  
  // Build CSS classes
  const cssClasses = [
    'pagebuilder-banner',
    `pagebuilder-banner--${bannerConfig?.appearance || 'poster'}`,
    getResponsiveClasses('pagebuilder-banner', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: bannerConfig?.minHeight || '300px',
    overflow: 'hidden',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add background styles
  if (element.metadata?.backgroundColor) {
    inlineStyles.backgroundColor = element.metadata.backgroundColor;
  }

  if (element.metadata?.backgroundImage) {
    inlineStyles.backgroundImage = `url(${element.metadata.backgroundImage})`;
    inlineStyles.backgroundSize = element.metadata.backgroundSize || 'cover';
    inlineStyles.backgroundPosition = element.metadata.backgroundPosition || 'center center';
    inlineStyles.backgroundRepeat = 'no-repeat';
  }

  // Overlay styles
  const overlayStyles: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: bannerConfig?.overlayColor || 'rgba(0,0,0,0.4)',
    display: bannerConfig?.showOverlay === 'never' ? 'none' : 'block',
  };

  // Content styles
  const contentStyles: React.CSSProperties = {
    position: 'relative',
    zIndex: 1,
    textAlign: 'center',
    color: 'white',
    padding: '2rem',
  };

  return (
    <div
      className={cssClasses}
      style={inlineStyles}
      data-content-type="banner"
      data-element-id={element.id}
    >
      {bannerConfig?.showOverlay !== 'never' && (
        <div className="pagebuilder-banner-overlay" style={overlayStyles} />
      )}
      
      <div className="pagebuilder-banner-content" style={contentStyles}>
        {bannerConfig?.message && (
          <div 
            className="pagebuilder-banner-message"
            dangerouslySetInnerHTML={{ __html: bannerConfig.message }}
          />
        )}
        
        {bannerConfig?.buttonText && bannerConfig?.showButton !== 'never' && (
          <div className="pagebuilder-banner-button" style={{ marginTop: '1rem' }}>
            {bannerConfig.link ? (
              <a
                href={bannerConfig.link}
                target={bannerConfig.linkTarget || '_self'}
                className={`pagebuilder-button pagebuilder-button--${bannerConfig.buttonType || 'primary'}`}
                style={{
                  display: 'inline-block',
                  padding: '0.75rem 1.5rem',
                  backgroundColor: bannerConfig.buttonType === 'secondary' ? '#6c757d' : '#007bff',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  fontWeight: '500',
                }}
              >
                {bannerConfig.buttonText}
              </a>
            ) : (
              <button
                className={`pagebuilder-button pagebuilder-button--${bannerConfig.buttonType || 'primary'}`}
                style={{
                  padding: '0.75rem 1.5rem',
                  backgroundColor: bannerConfig.buttonType === 'secondary' ? '#6c757d' : '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontWeight: '500',
                  cursor: 'pointer',
                }}
              >
                {bannerConfig.buttonText}
              </button>
            )}
          </div>
        )}
        
        {children}
      </div>
    </div>
  );
};
