// Heading component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses, sanitizeHtml } from '../../utils';

export const Heading: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { config, deviceType } = usePageBuilderContext();
  
  // Get heading metadata
  const headingType = element.metadata?.headingType || 'h2';
  const textAlign = element.metadata?.textAlign || 'left';
  const content = element.content || '';
  
  // Build CSS classes
  const cssClasses = [
    'pagebuilder-heading',
    `pagebuilder-heading--${headingType}`,
    `pagebuilder-heading--align-${textAlign}`,
    getResponsiveClasses('pagebuilder-heading', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    textAlign: textAlign as any,
    width: '100%',
    margin: '0',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) inlineStyles.marginTop = margins.top;
    if (margins.right) inlineStyles.marginRight = margins.right;
    if (margins.bottom) inlineStyles.marginBottom = margins.bottom;
    if (margins.left) inlineStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) inlineStyles.paddingTop = padding.top;
    if (padding.right) inlineStyles.paddingRight = padding.right;
    if (padding.bottom) inlineStyles.paddingBottom = padding.bottom;
    if (padding.left) inlineStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    inlineStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    inlineStyles.borderRadius = element.metadata.borderRadius;
  }

  // Sanitize content if enabled
  const sanitizedContent = config.sanitizeHtml 
    ? sanitizeHtml(content, config.allowedHtmlTags)
    : content;

  // Create the heading element
  const HeadingTag = headingType as keyof JSX.IntrinsicElements;

  return (
    <HeadingTag
      className={cssClasses}
      style={inlineStyles}
      data-content-type="heading"
      data-element-id={element.id}
      data-heading-type={headingType}
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  );
};
