// Image component for Page Builder with Next.js optimization

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

// Next.js Image component (optional peer dependency)
let NextImage: any = null;
try {
  NextImage = require('next/image').default;
} catch (e) {
  // Next.js Image not available, will use regular img tag
}

export const Image: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { config, deviceType } = usePageBuilderContext();
  
  // Get image metadata
  const imageConfig = element.metadata?.image;
  const linkConfig = element.metadata?.link;
  const caption = element.metadata?.caption;
  
  if (!imageConfig?.src) {
    return null;
  }

  // Build CSS classes
  const cssClasses = [
    'pagebuilder-image',
    getResponsiveClasses('pagebuilder-image', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build container styles
  const containerStyles: React.CSSProperties = {
    display: 'block',
    width: '100%',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(containerStyles, deviceStyles);
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) containerStyles.marginTop = margins.top;
    if (margins.right) containerStyles.marginRight = margins.right;
    if (margins.bottom) containerStyles.marginBottom = margins.bottom;
    if (margins.left) containerStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) containerStyles.paddingTop = padding.top;
    if (padding.right) containerStyles.paddingRight = padding.right;
    if (padding.bottom) containerStyles.paddingBottom = padding.bottom;
    if (padding.left) containerStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    containerStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    containerStyles.borderRadius = element.metadata.borderRadius;
  }

  // Image styles
  const imageStyles: React.CSSProperties = {
    width: '100%',
    height: 'auto',
    display: 'block',
  };

  // Process image source
  const imageSrc = config.imageLoader ? config.imageLoader(imageConfig.src) : imageConfig.src;

  // Create image element
  const createImageElement = () => {
    if (NextImage && config.enableImageOptimization) {
      // Use Next.js Image component for optimization
      return (
        <NextImage
          src={imageSrc}
          alt={imageConfig.alt || ''}
          title={imageConfig.title}
          width={imageConfig.width || 800}
          height={imageConfig.height || 600}
          quality={imageConfig.quality || config.defaultImageQuality}
          priority={imageConfig.priority}
          loading={imageConfig.loading}
          placeholder={imageConfig.placeholder}
          blurDataURL={imageConfig.blurDataURL}
          style={imageStyles}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      );
    } else {
      // Use regular img tag
      return (
        <img
          src={imageSrc}
          alt={imageConfig.alt || ''}
          title={imageConfig.title}
          width={imageConfig.width}
          height={imageConfig.height}
          loading={imageConfig.loading || (config.enableLazyLoading ? 'lazy' : 'eager')}
          style={imageStyles}
        />
      );
    }
  };

  // Wrap in link if specified
  const wrapWithLink = (imageElement: React.ReactNode) => {
    if (linkConfig?.href) {
      return (
        <a
          href={linkConfig.href}
          target={linkConfig.target || '_self'}
          title={linkConfig.title}
          className="pagebuilder-image-link"
          style={{ display: 'block', textDecoration: 'none' }}
        >
          {imageElement}
        </a>
      );
    }
    return imageElement;
  };

  // Create caption if specified
  const createCaption = () => {
    if (caption) {
      return (
        <figcaption 
          className="pagebuilder-image-caption"
          style={{
            marginTop: '0.5rem',
            fontSize: '0.875rem',
            color: '#666',
            textAlign: 'center',
          }}
        >
          {caption}
        </figcaption>
      );
    }
    return null;
  };

  const imageElement = createImageElement();
  const linkedImageElement = wrapWithLink(imageElement);
  const captionElement = createCaption();

  // Use figure if there's a caption, otherwise use div
  const ContainerTag = captionElement ? 'figure' : 'div';

  return (
    <ContainerTag
      className={cssClasses}
      style={containerStyles}
      data-content-type="image"
      data-element-id={element.id}
    >
      {linkedImageElement}
      {captionElement}
    </ContainerTag>
  );
};
