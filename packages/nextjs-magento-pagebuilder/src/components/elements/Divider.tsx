// Divider component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

export const Divider: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Get divider metadata
  const dividerConfig = element.metadata?.divider;
  
  // Build CSS classes
  const cssClasses = [
    'pagebuilder-divider',
    getResponsiveClasses('pagebuilder-divider', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Divider line styles
  const lineStyles: React.CSSProperties = {
    width: dividerConfig?.width || '100%',
    height: dividerConfig?.thickness || '1px',
    backgroundColor: dividerConfig?.color || '#e5e5e5',
    border: 'none',
  };

  return (
    <div
      className={cssClasses}
      style={inlineStyles}
      data-content-type="divider"
      data-element-id={element.id}
    >
      <hr style={lineStyles} />
    </div>
  );
};
