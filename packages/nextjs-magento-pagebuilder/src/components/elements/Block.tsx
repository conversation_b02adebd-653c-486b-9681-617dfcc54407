// Block component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';

export const Block: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Block component - for CMS blocks and dynamic blocks
  return (
    <div
      className={`pagebuilder-block ${className || ''}`}
      style={{ width: '100%', ...style }}
      data-content-type="block"
      data-element-id={element.id}
    >
      {element.content && (
        <div dangerouslySetInnerHTML={{ __html: element.content }} />
      )}
      {children}
    </div>
  );
};
