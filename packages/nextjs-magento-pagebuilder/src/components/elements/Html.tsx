// HTML component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses, sanitizeHtml } from '../../utils';

export const Html: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { config, deviceType } = usePageBuilderContext();
  
  // Get HTML content
  const content = element.content || element.rawHtml || '';
  
  if (!content.trim()) {
    return null;
  }

  // Build CSS classes
  const cssClasses = [
    'pagebuilder-html',
    getResponsiveClasses('pagebuilder-html', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    width: '100%',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) inlineStyles.marginTop = margins.top;
    if (margins.right) inlineStyles.marginRight = margins.right;
    if (margins.bottom) inlineStyles.marginBottom = margins.bottom;
    if (margins.left) inlineStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) inlineStyles.paddingTop = padding.top;
    if (padding.right) inlineStyles.paddingRight = padding.right;
    if (padding.bottom) inlineStyles.paddingBottom = padding.bottom;
    if (padding.left) inlineStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    inlineStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    inlineStyles.borderRadius = element.metadata.borderRadius;
  }

  // Sanitize content if enabled
  const sanitizedContent = config.sanitizeHtml 
    ? sanitizeHtml(content, config.allowedHtmlTags)
    : content;

  return (
    <div
      className={cssClasses}
      style={inlineStyles}
      data-content-type="html"
      data-element-id={element.id}
      dangerouslySetInnerHTML={{ __html: sanitizedContent }}
    />
  );
};
