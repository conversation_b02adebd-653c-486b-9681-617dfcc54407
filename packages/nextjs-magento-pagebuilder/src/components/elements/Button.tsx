// Button component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

export const Button: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Get button metadata
  const buttonConfig = element.metadata?.button;
  
  if (!buttonConfig?.text) {
    return null;
  }

  // Build CSS classes
  const cssClasses = [
    'pagebuilder-button',
    `pagebuilder-button--${buttonConfig.type || 'primary'}`,
    `pagebuilder-button--${buttonConfig.size || 'medium'}`,
    buttonConfig.disabled ? 'pagebuilder-button--disabled' : '',
    getResponsiveClasses('pagebuilder-button', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    display: 'inline-block',
    textDecoration: 'none',
    cursor: buttonConfig.disabled ? 'not-allowed' : 'pointer',
    border: 'none',
    borderRadius: '4px',
    padding: '0.75rem 1.5rem',
    fontSize: '1rem',
    fontWeight: '500',
    textAlign: 'center',
    transition: 'all 0.2s ease',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add button type styles
  switch (buttonConfig.type) {
    case 'primary':
      inlineStyles.backgroundColor = '#007bff';
      inlineStyles.color = '#ffffff';
      break;
    case 'secondary':
      inlineStyles.backgroundColor = '#6c757d';
      inlineStyles.color = '#ffffff';
      break;
    case 'link':
      inlineStyles.backgroundColor = 'transparent';
      inlineStyles.color = '#007bff';
      inlineStyles.textDecoration = 'underline';
      break;
    default:
      inlineStyles.backgroundColor = '#007bff';
      inlineStyles.color = '#ffffff';
  }

  // Add button size styles
  switch (buttonConfig.size) {
    case 'small':
      inlineStyles.padding = '0.5rem 1rem';
      inlineStyles.fontSize = '0.875rem';
      break;
    case 'large':
      inlineStyles.padding = '1rem 2rem';
      inlineStyles.fontSize = '1.125rem';
      break;
    case 'medium':
    default:
      // Already set above
      break;
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) inlineStyles.marginTop = margins.top;
    if (margins.right) inlineStyles.marginRight = margins.right;
    if (margins.bottom) inlineStyles.marginBottom = margins.bottom;
    if (margins.left) inlineStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) inlineStyles.paddingTop = padding.top;
    if (padding.right) inlineStyles.paddingRight = padding.right;
    if (padding.bottom) inlineStyles.paddingBottom = padding.bottom;
    if (padding.left) inlineStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    inlineStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    inlineStyles.borderRadius = element.metadata.borderRadius;
  }

  // Handle disabled state
  if (buttonConfig.disabled) {
    inlineStyles.opacity = 0.6;
    inlineStyles.cursor = 'not-allowed';
  }

  // Create button element
  const buttonProps = {
    className: cssClasses,
    style: inlineStyles,
    'data-content-type': 'button',
    'data-element-id': element.id,
    disabled: buttonConfig.disabled,
  };

  // Handle click event
  const handleClick = (e: React.MouseEvent) => {
    if (buttonConfig.disabled) {
      e.preventDefault();
      return;
    }
    
    if (buttonConfig.onClick) {
      e.preventDefault();
      buttonConfig.onClick();
    }
  };

  // Render as link or button based on whether there's a link
  if (buttonConfig.link && !buttonConfig.disabled) {
    return (
      <a
        {...buttonProps}
        href={buttonConfig.link}
        target={buttonConfig.target || '_self'}
        onClick={handleClick}
      >
        {buttonConfig.text}
      </a>
    );
  } else {
    return (
      <button
        {...buttonProps}
        type="button"
        onClick={handleClick}
      >
        {buttonConfig.text}
      </button>
    );
  }
};
