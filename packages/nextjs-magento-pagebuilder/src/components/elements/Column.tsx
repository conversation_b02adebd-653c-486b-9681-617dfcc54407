// Column component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

export const Column: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Get column metadata
  const width = element.metadata?.width || '100%';
  const appearance = element.metadata?.appearance || 'full-height';
  const verticalAlignment = element.metadata?.verticalAlignment || 'top';
  
  // Build CSS classes
  const cssClasses = [
    'pagebuilder-column',
    `pagebuilder-column--${appearance}`,
    `pagebuilder-column--valign-${verticalAlignment}`,
    getResponsiveClasses('pagebuilder-column', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
    minWidth: '0',
    width: width,
    position: 'relative',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  // Add background styles
  if (element.metadata?.backgroundColor) {
    inlineStyles.backgroundColor = element.metadata.backgroundColor;
  }

  if (element.metadata?.backgroundImage) {
    inlineStyles.backgroundImage = `url(${element.metadata.backgroundImage})`;
    inlineStyles.backgroundSize = element.metadata.backgroundSize || 'cover';
    inlineStyles.backgroundPosition = element.metadata.backgroundPosition || 'center center';
    inlineStyles.backgroundAttachment = element.metadata.backgroundAttachment || 'scroll';
    inlineStyles.backgroundRepeat = element.metadata.backgroundRepeat || 'no-repeat';
  }

  // Add spacing
  if (element.metadata?.margins) {
    const margins = element.metadata.margins;
    if (margins.top) inlineStyles.marginTop = margins.top;
    if (margins.right) inlineStyles.marginRight = margins.right;
    if (margins.bottom) inlineStyles.marginBottom = margins.bottom;
    if (margins.left) inlineStyles.marginLeft = margins.left;
  }

  if (element.metadata?.padding) {
    const padding = element.metadata.padding;
    if (padding.top) inlineStyles.paddingTop = padding.top;
    if (padding.right) inlineStyles.paddingRight = padding.right;
    if (padding.bottom) inlineStyles.paddingBottom = padding.bottom;
    if (padding.left) inlineStyles.paddingLeft = padding.left;
  }

  // Add border styles
  if (element.metadata?.border) {
    inlineStyles.border = element.metadata.border;
  }
  if (element.metadata?.borderRadius) {
    inlineStyles.borderRadius = element.metadata.borderRadius;
  }

  // Handle minimum height for appearance
  if (appearance === 'minimum-height' && element.metadata?.minHeight) {
    inlineStyles.minHeight = element.metadata.minHeight;
  }

  // Handle vertical alignment
  switch (verticalAlignment) {
    case 'middle':
      inlineStyles.justifyContent = 'center';
      break;
    case 'bottom':
      inlineStyles.justifyContent = 'flex-end';
      break;
    case 'top':
    default:
      inlineStyles.justifyContent = 'flex-start';
      break;
  }

  // Handle responsive width
  const getResponsiveWidth = () => {
    if (deviceType === 'mobile') {
      // On mobile, columns typically stack
      return '100%';
    }
    
    if (deviceType === 'tablet') {
      // On tablet, adjust width if needed
      const numericWidth = parseFloat(width);
      if (numericWidth < 50) {
        return '100%'; // Small columns become full width
      }
    }
    
    return width;
  };

  inlineStyles.width = getResponsiveWidth();

  // If this is a column group, use flexbox layout
  const isColumnGroup = element.type === 'column-group';
  if (isColumnGroup) {
    inlineStyles.display = 'flex';
    inlineStyles.flexDirection = 'row';
    inlineStyles.flexWrap = 'wrap';
    inlineStyles.width = '100%';
  }

  return (
    <div
      className={cssClasses}
      style={inlineStyles}
      data-content-type={element.type}
      data-appearance={appearance}
      data-element-id={element.id}
      data-width={width}
    >
      {children}
    </div>
  );
};
