// Video component for Page Builder

import React from 'react';
import { PageBuilderComponentProps } from '../../types';
import { usePageBuilderContext } from '../PageBuilderRenderer';
import { getResponsiveClasses } from '../../utils';

export const Video: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className,
  style 
}) => {
  const { deviceType } = usePageBuilderContext();
  
  // Get video metadata
  const videoConfig = element.metadata?.video;
  
  if (!videoConfig?.src) {
    return null;
  }

  // Build CSS classes
  const cssClasses = [
    'pagebuilder-video',
    getResponsiveClasses('pagebuilder-video', deviceType),
    className || '',
  ].filter(Boolean).join(' ');

  // Build inline styles
  const inlineStyles: React.CSSProperties = {
    width: '100%',
    ...style,
  };

  // Add responsive styles
  if (element.styles) {
    const deviceStyles = element.styles[deviceType] || element.styles.desktop || {};
    Object.assign(inlineStyles, deviceStyles);
  }

  return (
    <div
      className={cssClasses}
      style={inlineStyles}
      data-content-type="video"
      data-element-id={element.id}
    >
      <video
        src={videoConfig.src}
        poster={videoConfig.poster}
        width={videoConfig.width}
        height={videoConfig.height}
        autoPlay={videoConfig.autoplay}
        loop={videoConfig.loop}
        muted={videoConfig.muted}
        controls={videoConfig.controls}
        preload={videoConfig.preload}
        style={{ width: '100%', height: 'auto' }}
      />
    </div>
  );
};
