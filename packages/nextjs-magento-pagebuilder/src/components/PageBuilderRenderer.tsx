// Main Page Builder Renderer for Next.js

import React, { createContext, useContext, useMemo } from 'react';
import { 
  PageBuilderRendererProps, 
  PageBuilderContext, 
  PageBuilderContent,
  PageBuilderElement,
  PageBuilderElementType 
} from '../types';
import { mergeConfig } from '../utils';
import { PageBuilderParser } from '../parser/PageBuilderParser';
import { CSS_CLASSES } from '../config/default';

// Import components
import { Row } from './elements/Row';
import { Column } from './elements/Column';
import { Text } from './elements/Text';
import { Heading } from './elements/Heading';
import { Image } from './elements/Image';
import { Button } from './elements/Button';
import { Banner } from './elements/Banner';
import { Video } from './elements/Video';
import { Html } from './elements/Html';
import { Divider } from './elements/Divider';
import { Slider } from './elements/Slider';
import { Tabs } from './elements/Tabs';
import { Products } from './elements/Products';
import { Block } from './elements/Block';

// Page Builder Context
const PageBuilderContextProvider = createContext<PageBuilderContext | null>(null);

// Hook to use Page Builder context
export const usePageBuilderContext = (): PageBuilderContext => {
  const context = useContext(PageBuilderContextProvider);
  if (!context) {
    throw new Error('usePageBuilderContext must be used within a PageBuilderRenderer');
  }
  return context;
};

// Component mapping
const COMPONENT_MAP = {
  [PageBuilderElementType.ROW]: Row,
  [PageBuilderElementType.COLUMN]: Column,
  [PageBuilderElementType.COLUMN_GROUP]: Column, // Column group uses same component
  [PageBuilderElementType.TEXT]: Text,
  [PageBuilderElementType.HEADING]: Heading,
  [PageBuilderElementType.IMAGE]: Image,
  [PageBuilderElementType.BUTTON]: Button,
  [PageBuilderElementType.BUTTON_ITEM]: Button, // Button item uses same component
  [PageBuilderElementType.BANNER]: Banner,
  [PageBuilderElementType.VIDEO]: Video,
  [PageBuilderElementType.HTML]: Html,
  [PageBuilderElementType.DIVIDER]: Divider,
  [PageBuilderElementType.SLIDER]: Slider,
  [PageBuilderElementType.SLIDE]: Banner, // Slide uses banner component
  [PageBuilderElementType.TABS]: Tabs,
  [PageBuilderElementType.TAB_ITEM]: Html, // Tab item uses html component
  [PageBuilderElementType.PRODUCTS]: Products,
  [PageBuilderElementType.BLOCK]: Block,
  [PageBuilderElementType.DYNAMIC_BLOCK]: Block, // Dynamic block uses same component
  [PageBuilderElementType.MAP]: Html, // Map fallback to HTML
};

// Element renderer component
interface ElementRendererProps {
  element: PageBuilderElement;
  context: PageBuilderContext;
}

const ElementRenderer: React.FC<ElementRendererProps> = ({ element, context }) => {
  // Get component from mapping or custom overrides
  const Component = 
    context.config.componentOverrides?.[element.type] || 
    context.config.customElementTypes?.[element.type] ||
    COMPONENT_MAP[element.type];

  if (!Component) {
    // Fallback for unknown element types
    if (context.config.enableDebugMode) {
      console.warn(`Unknown Page Builder element type: ${element.type}`);
    }
    
    return (
      <div 
        className={`${CSS_CLASSES.element} ${CSS_CLASSES.element}--unknown`}
        style={{
          padding: '1rem',
          border: '1px dashed #ccc',
          borderRadius: '4px',
          backgroundColor: '#f5f5f5',
          textAlign: 'center',
          color: '#666',
        }}
      >
        Unknown element: {element.type}
        {element.content && (
          <div dangerouslySetInnerHTML={{ __html: element.content }} />
        )}
      </div>
    );
  }

  // Render children if they exist
  const children = element.children?.map((child, index) => (
    <ElementRenderer 
      key={child.id || `${child.type}-${index}`} 
      element={child} 
      context={context} 
    />
  ));

  return (
    <Component
      element={element}
      className={`${CSS_CLASSES.element} ${CSS_CLASSES[element.type as keyof typeof CSS_CLASSES] || ''}`}
    >
      {children}
    </Component>
  );
};

// Raw HTML fallback renderer
interface RawHtmlRendererProps {
  html: string;
  className?: string;
}

const RawHtmlRenderer: React.FC<RawHtmlRendererProps> = ({ html, className }) => {
  return (
    <div
      className={`${CSS_CLASSES.container} ${CSS_CLASSES.container}--raw-html ${className || ''}`}
      dangerouslySetInnerHTML={{ __html: html }}
      style={{
        width: '100%',
        // Basic Page Builder element styles
        '--pagebuilder-row-display': 'flex',
        '--pagebuilder-row-flex-direction': 'column',
        '--pagebuilder-column-group-display': 'flex',
        '--pagebuilder-column-group-flex-wrap': 'wrap',
        '--pagebuilder-column-flex': '1 1 auto',
        '--pagebuilder-column-min-width': '0',
      } as React.CSSProperties}
    />
  );
};

// Main Page Builder Renderer component
export const PageBuilderRenderer: React.FC<PageBuilderRendererProps> = ({
  content,
  config = {},
  className,
  style,
  isEditing = false,
  deviceType = 'desktop',
}) => {
  // Merge configuration
  const mergedConfig = useMemo(() => mergeConfig(config), [config]);

  // Parse content if it's a string
  const pageBuilderContent = useMemo((): PageBuilderContent => {
    if (typeof content === 'string') {
      return PageBuilderParser.parseWithFallback(content, mergedConfig);
    }
    return content;
  }, [content, mergedConfig]);

  // Create context value
  const contextValue = useMemo((): PageBuilderContext => ({
    config: mergedConfig,
    isEditing,
    deviceType,
  }), [mergedConfig, isEditing, deviceType]);

  // If no Page Builder elements found, render as raw HTML
  if (!pageBuilderContent.elements || pageBuilderContent.elements.length === 0) {
    if (pageBuilderContent.rawHtml && pageBuilderContent.rawHtml.trim()) {
      return <RawHtmlRenderer html={pageBuilderContent.rawHtml} className={className} />;
    }
    return null;
  }

  return (
    <PageBuilderContextProvider.Provider value={contextValue}>
      <div
        className={`${CSS_CLASSES.container} ${className || ''}`}
        style={{
          width: '100%',
          ...style,
        }}
      >
        {pageBuilderContent.elements.map((element, index) => (
          <ElementRenderer
            key={element.id || `${element.type}-${index}`}
            element={element}
            context={contextValue}
          />
        ))}
      </div>
    </PageBuilderContextProvider.Provider>
  );
};

// Server-side safe renderer
export const PageBuilderRendererSSR: React.FC<PageBuilderRendererProps> = (props) => {
  // For SSR, we can use the same renderer since we're using cheerio for parsing
  return <PageBuilderRenderer {...props} />;
};

// Utility hooks
export const usePageBuilderConfig = () => {
  const context = usePageBuilderContext();
  return context.config;
};

export const usePageBuilderEditing = () => {
  const context = usePageBuilderContext();
  return context.isEditing || false;
};

export const usePageBuilderDeviceType = () => {
  const context = usePageBuilderContext();
  return context.deviceType || 'desktop';
};

// Export default
export default PageBuilderRenderer;
