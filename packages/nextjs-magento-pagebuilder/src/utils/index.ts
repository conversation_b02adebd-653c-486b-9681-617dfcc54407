// Utility functions for Page Builder

import merge from 'lodash.merge';
import { v4 as uuidv4 } from 'uuid';
import { PageBuilderParserConfig, PageBuilderElement, PageBuilderStyles } from '../types';
import { DEFAULT_CONFIG } from '../config/default';

/**
 * Merge user configuration with default configuration
 */
export function mergeConfig(userConfig: Partial<PageBuilderParserConfig> = {}): PageBuilderParserConfig {
  return merge({}, DEFAULT_CONFIG, userConfig);
}

/**
 * Generate unique ID for Page Builder elements
 */
export function generateElementId(): string {
  return `pb-${uuidv4()}`;
}

/**
 * Check if an element is a Page Builder element
 */
export function isPageBuilderElement(element: Element): boolean {
  return element.hasAttribute('data-content-type') || 
         element.classList.contains('pagebuilder-element') ||
         element.hasAttribute('data-pb-style');
}

/**
 * Extract content type from element
 */
export function getContentType(element: Element): string | null {
  return element.getAttribute('data-content-type') || 
         element.getAttribute('data-element') ||
         null;
}

/**
 * Parse CSS string into object
 */
export function parseCssString(cssString: string): Record<string, string> {
  const styles: Record<string, string> = {};
  
  if (!cssString) return styles;
  
  const declarations = cssString.split(';').filter(Boolean);
  
  for (const declaration of declarations) {
    const [property, value] = declaration.split(':').map(s => s.trim());
    if (property && value) {
      // Convert kebab-case to camelCase
      const camelProperty = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      styles[camelProperty] = value;
    }
  }
  
  return styles;
}

/**
 * Convert camelCase to kebab-case
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
}

/**
 * Convert kebab-case to camelCase
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Sanitize HTML content
 */
export function sanitizeHtml(html: string, allowedTags: string[] = []): string {
  if (typeof window === 'undefined') {
    // Server-side: basic sanitization
    return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  }
  
  // Client-side: use DOMParser for better sanitization
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Remove script tags and other potentially dangerous elements
  const dangerousTags = ['script', 'object', 'embed', 'applet', 'meta', 'link'];
  dangerousTags.forEach(tag => {
    const elements = doc.querySelectorAll(tag);
    elements.forEach(el => el.remove());
  });
  
  return doc.body.innerHTML;
}

/**
 * Extract responsive styles from data attributes
 */
export function extractResponsiveStyles(element: Element): PageBuilderStyles {
  const styles: PageBuilderStyles = {};
  
  // Extract desktop styles (default)
  const desktopStyle = element.getAttribute('style');
  if (desktopStyle) {
    styles.desktop = parseCssString(desktopStyle);
  }
  
  // Extract mobile styles
  const mobileStyle = element.getAttribute('data-mobile-style');
  if (mobileStyle) {
    styles.mobile = parseCssString(mobileStyle);
  }
  
  // Extract tablet styles
  const tabletStyle = element.getAttribute('data-tablet-style');
  if (tabletStyle) {
    styles.tablet = parseCssString(tabletStyle);
  }
  
  return styles;
}

/**
 * Convert Page Builder styles to CSS string
 */
export function stylesToCss(styles: PageBuilderStyles, deviceType: 'mobile' | 'tablet' | 'desktop' = 'desktop'): string {
  const deviceStyles = styles[deviceType] || styles.desktop || {};
  
  return Object.entries(deviceStyles)
    .map(([property, value]) => `${camelToKebab(property)}: ${value}`)
    .join('; ');
}

/**
 * Get responsive CSS classes
 */
export function getResponsiveClasses(prefix: string, deviceType?: 'mobile' | 'tablet' | 'desktop'): string {
  const classes = [prefix];
  
  if (deviceType) {
    classes.push(`${prefix}--${deviceType}`);
  }
  
  return classes.join(' ');
}

/**
 * Parse background image URL from CSS
 */
export function parseBackgroundImage(cssValue: string): string | null {
  const match = cssValue.match(/url\(['"]?([^'"]+)['"]?\)/);
  return match ? match[1] : null;
}

/**
 * Format spacing value
 */
export function formatSpacing(value: string | number): string {
  if (typeof value === 'number') {
    return `${value}px`;
  }
  
  if (typeof value === 'string') {
    // If it's already a valid CSS value, return as is
    if (value.match(/^\d+(\.\d+)?(px|em|rem|%|vh|vw)$/)) {
      return value;
    }
    
    // If it's just a number, add px
    if (value.match(/^\d+(\.\d+)?$/)) {
      return `${value}px`;
    }
  }
  
  return value as string;
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Log parsing errors in development
 */
export function logParsingError(error: Error, context?: string): void {
  if (process.env.NODE_ENV === 'development') {
    console.error(`Page Builder parsing error${context ? ` in ${context}` : ''}:`, error);
  }
}

/**
 * Log parsing warnings in development
 */
export function logParsingWarning(message: string, context?: string): void {
  if (process.env.NODE_ENV === 'development') {
    console.warn(`Page Builder warning${context ? ` in ${context}` : ''}: ${message}`);
  }
}
