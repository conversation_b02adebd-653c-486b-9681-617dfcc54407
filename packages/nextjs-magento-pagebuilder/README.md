# Next.js Magento Page Builder

A comprehensive Next.js server-side Page Builder package for Magento 2 PWA integration, inspired by Magento's PWA Studio Page Builder implementation.

## Features

- 🚀 **Server-Side Rendering (SSR)** - Full SSR support with Next.js App Router
- 🎨 **Complete Element Support** - All standard Page Builder content types
- 📱 **Responsive Design** - Mobile-first responsive components
- 🖼️ **Next.js Image Optimization** - Automatic image optimization integration
- 🎯 **TypeScript Support** - Full TypeScript definitions
- 🔧 **Extensible** - Easy to customize and extend with your own components
- ⚡ **Performance Optimized** - Lightweight and fast parsing
- 🛡️ **Security** - Built-in HTML sanitization

## Installation

```bash
npm install @magento/nextjs-pagebuilder
# or
yarn add @magento/nextjs-pagebuilder
```

## Quick Start

```tsx
import { PageBuilderRenderer } from '@magento/nextjs-pagebuilder';
import '@magento/nextjs-pagebuilder/dist/styles/pagebuilder.css';

function MyPage({ pageBuilderContent }: { pageBuilderContent: string }) {
  return (
    <div>
      <PageBuilderRenderer content={pageBuilderContent} />
    </div>
  );
}
```

## Supported Page Builder Elements

- ✅ **Row** - Layout rows with background and spacing options
- ✅ **Column** - Flexible column layouts with responsive breakpoints
- ✅ **Text** - Rich text content with formatting
- ✅ **Heading** - H1-H6 headings with styling options
- ✅ **Image** - Optimized images with Next.js Image component
- ✅ **Button** - Interactive buttons with link support
- ✅ **Banner** - Hero banners with overlay and call-to-action
- ✅ **Video** - HTML5 video player with controls
- ✅ **HTML** - Custom HTML content blocks
- ✅ **Divider** - Visual separators with customizable styling
- ✅ **Slider** - Image and content sliders (basic implementation)
- ✅ **Tabs** - Tabbed content sections (basic implementation)
- ✅ **Products** - Product listings (requires custom implementation)
- ✅ **Block** - CMS and dynamic blocks

## Configuration

```tsx
import { PageBuilderRenderer } from '@magento/nextjs-pagebuilder';

const config = {
  enableImageOptimization: true,
  enableLazyLoading: true,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  componentOverrides: {
    // Override default components
    text: MyCustomTextComponent,
  },
  customElementTypes: {
    // Add custom element types
    'my-custom-element': MyCustomElement,
  },
};

function MyPage({ content }: { content: string }) {
  return <PageBuilderRenderer content={content} config={config} />;
}
```

## Server-Side Usage

```tsx
import { PageBuilderParser } from '@magento/nextjs-pagebuilder';

// In your API route or server component
export async function getServerSideProps() {
  const htmlContent = await fetchFromMagento();
  
  const result = PageBuilderParser.parseServerSide(htmlContent, {
    enableImageOptimization: true,
    sanitizeHtml: true,
  });
  
  if (result.success) {
    return {
      props: {
        pageBuilderContent: result.content,
      },
    };
  }
  
  return {
    props: {
      pageBuilderContent: { elements: [], rawHtml: htmlContent },
    },
  };
}
```

## Custom Components

```tsx
import { PageBuilderComponentProps } from '@magento/nextjs-pagebuilder';

const MyCustomText: React.FC<PageBuilderComponentProps> = ({ 
  element, 
  children, 
  className 
}) => {
  return (
    <div className={`my-custom-text ${className}`}>
      <div dangerouslySetInnerHTML={{ __html: element.content || '' }} />
      {children}
    </div>
  );
};

// Use in config
const config = {
  componentOverrides: {
    text: MyCustomText,
  },
};
```

## Styling

The package includes default CSS styles that you can import:

```tsx
import '@magento/nextjs-pagebuilder/dist/styles/pagebuilder.css';
```

Or create your own styles using the provided CSS classes:

```css
.pagebuilder-row {
  /* Your custom row styles */
}

.pagebuilder-text {
  /* Your custom text styles */
}
```

## API Reference

### PageBuilderRenderer Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `content` | `string \| PageBuilderContent` | - | Page Builder HTML content or parsed content object |
| `config` | `Partial<PageBuilderParserConfig>` | `{}` | Configuration options |
| `className` | `string` | - | Additional CSS class name |
| `style` | `CSSProperties` | - | Inline styles |
| `isEditing` | `boolean` | `false` | Whether in editing mode |
| `deviceType` | `'mobile' \| 'tablet' \| 'desktop'` | `'desktop'` | Current device type |

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enableServerSideRendering` | `boolean` | `true` | Enable SSR support |
| `enableImageOptimization` | `boolean` | `true` | Use Next.js Image component |
| `enableLazyLoading` | `boolean` | `true` | Enable lazy loading for images |
| `sanitizeHtml` | `boolean` | `true` | Sanitize HTML content |
| `breakpoints` | `object` | `{ mobile: 768, tablet: 1024, desktop: 1200 }` | Responsive breakpoints |
| `componentOverrides` | `object` | `{}` | Override default components |
| `customElementTypes` | `object` | `{}` | Add custom element types |

## Integration with Magento 2

This package is designed to work seamlessly with Magento 2's Page Builder GraphQL API:

```graphql
query GetCmsPage($identifier: String!) {
  cmsPage(identifier: $identifier) {
    content
    content_heading
    title
    meta_title
    meta_description
  }
}
```

## Development

```bash
# Install dependencies
npm install

# Build the package
npm run build

# Run tests
npm test

# Watch mode for development
npm run dev
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the examples in the `/examples` directory
