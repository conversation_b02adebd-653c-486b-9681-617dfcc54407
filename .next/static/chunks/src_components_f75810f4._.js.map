{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/layout/HeroBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Button,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Hero banner props\ninterface HeroBannerProps {\n  title: string;\n  subtitle: string;\n  description?: string;\n  cta: {\n    text: string;\n    link: string;\n  };\n  backgroundImage: string;\n  mobileBackgroundImage?: string;\n  height?: number | string;\n  overlay?: boolean;\n  overlayOpacity?: number;\n}\n\n// Hero banner component\nexport const HeroBanner: React.FC<HeroBannerProps> = ({\n  title,\n  subtitle,\n  description,\n  cta,\n  backgroundImage,\n  mobileBackgroundImage,\n  height = 600,\n  overlay = true,\n  overlayOpacity = 0.4,\n}) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  \n  const currentBackgroundImage = isMobile && mobileBackgroundImage \n    ? mobileBackgroundImage \n    : backgroundImage;\n\n  return (\n    <Box\n      sx={{\n        position: 'relative',\n        height: typeof height === 'number' ? `${height}px` : height,\n        display: 'flex',\n        alignItems: 'center',\n        overflow: 'hidden',\n        color: 'white',\n      }}\n    >\n      {/* Background Image */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: -2,\n        }}\n      >\n        <Image\n          src={currentBackgroundImage}\n          alt=\"Hero background\"\n          fill\n          style={{\n            objectFit: 'cover',\n            objectPosition: 'center',\n          }}\n          priority\n          sizes=\"100vw\"\n        />\n      </Box>\n\n      {/* Overlay */}\n      {overlay && (\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: `rgba(0, 0, 0, ${overlayOpacity})`,\n            zIndex: -1,\n          }}\n        />\n      )}\n\n      {/* Content */}\n      <Container maxWidth=\"lg\">\n        <Box\n          sx={{\n            maxWidth: { xs: '100%', md: '60%' },\n            textAlign: { xs: 'center', md: 'left' },\n          }}\n        >\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <Typography\n              variant=\"h1\"\n              component=\"h1\"\n              sx={{\n                fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },\n                fontWeight: 700,\n                mb: 2,\n                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',\n              }}\n            >\n              {title}\n            </Typography>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Typography\n              variant=\"h4\"\n              component=\"h2\"\n              sx={{\n                fontSize: { xs: '1.25rem', md: '1.5rem', lg: '1.75rem' },\n                fontWeight: 400,\n                mb: description ? 2 : 4,\n                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                opacity: 0.9,\n              }}\n            >\n              {subtitle}\n            </Typography>\n          </motion.div>\n\n          {description && (\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              <Typography\n                variant=\"body1\"\n                sx={{\n                  fontSize: { xs: '1rem', md: '1.125rem' },\n                  mb: 4,\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                  opacity: 0.8,\n                  maxWidth: '500px',\n                }}\n              >\n                {description}\n              </Typography>\n            </motion.div>\n          )}\n\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            <Button\n              component={Link}\n              href={cta.link}\n              variant=\"contained\"\n              size=\"large\"\n              sx={{\n                fontSize: '1.125rem',\n                fontWeight: 600,\n                px: 4,\n                py: 1.5,\n                borderRadius: 2,\n                textTransform: 'none',\n                boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',\n                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',\n                '&:hover': {\n                  background: 'linear-gradient(45deg, #1976d2 30%, #1cb5e0 90%)',\n                  boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',\n                  transform: 'translateY(-2px)',\n                },\n                transition: 'all 0.3s ease-in-out',\n              }}\n            >\n              {cta.text}\n            </Button>\n          </motion.div>\n        </Box>\n      </Container>\n\n      {/* Scroll indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        style={{\n          position: 'absolute',\n          bottom: 30,\n          left: '50%',\n          transform: 'translateX(-50%)',\n        }}\n      >\n        <Box\n          sx={{\n            width: 2,\n            height: 30,\n            backgroundColor: 'rgba(255,255,255,0.7)',\n            borderRadius: 1,\n            position: 'relative',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '30%',\n              backgroundColor: 'white',\n              borderRadius: 1,\n              animation: 'scroll 2s infinite',\n            },\n            '@keyframes scroll': {\n              '0%': {\n                transform: 'translateY(0)',\n                opacity: 0,\n              },\n              '50%': {\n                opacity: 1,\n              },\n              '100%': {\n                transform: 'translateY(200%)',\n                opacity: 0,\n              },\n            },\n          }}\n        />\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default HeroBanner;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;AAbA;;;;;AAgCO,MAAM,aAAwC,CAAC,EACpD,KAAK,EACL,QAAQ,EACR,WAAW,EACX,GAAG,EACH,eAAe,EACf,qBAAqB,EACrB,SAAS,GAAG,EACZ,UAAU,IAAI,EACd,iBAAiB,GAAG,EACrB;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IAEtD,MAAM,yBAAyB,YAAY,wBACvC,wBACA;IAEJ,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,IAAI;YACF,UAAU;YACV,QAAQ,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;YACrD,SAAS;YACT,YAAY;YACZ,UAAU;YACV,OAAO;QACT;;0BAGA,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,QAAQ,CAAC;gBACX;0BAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,IAAI;oBACJ,OAAO;wBACL,WAAW;wBACX,gBAAgB;oBAClB;oBACA,QAAQ;oBACR,OAAM;;;;;;;;;;;YAKT,yBACC,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;oBACnD,QAAQ,CAAC;gBACX;;;;;;0BAKJ,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;0BAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,UAAU;4BAAE,IAAI;4BAAQ,IAAI;wBAAM;wBAClC,WAAW;4BAAE,IAAI;4BAAU,IAAI;wBAAO;oBACxC;;sCAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,WAAU;gCACV,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAU,IAAI;wCAAU,IAAI;oCAAO;oCACnD,YAAY;oCACZ,IAAI;oCACJ,YAAY;gCACd;0CAEC;;;;;;;;;;;sCAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,WAAU;gCACV,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAW,IAAI;wCAAU,IAAI;oCAAU;oCACvD,YAAY;oCACZ,IAAI,cAAc,IAAI;oCACtB,YAAY;oCACZ,SAAS;gCACX;0CAEC;;;;;;;;;;;wBAIJ,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAQ,IAAI;oCAAW;oCACvC,IAAI;oCACJ,YAAY;oCACZ,SAAS;oCACT,UAAU;gCACZ;0CAEC;;;;;;;;;;;sCAKP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,oMAAA,CAAA,SAAM;gCACL,WAAW,+JAAA,CAAA,UAAI;gCACf,MAAM,IAAI,IAAI;gCACd,SAAQ;gCACR,MAAK;gCACL,IAAI;oCACF,UAAU;oCACV,YAAY;oCACZ,IAAI;oCACJ,IAAI;oCACJ,cAAc;oCACd,eAAe;oCACf,WAAW;oCACX,YAAY;oCACZ,WAAW;wCACT,YAAY;wCACZ,WAAW;wCACX,WAAW;oCACb;oCACA,YAAY;gCACd;0CAEC,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAOjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,OAAO;oBACL,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;0BAEA,cAAA,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,OAAO;wBACP,QAAQ;wBACR,iBAAiB;wBACjB,cAAc;wBACd,UAAU;wBACV,YAAY;4BACV,SAAS;4BACT,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,iBAAiB;4BACjB,cAAc;4BACd,WAAW;wBACb;wBACA,qBAAqB;4BACnB,MAAM;gCACJ,WAAW;gCACX,SAAS;4BACX;4BACA,OAAO;gCACL,SAAS;4BACX;4BACA,QAAQ;gCACN,WAAW;gCACX,SAAS;4BACX;wBACF;oBACF;;;;;;;;;;;;;;;;;AAKV;GAzNa;;QAWG,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;;;KAZnB;uCA2NE", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/sliders/BannerSlider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Autoplay, Navigation, Pagination, EffectFade } from 'swiper/modules';\nimport {\n  Box,\n  Typography,\n  Button,\n  Container,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\n// Banner interface\ninterface Banner {\n  id: number;\n  title: string;\n  subtitle: string;\n  description: string;\n  image: string;\n  mobileImage?: string;\n  link: string;\n  cta: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\n// Banner slider props\ninterface BannerSliderProps {\n  banners: Banner[];\n  height?: number | string;\n  autoplay?: boolean;\n  autoplayDelay?: number;\n  showNavigation?: boolean;\n  showPagination?: boolean;\n  effect?: 'slide' | 'fade';\n}\n\n// Individual banner slide component\nconst BannerSlide: React.FC<{ banner: Banner; height: number | string }> = ({\n  banner,\n  height,\n}) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  \n  const currentImage = isMobile && banner.mobileImage ? banner.mobileImage : banner.image;\n\n  return (\n    <Box\n      sx={{\n        position: 'relative',\n        height: typeof height === 'number' ? `${height}px` : height,\n        display: 'flex',\n        alignItems: 'center',\n        overflow: 'hidden',\n        backgroundColor: banner.backgroundColor || 'transparent',\n        color: banner.textColor || 'white',\n      }}\n    >\n      {/* Background Image */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          zIndex: -2,\n        }}\n      >\n        <Image\n          src={currentImage}\n          alt={banner.title}\n          fill\n          style={{\n            objectFit: 'cover',\n            objectPosition: 'center',\n          }}\n          sizes=\"100vw\"\n        />\n      </Box>\n\n      {/* Overlay */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.3)',\n          zIndex: -1,\n        }}\n      />\n\n      {/* Content */}\n      <Container maxWidth=\"lg\">\n        <Box\n          sx={{\n            maxWidth: { xs: '100%', md: '50%' },\n            textAlign: { xs: 'center', md: 'left' },\n          }}\n        >\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h6\"\n              component=\"p\"\n              sx={{\n                fontSize: { xs: '0.875rem', md: '1rem' },\n                fontWeight: 500,\n                mb: 1,\n                textTransform: 'uppercase',\n                letterSpacing: 1,\n                opacity: 0.9,\n              }}\n            >\n              {banner.subtitle}\n            </Typography>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h2\"\n              component=\"h3\"\n              sx={{\n                fontSize: { xs: '2rem', md: '2.5rem', lg: '3rem' },\n                fontWeight: 700,\n                mb: 2,\n                textShadow: '2px 2px 4px rgba(0,0,0,0.3)',\n              }}\n            >\n              {banner.title}\n            </Typography>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"body1\"\n              sx={{\n                fontSize: { xs: '1rem', md: '1.125rem' },\n                mb: 3,\n                textShadow: '1px 1px 2px rgba(0,0,0,0.3)',\n                opacity: 0.9,\n                maxWidth: '400px',\n              }}\n            >\n              {banner.description}\n            </Typography>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <Button\n              component={Link}\n              href={banner.link}\n              variant=\"contained\"\n              size=\"large\"\n              sx={{\n                fontSize: '1rem',\n                fontWeight: 600,\n                px: 3,\n                py: 1.5,\n                borderRadius: 2,\n                textTransform: 'none',\n                boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',\n                '&:hover': {\n                  boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',\n                  transform: 'translateY(-2px)',\n                },\n                transition: 'all 0.3s ease-in-out',\n              }}\n            >\n              {banner.cta}\n            </Button>\n          </motion.div>\n        </Box>\n      </Container>\n    </Box>\n  );\n};\n\n// Main banner slider component\nexport const BannerSlider: React.FC<BannerSliderProps> = ({\n  banners,\n  height = 400,\n  autoplay = true,\n  autoplayDelay = 5000,\n  showNavigation = true,\n  showPagination = true,\n  effect = 'slide',\n}) => {\n  if (!banners || banners.length === 0) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      <Swiper\n        modules={[Autoplay, Navigation, Pagination, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        autoplay={\n          autoplay\n            ? {\n                delay: autoplayDelay,\n                disableOnInteraction: false,\n              }\n            : false\n        }\n        navigation={showNavigation}\n        pagination={\n          showPagination\n            ? {\n                clickable: true,\n                dynamicBullets: true,\n              }\n            : false\n        }\n        effect={effect}\n        fadeEffect={{\n          crossFade: true,\n        }}\n        loop={banners.length > 1}\n        className=\"banner-slider\"\n        style={{\n          '--swiper-navigation-color': '#fff',\n          '--swiper-pagination-color': '#fff',\n          '--swiper-navigation-size': '24px',\n        } as React.CSSProperties}\n      >\n        {banners.map((banner) => (\n          <SwiperSlide key={banner.id}>\n            <BannerSlide banner={banner} height={height} />\n          </SwiperSlide>\n        ))}\n      </Swiper>\n\n      {/* Custom styles for Swiper */}\n      <style jsx global>{`\n        .banner-slider .swiper-button-next,\n        .banner-slider .swiper-button-prev {\n          background: rgba(0, 0, 0, 0.3);\n          border-radius: 50%;\n          width: 50px;\n          height: 50px;\n          margin-top: -25px;\n          transition: all 0.3s ease;\n        }\n        \n        .banner-slider .swiper-button-next:hover,\n        .banner-slider .swiper-button-prev:hover {\n          background: rgba(0, 0, 0, 0.5);\n          transform: scale(1.1);\n        }\n        \n        .banner-slider .swiper-button-next::after,\n        .banner-slider .swiper-button-prev::after {\n          font-size: 18px;\n          font-weight: bold;\n        }\n        \n        .banner-slider .swiper-pagination {\n          bottom: 20px;\n        }\n        \n        .banner-slider .swiper-pagination-bullet {\n          width: 12px;\n          height: 12px;\n          background: rgba(255, 255, 255, 0.5);\n          opacity: 1;\n          transition: all 0.3s ease;\n        }\n        \n        .banner-slider .swiper-pagination-bullet-active {\n          background: #fff;\n          transform: scale(1.2);\n        }\n        \n        @media (max-width: 768px) {\n          .banner-slider .swiper-button-next,\n          .banner-slider .swiper-button-prev {\n            width: 40px;\n            height: 40px;\n            margin-top: -20px;\n          }\n          \n          .banner-slider .swiper-button-next::after,\n          .banner-slider .swiper-button-prev::after {\n            font-size: 14px;\n          }\n        }\n      `}</style>\n    </Box>\n  );\n};\n\nexport default BannerSlider;\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;AAfA;;;;;;;;;;;;AAgDA,oCAAoC;AACpC,MAAM,cAAqE,CAAC,EAC1E,MAAM,EACN,MAAM,EACP;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IAEtD,MAAM,eAAe,YAAY,OAAO,WAAW,GAAG,OAAO,WAAW,GAAG,OAAO,KAAK;IAEvF,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,IAAI;YACF,UAAU;YACV,QAAQ,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;YACrD,SAAS;YACT,YAAY;YACZ,UAAU;YACV,iBAAiB,OAAO,eAAe,IAAI;YAC3C,OAAO,OAAO,SAAS,IAAI;QAC7B;;0BAGA,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,QAAQ,CAAC;gBACX;0BAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAK,OAAO,KAAK;oBACjB,IAAI;oBACJ,OAAO;wBACL,WAAW;wBACX,gBAAgB;oBAClB;oBACA,OAAM;;;;;;;;;;;0BAKV,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ,CAAC;gBACX;;;;;;0BAIF,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;0BAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,UAAU;4BAAE,IAAI;4BAAQ,IAAI;wBAAM;wBAClC,WAAW;4BAAE,IAAI;4BAAU,IAAI;wBAAO;oBACxC;;sCAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,WAAU;gCACV,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAY,IAAI;oCAAO;oCACvC,YAAY;oCACZ,IAAI;oCACJ,eAAe;oCACf,eAAe;oCACf,SAAS;gCACX;0CAEC,OAAO,QAAQ;;;;;;;;;;;sCAIpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,WAAU;gCACV,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAQ,IAAI;wCAAU,IAAI;oCAAO;oCACjD,YAAY;oCACZ,IAAI;oCACJ,YAAY;gCACd;0CAEC,OAAO,KAAK;;;;;;;;;;;sCAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,IAAI;oCACF,UAAU;wCAAE,IAAI;wCAAQ,IAAI;oCAAW;oCACvC,IAAI;oCACJ,YAAY;oCACZ,SAAS;oCACT,UAAU;gCACZ;0CAEC,OAAO,WAAW;;;;;;;;;;;sCAIvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,oMAAA,CAAA,SAAM;gCACL,WAAW,+JAAA,CAAA,UAAI;gCACf,MAAM,OAAO,IAAI;gCACjB,SAAQ;gCACR,MAAK;gCACL,IAAI;oCACF,UAAU;oCACV,YAAY;oCACZ,IAAI;oCACJ,IAAI;oCACJ,cAAc;oCACd,eAAe;oCACf,WAAW;oCACX,WAAW;wCACT,WAAW;wCACX,WAAW;oCACb;oCACA,YAAY;gCACd;0CAEC,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAhKM;;QAIU,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;;;KAL1B;AAmKC,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,SAAS,GAAG,EACZ,WAAW,IAAI,EACf,gBAAgB,IAAI,EACpB,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,SAAS,OAAO,EACjB;IACC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;0BAC9B,6LAAC,6IAAA,CAAA,SAAM;gBACL,SAAS;oBAAC,wLAAA,CAAA,WAAQ;oBAAE,4LAAA,CAAA,aAAU;oBAAE,4LAAA,CAAA,aAAU;oBAAE,gMAAA,CAAA,aAAU;iBAAC;gBACvD,cAAc;gBACd,eAAe;gBACf,UACE,WACI;oBACE,OAAO;oBACP,sBAAsB;gBACxB,IACA;gBAEN,YAAY;gBACZ,YACE,iBACI;oBACE,WAAW;oBACX,gBAAgB;gBAClB,IACA;gBAEN,QAAQ;gBACR,YAAY;oBACV,WAAW;gBACb;gBACA,MAAM,QAAQ,MAAM,GAAG;gBACvB,WAAU;gBACV,OAAO;oBACL,6BAA6B;oBAC7B,6BAA6B;oBAC7B,4BAA4B;gBAC9B;0BAEC,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,6IAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAY,QAAQ;4BAAQ,QAAQ;;;;;;uBADrB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;AA+DrC;MAhHa;uCAkHE", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/sliders/ProductSlider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, FreeMode } from 'swiper/modules';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardMedia,\n  CardContent,\n  CardActions,\n  Button,\n  Rating,\n  Chip,\n  IconButton,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  FavoriteBorder,\n  Favorite,\n  ShoppingCart,\n  Visibility,\n} from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/free-mode';\n\n// Product interface (simplified for slider)\ninterface Product {\n  id: number;\n  name: string;\n  sku: string;\n  url_key: string;\n  price: {\n    regular: number;\n    final: number;\n    currency: string;\n  };\n  image: {\n    url: string;\n    alt: string;\n  };\n  rating: number;\n  reviewCount: number;\n  isNew?: boolean;\n  isOnSale?: boolean;\n  stockStatus: 'IN_STOCK' | 'OUT_OF_STOCK';\n}\n\n// Product card component\nconst ProductCard: React.FC<{ product: Product }> = ({ product }) => {\n  const [isFavorite, setIsFavorite] = React.useState(false);\n  const theme = useTheme();\n\n  const handleFavoriteToggle = () => {\n    setIsFavorite(!isFavorite);\n  };\n\n  const discountPercentage = product.price.regular > product.price.final\n    ? Math.round(((product.price.regular - product.price.final) / product.price.regular) * 100)\n    : 0;\n\n  return (\n    <motion.div\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n    >\n      <Card\n        sx={{\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative',\n          borderRadius: 2,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',\n          },\n          transition: 'box-shadow 0.3s ease',\n        }}\n      >\n        {/* Product Image */}\n        <Box sx={{ position: 'relative', paddingTop: '75%' }}>\n          <Image\n            src={product.image.url}\n            alt={product.image.alt}\n            fill\n            style={{\n              objectFit: 'cover',\n            }}\n            sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\"\n          />\n          \n          {/* Badges */}\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 8,\n              left: 8,\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 0.5,\n            }}\n          >\n            {product.isNew && (\n              <Chip\n                label=\"New\"\n                size=\"small\"\n                sx={{\n                  backgroundColor: theme.palette.success.main,\n                  color: 'white',\n                  fontWeight: 600,\n                }}\n              />\n            )}\n            {discountPercentage > 0 && (\n              <Chip\n                label={`-${discountPercentage}%`}\n                size=\"small\"\n                sx={{\n                  backgroundColor: theme.palette.error.main,\n                  color: 'white',\n                  fontWeight: 600,\n                }}\n              />\n            )}\n          </Box>\n\n          {/* Favorite Button */}\n          <IconButton\n            onClick={handleFavoriteToggle}\n            sx={{\n              position: 'absolute',\n              top: 8,\n              right: 8,\n              backgroundColor: 'rgba(255,255,255,0.9)',\n              '&:hover': {\n                backgroundColor: 'rgba(255,255,255,1)',\n              },\n            }}\n            size=\"small\"\n          >\n            {isFavorite ? (\n              <Favorite sx={{ color: theme.palette.error.main }} />\n            ) : (\n              <FavoriteBorder />\n            )}\n          </IconButton>\n\n          {/* Quick Actions */}\n          <Box\n            sx={{\n              position: 'absolute',\n              bottom: 8,\n              right: 8,\n              display: 'flex',\n              gap: 0.5,\n              opacity: 0,\n              transition: 'opacity 0.3s ease',\n              '.MuiCard-root:hover &': {\n                opacity: 1,\n              },\n            }}\n          >\n            <IconButton\n              size=\"small\"\n              sx={{\n                backgroundColor: 'rgba(255,255,255,0.9)',\n                '&:hover': {\n                  backgroundColor: 'rgba(255,255,255,1)',\n                },\n              }}\n            >\n              <Visibility />\n            </IconButton>\n          </Box>\n        </Box>\n\n        {/* Product Info */}\n        <CardContent sx={{ flexGrow: 1, pb: 1 }}>\n          <Typography\n            variant=\"h6\"\n            component={Link}\n            href={`/products/${product.url_key}`}\n            sx={{\n              fontSize: '1rem',\n              fontWeight: 500,\n              mb: 1,\n              display: '-webkit-box',\n              WebkitLineClamp: 2,\n              WebkitBoxOrient: 'vertical',\n              overflow: 'hidden',\n              textDecoration: 'none',\n              color: 'inherit',\n              '&:hover': {\n                color: theme.palette.primary.main,\n              },\n            }}\n          >\n            {product.name}\n          </Typography>\n\n          {/* Rating */}\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n            <Rating\n              value={product.rating}\n              precision={0.5}\n              size=\"small\"\n              readOnly\n            />\n            <Typography variant=\"caption\" sx={{ ml: 0.5, color: 'text.secondary' }}>\n              ({product.reviewCount})\n            </Typography>\n          </Box>\n\n          {/* Price */}\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontWeight: 600,\n                color: theme.palette.primary.main,\n              }}\n            >\n              {product.price.currency}{product.price.final.toFixed(2)}\n            </Typography>\n            {discountPercentage > 0 && (\n              <Typography\n                variant=\"body2\"\n                sx={{\n                  textDecoration: 'line-through',\n                  color: 'text.secondary',\n                }}\n              >\n                {product.price.currency}{product.price.regular.toFixed(2)}\n              </Typography>\n            )}\n          </Box>\n        </CardContent>\n\n        {/* Actions */}\n        <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>\n          <Button\n            variant=\"contained\"\n            startIcon={<ShoppingCart />}\n            fullWidth\n            disabled={product.stockStatus === 'OUT_OF_STOCK'}\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n            }}\n          >\n            {product.stockStatus === 'OUT_OF_STOCK' ? 'Out of Stock' : 'Add to Cart'}\n          </Button>\n        </CardActions>\n      </Card>\n    </motion.div>\n  );\n};\n\n// Product slider props\ninterface ProductSliderProps {\n  products: Product[];\n  title?: string;\n  showNavigation?: boolean;\n  showPagination?: boolean;\n  slidesPerView?: {\n    mobile: number;\n    tablet: number;\n    desktop: number;\n  };\n  spaceBetween?: number;\n}\n\n// Main product slider component\nexport const ProductSlider: React.FC<ProductSliderProps> = ({\n  products,\n  title,\n  showNavigation = true,\n  showPagination = false,\n  slidesPerView = {\n    mobile: 1.2,\n    tablet: 2.5,\n    desktop: 4,\n  },\n  spaceBetween = 16,\n}) => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  if (!products || products.length === 0) {\n    return null;\n  }\n\n  return (\n    <Box>\n      {title && (\n        <Typography\n          variant=\"h4\"\n          component=\"h2\"\n          sx={{\n            mb: 3,\n            fontWeight: 600,\n            textAlign: { xs: 'center', md: 'left' },\n          }}\n        >\n          {title}\n        </Typography>\n      )}\n\n      <Box sx={{ position: 'relative' }}>\n        <Swiper\n          modules={[Navigation, Pagination, FreeMode]}\n          spaceBetween={spaceBetween}\n          slidesPerView={slidesPerView.mobile}\n          freeMode={true}\n          navigation={showNavigation && !isMobile}\n          pagination={\n            showPagination\n              ? {\n                  clickable: true,\n                  dynamicBullets: true,\n                }\n              : false\n          }\n          breakpoints={{\n            600: {\n              slidesPerView: slidesPerView.tablet,\n            },\n            900: {\n              slidesPerView: slidesPerView.desktop,\n            },\n          }}\n          className=\"product-slider\"\n          style={{\n            '--swiper-navigation-color': theme.palette.primary.main,\n            '--swiper-pagination-color': theme.palette.primary.main,\n          } as React.CSSProperties}\n        >\n          {products.map((product) => (\n            <SwiperSlide key={product.id}>\n              <ProductCard product={product} />\n            </SwiperSlide>\n          ))}\n        </Swiper>\n\n        {/* Custom styles for Swiper */}\n        <style jsx global>{`\n          .product-slider .swiper-button-next,\n          .product-slider .swiper-button-prev {\n            background: white;\n            border-radius: 50%;\n            width: 44px;\n            height: 44px;\n            margin-top: -22px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n            transition: all 0.3s ease;\n          }\n          \n          .product-slider .swiper-button-next:hover,\n          .product-slider .swiper-button-prev:hover {\n            transform: scale(1.1);\n            box-shadow: 0 4px 16px rgba(0,0,0,0.2);\n          }\n          \n          .product-slider .swiper-button-next::after,\n          .product-slider .swiper-button-prev::after {\n            font-size: 16px;\n            font-weight: bold;\n          }\n          \n          .product-slider .swiper-pagination {\n            bottom: -40px;\n          }\n        `}</style>\n      </Box>\n    </Box>\n  );\n};\n\nexport default ProductSlider;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;AAyDA,yBAAyB;AACzB,MAAM,cAA8C,CAAC,EAAE,OAAO,EAAE;;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnD,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,MAAM,uBAAuB;QAC3B,cAAc,CAAC;IACjB;IAEA,MAAM,qBAAqB,QAAQ,KAAK,CAAC,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,GAClE,KAAK,KAAK,CAAC,AAAC,CAAC,QAAQ,KAAK,CAAC,OAAO,GAAG,QAAQ,KAAK,CAAC,KAAK,IAAI,QAAQ,KAAK,CAAC,OAAO,GAAI,OACrF;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,8LAAA,CAAA,OAAI;YACH,IAAI;gBACF,QAAQ;gBACR,SAAS;gBACT,eAAe;gBACf,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,WAAW;oBACT,WAAW;gBACb;gBACA,YAAY;YACd;;8BAGA,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,UAAU;wBAAY,YAAY;oBAAM;;sCACjD,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,QAAQ,KAAK,CAAC,GAAG;4BACtB,KAAK,QAAQ,KAAK,CAAC,GAAG;4BACtB,IAAI;4BACJ,OAAO;gCACL,WAAW;4BACb;4BACA,OAAM;;;;;;sCAIR,6LAAC,2LAAA,CAAA,MAAG;4BACF,IAAI;gCACF,UAAU;gCACV,KAAK;gCACL,MAAM;gCACN,SAAS;gCACT,eAAe;gCACf,KAAK;4BACP;;gCAEC,QAAQ,KAAK,kBACZ,6LAAC,8LAAA,CAAA,OAAI;oCACH,OAAM;oCACN,MAAK;oCACL,IAAI;wCACF,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wCAC3C,OAAO;wCACP,YAAY;oCACd;;;;;;gCAGH,qBAAqB,mBACpB,6LAAC,8LAAA,CAAA,OAAI;oCACH,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;oCAChC,MAAK;oCACL,IAAI;wCACF,iBAAiB,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;wCACzC,OAAO;wCACP,YAAY;oCACd;;;;;;;;;;;;sCAMN,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAS;4BACT,IAAI;gCACF,UAAU;gCACV,KAAK;gCACL,OAAO;gCACP,iBAAiB;gCACjB,WAAW;oCACT,iBAAiB;gCACnB;4BACF;4BACA,MAAK;sCAEJ,2BACC,6LAAC,gKAAA,CAAA,UAAQ;gCAAC,IAAI;oCAAE,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;gCAAC;;;;;qDAEhD,6LAAC,sKAAA,CAAA,UAAc;;;;;;;;;;sCAKnB,6LAAC,2LAAA,CAAA,MAAG;4BACF,IAAI;gCACF,UAAU;gCACV,QAAQ;gCACR,OAAO;gCACP,SAAS;gCACT,KAAK;gCACL,SAAS;gCACT,YAAY;gCACZ,yBAAyB;oCACvB,SAAS;gCACX;4BACF;sCAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,MAAK;gCACL,IAAI;oCACF,iBAAiB;oCACjB,WAAW;wCACT,iBAAiB;oCACnB;gCACF;0CAEA,cAAA,6LAAC,kKAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;8BAMjB,6LAAC,mNAAA,CAAA,cAAW;oBAAC,IAAI;wBAAE,UAAU;wBAAG,IAAI;oBAAE;;sCACpC,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,WAAW,+JAAA,CAAA,UAAI;4BACf,MAAM,CAAC,UAAU,EAAE,QAAQ,OAAO,EAAE;4BACpC,IAAI;gCACF,UAAU;gCACV,YAAY;gCACZ,IAAI;gCACJ,SAAS;gCACT,iBAAiB;gCACjB,iBAAiB;gCACjB,UAAU;gCACV,gBAAgB;gCAChB,OAAO;gCACP,WAAW;oCACT,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;gCACnC;4BACF;sCAEC,QAAQ,IAAI;;;;;;sCAIf,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,IAAI;4BAAE;;8CACtD,6LAAC,oMAAA,CAAA,SAAM;oCACL,OAAO,QAAQ,MAAM;oCACrB,WAAW;oCACX,MAAK;oCACL,QAAQ;;;;;;8CAEV,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAU,IAAI;wCAAE,IAAI;wCAAK,OAAO;oCAAiB;;wCAAG;wCACpE,QAAQ,WAAW;wCAAC;;;;;;;;;;;;;sCAK1B,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAE;;8CACvD,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,YAAY;wCACZ,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;oCACnC;;wCAEC,QAAQ,KAAK,CAAC,QAAQ;wCAAE,QAAQ,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;gCAEtD,qBAAqB,mBACpB,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,gBAAgB;wCAChB,OAAO;oCACT;;wCAEC,QAAQ,KAAK,CAAC,QAAQ;wCAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;8BAO/D,6LAAC,mNAAA,CAAA,cAAW;oBAAC,IAAI;wBAAE,IAAI;wBAAG,IAAI;wBAAG,IAAI;oBAAE;8BACrC,cAAA,6LAAC,oMAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,yBAAW,6LAAC,oKAAA,CAAA,UAAY;;;;;wBACxB,SAAS;wBACT,UAAU,QAAQ,WAAW,KAAK;wBAClC,IAAI;4BACF,eAAe;4BACf,YAAY;wBACd;kCAEC,QAAQ,WAAW,KAAK,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAMvE;GAhNM;;QAEU,wMAAA,CAAA,WAAQ;;;KAFlB;AAiOC,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,EACR,KAAK,EACL,iBAAiB,IAAI,EACrB,iBAAiB,KAAK,EACtB,gBAAgB;IACd,QAAQ;IACR,QAAQ;IACR,SAAS;AACX,CAAC,EACD,eAAe,EAAE,EAClB;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IAEtD,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;;YACD,uBACC,6LAAC,gNAAA,CAAA,aAAU;gBACT,SAAQ;gBACR,WAAU;gBACV,IAAI;oBACF,IAAI;oBACJ,YAAY;oBACZ,WAAW;wBAAE,IAAI;wBAAU,IAAI;oBAAO;gBACxC;0BAEC;;;;;;0BAIL,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,UAAU;gBAAW;;kCAC9B,6LAAC,6IAAA,CAAA,SAAM;wBACL,SAAS;4BAAC,4LAAA,CAAA,aAAU;4BAAE,4LAAA,CAAA,aAAU;4BAAE,4LAAA,CAAA,WAAQ;yBAAC;wBAC3C,cAAc;wBACd,eAAe,cAAc,MAAM;wBACnC,UAAU;wBACV,YAAY,kBAAkB,CAAC;wBAC/B,YACE,iBACI;4BACE,WAAW;4BACX,gBAAgB;wBAClB,IACA;wBAEN,aAAa;4BACX,KAAK;gCACH,eAAe,cAAc,MAAM;4BACrC;4BACA,KAAK;gCACH,eAAe,cAAc,OAAO;4BACtC;wBACF;wBACA,WAAU;wBACV,OAAO;4BACL,6BAA6B,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;4BACvD,6BAA6B,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wBACzD;kCAEC,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,6IAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAY,SAAS;;;;;;+BADN,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCxC;IAvGa;;QAYG,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;;;MAbnB;uCAyGE", "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/layout/CategoryGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\n// Category interface\ninterface Category {\n  id: number;\n  name: string;\n  description: string;\n  image: string;\n  link: string;\n  productCount: number;\n}\n\n// Category card component\nconst CategoryCard: React.FC<{ category: Category; index: number }> = ({\n  category,\n  index,\n}) => {\n  const theme = useTheme();\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      viewport={{ once: true }}\n      whileHover={{ y: -8 }}\n    >\n      <Card\n        component={Link}\n        href={category.link}\n        sx={{\n          height: '100%',\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'relative',\n          borderRadius: 3,\n          overflow: 'hidden',\n          textDecoration: 'none',\n          color: 'inherit',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n          transition: 'all 0.3s ease',\n          '&:hover': {\n            boxShadow: '0 8px 30px rgba(0,0,0,0.15)',\n            transform: 'translateY(-4px)',\n            '& .category-image': {\n              transform: 'scale(1.05)',\n            },\n            '& .category-overlay': {\n              backgroundColor: alpha(theme.palette.primary.main, 0.8),\n            },\n            '& .category-content': {\n              transform: 'translateY(-10px)',\n            },\n          },\n        }}\n      >\n        {/* Category Image */}\n        <Box\n          sx={{\n            position: 'relative',\n            height: 200,\n            overflow: 'hidden',\n          }}\n        >\n          <Image\n            src={category.image}\n            alt={category.name}\n            fill\n            style={{\n              objectFit: 'cover',\n              transition: 'transform 0.3s ease',\n            }}\n            className=\"category-image\"\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n          />\n          \n          {/* Overlay */}\n          <Box\n            className=\"category-overlay\"\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: alpha(theme.palette.common.black, 0.4),\n              transition: 'background-color 0.3s ease',\n            }}\n          />\n\n          {/* Product Count Badge */}\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 16,\n              right: 16,\n              backgroundColor: alpha(theme.palette.background.paper, 0.9),\n              borderRadius: 2,\n              px: 1.5,\n              py: 0.5,\n            }}\n          >\n            <Typography\n              variant=\"caption\"\n              sx={{\n                fontWeight: 600,\n                color: theme.palette.text.primary,\n              }}\n            >\n              {category.productCount} items\n            </Typography>\n          </Box>\n        </Box>\n\n        {/* Category Content */}\n        <CardContent\n          className=\"category-content\"\n          sx={{\n            flexGrow: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            textAlign: 'center',\n            p: 3,\n            transition: 'transform 0.3s ease',\n          }}\n        >\n          <Typography\n            variant=\"h5\"\n            component=\"h3\"\n            sx={{\n              fontWeight: 600,\n              mb: 1,\n              color: theme.palette.text.primary,\n            }}\n          >\n            {category.name}\n          </Typography>\n          \n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: theme.palette.text.secondary,\n              lineHeight: 1.6,\n            }}\n          >\n            {category.description}\n          </Typography>\n        </CardContent>\n\n        {/* Hover Effect Arrow */}\n        <Box\n          sx={{\n            position: 'absolute',\n            bottom: 16,\n            right: 16,\n            width: 40,\n            height: 40,\n            borderRadius: '50%',\n            backgroundColor: theme.palette.primary.main,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: 0,\n            transform: 'scale(0.8)',\n            transition: 'all 0.3s ease',\n            '.MuiCard-root:hover &': {\n              opacity: 1,\n              transform: 'scale(1)',\n            },\n          }}\n        >\n          <Typography\n            sx={{\n              color: 'white',\n              fontSize: '1.2rem',\n              fontWeight: 'bold',\n            }}\n          >\n            →\n          </Typography>\n        </Box>\n      </Card>\n    </motion.div>\n  );\n};\n\n// Category grid props\ninterface CategoryGridProps {\n  categories: Category[];\n  title?: string;\n  subtitle?: string;\n  columns?: {\n    xs: number;\n    sm: number;\n    md: number;\n    lg: number;\n  };\n  spacing?: number;\n}\n\n// Main category grid component\nexport const CategoryGrid: React.FC<CategoryGridProps> = ({\n  categories,\n  title,\n  subtitle,\n  columns = {\n    xs: 1,\n    sm: 2,\n    md: 3,\n    lg: 3,\n  },\n  spacing = 3,\n}) => {\n  if (!categories || categories.length === 0) {\n    return null;\n  }\n\n  return (\n    <Box>\n      {/* Header */}\n      {(title || subtitle) && (\n        <Box sx={{ textAlign: 'center', mb: 5 }}>\n          {title && (\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <Typography\n                variant=\"h3\"\n                component=\"h2\"\n                sx={{\n                  fontWeight: 700,\n                  mb: subtitle ? 2 : 0,\n                  background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                }}\n              >\n                {title}\n              </Typography>\n            </motion.div>\n          )}\n          \n          {subtitle && (\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <Typography\n                variant=\"h6\"\n                sx={{\n                  color: 'text.secondary',\n                  maxWidth: 600,\n                  mx: 'auto',\n                  lineHeight: 1.6,\n                }}\n              >\n                {subtitle}\n              </Typography>\n            </motion.div>\n          )}\n        </Box>\n      )}\n\n      {/* Category Grid */}\n      <Grid container spacing={spacing}>\n        {categories.map((category, index) => (\n          <Grid\n            item\n            xs={columns.xs}\n            sm={columns.sm}\n            md={columns.md}\n            lg={columns.lg}\n            key={category.id}\n          >\n            <CategoryCard category={category} index={index} />\n          </Grid>\n        ))}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default CategoryGrid;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AAdA;;;;;AA0BA,0BAA0B;AAC1B,MAAM,eAAgE,CAAC,EACrE,QAAQ,EACR,KAAK,EACN;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,YAAY;YAAE,GAAG,CAAC;QAAE;kBAEpB,cAAA,6LAAC,8LAAA,CAAA,OAAI;YACH,WAAW,+JAAA,CAAA,UAAI;YACf,MAAM,SAAS,IAAI;YACnB,IAAI;gBACF,QAAQ;gBACR,SAAS;gBACT,eAAe;gBACf,UAAU;gBACV,cAAc;gBACd,UAAU;gBACV,gBAAgB;gBAChB,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,WAAW;oBACT,WAAW;oBACX,WAAW;oBACX,qBAAqB;wBACnB,WAAW;oBACb;oBACA,uBAAuB;wBACrB,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;oBACrD;oBACA,uBAAuB;wBACrB,WAAW;oBACb;gBACF;YACF;;8BAGA,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,UAAU;wBACV,QAAQ;wBACR,UAAU;oBACZ;;sCAEA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,SAAS,KAAK;4BACnB,KAAK,SAAS,IAAI;4BAClB,IAAI;4BACJ,OAAO;gCACL,WAAW;gCACX,YAAY;4BACd;4BACA,WAAU;4BACV,OAAM;;;;;;sCAIR,6LAAC,2LAAA,CAAA,MAAG;4BACF,WAAU;4BACV,IAAI;gCACF,UAAU;gCACV,KAAK;gCACL,MAAM;gCACN,OAAO;gCACP,QAAQ;gCACR,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;gCACnD,YAAY;4BACd;;;;;;sCAIF,6LAAC,2LAAA,CAAA,MAAG;4BACF,IAAI;gCACF,UAAU;gCACV,KAAK;gCACL,OAAO;gCACP,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE;gCACvD,cAAc;gCACd,IAAI;gCACJ,IAAI;4BACN;sCAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,IAAI;oCACF,YAAY;oCACZ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;gCACnC;;oCAEC,SAAS,YAAY;oCAAC;;;;;;;;;;;;;;;;;;8BAM7B,6LAAC,mNAAA,CAAA,cAAW;oBACV,WAAU;oBACV,IAAI;wBACF,UAAU;wBACV,SAAS;wBACT,eAAe;wBACf,gBAAgB;wBAChB,WAAW;wBACX,GAAG;wBACH,YAAY;oBACd;;sCAEA,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,WAAU;4BACV,IAAI;gCACF,YAAY;gCACZ,IAAI;gCACJ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;4BACnC;sCAEC,SAAS,IAAI;;;;;;sCAGhB,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,IAAI;gCACF,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;gCACnC,YAAY;4BACd;sCAEC,SAAS,WAAW;;;;;;;;;;;;8BAKzB,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,UAAU;wBACV,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,cAAc;wBACd,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wBAC3C,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,SAAS;wBACT,WAAW;wBACX,YAAY;wBACZ,yBAAyB;4BACvB,SAAS;4BACT,WAAW;wBACb;oBACF;8BAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;wBACT,IAAI;4BACF,OAAO;4BACP,UAAU;4BACV,YAAY;wBACd;kCACD;;;;;;;;;;;;;;;;;;;;;;AAOX;GA5KM;;QAIU,wMAAA,CAAA,WAAQ;;;KAJlB;AA6LC,MAAM,eAA4C,CAAC,EACxD,UAAU,EACV,KAAK,EACL,QAAQ,EACR,UAAU;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN,CAAC,EACD,UAAU,CAAC,EACZ;IACC,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO;IACT;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;;YAED,CAAC,SAAS,QAAQ,mBACjB,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,WAAW;oBAAU,IAAI;gBAAE;;oBACnC,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,WAAU;4BACV,IAAI;gCACF,YAAY;gCACZ,IAAI,WAAW,IAAI;gCACnB,YAAY;gCACZ,gBAAgB;gCAChB,sBAAsB;gCACtB,qBAAqB;4BACvB;sCAEC;;;;;;;;;;;oBAKN,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,IAAI;gCACF,OAAO;gCACP,UAAU;gCACV,IAAI;gCACJ,YAAY;4BACd;sCAEC;;;;;;;;;;;;;;;;;0BAQX,6LAAC,8LAAA,CAAA,OAAI;gBAAC,SAAS;gBAAC,SAAS;0BACtB,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,8LAAA,CAAA,OAAI;wBACH,IAAI;wBACJ,IAAI,QAAQ,EAAE;wBACd,IAAI,QAAQ,EAAE;wBACd,IAAI,QAAQ,EAAE;wBACd,IAAI,QAAQ,EAAE;kCAGd,cAAA,6LAAC;4BAAa,UAAU;4BAAU,OAAO;;;;;;uBAFpC,SAAS,EAAE;;;;;;;;;;;;;;;;AAQ5B;MArFa;uCAuFE", "debugId": null}}, {"offset": {"line": 1619, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/layout/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Autoplay, Pagination } from 'swiper/modules';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Avatar,\n  Rating,\n  Container,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { FormatQuote } from '@mui/icons-material';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/pagination';\n\n// Testimonial interface\ninterface Testimonial {\n  id: number;\n  name: string;\n  rating: number;\n  comment: string;\n  avatar?: string;\n  location?: string;\n  date: string;\n}\n\n// Individual testimonial card component\nconst TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({\n  testimonial,\n}) => {\n  const theme = useTheme();\n\n  return (\n    <Card\n      sx={{\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        position: 'relative',\n        borderRadius: 3,\n        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          boxShadow: '0 8px 30px rgba(0,0,0,0.12)',\n          transform: 'translateY(-4px)',\n        },\n      }}\n    >\n      {/* Quote Icon */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: -10,\n          left: 20,\n          width: 40,\n          height: 40,\n          borderRadius: '50%',\n          backgroundColor: theme.palette.primary.main,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n        }}\n      >\n        <FormatQuote sx={{ color: 'white', fontSize: 20 }} />\n      </Box>\n\n      <CardContent sx={{ pt: 4, pb: 3 }}>\n        {/* Rating */}\n        <Box sx={{ mb: 2 }}>\n          <Rating\n            value={testimonial.rating}\n            readOnly\n            size=\"small\"\n            sx={{\n              color: theme.palette.warning.main,\n            }}\n          />\n        </Box>\n\n        {/* Comment */}\n        <Typography\n          variant=\"body1\"\n          sx={{\n            mb: 3,\n            lineHeight: 1.7,\n            color: theme.palette.text.primary,\n            fontStyle: 'italic',\n            fontSize: '1.1rem',\n          }}\n        >\n          \"{testimonial.comment}\"\n        </Typography>\n\n        {/* Customer Info */}\n        <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>\n          <Avatar\n            src={testimonial.avatar}\n            alt={testimonial.name}\n            sx={{\n              width: 50,\n              height: 50,\n              mr: 2,\n              border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,\n            }}\n          >\n            {testimonial.name.charAt(0)}\n          </Avatar>\n          \n          <Box>\n            <Typography\n              variant=\"subtitle1\"\n              sx={{\n                fontWeight: 600,\n                color: theme.palette.text.primary,\n              }}\n            >\n              {testimonial.name}\n            </Typography>\n            \n            {testimonial.location && (\n              <Typography\n                variant=\"caption\"\n                sx={{\n                  color: theme.palette.text.secondary,\n                  display: 'block',\n                }}\n              >\n                {testimonial.location}\n              </Typography>\n            )}\n            \n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: theme.palette.text.secondary,\n                display: 'block',\n              }}\n            >\n              {new Date(testimonial.date).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric',\n              })}\n            </Typography>\n          </Box>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n};\n\n// Testimonials props\ninterface TestimonialsProps {\n  testimonials: Testimonial[];\n  title?: string;\n  subtitle?: string;\n  autoplay?: boolean;\n  autoplayDelay?: number;\n  slidesPerView?: {\n    mobile: number;\n    tablet: number;\n    desktop: number;\n  };\n}\n\n// Main testimonials component\nexport const Testimonials: React.FC<TestimonialsProps> = ({\n  testimonials,\n  title = \"What Our Customers Say\",\n  subtitle = \"Don't just take our word for it - hear from our satisfied customers\",\n  autoplay = true,\n  autoplayDelay = 4000,\n  slidesPerView = {\n    mobile: 1,\n    tablet: 2,\n    desktop: 3,\n  },\n}) => {\n  const theme = useTheme();\n\n  if (!testimonials || testimonials.length === 0) {\n    return null;\n  }\n\n  return (\n    <Box\n      sx={{\n        py: 8,\n        backgroundColor: alpha(theme.palette.primary.main, 0.02),\n        position: 'relative',\n        overflow: 'hidden',\n      }}\n    >\n      {/* Background Pattern */}\n      <Box\n        sx={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `radial-gradient(circle at 20% 80%, ${alpha(\n            theme.palette.primary.main,\n            0.05\n          )} 0%, transparent 50%), radial-gradient(circle at 80% 20%, ${alpha(\n            theme.palette.secondary.main,\n            0.05\n          )} 0%, transparent 50%)`,\n          zIndex: 0,\n        }}\n      />\n\n      <Container maxWidth=\"lg\" sx={{ position: 'relative', zIndex: 1 }}>\n        {/* Header */}\n        <Box sx={{ textAlign: 'center', mb: 6 }}>\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h3\"\n              component=\"h2\"\n              sx={{\n                fontWeight: 700,\n                mb: 2,\n                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n              }}\n            >\n              {title}\n            </Typography>\n          </motion.div>\n          \n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <Typography\n              variant=\"h6\"\n              sx={{\n                color: 'text.secondary',\n                maxWidth: 600,\n                mx: 'auto',\n                lineHeight: 1.6,\n              }}\n            >\n              {subtitle}\n            </Typography>\n          </motion.div>\n        </Box>\n\n        {/* Testimonials Slider */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <Swiper\n            modules={[Autoplay, Pagination]}\n            spaceBetween={24}\n            slidesPerView={slidesPerView.mobile}\n            autoplay={\n              autoplay\n                ? {\n                    delay: autoplayDelay,\n                    disableOnInteraction: false,\n                  }\n                : false\n            }\n            pagination={{\n              clickable: true,\n              dynamicBullets: true,\n            }}\n            breakpoints={{\n              600: {\n                slidesPerView: slidesPerView.tablet,\n              },\n              900: {\n                slidesPerView: slidesPerView.desktop,\n              },\n            }}\n            className=\"testimonials-slider\"\n            style={{\n              '--swiper-pagination-color': theme.palette.primary.main,\n              paddingBottom: '50px',\n            } as React.CSSProperties}\n          >\n            {testimonials.map((testimonial) => (\n              <SwiperSlide key={testimonial.id}>\n                <TestimonialCard testimonial={testimonial} />\n              </SwiperSlide>\n            ))}\n          </Swiper>\n        </motion.div>\n\n        {/* Custom styles for Swiper */}\n        <style jsx global>{`\n          .testimonials-slider .swiper-pagination {\n            bottom: 10px;\n          }\n          \n          .testimonials-slider .swiper-pagination-bullet {\n            width: 12px;\n            height: 12px;\n            background: ${alpha(theme.palette.primary.main, 0.3)};\n            opacity: 1;\n            transition: all 0.3s ease;\n          }\n          \n          .testimonials-slider .swiper-pagination-bullet-active {\n            background: ${theme.palette.primary.main};\n            transform: scale(1.2);\n          }\n        `}</style>\n      </Container>\n    </Box>\n  );\n};\n\nexport default Testimonials;\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAjBA;;;;;;;;;AAkCA,wCAAwC;AACxC,MAAM,kBAA0D,CAAC,EAC/D,WAAW,EACZ;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,qBACE,6LAAC,8LAAA,CAAA,OAAI;QACH,IAAI;YACF,QAAQ;YACR,SAAS;YACT,eAAe;YACf,UAAU;YACV,cAAc;YACd,WAAW;YACX,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM;YACxD,YAAY;YACZ,WAAW;gBACT,WAAW;gBACX,WAAW;YACb;QACF;;0BAGA,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK,CAAC;oBACN,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;oBAC3C,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,WAAW;gBACb;0BAEA,cAAA,6LAAC,mKAAA,CAAA,UAAW;oBAAC,IAAI;wBAAE,OAAO;wBAAS,UAAU;oBAAG;;;;;;;;;;;0BAGlD,6LAAC,mNAAA,CAAA,cAAW;gBAAC,IAAI;oBAAE,IAAI;oBAAG,IAAI;gBAAE;;kCAE9B,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,IAAI;wBAAE;kCACf,cAAA,6LAAC,oMAAA,CAAA,SAAM;4BACL,OAAO,YAAY,MAAM;4BACzB,QAAQ;4BACR,MAAK;4BACL,IAAI;gCACF,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;4BACnC;;;;;;;;;;;kCAKJ,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,IAAI;4BACF,IAAI;4BACJ,YAAY;4BACZ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;4BACjC,WAAW;4BACX,UAAU;wBACZ;;4BACD;4BACG,YAAY,OAAO;4BAAC;;;;;;;kCAIxB,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,IAAI;wBAAO;;0CAC3D,6LAAC,oMAAA,CAAA,SAAM;gCACL,KAAK,YAAY,MAAM;gCACvB,KAAK,YAAY,IAAI;gCACrB,IAAI;oCACF,OAAO;oCACP,QAAQ;oCACR,IAAI;oCACJ,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM;gCAC/D;0CAEC,YAAY,IAAI,CAAC,MAAM,CAAC;;;;;;0CAG3B,6LAAC,2LAAA,CAAA,MAAG;;kDACF,6LAAC,gNAAA,CAAA,aAAU;wCACT,SAAQ;wCACR,IAAI;4CACF,YAAY;4CACZ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;wCACnC;kDAEC,YAAY,IAAI;;;;;;oCAGlB,YAAY,QAAQ,kBACnB,6LAAC,gNAAA,CAAA,aAAU;wCACT,SAAQ;wCACR,IAAI;4CACF,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;4CACnC,SAAS;wCACX;kDAEC,YAAY,QAAQ;;;;;;kDAIzB,6LAAC,gNAAA,CAAA,aAAU;wCACT,SAAQ;wCACR,IAAI;4CACF,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;4CACnC,SAAS;wCACX;kDAEC,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB,CAAC,SAAS;4CACtD,MAAM;4CACN,OAAO;4CACP,KAAK;wCACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA5HM;;QAGU,wMAAA,CAAA,WAAQ;;;KAHlB;AA6IC,MAAM,eAA4C,CAAC,EACxD,YAAY,EACZ,QAAQ,wBAAwB,EAChC,WAAW,qEAAqE,EAChF,WAAW,IAAI,EACf,gBAAgB,IAAI,EACpB,gBAAgB;IACd,QAAQ;IACR,QAAQ;IACR,SAAS;AACX,CAAC,EACF;;IACC,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;QAC9C,OAAO;IACT;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,IAAI;YACF,IAAI;YACJ,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;YACnD,UAAU;YACV,UAAU;QACZ;;0BAGA,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB,CAAC,mCAAmC,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EACzD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAC1B,MACA,0DAA0D,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAChE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,EAC5B,MACA,qBAAqB,CAAC;oBACxB,QAAQ;gBACV;;;;;;0BAGF,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,UAAU;oBAAY,QAAQ;gBAAE;;kCAE7D,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,WAAW;4BAAU,IAAI;wBAAE;;0CACpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,WAAU;oCACV,IAAI;wCACF,YAAY;wCACZ,IAAI;wCACJ,YAAY;wCACZ,gBAAgB;wCAChB,sBAAsB;wCACtB,qBAAqB;oCACvB;8CAEC;;;;;;;;;;;0CAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,6LAAC,gNAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,IAAI;wCACF,OAAO;wCACP,UAAU;wCACV,IAAI;wCACJ,YAAY;oCACd;8CAEC;;;;;;;;;;;;;;;;;kCAMP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,6IAAA,CAAA,SAAM;4BACL,SAAS;gCAAC,wLAAA,CAAA,WAAQ;gCAAE,4LAAA,CAAA,aAAU;6BAAC;4BAC/B,cAAc;4BACd,eAAe,cAAc,MAAM;4BACnC,UACE,WACI;gCACE,OAAO;gCACP,sBAAsB;4BACxB,IACA;4BAEN,YAAY;gCACV,WAAW;gCACX,gBAAgB;4BAClB;4BACA,aAAa;gCACX,KAAK;oCACH,eAAe,cAAc,MAAM;gCACrC;gCACA,KAAK;oCACH,eAAe,cAAc,OAAO;gCACtC;4BACF;4BACA,WAAU;4BACV,OAAO;gCACL,6BAA6B,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;gCACvD,eAAe;4BACjB;sCAEC,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC,6IAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAgB,aAAa;;;;;;mCADd,YAAY,EAAE;;;;;;;;;;;;;;;;;;4BAgBpB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;4BAMlC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;;mJAN1B,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,6HAMlC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;;;;;;;;;;;;;;AAOpD;IA9Ja;;QAYG,wMAAA,CAAA,WAAQ;;;MAZX;uCAgKE", "debugId": null}}]}