{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_20989dc1.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"roboto_20989dc1-module__WZGeGG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/roboto_20989dc1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22theme.ts%22,%22import%22:%22Roboto%22,%22arguments%22:[{%22weight%22:[%22300%22,%22400%22,%22500%22,%22700%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22roboto%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Roboto', 'Roboto Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,yJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,yJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,yJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/theme.ts"], "sourcesContent": ["// Material-UI Theme Configuration\n'use client';\n\nimport { createTheme, ThemeOptions } from '@mui/material/styles';\nimport { <PERSON><PERSON> } from 'next/font/google';\n\n// Load Google Fonts\nconst roboto = Roboto({\n  weight: ['300', '400', '500', '700'],\n  subsets: ['latin'],\n  display: 'swap',\n});\n\n// Professional e-commerce brand colors\nconst brandColors = {\n  primary: {\n    50: '#f0f4f8',\n    100: '#d9e2ec',\n    200: '#bcccdc',\n    300: '#9fb3c8',\n    400: '#829ab1',\n    500: '#627d98', // Main primary color - Professional blue-gray\n    600: '#486581',\n    700: '#334e68',\n    800: '#243b53',\n    900: '#102a43',\n  },\n  secondary: {\n    50: '#fff5f5',\n    100: '#fed7d7',\n    200: '#feb2b2',\n    300: '#fc8181',\n    400: '#f56565',\n    500: '#e53e3e', // Main secondary color - Vibrant red\n    600: '#c53030',\n    700: '#9c1c1c',\n    800: '#742a2a',\n    900: '#4a1818',\n  },\n  success: {\n    50: '#f0fff4',\n    100: '#c6f6d5',\n    200: '#9ae6b4',\n    300: '#68d391',\n    400: '#48bb78',\n    500: '#38a169', // Main success color - Fresh green\n    600: '#2f855a',\n    700: '#276749',\n    800: '#22543d',\n    900: '#1c4532',\n  },\n  warning: {\n    50: '#fffbeb',\n    100: '#fef3c7',\n    200: '#fde68a',\n    300: '#fcd34d',\n    400: '#fbbf24',\n    500: '#f59e0b', // Main warning color - Warm orange\n    600: '#d97706',\n    700: '#b45309',\n    800: '#92400e',\n    900: '#78350f',\n  },\n  error: {\n    50: '#fff5f5',\n    100: '#fed7d7',\n    200: '#feb2b2',\n    300: '#fc8181',\n    400: '#f56565',\n    500: '#e53e3e', // Main error color - Clear red\n    600: '#c53030',\n    700: '#9c1c1c',\n    800: '#742a2a',\n    900: '#4a1818',\n  },\n  grey: {\n    50: '#f7fafc',\n    100: '#edf2f7',\n    200: '#e2e8f0',\n    300: '#cbd5e0',\n    400: '#a0aec0',\n    500: '#718096',\n    600: '#4a5568',\n    700: '#2d3748',\n    800: '#1a202c',\n    900: '#171923',\n  },\n};\n\n// Common theme options\nconst commonThemeOptions: ThemeOptions = {\n  typography: {\n    fontFamily: roboto.style.fontFamily,\n    h1: {\n      fontSize: '2.5rem',\n      fontWeight: 700,\n      lineHeight: 1.2,\n      letterSpacing: '-0.01562em',\n    },\n    h2: {\n      fontSize: '2rem',\n      fontWeight: 700,\n      lineHeight: 1.2,\n      letterSpacing: '-0.00833em',\n    },\n    h3: {\n      fontSize: '1.75rem',\n      fontWeight: 600,\n      lineHeight: 1.3,\n      letterSpacing: '0em',\n    },\n    h4: {\n      fontSize: '1.5rem',\n      fontWeight: 600,\n      lineHeight: 1.3,\n      letterSpacing: '0.00735em',\n    },\n    h5: {\n      fontSize: '1.25rem',\n      fontWeight: 500,\n      lineHeight: 1.4,\n      letterSpacing: '0em',\n    },\n    h6: {\n      fontSize: '1.125rem',\n      fontWeight: 500,\n      lineHeight: 1.4,\n      letterSpacing: '0.0075em',\n    },\n    subtitle1: {\n      fontSize: '1rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n      letterSpacing: '0.00938em',\n    },\n    subtitle2: {\n      fontSize: '0.875rem',\n      fontWeight: 500,\n      lineHeight: 1.5,\n      letterSpacing: '0.00714em',\n    },\n    body1: {\n      fontSize: '1rem',\n      fontWeight: 400,\n      lineHeight: 1.6,\n      letterSpacing: '0.00938em',\n    },\n    body2: {\n      fontSize: '0.875rem',\n      fontWeight: 400,\n      lineHeight: 1.6,\n      letterSpacing: '0.01071em',\n    },\n    button: {\n      fontSize: '0.875rem',\n      fontWeight: 600,\n      lineHeight: 1.75,\n      letterSpacing: '0.025em',\n      textTransform: 'none',\n    },\n    caption: {\n      fontSize: '0.75rem',\n      fontWeight: 400,\n      lineHeight: 1.66,\n      letterSpacing: '0.03333em',\n    },\n    overline: {\n      fontSize: '0.75rem',\n      fontWeight: 400,\n      lineHeight: 2.66,\n      letterSpacing: '0.08333em',\n      textTransform: 'uppercase',\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  spacing: 8,\n  breakpoints: {\n    values: {\n      xs: 0,\n      sm: 600,\n      md: 900,\n      lg: 1200,\n      xl: 1536,\n    },\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          borderRadius: 10,\n          textTransform: 'none',\n          fontWeight: 600,\n          padding: '10px 20px',\n          boxShadow: 'none',\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',\n            transform: 'translateY(-1px)',\n          },\n          '&:active': {\n            transform: 'translateY(0)',\n          },\n        },\n        contained: {\n          '&:hover': {\n            boxShadow: '0px 6px 20px rgba(0, 0, 0, 0.15)',\n          },\n        },\n        outlined: {\n          borderWidth: 2,\n          '&:hover': {\n            borderWidth: 2,\n            backgroundColor: 'rgba(98, 125, 152, 0.04)',\n          },\n        },\n        sizeLarge: {\n          padding: '14px 28px',\n          fontSize: '1rem',\n          borderRadius: 12,\n        },\n        sizeSmall: {\n          padding: '6px 14px',\n          fontSize: '0.8125rem',\n          borderRadius: 8,\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)',\n          border: '1px solid rgba(203, 213, 224, 0.3)',\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            boxShadow: '0px 8px 25px rgba(0, 0, 0, 0.12)',\n            transform: 'translateY(-2px)',\n            borderColor: 'rgba(98, 125, 152, 0.2)',\n          },\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 8,\n          },\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiAppBar: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)',\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          borderRadius: 8,\n        },\n        elevation1: {\n          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)',\n        },\n        elevation2: {\n          boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.1)',\n        },\n        elevation3: {\n          boxShadow: '0px 3px 9px rgba(0, 0, 0, 0.1)',\n        },\n      },\n    },\n  },\n};\n\n// Light theme\nexport const lightTheme = createTheme({\n  ...commonThemeOptions,\n  palette: {\n    mode: 'light',\n    primary: {\n      main: brandColors.primary[500],\n      ...brandColors.primary,\n    },\n    secondary: {\n      main: brandColors.secondary[500],\n      ...brandColors.secondary,\n    },\n    success: {\n      main: brandColors.success[500],\n      ...brandColors.success,\n    },\n    warning: {\n      main: brandColors.warning[500],\n      ...brandColors.warning,\n    },\n    error: {\n      main: brandColors.error[500],\n      ...brandColors.error,\n    },\n    grey: brandColors.grey,\n    background: {\n      default: '#f7fafc',\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#2d3748',\n      secondary: '#4a5568',\n      disabled: '#a0aec0',\n    },\n    divider: '#e2e8f0',\n  },\n});\n\n// Dark theme\nexport const darkTheme = createTheme({\n  ...commonThemeOptions,\n  palette: {\n    mode: 'dark',\n    primary: {\n      main: brandColors.primary[500],\n      ...brandColors.primary,\n    },\n    secondary: {\n      main: brandColors.secondary[500],\n      ...brandColors.secondary,\n    },\n    success: {\n      main: brandColors.success[500],\n      ...brandColors.success,\n    },\n    warning: {\n      main: brandColors.warning[500],\n      ...brandColors.warning,\n    },\n    error: {\n      main: brandColors.error[500],\n      ...brandColors.error,\n    },\n    grey: brandColors.grey,\n    background: {\n      default: '#121212',\n      paper: '#1e1e1e',\n    },\n    text: {\n      primary: 'rgba(255, 255, 255, 0.87)',\n      secondary: 'rgba(255, 255, 255, 0.6)',\n      disabled: 'rgba(255, 255, 255, 0.38)',\n    },\n    divider: 'rgba(255, 255, 255, 0.12)',\n  },\n});\n\n// Default theme (light)\nexport const theme = lightTheme;\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;AAGlC;;AAFA;;;AAYA,uCAAuC;AACvC,MAAM,cAAc;IAClB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,MAAM;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAEA,uBAAuB;AACvB,MAAM,qBAAmC;IACvC,YAAY;QACV,YAAY,6IAAA,CAAA,UAAM,CAAC,KAAK,CAAC,UAAU;QACnC,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,IAAI;YACF,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,WAAW;YACT,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,WAAW;YACT,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,OAAO;YACL,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,OAAO;YACL,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,QAAQ;YACN,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;YACf,eAAe;QACjB;QACA,SAAS;YACP,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QACA,UAAU;YACR,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,eAAe;YACf,eAAe;QACjB;IACF;IACA,OAAO;QACL,cAAc;IAChB;IACA,SAAS;IACT,aAAa;QACX,QAAQ;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,YAAY;QACV,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,eAAe;oBACf,YAAY;oBACZ,SAAS;oBACT,WAAW;oBACX,YAAY;oBACZ,WAAW;wBACT,WAAW;wBACX,WAAW;oBACb;oBACA,YAAY;wBACV,WAAW;oBACb;gBACF;gBACA,WAAW;oBACT,WAAW;wBACT,WAAW;oBACb;gBACF;gBACA,UAAU;oBACR,aAAa;oBACb,WAAW;wBACT,aAAa;wBACb,iBAAiB;oBACnB;gBACF;gBACA,WAAW;oBACT,SAAS;oBACT,UAAU;oBACV,cAAc;gBAChB;gBACA,WAAW;oBACT,SAAS;oBACT,UAAU;oBACV,cAAc;gBAChB;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,WAAW;wBACT,WAAW;wBACX,WAAW;wBACX,aAAa;oBACf;gBACF;YACF;QACF;QACA,cAAc;YACZ,gBAAgB;gBACd,MAAM;oBACJ,4BAA4B;wBAC1B,cAAc;oBAChB;gBACF;YACF;QACF;QACA,SAAS;YACP,gBAAgB;gBACd,MAAM;oBACJ,cAAc;oBACd,YAAY;gBACd;YACF;QACF;QACA,WAAW;YACT,gBAAgB;gBACd,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,UAAU;YACR,gBAAgB;gBACd,MAAM;oBACJ,cAAc;gBAChB;gBACA,YAAY;oBACV,WAAW;gBACb;gBACA,YAAY;oBACV,WAAW;gBACb;gBACA,YAAY;oBACV,WAAW;gBACb;YACF;QACF;IACF;AACF;AAGO,MAAM,aAAa,CAAA,GAAA,8MAAA,CAAA,cAAW,AAAD,EAAE;IACpC,GAAG,kBAAkB;IACrB,SAAS;QACP,MAAM;QACN,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,WAAW;YACT,MAAM,YAAY,SAAS,CAAC,IAAI;YAChC,GAAG,YAAY,SAAS;QAC1B;QACA,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,OAAO;YACL,MAAM,YAAY,KAAK,CAAC,IAAI;YAC5B,GAAG,YAAY,KAAK;QACtB;QACA,MAAM,YAAY,IAAI;QACtB,YAAY;YACV,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA,SAAS;IACX;AACF;AAGO,MAAM,YAAY,CAAA,GAAA,8MAAA,CAAA,cAAW,AAAD,EAAE;IACnC,GAAG,kBAAkB;IACrB,SAAS;QACP,MAAM;QACN,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,WAAW;YACT,MAAM,YAAY,SAAS,CAAC,IAAI;YAChC,GAAG,YAAY,SAAS;QAC1B;QACA,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,SAAS;YACP,MAAM,YAAY,OAAO,CAAC,IAAI;YAC9B,GAAG,YAAY,OAAO;QACxB;QACA,OAAO;YACL,MAAM,YAAY,KAAK,CAAC,IAAI;YAC5B,GAAG,YAAY,KAAK;QACtB;QACA,MAAM,YAAY,IAAI;QACtB,YAAY;YACV,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA,SAAS;IACX;AACF;AAGO,MAAM,QAAQ", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/ui/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { lightTheme, darkTheme } from '@/lib/theme';\n\n// Theme mode type\nexport type ThemeMode = 'light' | 'dark' | 'system';\n\n// Theme context interface\ninterface ThemeContextType {\n  mode: ThemeMode;\n  toggleTheme: () => void;\n  setTheme: (mode: ThemeMode) => void;\n  isDark: boolean;\n}\n\n// Create theme context\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\n// Theme provider props\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  defaultMode?: ThemeMode;\n}\n\n// Custom hook to use theme context\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Get system theme preference\nconst getSystemTheme = (): 'light' | 'dark' => {\n  if (typeof window !== 'undefined') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n  }\n  return 'light';\n};\n\n// Get stored theme from localStorage\nconst getStoredTheme = (): ThemeMode => {\n  if (typeof window !== 'undefined') {\n    const stored = localStorage.getItem('theme-mode');\n    if (stored && ['light', 'dark', 'system'].includes(stored)) {\n      return stored as ThemeMode;\n    }\n  }\n  return 'system';\n};\n\n// Theme provider component\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({\n  children,\n  defaultMode = 'system',\n}) => {\n  const [mode, setMode] = useState<ThemeMode>(defaultMode);\n  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');\n\n  // Initialize theme from localStorage\n  useEffect(() => {\n    const storedMode = getStoredTheme();\n    setMode(storedMode);\n    setSystemTheme(getSystemTheme());\n  }, []);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light');\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  // Determine if dark theme should be used\n  const isDark = mode === 'dark' || (mode === 'system' && systemTheme === 'dark');\n\n  // Toggle between light and dark themes\n  const toggleTheme = () => {\n    const newMode = isDark ? 'light' : 'dark';\n    setMode(newMode);\n    localStorage.setItem('theme-mode', newMode);\n  };\n\n  // Set specific theme mode\n  const setTheme = (newMode: ThemeMode) => {\n    setMode(newMode);\n    localStorage.setItem('theme-mode', newMode);\n  };\n\n  // Select the appropriate theme\n  const currentTheme = isDark ? darkTheme : lightTheme;\n\n  // Context value\n  const contextValue: ThemeContextType = {\n    mode,\n    toggleTheme,\n    setTheme,\n    isDark,\n  };\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      <MuiThemeProvider theme={currentTheme}>\n        <CssBaseline />\n        {children}\n      </MuiThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n\nexport default ThemeProvider;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAkBA,uBAAuB;AACvB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAS1D,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQb,8BAA8B;AAC9B,MAAM,iBAAiB;IACrB,wCAAmC;QACjC,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;IAC9E;;AAEF;AAEA,qCAAqC;AACrC,MAAM,iBAAiB;IACrB,wCAAmC;QACjC,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,UAAU;YAAC;YAAS;YAAQ;SAAS,CAAC,QAAQ,CAAC,SAAS;YAC1D,OAAO;QACT;IACF;IACA,OAAO;AACT;AAGO,MAAM,gBAA8C,CAAC,EAC1D,QAAQ,EACR,cAAc,QAAQ,EACvB;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa;YACnB,QAAQ;YACR,eAAe;QACjB;kCAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;wDAAe,CAAC;oBACpB,eAAe,EAAE,OAAO,GAAG,SAAS;gBACtC;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,SAAS,SAAS,UAAW,SAAS,YAAY,gBAAgB;IAExE,uCAAuC;IACvC,MAAM,cAAc;QAClB,MAAM,UAAU,SAAS,UAAU;QACnC,QAAQ;QACR,aAAa,OAAO,CAAC,cAAc;IACrC;IAEA,0BAA0B;IAC1B,MAAM,WAAW,CAAC;QAChB,QAAQ;QACR,aAAa,OAAO,CAAC,cAAc;IACrC;IAEA,+BAA+B;IAC/B,MAAM,eAAe,SAAS,sHAAA,CAAA,YAAS,GAAG,sHAAA,CAAA,aAAU;IAEpD,gBAAgB;IAChB,MAAM,eAAiC;QACrC;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC5B,cAAA,6LAAC,kNAAA,CAAA,gBAAgB;YAAC,OAAO;;8BACvB,6LAAC,yKAAA,CAAA,UAAW;;;;;gBACX;;;;;;;;;;;;AAIT;IA7Da;KAAA;uCA+DE", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/utils/imageUtils.ts"], "sourcesContent": ["// Image utilities for Magento media URL handling\n\n// Image size configurations\nexport const IMAGE_SIZES = {\n  // Product images\n  product: {\n    thumbnail: { width: 150, height: 150 },\n    small: { width: 300, height: 300 },\n    medium: { width: 600, height: 600 },\n    large: { width: 1200, height: 1200 },\n  },\n  // Category images\n  category: {\n    thumbnail: { width: 200, height: 200 },\n    small: { width: 400, height: 400 },\n    medium: { width: 800, height: 600 },\n    large: { width: 1200, height: 900 },\n  },\n  // Banner/Hero images\n  banner: {\n    mobile: { width: 768, height: 400 },\n    tablet: { width: 1024, height: 500 },\n    desktop: { width: 1920, height: 600 },\n  },\n  // Logo images\n  logo: {\n    small: { width: 120, height: 40 },\n    medium: { width: 180, height: 60 },\n    large: { width: 240, height: 80 },\n  },\n} as const;\n\n// Image type definitions\nexport type ImageSize = keyof typeof IMAGE_SIZES;\nexport type ImageVariant<T extends ImageSize> = keyof typeof IMAGE_SIZES[T];\n\n// Get base media URL from environment or store config\nexport function getBaseMediaUrl(storeConfig?: any): string {\n  return (\n    storeConfig?.base_media_url ||\n    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL ||\n    'https://your-magento-store.com/media'\n  );\n}\n\n// Build Magento media URL\nexport function buildMediaUrl(\n  imagePath: string,\n  storeConfig?: any,\n  type: 'product' | 'category' | 'logo' | 'cms' | 'banner' = 'product'\n): string {\n  if (!imagePath) return '';\n\n  // If already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    return imagePath;\n  }\n\n  const baseMediaUrl = getBaseMediaUrl(storeConfig);\n  const cleanPath = imagePath.replace(/^\\//, ''); // Remove leading slash\n\n  // Build URL based on type\n  switch (type) {\n    case 'product':\n      return `${baseMediaUrl}/catalog/product/${cleanPath}`;\n    case 'category':\n      return `${baseMediaUrl}/catalog/category/${cleanPath}`;\n    case 'logo':\n      return `${baseMediaUrl}/logo/${cleanPath}`;\n    case 'cms':\n      return `${baseMediaUrl}/wysiwyg/${cleanPath}`;\n    case 'banner':\n      return `${baseMediaUrl}/banner/${cleanPath}`;\n    default:\n      return `${baseMediaUrl}/${cleanPath}`;\n  }\n}\n\n// Build product image URL with cache busting\nexport function buildProductImageUrl(\n  imagePath: string,\n  storeConfig?: any,\n  variant: 'image' | 'small_image' | 'thumbnail' = 'image'\n): string {\n  if (!imagePath) return '';\n\n  const baseUrl = buildMediaUrl(imagePath, storeConfig, 'product');\n  \n  // Add cache busting parameter\n  const cacheBuster = new Date().getTime();\n  const separator = baseUrl.includes('?') ? '&' : '?';\n  \n  return `${baseUrl}${separator}v=${cacheBuster}`;\n}\n\n// Build category image URL\nexport function buildCategoryImageUrl(\n  imagePath: string,\n  storeConfig?: any\n): string {\n  return buildMediaUrl(imagePath, storeConfig, 'category');\n}\n\n// Build logo URL\nexport function buildLogoUrl(\n  imagePath: string,\n  storeConfig?: any\n): string {\n  return buildMediaUrl(imagePath, storeConfig, 'logo');\n}\n\n// Build CMS/WYSIWYG image URL\nexport function buildCmsImageUrl(\n  imagePath: string,\n  storeConfig?: any\n): string {\n  return buildMediaUrl(imagePath, storeConfig, 'cms');\n}\n\n// Generate responsive image srcSet\nexport function generateSrcSet(\n  imagePath: string,\n  sizes: Array<{ width: number; height?: number }>,\n  storeConfig?: any,\n  type: 'product' | 'category' | 'banner' = 'product'\n): string {\n  if (!imagePath) return '';\n\n  return sizes\n    .map(size => {\n      const url = buildMediaUrl(imagePath, storeConfig, type);\n      return `${url} ${size.width}w`;\n    })\n    .join(', ');\n}\n\n// Generate responsive image sizes attribute\nexport function generateSizesAttribute(\n  breakpoints: Array<{ minWidth?: number; maxWidth?: number; size: string }>\n): string {\n  return breakpoints\n    .map(bp => {\n      if (bp.minWidth && bp.maxWidth) {\n        return `(min-width: ${bp.minWidth}px) and (max-width: ${bp.maxWidth}px) ${bp.size}`;\n      } else if (bp.minWidth) {\n        return `(min-width: ${bp.minWidth}px) ${bp.size}`;\n      } else if (bp.maxWidth) {\n        return `(max-width: ${bp.maxWidth}px) ${bp.size}`;\n      } else {\n        return bp.size;\n      }\n    })\n    .join(', ');\n}\n\n// Get optimized image props for Next.js Image component\nexport function getOptimizedImageProps(\n  imagePath: string,\n  alt: string,\n  size: ImageSize,\n  variant: string,\n  storeConfig?: any,\n  type: 'product' | 'category' | 'banner' = 'product'\n) {\n  const imageConfig = IMAGE_SIZES[size]?.[variant as keyof typeof IMAGE_SIZES[typeof size]];\n  \n  if (!imageConfig) {\n    throw new Error(`Invalid image size configuration: ${size}.${variant}`);\n  }\n\n  const src = buildMediaUrl(imagePath, storeConfig, type);\n  \n  return {\n    src,\n    alt,\n    width: imageConfig.width,\n    height: imageConfig.height,\n    sizes: generateSizesAttribute([\n      { maxWidth: 768, size: '100vw' },\n      { minWidth: 769, size: `${imageConfig.width}px` },\n    ]),\n  };\n}\n\n// Extract image path from Magento URL\nexport function extractImagePath(fullUrl: string): string {\n  if (!fullUrl) return '';\n\n  // If it's already a path, return as is\n  if (!fullUrl.startsWith('http')) {\n    return fullUrl;\n  }\n\n  try {\n    const url = new URL(fullUrl);\n    const pathParts = url.pathname.split('/');\n    \n    // Find the media part and extract everything after it\n    const mediaIndex = pathParts.findIndex(part => part === 'media');\n    if (mediaIndex !== -1 && mediaIndex < pathParts.length - 1) {\n      return pathParts.slice(mediaIndex + 1).join('/');\n    }\n    \n    // Fallback: return the pathname without leading slash\n    return url.pathname.replace(/^\\//, '');\n  } catch {\n    return fullUrl;\n  }\n}\n\n// Validate image URL\nexport function isValidImageUrl(url: string): boolean {\n  if (!url) return false;\n  \n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// Get image placeholder for loading states\nexport function getImagePlaceholder(\n  width: number,\n  height: number,\n  text?: string\n): string {\n  const canvas = document.createElement('canvas');\n  canvas.width = width;\n  canvas.height = height;\n  \n  const ctx = canvas.getContext('2d');\n  if (!ctx) return '';\n  \n  // Fill with light gray background\n  ctx.fillStyle = '#f5f5f5';\n  ctx.fillRect(0, 0, width, height);\n  \n  // Add text if provided\n  if (text) {\n    ctx.fillStyle = '#999';\n    ctx.font = '16px Arial';\n    ctx.textAlign = 'center';\n    ctx.textBaseline = 'middle';\n    ctx.fillText(text, width / 2, height / 2);\n  }\n  \n  return canvas.toDataURL();\n}\n\n// Image error handling\nexport function handleImageError(\n  event: React.SyntheticEvent<HTMLImageElement>,\n  fallbackSrc?: string\n): void {\n  const img = event.currentTarget;\n  \n  if (fallbackSrc && img.src !== fallbackSrc) {\n    img.src = fallbackSrc;\n  } else {\n    // Generate a placeholder\n    const placeholder = getImagePlaceholder(\n      img.width || 300,\n      img.height || 300,\n      'Image not found'\n    );\n    img.src = placeholder;\n  }\n}\n\n// Preload critical images\nexport function preloadImage(src: string): Promise<void> {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => resolve();\n    img.onerror = reject;\n    img.src = src;\n  });\n}\n\n// Preload multiple images\nexport async function preloadImages(srcs: string[]): Promise<void> {\n  try {\n    await Promise.all(srcs.map(src => preloadImage(src)));\n  } catch (error) {\n    console.warn('Some images failed to preload:', error);\n  }\n}\n\n// Get image dimensions from URL (requires CORS)\nexport function getImageDimensions(src: string): Promise<{ width: number; height: number }> {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => {\n      resolve({ width: img.naturalWidth, height: img.naturalHeight });\n    };\n    img.onerror = reject;\n    img.src = src;\n  });\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;AAEjD,4BAA4B;;;;;;;;;;;;;;;;;;;;AAsCxB;AArCG,MAAM,cAAc;IACzB,iBAAiB;IACjB,SAAS;QACP,WAAW;YAAE,OAAO;YAAK,QAAQ;QAAI;QACrC,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAI;QACjC,QAAQ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAClC,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAK;IACrC;IACA,kBAAkB;IAClB,UAAU;QACR,WAAW;YAAE,OAAO;YAAK,QAAQ;QAAI;QACrC,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAI;QACjC,QAAQ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAClC,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAI;IACpC;IACA,qBAAqB;IACrB,QAAQ;QACN,QAAQ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAClC,QAAQ;YAAE,OAAO;YAAM,QAAQ;QAAI;QACnC,SAAS;YAAE,OAAO;YAAM,QAAQ;QAAI;IACtC;IACA,cAAc;IACd,MAAM;QACJ,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAG;QAChC,QAAQ;YAAE,OAAO;YAAK,QAAQ;QAAG;QACjC,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAG;IAClC;AACF;AAOO,SAAS,gBAAgB,WAAiB;IAC/C,OACE,aAAa,kBACb,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kCAAkC,IAC9C;AAEJ;AAGO,SAAS,cACd,SAAiB,EACjB,WAAiB,EACjB,OAA2D,SAAS;IAEpE,IAAI,CAAC,WAAW,OAAO;IAEvB,sCAAsC;IACtC,IAAI,UAAU,UAAU,CAAC,cAAc,UAAU,UAAU,CAAC,aAAa;QACvE,OAAO;IACT;IAEA,MAAM,eAAe,gBAAgB;IACrC,MAAM,YAAY,UAAU,OAAO,CAAC,OAAO,KAAK,uBAAuB;IAEvE,0BAA0B;IAC1B,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,aAAa,iBAAiB,EAAE,WAAW;QACvD,KAAK;YACH,OAAO,GAAG,aAAa,kBAAkB,EAAE,WAAW;QACxD,KAAK;YACH,OAAO,GAAG,aAAa,MAAM,EAAE,WAAW;QAC5C,KAAK;YACH,OAAO,GAAG,aAAa,SAAS,EAAE,WAAW;QAC/C,KAAK;YACH,OAAO,GAAG,aAAa,QAAQ,EAAE,WAAW;QAC9C;YACE,OAAO,GAAG,aAAa,CAAC,EAAE,WAAW;IACzC;AACF;AAGO,SAAS,qBACd,SAAiB,EACjB,WAAiB,EACjB,UAAiD,OAAO;IAExD,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,UAAU,cAAc,WAAW,aAAa;IAEtD,8BAA8B;IAC9B,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,YAAY,QAAQ,QAAQ,CAAC,OAAO,MAAM;IAEhD,OAAO,GAAG,UAAU,UAAU,EAAE,EAAE,aAAa;AACjD;AAGO,SAAS,sBACd,SAAiB,EACjB,WAAiB;IAEjB,OAAO,cAAc,WAAW,aAAa;AAC/C;AAGO,SAAS,aACd,SAAiB,EACjB,WAAiB;IAEjB,OAAO,cAAc,WAAW,aAAa;AAC/C;AAGO,SAAS,iBACd,SAAiB,EACjB,WAAiB;IAEjB,OAAO,cAAc,WAAW,aAAa;AAC/C;AAGO,SAAS,eACd,SAAiB,EACjB,KAAgD,EAChD,WAAiB,EACjB,OAA0C,SAAS;IAEnD,IAAI,CAAC,WAAW,OAAO;IAEvB,OAAO,MACJ,GAAG,CAAC,CAAA;QACH,MAAM,MAAM,cAAc,WAAW,aAAa;QAClD,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;IAChC,GACC,IAAI,CAAC;AACV;AAGO,SAAS,uBACd,WAA0E;IAE1E,OAAO,YACJ,GAAG,CAAC,CAAA;QACH,IAAI,GAAG,QAAQ,IAAI,GAAG,QAAQ,EAAE;YAC9B,OAAO,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,oBAAoB,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE;QACrF,OAAO,IAAI,GAAG,QAAQ,EAAE;YACtB,OAAO,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE;QACnD,OAAO,IAAI,GAAG,QAAQ,EAAE;YACtB,OAAO,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE;QACnD,OAAO;YACL,OAAO,GAAG,IAAI;QAChB;IACF,GACC,IAAI,CAAC;AACV;AAGO,SAAS,uBACd,SAAiB,EACjB,GAAW,EACX,IAAe,EACf,OAAe,EACf,WAAiB,EACjB,OAA0C,SAAS;IAEnD,MAAM,cAAc,WAAW,CAAC,KAAK,EAAE,CAAC,QAAiD;IAEzF,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,KAAK,CAAC,EAAE,SAAS;IACxE;IAEA,MAAM,MAAM,cAAc,WAAW,aAAa;IAElD,OAAO;QACL;QACA;QACA,OAAO,YAAY,KAAK;QACxB,QAAQ,YAAY,MAAM;QAC1B,OAAO,uBAAuB;YAC5B;gBAAE,UAAU;gBAAK,MAAM;YAAQ;YAC/B;gBAAE,UAAU;gBAAK,MAAM,GAAG,YAAY,KAAK,CAAC,EAAE,CAAC;YAAC;SACjD;IACH;AACF;AAGO,SAAS,iBAAiB,OAAe;IAC9C,IAAI,CAAC,SAAS,OAAO;IAErB,uCAAuC;IACvC,IAAI,CAAC,QAAQ,UAAU,CAAC,SAAS;QAC/B,OAAO;IACT;IAEA,IAAI;QACF,MAAM,MAAM,IAAI,IAAI;QACpB,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC;QAErC,sDAAsD;QACtD,MAAM,aAAa,UAAU,SAAS,CAAC,CAAA,OAAQ,SAAS;QACxD,IAAI,eAAe,CAAC,KAAK,aAAa,UAAU,MAAM,GAAG,GAAG;YAC1D,OAAO,UAAU,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9C;QAEA,sDAAsD;QACtD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO;IACrC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,GAAW;IACzC,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,oBACd,KAAa,EACb,MAAc,EACd,IAAa;IAEb,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,KAAK,GAAG;IACf,OAAO,MAAM,GAAG;IAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;IAC9B,IAAI,CAAC,KAAK,OAAO;IAEjB,kCAAkC;IAClC,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;IAE1B,uBAAuB;IACvB,IAAI,MAAM;QACR,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI,GAAG;QACX,IAAI,SAAS,GAAG;QAChB,IAAI,YAAY,GAAG;QACnB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,SAAS;IACzC;IAEA,OAAO,OAAO,SAAS;AACzB;AAGO,SAAS,iBACd,KAA6C,EAC7C,WAAoB;IAEpB,MAAM,MAAM,MAAM,aAAa;IAE/B,IAAI,eAAe,IAAI,GAAG,KAAK,aAAa;QAC1C,IAAI,GAAG,GAAG;IACZ,OAAO;QACL,yBAAyB;QACzB,MAAM,cAAc,oBAClB,IAAI,KAAK,IAAI,KACb,IAAI,MAAM,IAAI,KACd;QAEF,IAAI,GAAG,GAAG;IACZ;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG,IAAM;QACnB,IAAI,OAAO,GAAG;QACd,IAAI,GAAG,GAAG;IACZ;AACF;AAGO,eAAe,cAAc,IAAc;IAChD,IAAI;QACF,MAAM,QAAQ,GAAG,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,aAAa;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC;IACjD;AACF;AAGO,SAAS,mBAAmB,GAAW;IAC5C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,QAAQ;gBAAE,OAAO,IAAI,YAAY;gBAAE,QAAQ,IAAI,aAAa;YAAC;QAC/D;QACA,IAAI,OAAO,GAAG;QACd,IAAI,GAAG,GAAG;IACZ;AACF", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/ui/MagentoImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Image, { ImageProps } from 'next/image';\nimport { Box, Skeleton } from '@mui/material';\nimport { \n  buildMediaUrl, \n  buildProductImageUrl, \n  buildCategoryImageUrl, \n  buildLogoUrl,\n  buildCmsImageUrl,\n  handleImageError,\n  getImagePlaceholder,\n  IMAGE_SIZES,\n  type ImageSize,\n  type ImageVariant\n} from '@/lib/utils/imageUtils';\n\n// Magento Image Props\ninterface MagentoImageProps extends Omit<ImageProps, 'src'> {\n  src: string;\n  alt: string;\n  type?: 'product' | 'category' | 'logo' | 'cms' | 'banner';\n  variant?: string;\n  size?: ImageSize;\n  storeConfig?: any;\n  showSkeleton?: boolean;\n  fallbackSrc?: string;\n  onError?: (error: React.SyntheticEvent<HTMLImageElement>) => void;\n}\n\n// Product Image Component\nexport function ProductImage({\n  src,\n  alt,\n  variant = 'image',\n  storeConfig,\n  showSkeleton = true,\n  fallbackSrc,\n  onError,\n  ...props\n}: Omit<MagentoImageProps, 'type'>) {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  const imageUrl = buildProductImageUrl(src, storeConfig, variant as any);\n\n  const handleLoad = () => {\n    setLoading(false);\n  };\n\n  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {\n    setError(true);\n    setLoading(false);\n    \n    if (onError) {\n      onError(event);\n    } else {\n      handleImageError(event, fallbackSrc);\n    }\n  };\n\n  if (error && !fallbackSrc) {\n    return (\n      <Box\n        sx={{\n          width: props.width || 300,\n          height: props.height || 300,\n          backgroundColor: '#f5f5f5',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '14px',\n        }}\n      >\n        Image not available\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      {loading && showSkeleton && (\n        <Skeleton\n          variant=\"rectangular\"\n          width={props.width || 300}\n          height={props.height || 300}\n          sx={{ position: 'absolute', top: 0, left: 0 }}\n        />\n      )}\n      <Image\n        {...props}\n        src={imageUrl}\n        alt={alt}\n        onLoad={handleLoad}\n        onError={handleImageError}\n        style={{\n          ...props.style,\n          opacity: loading ? 0 : 1,\n          transition: 'opacity 0.3s ease',\n        }}\n      />\n    </Box>\n  );\n}\n\n// Category Image Component\nexport function CategoryImage({\n  src,\n  alt,\n  storeConfig,\n  showSkeleton = true,\n  fallbackSrc,\n  onError,\n  ...props\n}: Omit<MagentoImageProps, 'type' | 'variant'>) {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  const imageUrl = buildCategoryImageUrl(src, storeConfig);\n\n  const handleLoad = () => {\n    setLoading(false);\n  };\n\n  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {\n    setError(true);\n    setLoading(false);\n    \n    if (onError) {\n      onError(event);\n    } else {\n      handleImageError(event, fallbackSrc);\n    }\n  };\n\n  if (error && !fallbackSrc) {\n    return (\n      <Box\n        sx={{\n          width: props.width || 300,\n          height: props.height || 300,\n          backgroundColor: '#f5f5f5',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '14px',\n        }}\n      >\n        Category image not available\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      {loading && showSkeleton && (\n        <Skeleton\n          variant=\"rectangular\"\n          width={props.width || 300}\n          height={props.height || 300}\n          sx={{ position: 'absolute', top: 0, left: 0 }}\n        />\n      )}\n      <Image\n        {...props}\n        src={imageUrl}\n        alt={alt}\n        onLoad={handleLoad}\n        onError={handleImageError}\n        style={{\n          ...props.style,\n          opacity: loading ? 0 : 1,\n          transition: 'opacity 0.3s ease',\n        }}\n      />\n    </Box>\n  );\n}\n\n// Logo Image Component\nexport function LogoImage({\n  src,\n  alt,\n  storeConfig,\n  showSkeleton = false,\n  fallbackSrc,\n  onError,\n  ...props\n}: Omit<MagentoImageProps, 'type' | 'variant'>) {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  const imageUrl = buildLogoUrl(src, storeConfig);\n\n  const handleLoad = () => {\n    setLoading(false);\n  };\n\n  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {\n    setError(true);\n    setLoading(false);\n    \n    if (onError) {\n      onError(event);\n    } else {\n      handleImageError(event, fallbackSrc);\n    }\n  };\n\n  if (error && !fallbackSrc) {\n    return null; // Don't show anything for logo errors\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      {loading && showSkeleton && (\n        <Skeleton\n          variant=\"rectangular\"\n          width={props.width || 120}\n          height={props.height || 40}\n          sx={{ position: 'absolute', top: 0, left: 0 }}\n        />\n      )}\n      <Image\n        {...props}\n        src={imageUrl}\n        alt={alt}\n        onLoad={handleLoad}\n        onError={handleImageError}\n        style={{\n          ...props.style,\n          opacity: loading ? 0 : 1,\n          transition: 'opacity 0.3s ease',\n        }}\n      />\n    </Box>\n  );\n}\n\n// CMS Image Component\nexport function CmsImage({\n  src,\n  alt,\n  storeConfig,\n  showSkeleton = true,\n  fallbackSrc,\n  onError,\n  ...props\n}: Omit<MagentoImageProps, 'type' | 'variant'>) {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  const imageUrl = buildCmsImageUrl(src, storeConfig);\n\n  const handleLoad = () => {\n    setLoading(false);\n  };\n\n  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {\n    setError(true);\n    setLoading(false);\n    \n    if (onError) {\n      onError(event);\n    } else {\n      handleImageError(event, fallbackSrc);\n    }\n  };\n\n  if (error && !fallbackSrc) {\n    return (\n      <Box\n        sx={{\n          width: props.width || 300,\n          height: props.height || 200,\n          backgroundColor: '#f5f5f5',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '14px',\n        }}\n      >\n        Image not available\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      {loading && showSkeleton && (\n        <Skeleton\n          variant=\"rectangular\"\n          width={props.width || 300}\n          height={props.height || 200}\n          sx={{ position: 'absolute', top: 0, left: 0 }}\n        />\n      )}\n      <Image\n        {...props}\n        src={imageUrl}\n        alt={alt}\n        onLoad={handleLoad}\n        onError={handleImageError}\n        style={{\n          ...props.style,\n          opacity: loading ? 0 : 1,\n          transition: 'opacity 0.3s ease',\n        }}\n      />\n    </Box>\n  );\n}\n\n// Generic Magento Image Component\nexport default function MagentoImage({\n  src,\n  alt,\n  type = 'product',\n  variant,\n  size,\n  storeConfig,\n  showSkeleton = true,\n  fallbackSrc,\n  onError,\n  ...props\n}: MagentoImageProps) {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n\n  let imageUrl: string;\n  \n  switch (type) {\n    case 'product':\n      imageUrl = buildProductImageUrl(src, storeConfig, variant as any);\n      break;\n    case 'category':\n      imageUrl = buildCategoryImageUrl(src, storeConfig);\n      break;\n    case 'logo':\n      imageUrl = buildLogoUrl(src, storeConfig);\n      break;\n    case 'cms':\n      imageUrl = buildCmsImageUrl(src, storeConfig);\n      break;\n    default:\n      imageUrl = buildMediaUrl(src, storeConfig, type);\n  }\n\n  const handleLoad = () => {\n    setLoading(false);\n  };\n\n  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {\n    setError(true);\n    setLoading(false);\n    \n    if (onError) {\n      onError(event);\n    } else {\n      handleImageError(event, fallbackSrc);\n    }\n  };\n\n  if (error && !fallbackSrc) {\n    return (\n      <Box\n        sx={{\n          width: props.width || 300,\n          height: props.height || 300,\n          backgroundColor: '#f5f5f5',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: '#999',\n          fontSize: '14px',\n        }}\n      >\n        Image not available\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ position: 'relative' }}>\n      {loading && showSkeleton && (\n        <Skeleton\n          variant=\"rectangular\"\n          width={props.width || 300}\n          height={props.height || 300}\n          sx={{ position: 'absolute', top: 0, left: 0 }}\n        />\n      )}\n      <Image\n        {...props}\n        src={imageUrl}\n        alt={alt}\n        onLoad={handleLoad}\n        onError={handleImageError}\n        style={{\n          ...props.style,\n          opacity: loading ? 0 : 1,\n          transition: 'opacity 0.3s ease',\n        }}\n      />\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAgCO,SAAS,aAAa,EAC3B,GAAG,EACH,GAAG,EACH,UAAU,OAAO,EACjB,WAAW,EACX,eAAe,IAAI,EACnB,WAAW,EACX,OAAO,EACP,GAAG,OAC6B;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,aAAa;IAExD,MAAM,aAAa;QACjB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QAEX,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,SAAS,CAAC,aAAa;QACzB,qBACE,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,iBAAiB;gBACjB,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;sBACD;;;;;;IAIL;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;YAC7B,WAAW,8BACV,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,IAAI;oBAAE,UAAU;oBAAY,KAAK;oBAAG,MAAM;gBAAE;;;;;;0BAGhD,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,KAAK;gBACT,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;oBACL,GAAG,MAAM,KAAK;oBACd,SAAS,UAAU,IAAI;oBACvB,YAAY;gBACd;;;;;;;;;;;;AAIR;GAzEgB;KAAA;AA4ET,SAAS,cAAc,EAC5B,GAAG,EACH,GAAG,EACH,WAAW,EACX,eAAe,IAAI,EACnB,WAAW,EACX,OAAO,EACP,GAAG,OACyC;;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK;IAE5C,MAAM,aAAa;QACjB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QAEX,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,SAAS,CAAC,aAAa;QACzB,qBACE,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,iBAAiB;gBACjB,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;sBACD;;;;;;IAIL;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;YAC7B,WAAW,8BACV,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,IAAI;oBAAE,UAAU;oBAAY,KAAK;oBAAG,MAAM;gBAAE;;;;;;0BAGhD,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,KAAK;gBACT,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;oBACL,GAAG,MAAM,KAAK;oBACd,SAAS,UAAU,IAAI;oBACvB,YAAY;gBACd;;;;;;;;;;;;AAIR;IAxEgB;MAAA;AA2ET,SAAS,UAAU,EACxB,GAAG,EACH,GAAG,EACH,WAAW,EACX,eAAe,KAAK,EACpB,WAAW,EACX,OAAO,EACP,GAAG,OACyC;;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,KAAK;IAEnC,MAAM,aAAa;QACjB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QAEX,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,SAAS,CAAC,aAAa;QACzB,OAAO,MAAM,sCAAsC;IACrD;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;YAC7B,WAAW,8BACV,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,IAAI;oBAAE,UAAU;oBAAY,KAAK;oBAAG,MAAM;gBAAE;;;;;;0BAGhD,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,KAAK;gBACT,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;oBACL,GAAG,MAAM,KAAK;oBACd,SAAS,UAAU,IAAI;oBACvB,YAAY;gBACd;;;;;;;;;;;;AAIR;IAzDgB;MAAA;AA4DT,SAAS,SAAS,EACvB,GAAG,EACH,GAAG,EACH,WAAW,EACX,eAAe,IAAI,EACnB,WAAW,EACX,OAAO,EACP,GAAG,OACyC;;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;IAEvC,MAAM,aAAa;QACjB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QAEX,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,SAAS,CAAC,aAAa;QACzB,qBACE,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,iBAAiB;gBACjB,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;sBACD;;;;;;IAIL;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;YAC7B,WAAW,8BACV,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,IAAI;oBAAE,UAAU;oBAAY,KAAK;oBAAG,MAAM;gBAAE;;;;;;0BAGhD,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,KAAK;gBACT,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;oBACL,GAAG,MAAM,KAAK;oBACd,SAAS,UAAU,IAAI;oBACvB,YAAY;gBACd;;;;;;;;;;;;AAIR;IAxEgB;MAAA;AA2ED,SAAS,aAAa,EACnC,GAAG,EACH,GAAG,EACH,OAAO,SAAS,EAChB,OAAO,EACP,IAAI,EACJ,WAAW,EACX,eAAe,IAAI,EACnB,WAAW,EACX,OAAO,EACP,GAAG,OACe;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,WAAW,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,aAAa;YAClD;QACF,KAAK;YACH,WAAW,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK;YACtC;QACF,KAAK;YACH,WAAW,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAC7B;QACF,KAAK;YACH,WAAW,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YACjC;QACF;YACE,WAAW,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,aAAa;IAC/C;IAEA,MAAM,aAAa;QACjB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QAEX,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,iBAAiB,OAAO;QAC1B;IACF;IAEA,IAAI,SAAS,CAAC,aAAa;QACzB,qBACE,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,iBAAiB;gBACjB,SAAS;gBACT,YAAY;gBACZ,gBAAgB;gBAChB,OAAO;gBACP,UAAU;YACZ;sBACD;;;;;;IAIL;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,UAAU;QAAW;;YAC7B,WAAW,8BACV,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,OAAO,MAAM,KAAK,IAAI;gBACtB,QAAQ,MAAM,MAAM,IAAI;gBACxB,IAAI;oBAAE,UAAU;oBAAY,KAAK;oBAAG,MAAM;gBAAE;;;;;;0BAGhD,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,KAAK;gBACT,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;oBACL,GAAG,MAAM,KAAK;oBACd,SAAS,UAAU,IAAI;oBACvB,YAAY;gBACd;;;;;;;;;;;;AAIR;IA5FwB;MAAA", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/graphql/simpleClient.ts"], "sourcesContent": ["// Simple GraphQL Client for browser and server\n\n// GraphQL Client Configuration\ninterface GraphQLClientConfig {\n  endpoint: string;\n  headers?: Record<string, string>;\n  timeout?: number;\n  retries?: number;\n}\n\n// GraphQL Response Interface\ninterface GraphQLResponse<T = any> {\n  data?: T;\n  errors?: Array<{\n    message: string;\n    locations?: Array<{ line: number; column: number }>;\n    path?: Array<string | number>;\n    extensions?: Record<string, any>;\n  }>;\n  extensions?: Record<string, any>;\n}\n\n// GraphQL Variables Interface\ninterface GraphQLVariables {\n  [key: string]: any;\n}\n\n// Simple GraphQL Client Class\nexport class SimpleGraphQLClient {\n  private config: GraphQLClientConfig;\n\n  constructor(config: GraphQLClientConfig) {\n    this.config = {\n      timeout: 30000,\n      retries: 3,\n      ...config,\n    };\n  }\n\n  // Execute GraphQL query/mutation\n  async execute<T = any>(\n    query: string,\n    variables?: GraphQLVariables,\n    options?: {\n      headers?: Record<string, string>;\n      timeout?: number;\n      cache?: RequestCache;\n    }\n  ): Promise<T> {\n    const headers = {\n      'Content-Type': 'application/json',\n      ...this.config.headers,\n      ...options?.headers,\n    };\n\n    const body = JSON.stringify({\n      query,\n      variables: variables || {},\n    });\n\n    const requestOptions: RequestInit = {\n      method: 'POST',\n      headers,\n      body,\n      cache: options?.cache || 'default',\n    };\n\n    // Add timeout if specified\n    const timeout = options?.timeout || this.config.timeout;\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), timeout);\n    requestOptions.signal = controller.signal;\n\n    try {\n      const response = await this.executeWithRetry(requestOptions);\n      clearTimeout(timeoutId);\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const result: GraphQLResponse<T> = await response.json();\n\n      if (result.errors && result.errors.length > 0) {\n        const errorMessage = result.errors.map(error => error.message).join(', ');\n        console.error('GraphQL Errors:', result.errors);\n        throw new Error(`GraphQL Error: ${errorMessage}`);\n      }\n\n      if (!result.data) {\n        throw new Error('No data returned from GraphQL query');\n      }\n\n      return result.data;\n    } catch (error) {\n      clearTimeout(timeoutId);\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          throw new Error(`Request timeout after ${timeout}ms`);\n        }\n        throw error;\n      }\n      \n      throw new Error('Unknown error occurred during GraphQL request');\n    }\n  }\n\n  // Execute request with retry logic\n  private async executeWithRetry(options: RequestInit, attempt = 1): Promise<Response> {\n    try {\n      return await fetch(this.config.endpoint, options);\n    } catch (error) {\n      if (attempt < this.config.retries!) {\n        // Exponential backoff\n        const delay = Math.pow(2, attempt) * 1000;\n        await new Promise(resolve => setTimeout(resolve, delay));\n        return this.executeWithRetry(options, attempt + 1);\n      }\n      throw error;\n    }\n  }\n\n  // Update client configuration\n  updateConfig(newConfig: Partial<GraphQLClientConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n  }\n\n  // Get current configuration\n  getConfig(): GraphQLClientConfig {\n    return { ...this.config };\n  }\n}\n\n// Default GraphQL client instance\nexport const graphqlClient = new SimpleGraphQLClient({\n  endpoint: process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'https://your-magento-store.com/graphql',\n  headers: {\n    'Store': process.env.NEXT_PUBLIC_MAGENTO_STORE_CODE || 'default',\n    'X-Requested-With': 'XMLHttpRequest',\n  },\n});\n\n// Convenience function for making GraphQL requests\nexport async function gql<T = any>(\n  query: string,\n  variables?: GraphQLVariables,\n  options?: {\n    headers?: Record<string, string>;\n    timeout?: number;\n    cache?: RequestCache;\n  }\n): Promise<T> {\n  return graphqlClient.execute<T>(query, variables, options);\n}\n\n// Server-side GraphQL request with caching\nexport async function serverGraphQL<T = any>(\n  query: string,\n  variables?: GraphQLVariables,\n  revalidate?: number\n): Promise<T> {\n  const response = await fetch(graphqlClient.getConfig().endpoint, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      ...graphqlClient.getConfig().headers,\n    },\n    body: JSON.stringify({\n      query,\n      variables: variables || {},\n    }),\n    next: revalidate ? { revalidate } : undefined,\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n\n  const result: GraphQLResponse<T> = await response.json();\n\n  if (result.errors && result.errors.length > 0) {\n    const errorMessage = result.errors.map(error => error.message).join(', ');\n    console.error('GraphQL Errors:', result.errors);\n    throw new Error(`GraphQL Error: ${errorMessage}`);\n  }\n\n  if (!result.data) {\n    throw new Error('No data returned from GraphQL query');\n  }\n\n  return result.data;\n}\n\nexport default graphqlClient;\n"], "names": [], "mappings": "AAAA,+CAA+C;AAE/C,+BAA+B;;;;;;;;AAsInB;AA5GL,MAAM;IACH,OAA4B;IAEpC,YAAY,MAA2B,CAAE;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS;YACT,SAAS;YACT,GAAG,MAAM;QACX;IACF;IAEA,iCAAiC;IACjC,MAAM,QACJ,KAAa,EACb,SAA4B,EAC5B,OAIC,EACW;QACZ,MAAM,UAAU;YACd,gBAAgB;YAChB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO;YACtB,GAAG,SAAS,OAAO;QACrB;QAEA,MAAM,OAAO,KAAK,SAAS,CAAC;YAC1B;YACA,WAAW,aAAa,CAAC;QAC3B;QAEA,MAAM,iBAA8B;YAClC,QAAQ;YACR;YACA;YACA,OAAO,SAAS,SAAS;QAC3B;QAEA,2BAA2B;QAC3B,MAAM,UAAU,SAAS,WAAW,IAAI,CAAC,MAAM,CAAC,OAAO;QACvD,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;QACvD,eAAe,MAAM,GAAG,WAAW,MAAM;QAEzC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC7C,aAAa;YAEb,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,eAAe,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,OAAO,EAAE,IAAI,CAAC;gBACpE,QAAQ,KAAK,CAAC,mBAAmB,OAAO,MAAM;gBAC9C,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,cAAc;YAClD;YAEA,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,aAAa;YAEb,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAC;gBACtD;gBACA,MAAM;YACR;YAEA,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,mCAAmC;IACnC,MAAc,iBAAiB,OAAoB,EAAE,UAAU,CAAC,EAAqB;QACnF,IAAI;YACF,OAAO,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,EAAG;gBAClC,sBAAsB;gBACtB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW;gBACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,UAAU;YAClD;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,aAAa,SAAuC,EAAQ;QAC1D,IAAI,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;IAC/C;IAEA,4BAA4B;IAC5B,YAAiC;QAC/B,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;AACF;AAGO,MAAM,gBAAgB,IAAI,oBAAoB;IACnD,UAAU,sEAA+C;IACzD,SAAS;QACP,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI;QACvD,oBAAoB;IACtB;AACF;AAGO,eAAe,IACpB,KAAa,EACb,SAA4B,EAC5B,OAIC;IAED,OAAO,cAAc,OAAO,CAAI,OAAO,WAAW;AACpD;AAGO,eAAe,cACpB,KAAa,EACb,SAA4B,EAC5B,UAAmB;IAEnB,MAAM,WAAW,MAAM,MAAM,cAAc,SAAS,GAAG,QAAQ,EAAE;QAC/D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,GAAG,cAAc,SAAS,GAAG,OAAO;QACtC;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA,WAAW,aAAa,CAAC;QAC3B;QACA,MAAM,aAAa;YAAE;QAAW,IAAI;IACtC;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;IACnE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;QAC7C,MAAM,eAAe,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,OAAO,EAAE,IAAI,CAAC;QACpE,QAAQ,KAAK,CAAC,mBAAmB,OAAO,MAAM;QAC9C,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,cAAc;IAClD;IAEA,IAAI,CAAC,OAAO,IAAI,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/graphql/queries.ts"], "sourcesContent": ["// GraphQL Queries as constants (for browser compatibility)\n\n// Store Configuration Queries\nexport const GET_STORE_CONFIG_BASIC = `\n  query GetStoreConfigBasic {\n    storeConfig {\n      id\n      code\n      store_name\n      locale\n      base_currency_code\n      default_display_currency_code\n      timezone\n      base_url\n      base_media_url\n      cms_home_page\n      cms_no_route\n      default_title\n      default_description\n      default_keywords\n      header_logo_src\n      logo_alt\n      welcome\n      copyright\n    }\n  }\n`;\n\nexport const GET_HOMEPAGE_IDENTIFIER = `\n  query GetHomepageIdentifier {\n    storeConfig {\n      cms_home_page\n      store_name\n      default_title\n    }\n  }\n`;\n\n// Category Queries\nexport const GET_CATEGORIES_FOR_MENU = `\n  query GetCategoriesForMenu {\n    categoryList(filters: { include_in_menu: { eq: true } }) {\n      uid\n      id\n      name\n      url_key\n      url_path\n      position\n      level\n      include_in_menu\n      children_count\n      image\n      children {\n        uid\n        id\n        name\n        url_key\n        url_path\n        position\n        level\n        include_in_menu\n        children_count\n        image\n        children {\n          uid\n          id\n          name\n          url_key\n          url_path\n          position\n          level\n          include_in_menu\n          children_count\n          image\n        }\n      }\n    }\n  }\n`;\n\nexport const GET_CATEGORY_BY_URL_KEY = `\n  query GetCategoryByUrlKey($urlKey: String!) {\n    categoryList(filters: { url_key: { eq: $urlKey } }) {\n      uid\n      id\n      name\n      url_key\n      url_path\n      url_suffix\n      description\n      meta_title\n      meta_keywords\n      meta_description\n      image\n      path\n      path_in_store\n      position\n      level\n      children_count\n      include_in_menu\n      is_anchor\n      default_sort_by\n      available_sort_by\n      landing_page\n      custom_layout_update_file\n      breadcrumbs {\n        category_id\n        category_name\n        category_level\n        category_url_key\n        category_url_path\n      }\n    }\n  }\n`;\n\nexport const GET_CATEGORY_BY_ID = `\n  query GetCategoryById($id: String!) {\n    categoryList(filters: { ids: { eq: $id } }) {\n      uid\n      id\n      name\n      url_key\n      url_path\n      url_suffix\n      description\n      meta_title\n      meta_keywords\n      meta_description\n      image\n      path\n      path_in_store\n      position\n      level\n      children_count\n      include_in_menu\n      is_anchor\n      default_sort_by\n      available_sort_by\n      landing_page\n      custom_layout_update_file\n      breadcrumbs {\n        category_id\n        category_name\n        category_level\n        category_url_key\n        category_url_path\n      }\n    }\n  }\n`;\n\n// Product Search Query\nexport const SEARCH_PRODUCTS = `\n  query SearchProducts(\n    $search: String!\n    $pageSize: Int = 20\n    $currentPage: Int = 1\n    $sort: ProductAttributeSortInput\n    $filter: ProductAttributeFilterInput\n  ) {\n    products(\n      search: $search\n      pageSize: $pageSize\n      currentPage: $currentPage\n      sort: $sort\n      filter: $filter\n    ) {\n      items {\n        uid\n        id\n        name\n        sku\n        url_key\n        image {\n          url\n          label\n        }\n        small_image {\n          url\n          label\n        }\n        price_range {\n          minimum_price {\n            regular_price {\n              value\n              currency\n            }\n            final_price {\n              value\n              currency\n            }\n            discount {\n              amount_off\n              percent_off\n            }\n          }\n        }\n        rating_summary\n        review_count\n        stock_status\n      }\n      page_info {\n        page_size\n        current_page\n        total_pages\n      }\n      total_count\n      aggregations {\n        label\n        count\n        attribute_code\n        options {\n          label\n          value\n          count\n        }\n      }\n      sort_fields {\n        default\n        options {\n          label\n          value\n        }\n      }\n    }\n  }\n`;\n\n// CMS Queries\nexport const GET_CMS_PAGE = `\n  query GetCmsPage($identifier: String!) {\n    cmsPage(identifier: $identifier) {\n      identifier\n      url_key\n      title\n      content\n      content_heading\n      page_layout\n      meta_title\n      meta_description\n      meta_keywords\n      created_at\n      updated_at\n    }\n  }\n`;\n\nexport const GET_CMS_BLOCK = `\n  query GetCmsBlock($identifier: String!) {\n    cmsBlocks(identifiers: [$identifier]) {\n      items {\n        identifier\n        title\n        content\n        created_at\n        updated_at\n      }\n    }\n  }\n`;\n\n// Cart Queries\nexport const GET_CART = `\n  query GetCart($cartId: String!) {\n    cart(cart_id: $cartId) {\n      id\n      email\n      is_virtual\n      applied_coupons {\n        code\n      }\n      itemsV2 {\n        items {\n          uid\n          product {\n            uid\n            name\n            sku\n            url_key\n            thumbnail {\n              url\n              label\n            }\n            price_range {\n              minimum_price {\n                regular_price {\n                  value\n                  currency\n                }\n                final_price {\n                  value\n                  currency\n                }\n              }\n            }\n            stock_status\n          }\n          prices {\n            price {\n              value\n              currency\n            }\n            row_total {\n              value\n              currency\n            }\n            row_total_including_tax {\n              value\n              currency\n            }\n            total_item_discount {\n              value\n              currency\n            }\n          }\n          quantity\n        }\n        page_info {\n          page_size\n          current_page\n          total_pages\n        }\n        total_count\n      }\n      prices {\n        grand_total {\n          value\n          currency\n        }\n        subtotal_excluding_tax {\n          value\n          currency\n        }\n        subtotal_including_tax {\n          value\n          currency\n        }\n        applied_taxes {\n          label\n          amount {\n            value\n            currency\n          }\n        }\n        discounts {\n          amount {\n            value\n            currency\n          }\n          label\n        }\n      }\n      total_quantity\n    }\n  }\n`;\n\n// Cart Mutations\nexport const CREATE_EMPTY_CART = `\n  mutation CreateEmptyCart {\n    createEmptyCart\n  }\n`;\n\nexport const ADD_SIMPLE_PRODUCTS_TO_CART = `\n  mutation AddSimpleProductsToCart($cartId: String!, $cartItems: [SimpleProductCartItemInput!]!) {\n    addSimpleProductsToCart(input: { cart_id: $cartId, cart_items: $cartItems }) {\n      cart {\n        id\n        total_quantity\n        itemsV2 {\n          total_count\n        }\n      }\n    }\n  }\n`;\n"], "names": [], "mappings": "AAAA,2DAA2D;AAE3D,8BAA8B;;;;;;;;;;;;;;AACvB,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBvC,CAAC;AAEM,MAAM,0BAA0B,CAAC;;;;;;;;AAQxC,CAAC;AAGM,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCxC,CAAC;AAEM,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCxC,CAAC;AAEM,MAAM,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCnC,CAAC;AAGM,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EhC,CAAC;AAGM,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;AAgB7B,CAAC;AAEM,MAAM,gBAAgB,CAAC;;;;;;;;;;;;AAY9B,CAAC;AAGM,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FzB,CAAC;AAGM,MAAM,oBAAoB,CAAC;;;;AAIlC,CAAC;AAEM,MAAM,8BAA8B,CAAC;;;;;;;;;;;;AAY5C,CAAC", "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/api/categories.ts"], "sourcesContent": ["// Categories API functions using Magento GraphQL\n\nimport { serverGraphQL, gql } from '@/lib/graphql/simpleClient';\nimport {\n  GET_CATEGORIES_FOR_MENU,\n  GET_CATEGORY_BY_URL_KEY,\n  GET_CATEGORY_BY_ID,\n} from '@/lib/graphql/queries';\n\n// Category interface\nexport interface Category {\n  uid: string;\n  id: number;\n  name: string;\n  url_key: string;\n  url_path: string;\n  url_suffix?: string;\n  description?: string;\n  meta_title?: string;\n  meta_keywords?: string;\n  meta_description?: string;\n  image?: string;\n  path: string;\n  path_in_store?: string;\n  position: number;\n  level: number;\n  children_count: number;\n  include_in_menu: boolean;\n  is_anchor?: boolean;\n  default_sort_by?: string;\n  available_sort_by?: string[];\n  landing_page?: number;\n  custom_layout_update_file?: string;\n  children?: Category[];\n  breadcrumbs?: Array<{\n    category_id: number;\n    category_name: string;\n    category_level: number;\n    category_url_key: string;\n    category_url_path: string;\n  }>;\n}\n\n// Get categories for menu navigation\nexport async function getCategoriesForMenu(\n  revalidate: number = 3600 // 1 hour\n): Promise<Category[]> {\n  try {\n    const response = await serverGraphQL<{\n      categoryList: Category[];\n    }>(GET_CATEGORIES_FOR_MENU, {}, revalidate);\n\n    return response.categoryList || [];\n  } catch (error) {\n    console.error('Error fetching categories for menu:', error);\n    return [];\n  }\n}\n\n// Get category by URL key\nexport async function getCategoryByUrlKey(\n  urlKey: string,\n  revalidate: number = 3600 // 1 hour\n): Promise<Category | null> {\n  try {\n    const response = await serverGraphQL<{\n      categoryList: Category[];\n    }>(GET_CATEGORY_BY_URL_KEY, { urlKey }, revalidate);\n\n    return response.categoryList[0] || null;\n  } catch (error) {\n    console.error('Error fetching category by URL key:', error);\n    return null;\n  }\n}\n\n// Get category by ID\nexport async function getCategoryById(\n  id: string,\n  revalidate: number = 3600 // 1 hour\n): Promise<Category | null> {\n  try {\n    const response = await serverGraphQL<{\n      categoryList: Category[];\n    }>(GET_CATEGORY_BY_ID, { id }, revalidate);\n\n    return response.categoryList[0] || null;\n  } catch (error) {\n    console.error('Error fetching category by ID:', error);\n    return null;\n  }\n}\n\n// Client-side function to get categories for menu\nexport async function getCategoriesForMenuClient(): Promise<Category[]> {\n  try {\n    const response = await gql<{\n      categoryList: Category[];\n    }>(GET_CATEGORIES_FOR_MENU);\n\n    return response.categoryList || [];\n  } catch (error) {\n    console.error('Error fetching categories for menu (client):', error);\n    return [];\n  }\n}\n\n// Build category URL\nexport function getCategoryUrl(category: Category, storeConfig?: any): string {\n  const suffix = storeConfig?.category_url_suffix || '.html';\n  return `/${category.url_path}${suffix}`;\n}\n\n// Get category image URL\nexport function getCategoryImageUrl(\n  category: Category,\n  storeConfig?: any\n): string | null {\n  if (!category.image) return null;\n\n  const baseMediaUrl =\n    storeConfig?.base_media_url ||\n    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL;\n\n  if (category.image.startsWith('http')) {\n    return category.image;\n  }\n\n  return `${baseMediaUrl}/catalog/category/${category.image}`;\n}\n\n// Get top-level categories\nexport function getTopLevelCategories(categories: Category[]): Category[] {\n  return categories.filter(category => category.level === 2); // Level 2 is typically top-level in Magento\n}\n\n// Find category by URL key in tree\nexport function findCategoryByUrlKey(\n  categories: Category[],\n  urlKey: string\n): Category | null {\n  for (const category of categories) {\n    if (category.url_key === urlKey) {\n      return category;\n    }\n\n    if (category.children && category.children.length > 0) {\n      const found = findCategoryByUrlKey(category.children, urlKey);\n      if (found) return found;\n    }\n  }\n\n  return null;\n}\n\n// Build category breadcrumbs\nexport function getCategoryBreadcrumbs(\n  category: Category\n): Array<{ name: string; url: string }> {\n  const breadcrumbs = [{ name: 'Home', url: '/' }];\n\n  if (category.breadcrumbs) {\n    category.breadcrumbs.forEach(breadcrumb => {\n      breadcrumbs.push({\n        name: breadcrumb.category_name,\n        url: `/${breadcrumb.category_url_path}`,\n      });\n    });\n  }\n\n  return breadcrumbs;\n}\n\n// Build category tree for navigation\nexport function buildCategoryTree(categories: Category[]): Category[] {\n  const categoryMap = new Map<number, Category>();\n  const rootCategories: Category[] = [];\n\n  // First pass: create map of all categories\n  categories.forEach(category => {\n    categoryMap.set(category.id, { ...category, children: [] });\n  });\n\n  // Second pass: build tree structure\n  categories.forEach(category => {\n    const cat = categoryMap.get(category.id);\n    if (!cat) return;\n\n    if (category.level === 2) {\n      // Top-level category\n      rootCategories.push(cat);\n    } else {\n      // Find parent and add as child\n      const parentPath = category.path.split('/').slice(0, -1).join('/');\n      const parent = Array.from(categoryMap.values()).find(\n        c => c.path === parentPath\n      );\n      if (parent) {\n        parent.children = parent.children || [];\n        parent.children.push(cat);\n      }\n    }\n  });\n\n  return rootCategories;\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;;;;;;AA0H7C;AAxHJ;AACA;;;AAyCO,eAAe,qBACpB,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAEhC,mIAAA,CAAA,0BAAuB,EAAE,CAAC,GAAG;QAEhC,OAAO,SAAS,YAAY,IAAI,EAAE;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,EAAE;IACX;AACF;AAGO,eAAe,oBACpB,MAAc,EACd,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAEhC,mIAAA,CAAA,0BAAuB,EAAE;YAAE;QAAO,GAAG;QAExC,OAAO,SAAS,YAAY,CAAC,EAAE,IAAI;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,eAAe,gBACpB,EAAU,EACV,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAEhC,mIAAA,CAAA,qBAAkB,EAAE;YAAE;QAAG,GAAG;QAE/B,OAAO,SAAS,YAAY,CAAC,EAAE,IAAI;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,MAAG,AAAD,EAEtB,mIAAA,CAAA,0BAAuB;QAE1B,OAAO,SAAS,YAAY,IAAI,EAAE;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eAAe,QAAkB,EAAE,WAAiB;IAClE,MAAM,SAAS,aAAa,uBAAuB;IACnD,OAAO,CAAC,CAAC,EAAE,SAAS,QAAQ,GAAG,QAAQ;AACzC;AAGO,SAAS,oBACd,QAAkB,EAClB,WAAiB;IAEjB,IAAI,CAAC,SAAS,KAAK,EAAE,OAAO;IAE5B,MAAM,eACJ,aAAa,kBACb,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kCAAkC;IAEhD,IAAI,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS;QACrC,OAAO,SAAS,KAAK;IACvB;IAEA,OAAO,GAAG,aAAa,kBAAkB,EAAE,SAAS,KAAK,EAAE;AAC7D;AAGO,SAAS,sBAAsB,UAAsB;IAC1D,OAAO,WAAW,MAAM,CAAC,CAAA,WAAY,SAAS,KAAK,KAAK,IAAI,4CAA4C;AAC1G;AAGO,SAAS,qBACd,UAAsB,EACtB,MAAc;IAEd,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,OAAO,KAAK,QAAQ;YAC/B,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACrD,MAAM,QAAQ,qBAAqB,SAAS,QAAQ,EAAE;YACtD,IAAI,OAAO,OAAO;QACpB;IACF;IAEA,OAAO;AACT;AAGO,SAAS,uBACd,QAAkB;IAElB,MAAM,cAAc;QAAC;YAAE,MAAM;YAAQ,KAAK;QAAI;KAAE;IAEhD,IAAI,SAAS,WAAW,EAAE;QACxB,SAAS,WAAW,CAAC,OAAO,CAAC,CAAA;YAC3B,YAAY,IAAI,CAAC;gBACf,MAAM,WAAW,aAAa;gBAC9B,KAAK,CAAC,CAAC,EAAE,WAAW,iBAAiB,EAAE;YACzC;QACF;IACF;IAEA,OAAO;AACT;AAGO,SAAS,kBAAkB,UAAsB;IACtD,MAAM,cAAc,IAAI;IACxB,MAAM,iBAA6B,EAAE;IAErC,2CAA2C;IAC3C,WAAW,OAAO,CAAC,CAAA;QACjB,YAAY,GAAG,CAAC,SAAS,EAAE,EAAE;YAAE,GAAG,QAAQ;YAAE,UAAU,EAAE;QAAC;IAC3D;IAEA,oCAAoC;IACpC,WAAW,OAAO,CAAC,CAAA;QACjB,MAAM,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE;QACvC,IAAI,CAAC,KAAK;QAEV,IAAI,SAAS,KAAK,KAAK,GAAG;YACxB,qBAAqB;YACrB,eAAe,IAAI,CAAC;QACtB,OAAO;YACL,+BAA+B;YAC/B,MAAM,aAAa,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAC9D,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,CAClD,CAAA,IAAK,EAAE,IAAI,KAAK;YAElB,IAAI,QAAQ;gBACV,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE;gBACvC,OAAO,QAAQ,CAAC,IAAI,CAAC;YACvB;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/api/storeConfig.ts"], "sourcesContent": ["// Store Configuration API functions using Magento GraphQL\n\nimport { serverGraphQL, gql } from '@/lib/graphql/simpleClient';\nimport {\n  GET_STORE_CONFIG_BASIC,\n  GET_HOMEPAGE_IDENTIFIER,\n} from '@/lib/graphql/queries';\n\n// Store Configuration interface\nexport interface StoreConfig {\n  id: number;\n  code: string;\n  website_id: number;\n  locale: string;\n  base_currency_code: string;\n  default_display_currency_code: string;\n  timezone: string;\n  weight_unit: string;\n  base_url: string;\n  base_link_url: string;\n  base_static_url: string;\n  base_media_url: string;\n  secure_base_url: string;\n  secure_base_link_url: string;\n  secure_base_static_url: string;\n  secure_base_media_url: string;\n  store_name: string;\n  store_sort_order: number;\n  is_default_store: boolean;\n  store_group_code: string;\n  store_group_name: string;\n  is_default_store_group: boolean;\n  website_code: string;\n  website_name: string;\n  is_default_website: boolean;\n  root_category_id: number;\n  root_category_uid: string;\n  category_url_suffix: string;\n  product_url_suffix: string;\n  cms_home_page: string;\n  cms_no_route: string;\n  cms_no_cookies: string;\n  show_cms_breadcrumbs: boolean;\n  catalog_default_sort_by: string;\n  grid_per_page: number;\n  list_per_page: number;\n  grid_per_page_values: string;\n  list_per_page_values: string;\n  minimum_password_length: number;\n  required_character_classes_number: number;\n  default_title: string;\n  title_prefix: string;\n  title_suffix: string;\n  default_description: string;\n  default_keywords: string;\n  head_shortcut_icon: string;\n  head_includes: string;\n  header_logo_src: string;\n  logo_width: number;\n  logo_height: number;\n  logo_alt: string;\n  welcome: string;\n  copyright: string;\n  absolute_footer: string;\n  front: string;\n  no_route: string;\n  enable_multiple_wishlists: boolean;\n  send_friend: {\n    enabled_for_customers: boolean;\n    enabled_for_guests: boolean;\n  };\n  magento_wishlist_general_is_enabled: boolean;\n  magento_reward_general_is_enabled: boolean;\n  magento_reward_general_is_enabled_on_front: boolean;\n  magento_reward_general_min_points_balance: number;\n  magento_reward_general_max_points_balance: number;\n  magento_cataloginventory_item_options_manage_stock: boolean;\n  magento_cataloginventory_item_options_backorders: number;\n  magento_cataloginventory_item_options_max_sale_qty: number;\n  magento_cataloginventory_item_options_min_sale_qty: number;\n  magento_cataloginventory_item_options_min_qty: number;\n  magento_cataloginventory_item_options_notify_stock_qty: number;\n  magento_cataloginventory_item_options_enable_qty_increments: boolean;\n  magento_cataloginventory_item_options_qty_increments: number;\n  magento_cataloginventory_item_options_auto_return: boolean;\n  payment_payflowpro_cc_vault_active: boolean;\n  braintree_cc_vault_active: boolean;\n  braintree_paypal_vault_active: boolean;\n  autocomplete_on_storefront: boolean;\n  optional_zip_countries: string;\n  eu_countries_list: string;\n  top_destinations: string;\n  countries_with_required_region: string;\n  allow_guests_to_write_product_reviews: boolean;\n  is_negotiable_quote_active: boolean;\n  is_requisition_list_active: boolean;\n  magento_catalogpermissions_enabled: boolean;\n  magento_rma_enabled: boolean;\n  magento_rma_enabled_on_product: boolean;\n  magento_rma_use_store_address: boolean;\n  configurable_thumbnail_source: string;\n  category_fixed_product_tax_display_setting: number;\n  product_fixed_product_tax_display_setting: number;\n  sales_fixed_product_tax_display_setting: number;\n  shopping_cart_display_full_summary: boolean;\n  shopping_cart_display_grand_total: boolean;\n  shopping_cart_display_price: number;\n  shopping_cart_display_shipping: number;\n  shopping_cart_display_subtotal: boolean;\n  shopping_cart_display_tax_gift_wrapping: number;\n  shopping_cart_display_zero_tax: boolean;\n  sales_display_full_summary: boolean;\n  sales_display_grand_total: boolean;\n  sales_display_price: number;\n  sales_display_shipping: number;\n  sales_display_subtotal: boolean;\n  sales_display_tax_gift_wrapping: number;\n  sales_display_zero_tax: boolean;\n\n  // Additional SEO fields\n  google_site_verification?: string;\n  yandex_verification?: string;\n  yahoo_verification?: string;\n  bing_verification?: string;\n}\n\n// Basic store configuration interface\nexport interface BasicStoreConfig {\n  id: number;\n  code: string;\n  store_name: string;\n  locale: string;\n  base_currency_code: string;\n  default_display_currency_code: string;\n  timezone: string;\n  base_url: string;\n  base_media_url: string;\n  cms_home_page: string;\n  cms_no_route: string;\n  default_title: string;\n  default_description: string;\n  default_keywords: string;\n  header_logo_src: string;\n  logo_alt: string;\n  welcome: string;\n  copyright: string;\n}\n\n// Homepage configuration interface\nexport interface HomepageConfig {\n  cms_home_page: string;\n  store_name: string;\n  default_title: string;\n}\n\n// Get full store configuration\nexport async function getStoreConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<StoreConfig | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: StoreConfig;\n    }>(GET_STORE_CONFIG, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching store configuration:', error);\n    return null;\n  }\n}\n\n// Get basic store configuration\nexport async function getBasicStoreConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<BasicStoreConfig | null> {\n  try {\n    const response = await serverGraphQL<{\n      storeConfig: BasicStoreConfig;\n    }>(GET_STORE_CONFIG_BASIC, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching basic store configuration:', error);\n    return null;\n  }\n}\n\n// Get homepage identifier from store configuration\nexport async function getHomepageIdentifier(\n  revalidate: number = 86400 // 24 hours\n): Promise<string | null> {\n  try {\n    const response = await serverGraphQL<{\n      storeConfig: HomepageConfig;\n    }>(GET_HOMEPAGE_IDENTIFIER, {}, revalidate);\n\n    return response.storeConfig.cms_home_page;\n  } catch (error) {\n    console.error('Error fetching homepage identifier:', error);\n    return null;\n  }\n}\n\n// Get homepage configuration\nexport async function getHomepageConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<HomepageConfig | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: HomepageConfig;\n    }>(GET_HOMEPAGE_IDENTIFIER, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching homepage configuration:', error);\n    return null;\n  }\n}\n\n// Get store SEO configuration\nexport async function getStoreSeoConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<Partial<StoreConfig> | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: Partial<StoreConfig>;\n    }>(GET_STORE_SEO_CONFIG, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching store SEO configuration:', error);\n    return null;\n  }\n}\n\n// Get store branding configuration\nexport async function getStoreBrandingConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<Partial<StoreConfig> | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: Partial<StoreConfig>;\n    }>(GET_STORE_BRANDING_CONFIG, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching store branding configuration:', error);\n    return null;\n  }\n}\n\n// Get store catalog configuration\nexport async function getStoreCatalogConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<Partial<StoreConfig> | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: Partial<StoreConfig>;\n    }>(GET_STORE_CATALOG_CONFIG, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching store catalog configuration:', error);\n    return null;\n  }\n}\n\n// Get store checkout configuration\nexport async function getStoreCheckoutConfig(\n  revalidate: number = 86400 // 24 hours\n): Promise<Partial<StoreConfig> | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      storeConfig: Partial<StoreConfig>;\n    }>(GET_STORE_CHECKOUT_CONFIG, {}, revalidate);\n\n    return response.storeConfig;\n  } catch (error) {\n    console.error('Error fetching store checkout configuration:', error);\n    return null;\n  }\n}\n\n// Utility function to get media URL for assets\nexport function getMediaUrl(\n  path: string,\n  storeConfig?: BasicStoreConfig\n): string {\n  if (!path) return '';\n\n  // If path is already a full URL, return as is\n  if (path.startsWith('http://') || path.startsWith('https://')) {\n    return path;\n  }\n\n  // Use store config base media URL if available\n  if (storeConfig?.base_media_url) {\n    return `${storeConfig.base_media_url.replace(/\\/$/, '')}/${path.replace(/^\\//, '')}`;\n  }\n\n  // Fallback to environment variable or default\n  const baseMediaUrl =\n    process.env.NEXT_PUBLIC_MAGENTO_BASE_MEDIA_URL ||\n    'https://your-magento-store.com/media';\n  return `${baseMediaUrl.replace(/\\/$/, '')}/${path.replace(/^\\//, '')}`;\n}\n\n// Utility function to format store title\nexport function formatStoreTitle(\n  title: string,\n  storeConfig?: Partial<StoreConfig>\n): string {\n  if (!storeConfig) return title;\n\n  let formattedTitle = title;\n\n  if (storeConfig.title_prefix) {\n    formattedTitle = `${storeConfig.title_prefix} ${formattedTitle}`;\n  }\n\n  if (storeConfig.title_suffix) {\n    formattedTitle = `${formattedTitle} ${storeConfig.title_suffix}`;\n  }\n\n  return formattedTitle;\n}\n\n// Utility function to get default meta description\nexport function getDefaultMetaDescription(\n  storeConfig?: Partial<StoreConfig>\n): string {\n  return (\n    storeConfig?.default_description ||\n    'Discover amazing products at our online store. Shop the latest trends with fast shipping and great customer service.'\n  );\n}\n\n// Utility function to get default meta keywords\nexport function getDefaultMetaKeywords(\n  storeConfig?: Partial<StoreConfig>\n): string {\n  return (\n    storeConfig?.default_keywords ||\n    'ecommerce, online shopping, products, store'\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;;;;;;;;;AA8StD;AA5SJ;AACA;;;AAyJO,eAAe,eACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,kBAAkB,CAAC,GAAG;QAEzB,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,eAAe,oBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAEhC,mIAAA,CAAA,yBAAsB,EAAE,CAAC,GAAG;QAE/B,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;IACT;AACF;AAGO,eAAe,sBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAEhC,mIAAA,CAAA,0BAAuB,EAAE,CAAC,GAAG;QAEhC,OAAO,SAAS,WAAW,CAAC,aAAa;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,eAAe,kBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,mIAAA,CAAA,0BAAuB,EAAE,CAAC,GAAG;QAEhC,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF;AAGO,eAAe,kBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,sBAAsB,CAAC,GAAG;QAE7B,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;IACT;AACF;AAGO,eAAe,uBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,2BAA2B,CAAC,GAAG;QAElC,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;IACT;AACF;AAGO,eAAe,sBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,0BAA0B,CAAC,GAAG;QAEjC,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;IACT;AACF;AAGO,eAAe,uBACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,oBAEpB,2BAA2B,CAAC,GAAG;QAElC,OAAO,SAAS,WAAW;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;IACT;AACF;AAGO,SAAS,YACd,IAAY,EACZ,WAA8B;IAE9B,IAAI,CAAC,MAAM,OAAO;IAElB,8CAA8C;IAC9C,IAAI,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,aAAa;QAC7D,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAI,aAAa,gBAAgB;QAC/B,OAAO,GAAG,YAAY,cAAc,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,OAAO,KAAK;IACtF;IAEA,8CAA8C;IAC9C,MAAM,eACJ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kCAAkC,IAC9C;IACF,OAAO,GAAG,aAAa,OAAO,CAAC,OAAO,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,OAAO,KAAK;AACxE;AAGO,SAAS,iBACd,KAAa,EACb,WAAkC;IAElC,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI,iBAAiB;IAErB,IAAI,YAAY,YAAY,EAAE;QAC5B,iBAAiB,GAAG,YAAY,YAAY,CAAC,CAAC,EAAE,gBAAgB;IAClE;IAEA,IAAI,YAAY,YAAY,EAAE;QAC5B,iBAAiB,GAAG,eAAe,CAAC,EAAE,YAAY,YAAY,EAAE;IAClE;IAEA,OAAO;AACT;AAGO,SAAS,0BACd,WAAkC;IAElC,OACE,aAAa,uBACb;AAEJ;AAGO,SAAS,uBACd,WAAkC;IAElC,OACE,aAAa,oBACb;AAEJ", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/api/search.ts"], "sourcesContent": ["// Search API functions using Magento GraphQL\n\nimport { serverGraphQL, gql } from '@/lib/graphql/simpleClient';\nimport { SEARCH_PRODUCTS } from '@/lib/graphql/queries';\n\n// Product interface for search results\nexport interface SearchProduct {\n  uid: string;\n  id: number;\n  name: string;\n  sku: string;\n  url_key: string;\n  image?: {\n    url: string;\n    label: string;\n  };\n  small_image?: {\n    url: string;\n    label: string;\n  };\n  price_range: {\n    minimum_price: {\n      regular_price: {\n        value: number;\n        currency: string;\n      };\n      final_price: {\n        value: number;\n        currency: string;\n      };\n      discount?: {\n        amount_off: number;\n        percent_off: number;\n      };\n    };\n  };\n  rating_summary: number;\n  review_count: number;\n  stock_status: string;\n}\n\n// Search results interface\nexport interface SearchResults {\n  items: SearchProduct[];\n  page_info: {\n    page_size: number;\n    current_page: number;\n    total_pages: number;\n  };\n  total_count: number;\n  aggregations?: Array<{\n    label: string;\n    count: number;\n    attribute_code: string;\n    options: Array<{\n      label: string;\n      value: string;\n      count: number;\n    }>;\n  }>;\n  sort_fields?: {\n    default: string;\n    options: Array<{\n      label: string;\n      value: string;\n    }>;\n  };\n}\n\n// Search parameters interface\nexport interface SearchParams {\n  search: string;\n  pageSize?: number;\n  currentPage?: number;\n  sort?: any;\n  filter?: any;\n}\n\n// Search products\nexport async function searchProducts(\n  params: SearchParams,\n  revalidate: number = 1800 // 30 minutes\n): Promise<SearchResults> {\n  const { search, pageSize = 20, currentPage = 1, sort, filter } = params;\n\n  try {\n    const response = await serverGraphQL<{\n      products: SearchResults;\n    }>(\n      SEARCH_PRODUCTS,\n      {\n        search,\n        pageSize,\n        currentPage,\n        sort,\n        filter,\n      },\n      revalidate\n    );\n\n    return response.products;\n  } catch (error) {\n    console.error('Error searching products:', error);\n    return {\n      items: [],\n      page_info: {\n        page_size: pageSize,\n        current_page: currentPage,\n        total_pages: 0,\n      },\n      total_count: 0,\n    };\n  }\n}\n\n// Client-side search products\nexport async function searchProductsClient(\n  params: SearchParams\n): Promise<SearchResults> {\n  const { search, pageSize = 20, currentPage = 1, sort, filter } = params;\n\n  try {\n    const response = await gql<{\n      products: SearchResults;\n    }>(SEARCH_PRODUCTS, {\n      search,\n      pageSize,\n      currentPage,\n      sort,\n      filter,\n    });\n\n    return response.products;\n  } catch (error) {\n    console.error('Error searching products (client):', error);\n    return {\n      items: [],\n      page_info: {\n        page_size: pageSize,\n        current_page: currentPage,\n        total_pages: 0,\n      },\n      total_count: 0,\n    };\n  }\n}\n\n// Get search suggestions\nexport async function getSearchSuggestions(\n  query: string,\n  limit: number = 10\n): Promise<string[]> {\n  try {\n    // This would typically use a dedicated search suggestions endpoint\n    // For now, we'll search products and extract names\n    const results = await searchProducts({\n      search: query,\n      pageSize: limit,\n    });\n\n    return results.items.map(product => product.name);\n  } catch (error) {\n    console.error('Error getting search suggestions:', error);\n    return [];\n  }\n}\n\n// Build search URL\nexport function buildSearchUrl(\n  query: string,\n  filters?: Record<string, any>\n): string {\n  const params = new URLSearchParams();\n  params.set('q', query);\n\n  if (filters) {\n    Object.entries(filters).forEach(([key, value]) => {\n      if (Array.isArray(value)) {\n        value.forEach(v => params.append(key, v));\n      } else {\n        params.set(key, value);\n      }\n    });\n  }\n\n  return `/search?${params.toString()}`;\n}\n\n// Parse search URL\nexport function parseSearchUrl(url: string): {\n  query: string;\n  filters: Record<string, any>;\n} {\n  const urlObj = new URL(url, 'http://localhost');\n  const params = urlObj.searchParams;\n\n  const query = params.get('q') || '';\n  const filters: Record<string, any> = {};\n\n  params.forEach((value, key) => {\n    if (key !== 'q') {\n      if (filters[key]) {\n        if (Array.isArray(filters[key])) {\n          filters[key].push(value);\n        } else {\n          filters[key] = [filters[key], value];\n        }\n      } else {\n        filters[key] = value;\n      }\n    }\n  });\n\n  return { query, filters };\n}\n\n// Format search query for display\nexport function formatSearchQuery(query: string): string {\n  return query.trim().replace(/\\s+/g, ' ');\n}\n\n// Get popular search terms (mock implementation)\nexport async function getPopularSearchTerms(): Promise<string[]> {\n  // In a real implementation, this would fetch from analytics or a dedicated endpoint\n  return [\n    'laptop',\n    'smartphone',\n    'headphones',\n    'camera',\n    'watch',\n    'shoes',\n    'dress',\n    'bag',\n    'sunglasses',\n    'jewelry',\n  ];\n}\n\n// Search history management (client-side)\nexport function getSearchHistory(): string[] {\n  if (typeof window === 'undefined') return [];\n\n  try {\n    const history = localStorage.getItem('search_history');\n    return history ? JSON.parse(history) : [];\n  } catch {\n    return [];\n  }\n}\n\nexport function addToSearchHistory(query: string): void {\n  if (typeof window === 'undefined') return;\n\n  try {\n    const history = getSearchHistory();\n    const formattedQuery = formatSearchQuery(query);\n\n    if (formattedQuery && !history.includes(formattedQuery)) {\n      const newHistory = [formattedQuery, ...history.slice(0, 9)]; // Keep last 10\n      localStorage.setItem('search_history', JSON.stringify(newHistory));\n    }\n  } catch (error) {\n    console.error('Error saving search history:', error);\n  }\n}\n\nexport function clearSearchHistory(): void {\n  if (typeof window === 'undefined') return;\n\n  try {\n    localStorage.removeItem('search_history');\n  } catch (error) {\n    console.error('Error clearing search history:', error);\n  }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;;;;;;;AAE7C;AACA;;;AA4EO,eAAe,eACpB,MAAoB,EACpB,aAAqB,KAAK,aAAa;AAAd;IAEzB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;IAEjE,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAGjC,mIAAA,CAAA,kBAAe,EACf;YACE;YACA;YACA;YACA;YACA;QACF,GACA;QAGF,OAAO,SAAS,QAAQ;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,OAAO,EAAE;YACT,WAAW;gBACT,WAAW;gBACX,cAAc;gBACd,aAAa;YACf;YACA,aAAa;QACf;IACF;AACF;AAGO,eAAe,qBACpB,MAAoB;IAEpB,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,cAAc,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;IAEjE,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,MAAG,AAAD,EAEtB,mIAAA,CAAA,kBAAe,EAAE;YAClB;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,SAAS,QAAQ;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YACL,OAAO,EAAE;YACT,WAAW;gBACT,WAAW;gBACX,cAAc;gBACd,aAAa;YACf;YACA,aAAa;QACf;IACF;AACF;AAGO,eAAe,qBACpB,KAAa,EACb,QAAgB,EAAE;IAElB,IAAI;QACF,mEAAmE;QACnE,mDAAmD;QACnD,MAAM,UAAU,MAAM,eAAe;YACnC,QAAQ;YACR,UAAU;QACZ;QAEA,OAAO,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,EAAE;IACX;AACF;AAGO,SAAS,eACd,KAAa,EACb,OAA6B;IAE7B,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,CAAC,KAAK;IAEhB,IAAI,SAAS;QACX,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,KAAK;YACxC,OAAO;gBACL,OAAO,GAAG,CAAC,KAAK;YAClB;QACF;IACF;IAEA,OAAO,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI;AACvC;AAGO,SAAS,eAAe,GAAW;IAIxC,MAAM,SAAS,IAAI,IAAI,KAAK;IAC5B,MAAM,SAAS,OAAO,YAAY;IAElC,MAAM,QAAQ,OAAO,GAAG,CAAC,QAAQ;IACjC,MAAM,UAA+B,CAAC;IAEtC,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,IAAI,QAAQ,KAAK;YACf,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChB,IAAI,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG;oBAC/B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;gBACpB,OAAO;oBACL,OAAO,CAAC,IAAI,GAAG;wBAAC,OAAO,CAAC,IAAI;wBAAE;qBAAM;gBACtC;YACF,OAAO;gBACL,OAAO,CAAC,IAAI,GAAG;YACjB;QACF;IACF;IAEA,OAAO;QAAE;QAAO;IAAQ;AAC1B;AAGO,SAAS,kBAAkB,KAAa;IAC7C,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ;AACtC;AAGO,eAAe;IACpB,oFAAoF;IACpF,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,SAAS;IACd,uCAAmC;;IAAS;IAE5C,IAAI;QACF,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,OAAO,UAAU,KAAK,KAAK,CAAC,WAAW,EAAE;IAC3C,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAEO,SAAS,mBAAmB,KAAa;IAC9C,uCAAmC;;IAAM;IAEzC,IAAI;QACF,MAAM,UAAU;QAChB,MAAM,iBAAiB,kBAAkB;QAEzC,IAAI,kBAAkB,CAAC,QAAQ,QAAQ,CAAC,iBAAiB;YACvD,MAAM,aAAa;gBAAC;mBAAmB,QAAQ,KAAK,CAAC,GAAG;aAAG,EAAE,eAAe;YAC5E,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACxD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF;AAEO,SAAS;IACd,uCAAmC;;IAAM;IAEzC,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF", "debugId": null}}, {"offset": {"line": 2246, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/layout/DynamicHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  AppBar,\n  Tool<PERSON>,\n  Box,\n  Container,\n  Typography,\n  IconButton,\n  Badge,\n  Menu,\n  MenuItem,\n  InputBase,\n  Button,\n  Drawer,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Collapse,\n  useTheme,\n  useMediaQuery,\n  alpha,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  ShoppingCart as CartIcon,\n  Person as PersonIcon,\n  Menu as MenuIcon,\n  Close as CloseIcon,\n  ExpandLess,\n  ExpandMore,\n  Category as CategoryIcon,\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { LogoImage, ProductImage } from '@/components/ui/MagentoImage';\nimport { getCategoriesForMenuClient } from '@/lib/magento/api/categories';\nimport { getBasicStoreConfig } from '@/lib/magento/api/storeConfig';\nimport { searchProductsClient } from '@/lib/magento/api/search';\nimport type { Category } from '@/lib/magento/api/categories';\n\n// Styled components\nconst StyledAppBar = styled(AppBar)(({ theme }) => ({\n  backgroundColor: theme.palette.background.paper,\n  color: theme.palette.text.primary,\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  borderBottom: `1px solid ${theme.palette.divider}`,\n}));\n\nconst SearchContainer = styled(Box)(({ theme }) => ({\n  position: 'relative',\n  borderRadius: theme.shape.borderRadius * 2,\n  backgroundColor: alpha(theme.palette.common.black, 0.05),\n  '&:hover': {\n    backgroundColor: alpha(theme.palette.common.black, 0.08),\n  },\n  marginLeft: theme.spacing(2),\n  marginRight: theme.spacing(2),\n  width: '100%',\n  maxWidth: 400,\n  [theme.breakpoints.up('sm')]: {\n    marginLeft: theme.spacing(3),\n    width: 'auto',\n  },\n}));\n\nconst SearchIconWrapper = styled('div')(({ theme }) => ({\n  padding: theme.spacing(0, 2),\n  height: '100%',\n  position: 'absolute',\n  pointerEvents: 'none',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  color: theme.palette.text.secondary,\n}));\n\nconst StyledInputBase = styled(InputBase)(({ theme }) => ({\n  color: 'inherit',\n  width: '100%',\n  '& .MuiInputBase-input': {\n    padding: theme.spacing(1, 1, 1, 0),\n    paddingLeft: `calc(1em + ${theme.spacing(4)})`,\n    transition: theme.transitions.create('width'),\n    [theme.breakpoints.up('sm')]: {\n      width: '20ch',\n      '&:focus': {\n        width: '30ch',\n      },\n    },\n  },\n}));\n\nconst MegaMenuContainer = styled(Box)(({ theme }) => ({\n  position: 'absolute',\n  top: '100%',\n  left: 0,\n  right: 0,\n  backgroundColor: theme.palette.background.paper,\n  boxShadow: theme.shadows[8],\n  zIndex: theme.zIndex.modal,\n  maxHeight: '70vh',\n  overflowY: 'auto',\n}));\n\n// Interface for store configuration\ninterface StoreConfig {\n  store_name: string;\n  header_logo_src?: string;\n  logo_alt?: string;\n  base_media_url: string;\n}\n\n// Dynamic Header Props\ninterface DynamicHeaderProps {\n  cartItemCount?: number;\n  onCartClick?: () => void;\n  onSearchSubmit?: (query: string) => void;\n}\n\nexport default function DynamicHeader({\n  cartItemCount = 0,\n  onCartClick,\n  onSearchSubmit,\n}: DynamicHeaderProps) {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // State\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<any[]>([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [megaMenuOpen, setMegaMenuOpen] = useState<string | null>(null);\n  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(\n    null\n  );\n  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(\n    new Set()\n  );\n\n  // Load data on mount\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        const [categoriesData, configData] = await Promise.all([\n          getCategoriesForMenuClient(),\n          getBasicStoreConfig(),\n        ]);\n\n        setCategories(categoriesData);\n        setStoreConfig(configData);\n      } catch (error) {\n        console.error('Error loading header data:', error);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Search functionality\n  const handleSearchChange = async (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const query = event.target.value;\n    setSearchQuery(query);\n\n    if (query.length > 2) {\n      try {\n        const results = await searchProductsClient({\n          search: query,\n          pageSize: 5,\n        });\n        setSearchResults(results.items);\n        setShowSearchResults(true);\n      } catch (error) {\n        console.error('Error searching products:', error);\n        setSearchResults([]);\n      }\n    } else {\n      setSearchResults([]);\n      setShowSearchResults(false);\n    }\n  };\n\n  const handleSearchSubmit = (event: React.FormEvent) => {\n    event.preventDefault();\n    if (searchQuery.trim()) {\n      setShowSearchResults(false);\n      onSearchSubmit?.(searchQuery.trim());\n      // Navigate to search results page\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`;\n    }\n  };\n\n  // Menu handlers\n  const handleMegaMenuOpen = (categoryId: string) => {\n    setMegaMenuOpen(categoryId);\n  };\n\n  const handleMegaMenuClose = () => {\n    setMegaMenuOpen(null);\n  };\n\n  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setUserMenuAnchor(event.currentTarget);\n  };\n\n  const handleUserMenuClose = () => {\n    setUserMenuAnchor(null);\n  };\n\n  const toggleMobileCategory = (categoryId: string) => {\n    const newExpanded = new Set(expandedCategories);\n    if (newExpanded.has(categoryId)) {\n      newExpanded.delete(categoryId);\n    } else {\n      newExpanded.add(categoryId);\n    }\n    setExpandedCategories(newExpanded);\n  };\n\n  // Render category menu items\n  const renderCategoryMenuItem = (category: Category, level = 0) => (\n    <MenuItem\n      key={category.uid}\n      component={Link}\n      href={`/category/${category.url_key}`}\n      sx={{\n        pl: level * 2 + 2,\n        py: 1,\n        '&:hover': {\n          backgroundColor: alpha(theme.palette.primary.main, 0.08),\n        },\n      }}\n    >\n      <Typography variant=\"body2\" color=\"text.primary\">\n        {category.name}\n      </Typography>\n    </MenuItem>\n  );\n\n  // Render mobile category items\n  const renderMobileCategoryItem = (category: Category, level = 0) => (\n    <React.Fragment key={category.uid}>\n      <ListItem\n        button\n        sx={{ pl: level * 2 + 2 }}\n        onClick={() => {\n          if (category.children && category.children.length > 0) {\n            toggleMobileCategory(category.uid);\n          } else {\n            setMobileMenuOpen(false);\n            window.location.href = `/category/${category.url_key}`;\n          }\n        }}\n      >\n        <ListItemIcon sx={{ minWidth: 32 }}>\n          <CategoryIcon fontSize=\"small\" />\n        </ListItemIcon>\n        <ListItemText primary={category.name} />\n        {category.children &&\n          category.children.length > 0 &&\n          (expandedCategories.has(category.uid) ? (\n            <ExpandLess />\n          ) : (\n            <ExpandMore />\n          ))}\n      </ListItem>\n      {category.children && category.children.length > 0 && (\n        <Collapse\n          in={expandedCategories.has(category.uid)}\n          timeout=\"auto\"\n          unmountOnExit\n        >\n          <List component=\"div\" disablePadding>\n            {category.children.map(child =>\n              renderMobileCategoryItem(child, level + 1)\n            )}\n          </List>\n        </Collapse>\n      )}\n    </React.Fragment>\n  );\n\n  return (\n    <>\n      <StyledAppBar position=\"sticky\">\n        <Container maxWidth=\"xl\">\n          <Toolbar sx={{ px: { xs: 0, sm: 2 } }}>\n            {/* Mobile Menu Button */}\n            {isMobile && (\n              <IconButton\n                edge=\"start\"\n                color=\"inherit\"\n                aria-label=\"menu\"\n                onClick={() => setMobileMenuOpen(true)}\n                sx={{ mr: 1 }}\n              >\n                <MenuIcon />\n              </IconButton>\n            )}\n\n            {/* Logo */}\n            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n              <Link\n                href=\"/\"\n                style={{ textDecoration: 'none', color: 'inherit' }}\n              >\n                {storeConfig?.header_logo_src ? (\n                  <LogoImage\n                    src={storeConfig.header_logo_src}\n                    alt={\n                      storeConfig?.logo_alt ||\n                      storeConfig?.store_name ||\n                      'Store Logo'\n                    }\n                    width={120}\n                    height={40}\n                    storeConfig={storeConfig}\n                    style={{ objectFit: 'contain' }}\n                  />\n                ) : (\n                  <Typography\n                    variant=\"h6\"\n                    component=\"div\"\n                    sx={{\n                      fontWeight: 700,\n                      background:\n                        'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',\n                      backgroundClip: 'text',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent',\n                    }}\n                  >\n                    {storeConfig?.store_name || 'Store'}\n                  </Typography>\n                )}\n              </Link>\n            </Box>\n\n            {/* Desktop Navigation */}\n            {!isMobile && (\n              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                {categories.slice(0, 6).map(category => (\n                  <Button\n                    key={category.uid}\n                    color=\"inherit\"\n                    onMouseEnter={() => handleMegaMenuOpen(category.uid)}\n                    onMouseLeave={handleMegaMenuClose}\n                    component={Link}\n                    href={`/category/${category.url_key}`}\n                    sx={{\n                      mx: 1,\n                      textTransform: 'none',\n                      fontWeight: 500,\n                      '&:hover': {\n                        backgroundColor: alpha(\n                          theme.palette.primary.main,\n                          0.08\n                        ),\n                      },\n                    }}\n                  >\n                    {category.name}\n                  </Button>\n                ))}\n              </Box>\n            )}\n\n            {/* Search */}\n            <SearchContainer>\n              <SearchIconWrapper>\n                <SearchIcon />\n              </SearchIconWrapper>\n              <form onSubmit={handleSearchSubmit}>\n                <StyledInputBase\n                  placeholder=\"Search products...\"\n                  value={searchQuery}\n                  onChange={handleSearchChange}\n                  onFocus={() =>\n                    searchResults.length > 0 && setShowSearchResults(true)\n                  }\n                  onBlur={() =>\n                    setTimeout(() => setShowSearchResults(false), 200)\n                  }\n                />\n              </form>\n\n              {/* Search Results Dropdown */}\n              <AnimatePresence>\n                {showSearchResults && searchResults.length > 0 && (\n                  <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -10 }}\n                    style={{\n                      position: 'absolute',\n                      top: '100%',\n                      left: 0,\n                      right: 0,\n                      backgroundColor: theme.palette.background.paper,\n                      boxShadow: theme.shadows[8],\n                      borderRadius: theme.shape.borderRadius,\n                      zIndex: theme.zIndex.modal,\n                      maxHeight: 300,\n                      overflowY: 'auto',\n                    }}\n                  >\n                    {searchResults.map(product => (\n                      <MenuItem\n                        key={product.uid}\n                        component={Link}\n                        href={`/product/${product.url_key}`}\n                        sx={{ py: 1 }}\n                      >\n                        <Box\n                          sx={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: '100%',\n                          }}\n                        >\n                          {product.image && (\n                            <ProductImage\n                              src={product.image.url}\n                              alt={product.image.label || product.name}\n                              width={40}\n                              height={40}\n                              variant=\"thumbnail\"\n                              showSkeleton={false}\n                              style={{ objectFit: 'cover', marginRight: 12 }}\n                            />\n                          )}\n                          <Box>\n                            <Typography variant=\"body2\" noWrap>\n                              {product.name}\n                            </Typography>\n                            <Typography\n                              variant=\"caption\"\n                              color=\"text.secondary\"\n                            >\n                              $\n                              {\n                                product.price_range.minimum_price.final_price\n                                  .value\n                              }\n                            </Typography>\n                          </Box>\n                        </Box>\n                      </MenuItem>\n                    ))}\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </SearchContainer>\n\n            <Box sx={{ flexGrow: 1 }} />\n\n            {/* Cart */}\n            <IconButton color=\"inherit\" onClick={onCartClick} sx={{ mr: 1 }}>\n              <Badge badgeContent={cartItemCount} color=\"primary\">\n                <CartIcon />\n              </Badge>\n            </IconButton>\n\n            {/* User Menu */}\n            <IconButton color=\"inherit\" onClick={handleUserMenuOpen}>\n              <PersonIcon />\n            </IconButton>\n          </Toolbar>\n        </Container>\n\n        {/* Mega Menu */}\n        <AnimatePresence>\n          {megaMenuOpen && !isMobile && (\n            <MegaMenuContainer\n              onMouseEnter={() => setMegaMenuOpen(megaMenuOpen)}\n              onMouseLeave={handleMegaMenuClose}\n            >\n              <Container maxWidth=\"xl\">\n                <Box sx={{ py: 2 }}>\n                  {categories\n                    .find(cat => cat.uid === megaMenuOpen)\n                    ?.children?.map(category => (\n                      <Box key={category.uid} sx={{ mb: 2 }}>\n                        <Typography\n                          variant=\"subtitle1\"\n                          component={Link}\n                          href={`/category/${category.url_key}`}\n                          sx={{\n                            fontWeight: 600,\n                            color: 'primary.main',\n                            textDecoration: 'none',\n                            '&:hover': { textDecoration: 'underline' },\n                          }}\n                        >\n                          {category.name}\n                        </Typography>\n                        {category.children && (\n                          <Box sx={{ mt: 1, ml: 2 }}>\n                            {category.children.slice(0, 8).map(subCategory => (\n                              <Typography\n                                key={subCategory.uid}\n                                variant=\"body2\"\n                                component={Link}\n                                href={`/category/${subCategory.url_key}`}\n                                sx={{\n                                  display: 'block',\n                                  py: 0.5,\n                                  color: 'text.secondary',\n                                  textDecoration: 'none',\n                                  '&:hover': {\n                                    color: 'primary.main',\n                                    textDecoration: 'underline',\n                                  },\n                                }}\n                              >\n                                {subCategory.name}\n                              </Typography>\n                            ))}\n                          </Box>\n                        )}\n                      </Box>\n                    ))}\n                </Box>\n              </Container>\n            </MegaMenuContainer>\n          )}\n        </AnimatePresence>\n      </StyledAppBar>\n\n      {/* Mobile Menu Drawer */}\n      <Drawer\n        anchor=\"left\"\n        open={mobileMenuOpen}\n        onClose={() => setMobileMenuOpen(false)}\n        sx={{\n          '& .MuiDrawer-paper': {\n            width: 280,\n            maxWidth: '80vw',\n          },\n        }}\n      >\n        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n            }}\n          >\n            <Typography variant=\"h6\">Menu</Typography>\n            <IconButton onClick={() => setMobileMenuOpen(false)}>\n              <CloseIcon />\n            </IconButton>\n          </Box>\n        </Box>\n        <List>\n          {categories.map(category => renderMobileCategoryItem(category))}\n        </List>\n      </Drawer>\n\n      {/* User Menu */}\n      <Menu\n        anchorEl={userMenuAnchor}\n        open={Boolean(userMenuAnchor)}\n        onClose={handleUserMenuClose}\n        transformOrigin={{ horizontal: 'right', vertical: 'top' }}\n        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\n      >\n        <MenuItem\n          component={Link}\n          href=\"/account/login\"\n          onClick={handleUserMenuClose}\n        >\n          Login\n        </MenuItem>\n        <MenuItem\n          component={Link}\n          href=\"/account/register\"\n          onClick={handleUserMenuClose}\n        >\n          Register\n        </MenuItem>\n        <MenuItem\n          component={Link}\n          href=\"/account/dashboard\"\n          onClick={handleUserMenuClose}\n        >\n          My Account\n        </MenuItem>\n        <MenuItem\n          component={Link}\n          href=\"/account/orders\"\n          onClick={handleUserMenuClose}\n        >\n          My Orders\n        </MenuItem>\n        <MenuItem\n          component={Link}\n          href=\"/wishlist\"\n          onClick={handleUserMenuClose}\n        >\n          Wishlist\n        </MenuItem>\n      </Menu>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAzCA;;;;;;;;;;;;;;;;;;AA4CA,oBAAoB;AACpB,MAAM,eAAe,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,oMAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAClD,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,WAAW;QACX,cAAc,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;IACpD,CAAC;KALK;AAON,MAAM,kBAAkB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAClD,UAAU;QACV,cAAc,MAAM,KAAK,CAAC,YAAY,GAAG;QACzC,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;QACnD,WAAW;YACT,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;QACrD;QACA,YAAY,MAAM,OAAO,CAAC;QAC1B,aAAa,MAAM,OAAO,CAAC;QAC3B,OAAO;QACP,UAAU;QACV,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE;YAC5B,YAAY,MAAM,OAAO,CAAC;YAC1B,OAAO;QACT;IACF,CAAC;MAfK;AAiBN,MAAM,oBAAoB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACtD,SAAS,MAAM,OAAO,CAAC,GAAG;QAC1B,QAAQ;QACR,UAAU;QACV,eAAe;QACf,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;IACrC,CAAC;MATK;AAWN,MAAM,kBAAkB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,6MAAA,CAAA,YAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,OAAO;QACP,OAAO;QACP,yBAAyB;YACvB,SAAS,MAAM,OAAO,CAAC,GAAG,GAAG,GAAG;YAChC,aAAa,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9C,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;YACrC,CAAC,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE;gBAC5B,OAAO;gBACP,WAAW;oBACT,OAAO;gBACT;YACF;QACF;IACF,CAAC;MAdK;AAgBN,MAAM,oBAAoB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACpD,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,WAAW,MAAM,OAAO,CAAC,EAAE;QAC3B,QAAQ,MAAM,MAAM,CAAC,KAAK;QAC1B,WAAW;QACX,WAAW;IACb,CAAC;MAVK;AA2BS,SAAS,cAAc,EACpC,gBAAgB,CAAC,EACjB,WAAW,EACX,cAAc,EACK;;IACnB,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IAEtD,QAAQ;IACR,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD,IAAI;IAGN,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;oDAAW;oBACf,IAAI;wBACF,MAAM,CAAC,gBAAgB,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACrD,CAAA,GAAA,6IAAA,CAAA,6BAA0B,AAAD;4BACzB,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;yBACnB;wBAED,cAAc;wBACd,eAAe;oBACjB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,OACzB;QAEA,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,eAAe;QAEf,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,yIAAA,CAAA,uBAAoB,AAAD,EAAE;oBACzC,QAAQ;oBACR,UAAU;gBACZ;gBACA,iBAAiB,QAAQ,KAAK;gBAC9B,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,iBAAiB,EAAE;YACrB;QACF,OAAO;YACL,iBAAiB,EAAE;YACnB,qBAAqB;QACvB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc;QACpB,IAAI,YAAY,IAAI,IAAI;YACtB,qBAAqB;YACrB,iBAAiB,YAAY,IAAI;YACjC,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;QAC9E;IACF;IAEA,gBAAgB;IAChB,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB,MAAM,aAAa;IACvC;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;IACpB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,sBAAsB;IACxB;IAEA,6BAA6B;IAC7B,MAAM,yBAAyB,CAAC,UAAoB,QAAQ,CAAC,iBAC3D,6LAAC,0MAAA,CAAA,WAAQ;YAEP,WAAW,+JAAA,CAAA,UAAI;YACf,MAAM,CAAC,UAAU,EAAE,SAAS,OAAO,EAAE;YACrC,IAAI;gBACF,IAAI,QAAQ,IAAI;gBAChB,IAAI;gBACJ,WAAW;oBACT,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;gBACrD;YACF;sBAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;gBAAC,SAAQ;gBAAQ,OAAM;0BAC/B,SAAS,IAAI;;;;;;WAZX,SAAS,GAAG;;;;;IAiBrB,+BAA+B;IAC/B,MAAM,2BAA2B,CAAC,UAAoB,QAAQ,CAAC,iBAC7D,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;8BACb,6LAAC,0MAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,IAAI;wBAAE,IAAI,QAAQ,IAAI;oBAAE;oBACxB,SAAS;wBACP,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;4BACrD,qBAAqB,SAAS,GAAG;wBACnC,OAAO;4BACL,kBAAkB;4BAClB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,SAAS,OAAO,EAAE;wBACxD;oBACF;;sCAEA,6LAAC,sNAAA,CAAA,eAAY;4BAAC,IAAI;gCAAE,UAAU;4BAAG;sCAC/B,cAAA,6LAAC,gKAAA,CAAA,UAAY;gCAAC,UAAS;;;;;;;;;;;sCAEzB,6LAAC,sNAAA,CAAA,eAAY;4BAAC,SAAS,SAAS,IAAI;;;;;;wBACnC,SAAS,QAAQ,IAChB,SAAS,QAAQ,CAAC,MAAM,GAAG,KAC3B,CAAC,mBAAmB,GAAG,CAAC,SAAS,GAAG,kBAClC,6LAAC,kKAAA,CAAA,UAAU;;;;iDAEX,6LAAC,kKAAA,CAAA,UAAU;;;;gCACZ;;;;;;;gBAEJ,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,6LAAC,0MAAA,CAAA,WAAQ;oBACP,IAAI,mBAAmB,GAAG,CAAC,SAAS,GAAG;oBACvC,SAAQ;oBACR,aAAa;8BAEb,cAAA,6LAAC,8LAAA,CAAA,OAAI;wBAAC,WAAU;wBAAM,cAAc;kCACjC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,QACrB,yBAAyB,OAAO,QAAQ;;;;;;;;;;;;WAjC7B,SAAS,GAAG;;;;;IAyCnC,qBACE;;0BACE,6LAAC;gBAAa,UAAS;;kCACrB,6LAAC,6MAAA,CAAA,YAAS;wBAAC,UAAS;kCAClB,cAAA,6LAAC,uMAAA,CAAA,UAAO;4BAAC,IAAI;gCAAE,IAAI;oCAAE,IAAI;oCAAG,IAAI;gCAAE;4BAAE;;gCAEjC,0BACC,6LAAC,gNAAA,CAAA,aAAU;oCACT,MAAK;oCACL,OAAM;oCACN,cAAW;oCACX,SAAS,IAAM,kBAAkB;oCACjC,IAAI;wCAAE,IAAI;oCAAE;8CAEZ,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;8CAKb,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,SAAS;wCAAQ,YAAY;wCAAU,IAAI;oCAAE;8CACtD,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,OAAO;4CAAE,gBAAgB;4CAAQ,OAAO;wCAAU;kDAEjD,aAAa,gCACZ,6LAAC,2IAAA,CAAA,YAAS;4CACR,KAAK,YAAY,eAAe;4CAChC,KACE,aAAa,YACb,aAAa,cACb;4CAEF,OAAO;4CACP,QAAQ;4CACR,aAAa;4CACb,OAAO;gDAAE,WAAW;4CAAU;;;;;iEAGhC,6LAAC,gNAAA,CAAA,aAAU;4CACT,SAAQ;4CACR,WAAU;4CACV,IAAI;gDACF,YAAY;gDACZ,YACE;gDACF,gBAAgB;gDAChB,sBAAsB;gDACtB,qBAAqB;4CACvB;sDAEC,aAAa,cAAc;;;;;;;;;;;;;;;;gCAOnC,CAAC,0BACA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,SAAS;wCAAQ,YAAY;wCAAU,IAAI;oCAAE;8CACrD,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,yBAC1B,6LAAC,oMAAA,CAAA,SAAM;4CAEL,OAAM;4CACN,cAAc,IAAM,mBAAmB,SAAS,GAAG;4CACnD,cAAc;4CACd,WAAW,+JAAA,CAAA,UAAI;4CACf,MAAM,CAAC,UAAU,EAAE,SAAS,OAAO,EAAE;4CACrC,IAAI;gDACF,IAAI;gDACJ,eAAe;gDACf,YAAY;gDACZ,WAAW;oDACT,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EACnB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAC1B;gDAEJ;4CACF;sDAEC,SAAS,IAAI;2CAlBT,SAAS,GAAG;;;;;;;;;;8CAyBzB,6LAAC;;sDACC,6LAAC;sDACC,cAAA,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;;sDAEb,6LAAC;4CAAK,UAAU;sDACd,cAAA,6LAAC;gDACC,aAAY;gDACZ,OAAO;gDACP,UAAU;gDACV,SAAS,IACP,cAAc,MAAM,GAAG,KAAK,qBAAqB;gDAEnD,QAAQ,IACN,WAAW,IAAM,qBAAqB,QAAQ;;;;;;;;;;;sDAMpD,6LAAC,4LAAA,CAAA,kBAAe;sDACb,qBAAqB,cAAc,MAAM,GAAG,mBAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,OAAO;oDACL,UAAU;oDACV,KAAK;oDACL,MAAM;oDACN,OAAO;oDACP,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;oDAC/C,WAAW,MAAM,OAAO,CAAC,EAAE;oDAC3B,cAAc,MAAM,KAAK,CAAC,YAAY;oDACtC,QAAQ,MAAM,MAAM,CAAC,KAAK;oDAC1B,WAAW;oDACX,WAAW;gDACb;0DAEC,cAAc,GAAG,CAAC,CAAA,wBACjB,6LAAC,0MAAA,CAAA,WAAQ;wDAEP,WAAW,+JAAA,CAAA,UAAI;wDACf,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;wDACnC,IAAI;4DAAE,IAAI;wDAAE;kEAEZ,cAAA,6LAAC,2LAAA,CAAA,MAAG;4DACF,IAAI;gEACF,SAAS;gEACT,YAAY;gEACZ,OAAO;4DACT;;gEAEC,QAAQ,KAAK,kBACZ,6LAAC,2IAAA,CAAA,eAAY;oEACX,KAAK,QAAQ,KAAK,CAAC,GAAG;oEACtB,KAAK,QAAQ,KAAK,CAAC,KAAK,IAAI,QAAQ,IAAI;oEACxC,OAAO;oEACP,QAAQ;oEACR,SAAQ;oEACR,cAAc;oEACd,OAAO;wEAAE,WAAW;wEAAS,aAAa;oEAAG;;;;;;8EAGjD,6LAAC,2LAAA,CAAA,MAAG;;sFACF,6LAAC,gNAAA,CAAA,aAAU;4EAAC,SAAQ;4EAAQ,MAAM;sFAC/B,QAAQ,IAAI;;;;;;sFAEf,6LAAC,gNAAA,CAAA,aAAU;4EACT,SAAQ;4EACR,OAAM;;gFACP;gFAGG,QAAQ,WAAW,CAAC,aAAa,CAAC,WAAW,CAC1C,KAAK;;;;;;;;;;;;;;;;;;;uDAlCX,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;8CA8C5B,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,UAAU;oCAAE;;;;;;8CAGvB,6LAAC,gNAAA,CAAA,aAAU;oCAAC,OAAM;oCAAU,SAAS;oCAAa,IAAI;wCAAE,IAAI;oCAAE;8CAC5D,cAAA,6LAAC,iMAAA,CAAA,QAAK;wCAAC,cAAc;wCAAe,OAAM;kDACxC,cAAA,6LAAC,oKAAA,CAAA,UAAQ;;;;;;;;;;;;;;;8CAKb,6LAAC,gNAAA,CAAA,aAAU;oCAAC,OAAM;oCAAU,SAAS;8CACnC,cAAA,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;kCAMjB,6LAAC,4LAAA,CAAA,kBAAe;kCACb,gBAAgB,CAAC,0BAChB,6LAAC;4BACC,cAAc,IAAM,gBAAgB;4BACpC,cAAc;sCAEd,cAAA,6LAAC,6MAAA,CAAA,YAAS;gCAAC,UAAS;0CAClB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,IAAI;oCAAE;8CACd,WACE,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,eACvB,UAAU,IAAI,CAAA,yBACd,6LAAC,2LAAA,CAAA,MAAG;4CAAoB,IAAI;gDAAE,IAAI;4CAAE;;8DAClC,6LAAC,gNAAA,CAAA,aAAU;oDACT,SAAQ;oDACR,WAAW,+JAAA,CAAA,UAAI;oDACf,MAAM,CAAC,UAAU,EAAE,SAAS,OAAO,EAAE;oDACrC,IAAI;wDACF,YAAY;wDACZ,OAAO;wDACP,gBAAgB;wDAChB,WAAW;4DAAE,gBAAgB;wDAAY;oDAC3C;8DAEC,SAAS,IAAI;;;;;;gDAEf,SAAS,QAAQ,kBAChB,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,IAAI;wDAAG,IAAI;oDAAE;8DACrB,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,4BACjC,6LAAC,gNAAA,CAAA,aAAU;4DAET,SAAQ;4DACR,WAAW,+JAAA,CAAA,UAAI;4DACf,MAAM,CAAC,UAAU,EAAE,YAAY,OAAO,EAAE;4DACxC,IAAI;gEACF,SAAS;gEACT,IAAI;gEACJ,OAAO;gEACP,gBAAgB;gEAChB,WAAW;oEACT,OAAO;oEACP,gBAAgB;gEAClB;4DACF;sEAEC,YAAY,IAAI;2DAfZ,YAAY,GAAG;;;;;;;;;;;2CAlBpB,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgDtC,6LAAC,oMAAA,CAAA,SAAM;gBACL,QAAO;gBACP,MAAM;gBACN,SAAS,IAAM,kBAAkB;gBACjC,IAAI;oBACF,sBAAsB;wBACpB,OAAO;wBACP,UAAU;oBACZ;gBACF;;kCAEA,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,GAAG;4BAAG,cAAc;4BAAG,aAAa;wBAAU;kCACvD,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BACF,IAAI;gCACF,SAAS;gCACT,gBAAgB;gCAChB,YAAY;4BACd;;8CAEA,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;8CAAK;;;;;;8CACzB,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAS,IAAM,kBAAkB;8CAC3C,cAAA,6LAAC,6JAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,8LAAA,CAAA,OAAI;kCACF,WAAW,GAAG,CAAC,CAAA,WAAY,yBAAyB;;;;;;;;;;;;0BAKzD,6LAAC,8LAAA,CAAA,OAAI;gBACH,UAAU;gBACV,MAAM,QAAQ;gBACd,SAAS;gBACT,iBAAiB;oBAAE,YAAY;oBAAS,UAAU;gBAAM;gBACxD,cAAc;oBAAE,YAAY;oBAAS,UAAU;gBAAS;;kCAExD,6LAAC,0MAAA,CAAA,WAAQ;wBACP,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,SAAS;kCACV;;;;;;kCAGD,6LAAC,0MAAA,CAAA,WAAQ;wBACP,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,SAAS;kCACV;;;;;;kCAGD,6LAAC,0MAAA,CAAA,WAAQ;wBACP,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,SAAS;kCACV;;;;;;kCAGD,6LAAC,0MAAA,CAAA,WAAQ;wBACP,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,SAAS;kCACV;;;;;;kCAGD,6LAAC,0MAAA,CAAA,WAAQ;wBACP,WAAW,+JAAA,CAAA,UAAI;wBACf,MAAK;wBACL,SAAS;kCACV;;;;;;;;;;;;;;AAMT;GA3ewB;;QAKR,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;;;MANR", "debugId": null}}, {"offset": {"line": 3133, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/layout/DynamicFooter.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Grid,\n  Typography,\n  Link,\n  TextField,\n  Button,\n  IconButton,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  Facebook as FacebookIcon,\n  Twitter as TwitterIcon,\n  Instagram as InstagramIcon,\n  LinkedIn as LinkedInIcon,\n  YouTube as YouTubeIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  LocationOn as LocationIcon,\n  Send as SendIcon,\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\nimport NextLink from 'next/link';\nimport { motion } from 'framer-motion';\nimport { getBasicStoreConfig } from '@/lib/magento/api/storeConfig';\nimport { gql } from '@/lib/graphql/simpleClient';\nimport { GET_CMS_BLOCK } from '@/lib/graphql/queries';\n\n// Styled components\nconst StyledFooter = styled(Box)(({ theme }) => ({\n  backgroundColor: theme.palette.grey[900],\n  color: theme.palette.common.white,\n  marginTop: 'auto',\n}));\n\nconst FooterSection = styled(Box)(({ theme }) => ({\n  padding: theme.spacing(6, 0),\n}));\n\nconst FooterBottom = styled(Box)(({ theme }) => ({\n  backgroundColor: theme.palette.grey[800],\n  padding: theme.spacing(3, 0),\n  borderTop: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,\n}));\n\nconst SocialIconButton = styled(IconButton)(({ theme }) => ({\n  color: theme.palette.common.white,\n  backgroundColor: alpha(theme.palette.common.white, 0.1),\n  margin: theme.spacing(0, 1),\n  transition: 'all 0.3s ease',\n  '&:hover': {\n    backgroundColor: theme.palette.primary.main,\n    transform: 'translateY(-2px)',\n  },\n}));\n\nconst NewsletterBox = styled(Box)(({ theme }) => ({\n  backgroundColor: alpha(theme.palette.common.white, 0.05),\n  borderRadius: theme.shape.borderRadius * 2,\n  padding: theme.spacing(3),\n  border: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,\n}));\n\n// Interface for store configuration\ninterface StoreConfig {\n  store_name: string;\n  copyright?: string;\n  base_url: string;\n}\n\n// Interface for CMS blocks\ninterface CmsBlock {\n  identifier: string;\n  title: string;\n  content: string;\n}\n\n// Footer link interface\ninterface FooterLink {\n  label: string;\n  href: string;\n  external?: boolean;\n}\n\n// Footer section interface\ninterface FooterSection {\n  title: string;\n  links: FooterLink[];\n}\n\n// Dynamic Footer Props\ninterface DynamicFooterProps {\n  className?: string;\n}\n\nexport default function DynamicFooter({ className }: DynamicFooterProps) {\n  const theme = useTheme();\n  \n  // State\n  const [storeConfig, setStoreConfig] = useState<StoreConfig | null>(null);\n  const [footerBlocks, setFooterBlocks] = useState<CmsBlock[]>([]);\n  const [newsletterEmail, setNewsletterEmail] = useState('');\n  const [newsletterLoading, setNewsletterLoading] = useState(false);\n  const [newsletterMessage, setNewsletterMessage] = useState('');\n\n  // Load data on mount\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        const [configData] = await Promise.all([\n          getBasicStoreConfig(),\n          // Load footer CMS blocks\n          loadFooterBlocks(),\n        ]);\n        \n        setStoreConfig(configData);\n      } catch (error) {\n        console.error('Error loading footer data:', error);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Load footer CMS blocks\n  const loadFooterBlocks = async () => {\n    try {\n      // Try to load common footer blocks\n      const blockIdentifiers = ['footer-links', 'footer-about', 'footer-contact'];\n      const blocks: CmsBlock[] = [];\n      \n      for (const identifier of blockIdentifiers) {\n        try {\n          const response = await gql<{\n            cmsBlocks: { items: CmsBlock[] };\n          }>(GET_CMS_BLOCK, { identifier });\n          \n          if (response.cmsBlocks.items.length > 0) {\n            blocks.push(response.cmsBlocks.items[0]);\n          }\n        } catch (error) {\n          // Block doesn't exist, continue\n          console.warn(`Footer block ${identifier} not found`);\n        }\n      }\n      \n      setFooterBlocks(blocks);\n    } catch (error) {\n      console.error('Error loading footer blocks:', error);\n    }\n  };\n\n  // Newsletter subscription\n  const handleNewsletterSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n    \n    if (!newsletterEmail.trim()) return;\n    \n    setNewsletterLoading(true);\n    setNewsletterMessage('');\n    \n    try {\n      // In a real implementation, this would call a newsletter subscription API\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call\n      \n      setNewsletterMessage('Thank you for subscribing to our newsletter!');\n      setNewsletterEmail('');\n    } catch (error) {\n      setNewsletterMessage('Failed to subscribe. Please try again.');\n    } finally {\n      setNewsletterLoading(false);\n    }\n  };\n\n  // Default footer sections\n  const defaultFooterSections: FooterSection[] = [\n    {\n      title: 'Shop',\n      links: [\n        { label: 'New Arrivals', href: '/new-arrivals' },\n        { label: 'Best Sellers', href: '/best-sellers' },\n        { label: 'Sale', href: '/sale' },\n        { label: 'Gift Cards', href: '/gift-cards' },\n      ],\n    },\n    {\n      title: 'Customer Service',\n      links: [\n        { label: 'Contact Us', href: '/contact' },\n        { label: 'FAQ', href: '/faq' },\n        { label: 'Shipping Info', href: '/shipping' },\n        { label: 'Returns', href: '/returns' },\n        { label: 'Size Guide', href: '/size-guide' },\n      ],\n    },\n    {\n      title: 'Company',\n      links: [\n        { label: 'About Us', href: '/about' },\n        { label: 'Careers', href: '/careers' },\n        { label: 'Press', href: '/press' },\n        { label: 'Sustainability', href: '/sustainability' },\n      ],\n    },\n    {\n      title: 'Legal',\n      links: [\n        { label: 'Privacy Policy', href: '/privacy-policy' },\n        { label: 'Terms of Service', href: '/terms' },\n        { label: 'Cookie Policy', href: '/cookies' },\n        { label: 'Accessibility', href: '/accessibility' },\n      ],\n    },\n  ];\n\n  // Social media links\n  const socialLinks = [\n    { icon: FacebookIcon, href: 'https://facebook.com', label: 'Facebook' },\n    { icon: TwitterIcon, href: 'https://twitter.com', label: 'Twitter' },\n    { icon: InstagramIcon, href: 'https://instagram.com', label: 'Instagram' },\n    { icon: LinkedInIcon, href: 'https://linkedin.com', label: 'LinkedIn' },\n    { icon: YouTubeIcon, href: 'https://youtube.com', label: 'YouTube' },\n  ];\n\n  return (\n    <StyledFooter className={className} component=\"footer\">\n      <FooterSection>\n        <Container maxWidth=\"xl\">\n          <Grid container spacing={4}>\n            {/* Newsletter Section */}\n            <Grid item xs={12} md={4}>\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n              >\n                <NewsletterBox>\n                  <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                    Stay Updated\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ mb: 3, opacity: 0.8 }}>\n                    Subscribe to our newsletter for the latest updates, exclusive offers, and new arrivals.\n                  </Typography>\n                  \n                  <Box component=\"form\" onSubmit={handleNewsletterSubmit}>\n                    <Box sx={{ display: 'flex', gap: 1 }}>\n                      <TextField\n                        fullWidth\n                        size=\"small\"\n                        placeholder=\"Enter your email\"\n                        value={newsletterEmail}\n                        onChange={(e) => setNewsletterEmail(e.target.value)}\n                        type=\"email\"\n                        required\n                        sx={{\n                          '& .MuiOutlinedInput-root': {\n                            backgroundColor: alpha(theme.palette.common.white, 0.1),\n                            color: 'white',\n                            '& fieldset': {\n                              borderColor: alpha(theme.palette.common.white, 0.3),\n                            },\n                            '&:hover fieldset': {\n                              borderColor: alpha(theme.palette.common.white, 0.5),\n                            },\n                            '&.Mui-focused fieldset': {\n                              borderColor: theme.palette.primary.main,\n                            },\n                          },\n                          '& .MuiInputBase-input::placeholder': {\n                            color: alpha(theme.palette.common.white, 0.7),\n                            opacity: 1,\n                          },\n                        }}\n                      />\n                      <Button\n                        type=\"submit\"\n                        variant=\"contained\"\n                        disabled={newsletterLoading}\n                        sx={{\n                          minWidth: 'auto',\n                          px: 2,\n                          backgroundColor: theme.palette.primary.main,\n                          '&:hover': {\n                            backgroundColor: theme.palette.primary.dark,\n                          },\n                        }}\n                      >\n                        <SendIcon />\n                      </Button>\n                    </Box>\n                    \n                    {newsletterMessage && (\n                      <Typography\n                        variant=\"caption\"\n                        sx={{\n                          display: 'block',\n                          mt: 1,\n                          color: newsletterMessage.includes('Thank you')\n                            ? theme.palette.success.light\n                            : theme.palette.error.light,\n                        }}\n                      >\n                        {newsletterMessage}\n                      </Typography>\n                    )}\n                  </Box>\n                </NewsletterBox>\n              </motion.div>\n            </Grid>\n\n            {/* Footer Links */}\n            {defaultFooterSections.map((section, index) => (\n              <Grid item xs={6} sm={3} md={2} key={section.title}>\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>\n                    {section.title}\n                  </Typography>\n                  <List dense sx={{ p: 0 }}>\n                    {section.links.map((link) => (\n                      <ListItem key={link.href} sx={{ p: 0, mb: 1 }}>\n                        <Link\n                          component={link.external ? 'a' : NextLink}\n                          href={link.href}\n                          target={link.external ? '_blank' : undefined}\n                          rel={link.external ? 'noopener noreferrer' : undefined}\n                          sx={{\n                            color: alpha(theme.palette.common.white, 0.8),\n                            textDecoration: 'none',\n                            fontSize: '0.875rem',\n                            transition: 'color 0.2s ease',\n                            '&:hover': {\n                              color: theme.palette.primary.light,\n                            },\n                          }}\n                        >\n                          {link.label}\n                        </Link>\n                      </ListItem>\n                    ))}\n                  </List>\n                </motion.div>\n              </Grid>\n            ))}\n          </Grid>\n\n          {/* Social Media & Contact */}\n          <Box sx={{ mt: 6, pt: 4, borderTop: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>\n            <Grid container spacing={4} alignItems=\"center\">\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                  Follow Us\n                </Typography>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                  {socialLinks.map((social) => (\n                    <SocialIconButton\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      aria-label={social.label}\n                    >\n                      <social.icon />\n                    </SocialIconButton>\n                  ))}\n                </Box>\n              </Grid>\n              \n              <Grid item xs={12} md={6}>\n                <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600 }}>\n                  Contact Info\n                </Typography>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <EmailIcon fontSize=\"small\" sx={{ opacity: 0.7 }} />\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      support@{storeConfig?.store_name?.toLowerCase().replace(/\\s+/g, '') || 'store'}.com\n                    </Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <PhoneIcon fontSize=\"small\" sx={{ opacity: 0.7 }} />\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      +****************\n                    </Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <LocationIcon fontSize=\"small\" sx={{ opacity: 0.7 }} />\n                    <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                      123 Commerce St, City, State 12345\n                    </Typography>\n                  </Box>\n                </Box>\n              </Grid>\n            </Grid>\n          </Box>\n        </Container>\n      </FooterSection>\n\n      {/* Footer Bottom */}\n      <FooterBottom>\n        <Container maxWidth=\"xl\">\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n                {storeConfig?.copyright || `© ${new Date().getFullYear()} ${storeConfig?.store_name || 'E-commerce Store'}. All rights reserved.`}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' }, gap: 3 }}>\n                <Link\n                  component={NextLink}\n                  href=\"/privacy-policy\"\n                  sx={{\n                    color: alpha(theme.palette.common.white, 0.8),\n                    textDecoration: 'none',\n                    fontSize: '0.875rem',\n                    '&:hover': {\n                      color: theme.palette.primary.light,\n                    },\n                  }}\n                >\n                  Privacy\n                </Link>\n                <Link\n                  component={NextLink}\n                  href=\"/terms\"\n                  sx={{\n                    color: alpha(theme.palette.common.white, 0.8),\n                    textDecoration: 'none',\n                    fontSize: '0.875rem',\n                    '&:hover': {\n                      color: theme.palette.primary.light,\n                    },\n                  }}\n                >\n                  Terms\n                </Link>\n                <Link\n                  component={NextLink}\n                  href=\"/cookies\"\n                  sx={{\n                    color: alpha(theme.palette.common.white, 0.8),\n                    textDecoration: 'none',\n                    fontSize: '0.875rem',\n                    '&:hover': {\n                      color: theme.palette.primary.light,\n                    },\n                  }}\n                >\n                  Cookies\n                </Link>\n              </Box>\n            </Grid>\n          </Grid>\n        </Container>\n      </FooterBottom>\n    </StyledFooter>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;;;AAnCA;;;;;;;;;;;;;;;;;;AAqCA,oBAAoB;AACpB,MAAM,eAAe,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/C,iBAAiB,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;QACxC,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QACjC,WAAW;IACb,CAAC;KAJK;AAMN,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAChD,SAAS,MAAM,OAAO,CAAC,GAAG;IAC5B,CAAC;MAFK;AAIN,MAAM,eAAe,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/C,iBAAiB,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;QACxC,SAAS,MAAM,OAAO,CAAC,GAAG;QAC1B,WAAW,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM;IAClE,CAAC;MAJK;AAMN,MAAM,mBAAmB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,gNAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC1D,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QACjC,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;QACnD,QAAQ,MAAM,OAAO,CAAC,GAAG;QACzB,YAAY;QACZ,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC3C,WAAW;QACb;IACF,CAAC;MATK;AAWN,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAE,2LAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAChD,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;QACnD,cAAc,MAAM,KAAK,CAAC,YAAY,GAAG;QACzC,SAAS,MAAM,OAAO,CAAC;QACvB,QAAQ,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM;IAC/D,CAAC;MALK;AAuCS,SAAS,cAAc,EAAE,SAAS,EAAsB;;IACrE,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IAErB,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;oDAAW;oBACf,IAAI;wBACF,MAAM,CAAC,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACrC,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;4BAClB,yBAAyB;4BACzB;yBACD;wBAED,eAAe;oBACjB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,mBAAmB;QACvB,IAAI;YACF,mCAAmC;YACnC,MAAM,mBAAmB;gBAAC;gBAAgB;gBAAgB;aAAiB;YAC3E,MAAM,SAAqB,EAAE;YAE7B,KAAK,MAAM,cAAc,iBAAkB;gBACzC,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,MAAG,AAAD,EAEtB,mIAAA,CAAA,gBAAa,EAAE;wBAAE;oBAAW;oBAE/B,IAAI,SAAS,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;wBACvC,OAAO,IAAI,CAAC,SAAS,SAAS,CAAC,KAAK,CAAC,EAAE;oBACzC;gBACF,EAAE,OAAO,OAAO;oBACd,gCAAgC;oBAChC,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,UAAU,CAAC;gBACrD;YACF;YAEA,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,0BAA0B;IAC1B,MAAM,yBAAyB,OAAO;QACpC,MAAM,cAAc;QAEpB,IAAI,CAAC,gBAAgB,IAAI,IAAI;QAE7B,qBAAqB;QACrB,qBAAqB;QAErB,IAAI;YACF,0EAA0E;YAC1E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,oBAAoB;YAE7E,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,qBAAqB;QACvB,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAyC;QAC7C;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAgB,MAAM;gBAAgB;gBAC/C;oBAAE,OAAO;oBAAgB,MAAM;gBAAgB;gBAC/C;oBAAE,OAAO;oBAAQ,MAAM;gBAAQ;gBAC/B;oBAAE,OAAO;oBAAc,MAAM;gBAAc;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAc,MAAM;gBAAW;gBACxC;oBAAE,OAAO;oBAAO,MAAM;gBAAO;gBAC7B;oBAAE,OAAO;oBAAiB,MAAM;gBAAY;gBAC5C;oBAAE,OAAO;oBAAW,MAAM;gBAAW;gBACrC;oBAAE,OAAO;oBAAc,MAAM;gBAAc;aAC5C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAY,MAAM;gBAAS;gBACpC;oBAAE,OAAO;oBAAW,MAAM;gBAAW;gBACrC;oBAAE,OAAO;oBAAS,MAAM;gBAAS;gBACjC;oBAAE,OAAO;oBAAkB,MAAM;gBAAkB;aACpD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAkB,MAAM;gBAAkB;gBACnD;oBAAE,OAAO;oBAAoB,MAAM;gBAAS;gBAC5C;oBAAE,OAAO;oBAAiB,MAAM;gBAAW;gBAC3C;oBAAE,OAAO;oBAAiB,MAAM;gBAAiB;aAClD;QACH;KACD;IAED,qBAAqB;IACrB,MAAM,cAAc;QAClB;YAAE,MAAM,gKAAA,CAAA,UAAY;YAAE,MAAM;YAAwB,OAAO;QAAW;QACtE;YAAE,MAAM,+JAAA,CAAA,UAAW;YAAE,MAAM;YAAuB,OAAO;QAAU;QACnE;YAAE,MAAM,iKAAA,CAAA,UAAa;YAAE,MAAM;YAAyB,OAAO;QAAY;QACzE;YAAE,MAAM,gKAAA,CAAA,UAAY;YAAE,MAAM;YAAwB,OAAO;QAAW;QACtE;YAAE,MAAM,+JAAA,CAAA,UAAW;YAAE,MAAM;YAAuB,OAAO;QAAU;KACpE;IAED,qBACE,6LAAC;QAAa,WAAW;QAAW,WAAU;;0BAC5C,6LAAC;0BACC,cAAA,6LAAC,6MAAA,CAAA,YAAS;oBAAC,UAAS;;sCAClB,6LAAC,8LAAA,CAAA,OAAI;4BAAC,SAAS;4BAAC,SAAS;;8CAEvB,6LAAC,8LAAA,CAAA,OAAI;oCAAC,IAAI;oCAAC,IAAI;oCAAI,IAAI;8CACrB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;;8DACC,6LAAC,gNAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAK,YAAY;oDAAC,IAAI;wDAAE,YAAY;oDAAI;8DAAG;;;;;;8DAG/D,6LAAC,gNAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAQ,IAAI;wDAAE,IAAI;wDAAG,SAAS;oDAAI;8DAAG;;;;;;8DAIzD,6LAAC,2LAAA,CAAA,MAAG;oDAAC,WAAU;oDAAO,UAAU;;sEAC9B,6LAAC,2LAAA,CAAA,MAAG;4DAAC,IAAI;gEAAE,SAAS;gEAAQ,KAAK;4DAAE;;8EACjC,6LAAC,6MAAA,CAAA,YAAS;oEACR,SAAS;oEACT,MAAK;oEACL,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEAClD,MAAK;oEACL,QAAQ;oEACR,IAAI;wEACF,4BAA4B;4EAC1B,iBAAiB,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;4EACnD,OAAO;4EACP,cAAc;gFACZ,aAAa,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;4EACjD;4EACA,oBAAoB;gFAClB,aAAa,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;4EACjD;4EACA,0BAA0B;gFACxB,aAAa,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;4EACzC;wEACF;wEACA,sCAAsC;4EACpC,OAAO,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;4EACzC,SAAS;wEACX;oEACF;;;;;;8EAEF,6LAAC,oMAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,UAAU;oEACV,IAAI;wEACF,UAAU;wEACV,IAAI;wEACJ,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wEAC3C,WAAW;4EACT,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;wEAC7C;oEACF;8EAEA,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;wDAIZ,mCACC,6LAAC,gNAAA,CAAA,aAAU;4DACT,SAAQ;4DACR,IAAI;gEACF,SAAS;gEACT,IAAI;gEACJ,OAAO,kBAAkB,QAAQ,CAAC,eAC9B,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,GAC3B,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK;4DAC/B;sEAEC;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCASZ,sBAAsB,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC,8LAAA,CAAA,OAAI;wCAAC,IAAI;wCAAC,IAAI;wCAAG,IAAI;wCAAG,IAAI;kDAC3B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;;8DAEhD,6LAAC,gNAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAK,YAAY;oDAAC,IAAI;wDAAE,YAAY;wDAAK,IAAI;oDAAE;8DAChE,QAAQ,KAAK;;;;;;8DAEhB,6LAAC,8LAAA,CAAA,OAAI;oDAAC,KAAK;oDAAC,IAAI;wDAAE,GAAG;oDAAE;8DACpB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC,0MAAA,CAAA,WAAQ;4DAAiB,IAAI;gEAAE,GAAG;gEAAG,IAAI;4DAAE;sEAC1C,cAAA,6LAAC,8LAAA,CAAA,OAAI;gEACH,WAAW,KAAK,QAAQ,GAAG,MAAM,+JAAA,CAAA,UAAQ;gEACzC,MAAM,KAAK,IAAI;gEACf,QAAQ,KAAK,QAAQ,GAAG,WAAW;gEACnC,KAAK,KAAK,QAAQ,GAAG,wBAAwB;gEAC7C,IAAI;oEACF,OAAO,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;oEACzC,gBAAgB;oEAChB,UAAU;oEACV,YAAY;oEACZ,WAAW;wEACT,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK;oEACpC;gEACF;0EAEC,KAAK,KAAK;;;;;;2DAhBA,KAAK,IAAI;;;;;;;;;;;;;;;;uCAXK,QAAQ,KAAK;;;;;;;;;;;sCAsCtD,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,IAAI;gCAAG,IAAI;gCAAG,WAAW,CAAC,UAAU,EAAE,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM;4BAAC;sCACxF,cAAA,6LAAC,8LAAA,CAAA,OAAI;gCAAC,SAAS;gCAAC,SAAS;gCAAG,YAAW;;kDACrC,6LAAC,8LAAA,CAAA,OAAI;wCAAC,IAAI;wCAAC,IAAI;wCAAI,IAAI;;0DACrB,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,YAAY;gDAAC,IAAI;oDAAE,YAAY;gDAAI;0DAAG;;;;;;0DAG/D,6LAAC,2LAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,SAAS;oDAAQ,UAAU;oDAAQ,KAAK;gDAAE;0DAClD,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wDAEC,MAAM,OAAO,IAAI;wDACjB,QAAO;wDACP,KAAI;wDACJ,cAAY,OAAO,KAAK;kEAExB,cAAA,6LAAC,OAAO,IAAI;;;;;uDANP,OAAO,KAAK;;;;;;;;;;;;;;;;kDAYzB,6LAAC,8LAAA,CAAA,OAAI;wCAAC,IAAI;wCAAC,IAAI;wCAAI,IAAI;;0DACrB,6LAAC,gNAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,YAAY;gDAAC,IAAI;oDAAE,YAAY;gDAAI;0DAAG;;;;;;0DAG/D,6LAAC,2LAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,SAAS;oDAAQ,eAAe;oDAAU,KAAK;gDAAE;;kEAC1D,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;wDAAE;;0EACvD,6LAAC,6JAAA,CAAA,UAAS;gEAAC,UAAS;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;;;;;;0EAC/C,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;;oEAAG;oEACvC,aAAa,YAAY,cAAc,QAAQ,QAAQ,OAAO;oEAAQ;;;;;;;;;;;;;kEAGnF,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;wDAAE;;0EACvD,6LAAC,6JAAA,CAAA,UAAS;gEAAC,UAAS;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;;;;;;0EAC/C,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;0EAAG;;;;;;;;;;;;kEAIpD,6LAAC,2LAAA,CAAA,MAAG;wDAAC,IAAI;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;wDAAE;;0EACvD,6LAAC,kKAAA,CAAA,UAAY;gEAAC,UAAS;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;;;;;;0EAClD,6LAAC,gNAAA,CAAA,aAAU;gEAAC,SAAQ;gEAAQ,IAAI;oEAAE,SAAS;gEAAI;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYhE,6LAAC;0BACC,cAAA,6LAAC,6MAAA,CAAA,YAAS;oBAAC,UAAS;8BAClB,cAAA,6LAAC,8LAAA,CAAA,OAAI;wBAAC,SAAS;wBAAC,SAAS;wBAAG,YAAW;;0CACrC,6LAAC,8LAAA,CAAA,OAAI;gCAAC,IAAI;gCAAC,IAAI;gCAAI,IAAI;0CACrB,cAAA,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,IAAI;wCAAE,SAAS;oCAAI;8CAC5C,aAAa,aAAa,CAAC,EAAE,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,aAAa,cAAc,mBAAmB,sBAAsB,CAAC;;;;;;;;;;;0CAGrI,6LAAC,8LAAA,CAAA,OAAI;gCAAC,IAAI;gCAAC,IAAI;gCAAI,IAAI;0CACrB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,SAAS;wCAAQ,gBAAgB;4CAAE,IAAI;4CAAc,IAAI;wCAAW;wCAAG,KAAK;oCAAE;;sDACvF,6LAAC,8LAAA,CAAA,OAAI;4CACH,WAAW,+JAAA,CAAA,UAAQ;4CACnB,MAAK;4CACL,IAAI;gDACF,OAAO,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;gDACzC,gBAAgB;gDAChB,UAAU;gDACV,WAAW;oDACT,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK;gDACpC;4CACF;sDACD;;;;;;sDAGD,6LAAC,8LAAA,CAAA,OAAI;4CACH,WAAW,+JAAA,CAAA,UAAQ;4CACnB,MAAK;4CACL,IAAI;gDACF,OAAO,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;gDACzC,gBAAgB;gDAChB,UAAU;gDACV,WAAW;oDACT,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK;gDACpC;4CACF;sDACD;;;;;;sDAGD,6LAAC,8LAAA,CAAA,OAAI;4CACH,WAAW,+JAAA,CAAA,UAAQ;4CACnB,MAAK;4CACL,IAAI;gDACF,OAAO,CAAA,GAAA,iLAAA,CAAA,QAAK,AAAD,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE;gDACzC,gBAAgB;gDAChB,UAAU;gDACV,WAAW;oDACT,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK;gDACpC;4CACF;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA9WwB;;QACR,wMAAA,CAAA,WAAQ;;;MADA", "debugId": null}}]}