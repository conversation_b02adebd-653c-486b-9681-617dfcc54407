(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Types for Next.js
__turbopack_context__.s({
    "PageBuilderElementType": (()=>PageBuilderElementType)
});
var PageBuilderElementType = /*#__PURE__*/ function(PageBuilderElementType) {
    PageBuilderElementType["ROW"] = "row";
    PageBuilderElementType["COLUMN"] = "column";
    PageBuilderElementType["TEXT"] = "text";
    PageBuilderElementType["HEADING"] = "heading";
    PageBuilderElementType["IMAGE"] = "image";
    PageBuilderElementType["BUTTON"] = "button";
    PageBuilderElementType["BANNER"] = "banner";
    PageBuilderElementType["SLIDER"] = "slider";
    PageBuilderElementType["PRODUCTS"] = "products";
    PageBuilderElementType["VIDEO"] = "video";
    PageBuilderElementType["MAP"] = "map";
    PageBuilderElementType["BLOCK"] = "block";
    PageBuilderElementType["HTML"] = "html";
    PageBuilderElementType["DIVIDER"] = "divider";
    PageBuilderElementType["TABS"] = "tabs";
    PageBuilderElementType["TAB_ITEM"] = "tab-item";
    return PageBuilderElementType;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Constants
__turbopack_context__.s({
    "ALIGNMENT_OPTIONS": (()=>ALIGNMENT_OPTIONS),
    "BACKGROUND_ATTACHMENT_OPTIONS": (()=>BACKGROUND_ATTACHMENT_OPTIONS),
    "BACKGROUND_REPEAT_OPTIONS": (()=>BACKGROUND_REPEAT_OPTIONS),
    "BACKGROUND_SIZE_OPTIONS": (()=>BACKGROUND_SIZE_OPTIONS),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "BUTTON_TYPES": (()=>BUTTON_TYPES),
    "CONTENT_TYPE_MAPPING": (()=>CONTENT_TYPE_MAPPING),
    "CSS_PROPERTY_MAPPING": (()=>CSS_PROPERTY_MAPPING),
    "DEFAULT_CONFIG": (()=>DEFAULT_CONFIG),
    "DEFAULT_STYLES": (()=>DEFAULT_STYLES),
    "ELEMENT_TYPE_MAPPING": (()=>ELEMENT_TYPE_MAPPING),
    "PAGE_BUILDER_ATTRIBUTES": (()=>PAGE_BUILDER_ATTRIBUTES),
    "PAGE_BUILDER_CLASSES": (()=>PAGE_BUILDER_CLASSES),
    "VERTICAL_ALIGNMENT_OPTIONS": (()=>VERTICAL_ALIGNMENT_OPTIONS),
    "VIDEO_PROVIDERS": (()=>VIDEO_PROVIDERS)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
;
const PAGE_BUILDER_CLASSES = {
    // Row classes
    ROW: 'pagebuilder-row',
    ROW_CONTAINED: 'pagebuilder-row-contained',
    ROW_FULL_WIDTH: 'pagebuilder-row-full-width',
    ROW_FULL_BLEED: 'pagebuilder-row-full-bleed',
    // Column classes
    COLUMN: 'pagebuilder-column',
    COLUMN_GROUP: 'pagebuilder-column-group',
    COLUMN_LINE: 'pagebuilder-column-line',
    // Content type classes
    TEXT: 'pagebuilder-text',
    HEADING: 'pagebuilder-heading',
    IMAGE: 'pagebuilder-image',
    BUTTON: 'pagebuilder-button-item',
    BANNER: 'pagebuilder-banner-item',
    SLIDER: 'pagebuilder-slider',
    PRODUCTS: 'pagebuilder-products',
    VIDEO: 'pagebuilder-video',
    MAP: 'pagebuilder-map',
    BLOCK: 'pagebuilder-block',
    HTML: 'pagebuilder-html-code',
    DIVIDER: 'pagebuilder-divider',
    TABS: 'pagebuilder-tabs',
    TAB_ITEM: 'pagebuilder-tab-item'
};
const PAGE_BUILDER_ATTRIBUTES = {
    // Common attributes
    CONTENT_TYPE: 'data-content-type',
    APPEARANCE: 'data-appearance',
    ELEMENT: 'data-element',
    // Background attributes
    BACKGROUND_IMAGES: 'data-background-images',
    BACKGROUND_TYPE: 'data-background-type',
    VIDEO_LOOP: 'data-video-loop',
    VIDEO_PLAY_ONLY_VISIBLE: 'data-video-play-only-visible',
    VIDEO_LAZY_LOAD: 'data-video-lazy-load',
    VIDEO_FALLBACK_SRC: 'data-video-fallback-src',
    // Parallax attributes
    ENABLE_PARALLAX: 'data-enable-parallax',
    PARALLAX_SPEED: 'data-parallax-speed',
    // Banner attributes
    SHOW_BUTTON: 'data-show-button',
    SHOW_OVERLAY: 'data-show-overlay',
    // Slider attributes
    AUTOPLAY: 'data-autoplay',
    AUTOPLAY_SPEED: 'data-autoplay-speed',
    FADE: 'data-fade',
    INFINITE_LOOP: 'data-infinite-loop',
    SHOW_ARROWS: 'data-show-arrows',
    SHOW_DOTS: 'data-show-dots',
    // Products attributes
    PRODUCTS_COUNT: 'data-products-count',
    SORT_ORDER: 'data-sort-order',
    CAROUSEL_MODE: 'data-carousel-mode',
    // Map attributes
    SHOW_CONTROLS: 'data-show-controls',
    // Tabs attributes
    DEFAULT_ACTIVE_TAB: 'data-default-active-tab',
    TABS_ALIGNMENT: 'data-tabs-alignment',
    TABS_NAVIGATION: 'data-tabs-navigation'
};
const ELEMENT_TYPE_MAPPING = {
    [PAGE_BUILDER_CLASSES.ROW]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
    [PAGE_BUILDER_CLASSES.COLUMN]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    [PAGE_BUILDER_CLASSES.TEXT]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
    [PAGE_BUILDER_CLASSES.HEADING]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
    [PAGE_BUILDER_CLASSES.IMAGE]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
    [PAGE_BUILDER_CLASSES.BUTTON]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
    [PAGE_BUILDER_CLASSES.BANNER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
    [PAGE_BUILDER_CLASSES.SLIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].SLIDER,
    [PAGE_BUILDER_CLASSES.PRODUCTS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].PRODUCTS,
    [PAGE_BUILDER_CLASSES.VIDEO]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
    [PAGE_BUILDER_CLASSES.MAP]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].MAP,
    [PAGE_BUILDER_CLASSES.BLOCK]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BLOCK,
    [PAGE_BUILDER_CLASSES.HTML]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
    [PAGE_BUILDER_CLASSES.DIVIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
    [PAGE_BUILDER_CLASSES.TABS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TABS,
    [PAGE_BUILDER_CLASSES.TAB_ITEM]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TAB_ITEM
};
const CONTENT_TYPE_MAPPING = {
    'row': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
    'column-group': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    'column': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    'text': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
    'heading': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
    'image': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
    'button-item': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
    'banner': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
    'slider': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].SLIDER,
    'products': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].PRODUCTS,
    'video': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
    'map': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].MAP,
    'block': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BLOCK,
    'html': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
    'divider': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
    'tabs': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TABS,
    'tab-item': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TAB_ITEM
};
const DEFAULT_STYLES = {
    ROW: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        boxSizing: 'border-box'
    },
    COLUMN: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        boxSizing: 'border-box'
    },
    TEXT: {
        wordWrap: 'break-word'
    },
    HEADING: {
        wordWrap: 'break-word'
    },
    IMAGE: {
        maxWidth: '100%',
        height: 'auto'
    },
    BUTTON: {
        display: 'inline-block',
        textDecoration: 'none',
        cursor: 'pointer'
    },
    BANNER: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
    },
    SLIDER: {
        position: 'relative'
    },
    PRODUCTS: {
        display: 'flex',
        flexWrap: 'wrap'
    },
    VIDEO: {
        position: 'relative',
        width: '100%'
    },
    MAP: {
        width: '100%',
        height: '400px'
    },
    DIVIDER: {
        width: '100%',
        height: '1px',
        backgroundColor: '#e0e0e0'
    },
    TABS: {
        width: '100%'
    }
};
const BREAKPOINTS = {
    MOBILE: 768,
    TABLET: 1024,
    DESKTOP: 1200
};
const DEFAULT_CONFIG = {
    enableLazyLoading: true,
    imageOptimization: true,
    customElementParsers: {},
    componentOverrides: {}
};
const CSS_PROPERTY_MAPPING = {
    'background-color': 'backgroundColor',
    'background-image': 'backgroundImage',
    'background-size': 'backgroundSize',
    'background-position': 'backgroundPosition',
    'background-repeat': 'backgroundRepeat',
    'background-attachment': 'backgroundAttachment',
    'min-height': 'minHeight',
    'text-align': 'textAlign',
    'vertical-align': 'verticalAlign',
    'border-color': 'borderColor',
    'border-width': 'borderWidth',
    'border-radius': 'borderRadius',
    'border-style': 'borderStyle',
    'margin-top': 'marginTop',
    'margin-right': 'marginRight',
    'margin-bottom': 'marginBottom',
    'margin-left': 'marginLeft',
    'padding-top': 'paddingTop',
    'padding-right': 'paddingRight',
    'padding-bottom': 'paddingBottom',
    'padding-left': 'paddingLeft'
};
const VIDEO_PROVIDERS = {
    YOUTUBE: 'youtube',
    VIMEO: 'vimeo',
    MP4: 'mp4'
};
const BUTTON_TYPES = {
    PRIMARY: 'primary',
    SECONDARY: 'secondary',
    LINK: 'link'
};
const ALIGNMENT_OPTIONS = {
    LEFT: 'left',
    CENTER: 'center',
    RIGHT: 'right',
    JUSTIFY: 'justify'
};
const VERTICAL_ALIGNMENT_OPTIONS = {
    TOP: 'top',
    MIDDLE: 'middle',
    BOTTOM: 'bottom'
};
const BACKGROUND_SIZE_OPTIONS = {
    COVER: 'cover',
    CONTAIN: 'contain',
    AUTO: 'auto'
};
const BACKGROUND_REPEAT_OPTIONS = {
    NO_REPEAT: 'no-repeat',
    REPEAT: 'repeat',
    REPEAT_X: 'repeat-x',
    REPEAT_Y: 'repeat-y'
};
const BACKGROUND_ATTACHMENT_OPTIONS = {
    SCROLL: 'scroll',
    FIXED: 'fixed'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Utilities
__turbopack_context__.s({
    "cleanHtmlContent": (()=>cleanHtmlContent),
    "convertColumnWidth": (()=>convertColumnWidth),
    "cssValueToNumber": (()=>cssValueToNumber),
    "debounce": (()=>debounce),
    "extractAttributes": (()=>extractAttributes),
    "extractStyles": (()=>extractStyles),
    "generateElementId": (()=>generateElementId),
    "getBreakpoint": (()=>getBreakpoint),
    "getElementContent": (()=>getElementContent),
    "getElementType": (()=>getElementType),
    "isBrowser": (()=>isBrowser),
    "isPageBuilderElement": (()=>isPageBuilderElement),
    "logParsingError": (()=>logParsingError),
    "mergeConfig": (()=>mergeConfig),
    "numberToCssValue": (()=>numberToCssValue),
    "optimizeImageUrl": (()=>optimizeImageUrl),
    "parseBackgroundImages": (()=>parseBackgroundImages),
    "safeJsonParse": (()=>safeJsonParse),
    "validateElement": (()=>validateElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)");
;
;
function generateElementId() {
    return `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
function getElementType(element) {
    // Check data-content-type attribute first
    const contentType = element.getAttribute('data-content-type');
    if (contentType && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_TYPE_MAPPING"][contentType]) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_TYPE_MAPPING"][contentType];
    }
    // Check CSS classes
    const classList = Array.from(element.classList);
    for (const className of classList){
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_TYPE_MAPPING"][className]) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_TYPE_MAPPING"][className];
        }
    }
    // Check for specific element patterns
    if (element.tagName === 'IMG') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE;
    }
    if (element.tagName === 'VIDEO' || element.tagName === 'IFRAME') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO;
    }
    if ([
        'H1',
        'H2',
        'H3',
        'H4',
        'H5',
        'H6'
    ].includes(element.tagName)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING;
    }
    if (element.tagName === 'A' && element.classList.contains('pagebuilder-button-primary')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON;
    }
    // Default to text for content elements
    if (element.textContent && element.textContent.trim()) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT;
    }
    return null;
}
function extractAttributes(element) {
    const attributes = {};
    // Extract data attributes
    Array.from(element.attributes).forEach((attr)=>{
        if (attr.name.startsWith('data-')) {
            const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
            attributes[key] = attr.value;
        }
    });
    // Extract common HTML attributes
    if (element.getAttribute('id')) {
        attributes.id = element.getAttribute('id');
    }
    if (element.getAttribute('class')) {
        attributes.className = element.getAttribute('class');
    }
    // Extract specific attributes based on element type
    if (element.tagName === 'IMG') {
        attributes.src = element.getAttribute('src');
        attributes.alt = element.getAttribute('alt');
        attributes.title = element.getAttribute('title');
    }
    if (element.tagName === 'A') {
        attributes.href = element.getAttribute('href');
        attributes.target = element.getAttribute('target');
    }
    if (element.tagName === 'VIDEO') {
        attributes.src = element.getAttribute('src');
        attributes.autoplay = element.hasAttribute('autoplay');
        attributes.muted = element.hasAttribute('muted');
        attributes.loop = element.hasAttribute('loop');
        attributes.controls = element.hasAttribute('controls');
    }
    return attributes;
}
function extractStyles(element) {
    const styles = {};
    // Get computed styles
    if ("TURBOPACK compile-time truthy", 1) {
        const computedStyles = window.getComputedStyle(element);
        // Extract relevant CSS properties
        Object.entries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"]).forEach(([cssProperty, jsProperty])=>{
            const value = computedStyles.getPropertyValue(cssProperty);
            if (value && value !== 'initial' && value !== 'inherit') {
                styles[jsProperty] = value;
            }
        });
    }
    // Extract inline styles
    const inlineStyle = element.getAttribute('style');
    if (inlineStyle) {
        const styleDeclarations = inlineStyle.split(';');
        styleDeclarations.forEach((declaration)=>{
            const [property, value] = declaration.split(':').map((s)=>s.trim());
            if (property && value) {
                const jsProperty = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"][property] || property.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
                styles[jsProperty] = value;
            }
        });
    }
    return styles;
}
function parseBackgroundImages(backgroundImagesData) {
    try {
        const data = JSON.parse(backgroundImagesData);
        if (data && data.desktop_image) {
            return [
                data.desktop_image,
                data.mobile_image
            ].filter(Boolean);
        }
        return [];
    } catch  {
        return [];
    }
}
function cssValueToNumber(value) {
    return parseFloat(value.replace(/[^\d.-]/g, ''));
}
function numberToCssValue(value, unit = 'px') {
    return `${value}${unit}`;
}
function isPageBuilderElement(element) {
    return element.hasAttribute('data-content-type') || Array.from(element.classList).some((className)=>className.startsWith('pagebuilder-'));
}
function getElementContent(element, preserveHtml = false) {
    if (preserveHtml) {
        return element.innerHTML;
    }
    return element.textContent || '';
}
function cleanHtmlContent(html) {
    // Remove Magento-specific attributes that aren't needed in React
    return html.replace(/data-pb-style="[^"]*"/g, '').replace(/data-element="[^"]*"/g, '').replace(/class="[^"]*pagebuilder-[^"]*"/g, '').trim();
}
function mergeConfig(userConfig = {}) {
    return {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"],
        ...userConfig,
        customElementParsers: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"].customElementParsers,
            ...userConfig.customElementParsers
        },
        componentOverrides: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"].componentOverrides,
            ...userConfig.componentOverrides
        }
    };
}
function optimizeImageUrl(url, width, height) {
    if (!url) return '';
    // If it's already a Next.js optimized URL, return as is
    if (url.includes('/_next/image')) {
        return url;
    }
    // Convert Magento media URLs
    if (url.includes('/media/')) {
        // Remove any existing resize parameters
        const cleanUrl = url.split('?')[0];
        // Add Next.js image optimization parameters
        const params = new URLSearchParams();
        params.set('url', cleanUrl);
        if (width) params.set('w', width.toString());
        if (height) params.set('h', height.toString());
        params.set('q', '75'); // Default quality
        return `/_next/image?${params.toString()}`;
    }
    return url;
}
function validateElement(element) {
    if (!element.type || !element.id) {
        return false;
    }
    // Type-specific validation
    switch(element.type){
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE:
            return !!element.attributes?.src;
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON:
            return !!(element.content || element.attributes?.buttonText);
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO:
            return !!(element.attributes?.videoUrl || element.attributes?.src);
        default:
            return true;
    }
}
function getBreakpoint(width) {
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
}
function convertColumnWidth(width) {
    // Magento uses percentages like "33.3333%"
    const percentage = parseFloat(width);
    if (percentage > 0) {
        return `${percentage}%`;
    }
    // Fallback to equal distribution
    return '1fr';
}
function safeJsonParse(json, fallback) {
    try {
        return JSON.parse(json);
    } catch  {
        return fallback;
    }
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function isBrowser() {
    return "object" !== 'undefined';
}
function logParsingError(error, element) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.error('Page Builder parsing error:', error);
        if (element) {
            console.error('Element:', element);
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Row": (()=>Row),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Container/Container.js [app-client] (ecmascript) <export default as Container>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Row = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Determine container type based on appearance
    const getContainerComponent = ()=>{
        switch(attributes.appearance){
            case 'contained':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"];
            case 'full-width':
            case 'full-bleed':
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"];
        }
    };
    const ContainerComponent = getContainerComponent();
    // Build styles
    const rowStyles = {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        boxSizing: 'border-box',
        ...styles,
        ...style
    };
    // Background styles
    if (attributes.backgroundColor) {
        rowStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.backgroundImage) {
        rowStyles.backgroundImage = `url(${attributes.backgroundImage})`;
        rowStyles.backgroundSize = attributes.backgroundSize || 'cover';
        rowStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
        rowStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
        rowStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
    }
    if (attributes.minHeight) {
        rowStyles.minHeight = attributes.minHeight;
    }
    // Vertical alignment
    if (attributes.verticalAlignment) {
        switch(attributes.verticalAlignment){
            case 'top':
                rowStyles.justifyContent = 'flex-start';
                break;
            case 'middle':
                rowStyles.justifyContent = 'center';
                break;
            case 'bottom':
                rowStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Parallax effect (if enabled and in browser)
    const parallaxProps = attributes.enableParallax && "object" !== 'undefined' ? {
        initial: {
            y: 0
        },
        whileInView: {
            y: -20
        },
        transition: {
            duration: 0.6,
            ease: 'easeOut'
        },
        viewport: {
            once: false,
            amount: 0.3
        }
    } : {};
    const containerProps = attributes.appearance === 'contained' ? {
        maxWidth: 'lg',
        sx: {
            px: {
                xs: 2,
                sm: 3
            }
        }
    } : {
        sx: {
            width: '100%',
            maxWidth: 'none',
            px: attributes.appearance === 'full-bleed' ? 0 : {
                xs: 2,
                sm: 3
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-row ${className || ''}`,
        style: rowStyles,
        ...parallaxProps,
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    zIndex: 0
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
                lineNumber: 102,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ContainerComponent, {
                ...containerProps,
                sx: {
                    position: 'relative',
                    zIndex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    height: '100%',
                    ...containerProps.sx
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
};
_c = Row;
const __TURBOPACK__default__export__ = Row;
var _c;
__turbopack_context__.k.register(_c, "Row");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Column": (()=>Column),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
const Column = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const columnStyles = {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        boxSizing: 'border-box',
        flex: '1 1 auto',
        ...styles,
        ...style
    };
    // Width handling
    if (attributes.width) {
        const width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertColumnWidth"])(attributes.width);
        columnStyles.width = width;
        columnStyles.flexBasis = width;
        columnStyles.flexGrow = 0;
        columnStyles.flexShrink = 0;
    }
    // Background styles
    if (attributes.backgroundColor) {
        columnStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.backgroundImage) {
        columnStyles.backgroundImage = `url(${attributes.backgroundImage})`;
        columnStyles.backgroundSize = attributes.backgroundSize || 'cover';
        columnStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
        columnStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
        columnStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
    }
    // Height handling
    if (attributes.appearance === 'full-height') {
        columnStyles.height = '100%';
    } else if (attributes.minHeight) {
        columnStyles.minHeight = attributes.minHeight;
    }
    // Vertical alignment
    if (attributes.verticalAlignment) {
        switch(attributes.verticalAlignment){
            case 'top':
                columnStyles.justifyContent = 'flex-start';
                break;
            case 'middle':
                columnStyles.justifyContent = 'center';
                break;
            case 'bottom':
                columnStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Animation variants
    const columnVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-column ${className || ''}`,
        style: columnStyles,
        variants: columnVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    zIndex: 0
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
                lineNumber: 95,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'relative',
                    zIndex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    height: '100%',
                    padding: {
                        xs: 1,
                        sm: 2
                    }
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
                lineNumber: 108,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
};
_c = Column;
const __TURBOPACK__default__export__ = Column;
var _c;
__turbopack_context__.k.register(_c, "Column");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>Text),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Text = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Build styles
    const textStyles = {
        wordWrap: 'break-word',
        lineHeight: 1.6,
        ...styles,
        ...style
    };
    // Text alignment
    if (attributes.textAlign) {
        textStyles.textAlign = attributes.textAlign;
    }
    // Border styles
    if (attributes.border) {
        textStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        textStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        textStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        textStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const textVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    // Check if content contains HTML
    const isHtml = content && (content.includes('<') || content.includes('&'));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-text ${className || ''}`,
        variants: textVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: isHtml ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: textStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
            lineNumber: 67,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            component: "div",
            sx: textStyles,
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
            lineNumber: 73,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
_c = Text;
const __TURBOPACK__default__export__ = Text;
var _c;
__turbopack_context__.k.register(_c, "Text");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heading": (()=>Heading),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Heading = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Determine heading variant
    const variant = attributes.headingType || 'h2';
    // Build styles
    const headingStyles = {
        wordWrap: 'break-word',
        marginBottom: '1rem',
        ...styles,
        ...style
    };
    // Text alignment
    if (attributes.textAlign) {
        headingStyles.textAlign = attributes.textAlign;
    }
    // Border styles
    if (attributes.border) {
        headingStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        headingStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        headingStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        headingStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const headingVariants = {
        hidden: {
            opacity: 0,
            y: 30
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    // Check if content contains HTML
    const isHtml = content && (content.includes('<') || content.includes('&'));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-heading ${className || ''}`,
        variants: headingVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: isHtml ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            variant: variant,
            component: variant,
            sx: headingStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
            lineNumber: 70,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            variant: variant,
            component: variant,
            sx: headingStyles,
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
            lineNumber: 77,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
};
_c = Heading;
const __TURBOPACK__default__export__ = Heading;
var _c;
__turbopack_context__.k.register(_c, "Heading");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderImage": (()=>PageBuilderImage),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const PageBuilderImage = ({ element, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const imageContainerStyles = {
        position: 'relative',
        display: 'inline-block',
        maxWidth: '100%',
        ...styles,
        ...style
    };
    // Alignment
    if (attributes.alignment) {
        switch(attributes.alignment){
            case 'left':
                imageContainerStyles.textAlign = 'left';
                break;
            case 'center':
                imageContainerStyles.textAlign = 'center';
                imageContainerStyles.margin = '0 auto';
                break;
            case 'right':
                imageContainerStyles.textAlign = 'right';
                imageContainerStyles.marginLeft = 'auto';
                break;
        }
    }
    // Border styles
    const imageStyles = {
        maxWidth: '100%',
        height: 'auto'
    };
    if (attributes.border) {
        imageStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        imageStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        imageStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        imageStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const imageVariants = {
        hidden: {
            opacity: 0,
            scale: 0.95
        },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    // Optimize image URL
    const optimizedSrc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.src);
    // Image component
    const ImageComponent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: imageVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        whileHover: {
            scale: 1.02
        },
        transition: {
            duration: 0.3
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: {
                position: 'relative',
                display: 'inline-block',
                ...imageStyles
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: optimizedSrc,
                    alt: attributes.alt || '',
                    title: attributes.title,
                    width: 800,
                    height: 600,
                    style: {
                        width: '100%',
                        height: 'auto',
                        ...imageStyles
                    },
                    loading: attributes.lazyLoading !== false ? 'lazy' : 'eager',
                    quality: 85
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                attributes.caption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    component: "figcaption",
                    sx: {
                        mt: 1,
                        fontSize: '0.875rem',
                        color: 'text.secondary',
                        textAlign: attributes.alignment || 'center',
                        fontStyle: 'italic'
                    },
                    children: attributes.caption
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
                    lineNumber: 114,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-image ${className || ''}`,
        component: "figure",
        sx: {
            margin: 0,
            padding: 0,
            ...imageContainerStyles
        },
        children: attributes.link ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: attributes.link,
            target: attributes.linkTarget || '_self',
            style: {
                textDecoration: 'none'
            },
            children: ImageComponent
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
            lineNumber: 142,
            columnNumber: 9
        }, this) : ImageComponent
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
};
_c = PageBuilderImage;
const __TURBOPACK__default__export__ = PageBuilderImage;
var _c;
__turbopack_context__.k.register(_c, "PageBuilderImage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Button/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
'use client';
;
;
;
;
const Button = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Build container styles
    const containerStyles = {
        display: 'flex',
        ...styles,
        ...style
    };
    // Text alignment for container
    if (attributes.textAlign) {
        switch(attributes.textAlign){
            case 'left':
                containerStyles.justifyContent = 'flex-start';
                break;
            case 'center':
                containerStyles.justifyContent = 'center';
                break;
            case 'right':
                containerStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Build button styles
    const buttonStyles = {
        textTransform: 'none',
        fontWeight: 500,
        borderRadius: '8px',
        padding: '12px 24px',
        fontSize: '1rem',
        transition: 'all 0.3s ease'
    };
    // Button type styling
    let variant = 'contained';
    let color = 'primary';
    switch(attributes.buttonType){
        case 'primary':
            variant = 'contained';
            color = 'primary';
            break;
        case 'secondary':
            variant = 'outlined';
            color = 'primary';
            break;
        case 'link':
            variant = 'text';
            color = 'primary';
            break;
    }
    // Custom colors
    if (attributes.backgroundColor) {
        buttonStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.textColor) {
        buttonStyles.color = attributes.textColor;
    }
    // Border styles
    if (attributes.border) {
        buttonStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        buttonStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        buttonStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        buttonStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const buttonVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        },
        hover: {
            scale: 1.05,
            transition: {
                duration: 0.2
            }
        },
        tap: {
            scale: 0.95,
            transition: {
                duration: 0.1
            }
        }
    };
    const ButtonComponent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: buttonVariants,
        initial: "hidden",
        whileInView: "visible",
        whileHover: "hover",
        whileTap: "tap",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
            variant: variant,
            color: color,
            sx: {
                ...buttonStyles,
                '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                }
            },
            children: content || attributes.buttonText || 'Button'
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
            lineNumber: 119,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-button ${className || ''}`,
        sx: containerStyles,
        children: attributes.link ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: attributes.link,
            target: attributes.linkTarget || '_self',
            style: {
                textDecoration: 'none'
            },
            children: ButtonComponent
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
            lineNumber: 141,
            columnNumber: 9
        }, this) : ButtonComponent
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
};
_c = Button;
const __TURBOPACK__default__export__ = Button;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Banner": (()=>Banner),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Button/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const Banner = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Build container styles
    const bannerStyles = {
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        minHeight: attributes.minHeight || '400px',
        ...styles,
        ...style
    };
    // Background styles
    if (attributes.backgroundColor) {
        bannerStyles.backgroundColor = attributes.backgroundColor;
    }
    // Content placement
    let contentAlignment = 'center';
    if (attributes.contentPlacement) {
        contentAlignment = attributes.contentPlacement;
    }
    // Animation variants
    const bannerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    const contentVariants = {
        hidden: {
            opacity: 0,
            y: 50
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                delay: 0.3,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-banner ${className || ''}`,
        style: bannerStyles,
        variants: bannerVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 0
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.backgroundImage),
                    alt: "",
                    fill: true,
                    style: {
                        objectFit: attributes.backgroundSize === 'contain' ? 'contain' : 'cover',
                        objectPosition: attributes.backgroundPosition || 'center center'
                    },
                    quality: 85,
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                    lineNumber: 87,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 77,
                columnNumber: 9
            }, this),
            attributes.showOverlay && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: attributes.overlayColor || 'rgba(0, 0, 0, 0.4)',
                    zIndex: 1
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'relative',
                    zIndex: 2,
                    width: '100%',
                    maxWidth: '800px',
                    padding: {
                        xs: 3,
                        md: 6
                    },
                    textAlign: contentAlignment,
                    color: 'white'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: contentVariants,
                    initial: "hidden",
                    whileInView: "visible",
                    viewport: {
                        once: true,
                        amount: 0.1
                    },
                    children: [
                        attributes.content && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                mb: 3
                            },
                            children: attributes.content.includes('<') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                dangerouslySetInnerHTML: {
                                    __html: attributes.content
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 138,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                variant: "h3",
                                component: "h2",
                                sx: {
                                    fontWeight: 700,
                                    mb: 2,
                                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                                    fontSize: {
                                        xs: '2rem',
                                        md: '3rem'
                                    }
                                },
                                children: attributes.content
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 140,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 136,
                            columnNumber: 13
                        }, this),
                        children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                mb: 3
                            },
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 158,
                            columnNumber: 13
                        }, this),
                        attributes.showButton && attributes.buttonText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            whileHover: {
                                scale: 1.05
                            },
                            whileTap: {
                                scale: 0.95
                            },
                            children: attributes.buttonLink ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: attributes.buttonLink,
                                target: attributes.buttonTarget || '_self',
                                style: {
                                    textDecoration: 'none'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    variant: attributes.buttonType === 'secondary' ? 'outlined' : 'contained',
                                    size: "large",
                                    sx: {
                                        fontSize: '1.125rem',
                                        fontWeight: 600,
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: 2,
                                        textTransform: 'none',
                                        boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                                        color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                        borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                        '&:hover': {
                                            boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                                            transform: 'translateY(-2px)'
                                        },
                                        transition: 'all 0.3s ease-in-out'
                                    },
                                    children: attributes.buttonText
                                }, void 0, false, {
                                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                    lineNumber: 175,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 170,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: attributes.buttonType === 'secondary' ? 'outlined' : 'contained',
                                size: "large",
                                sx: {
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                                    color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                    borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                    '&:hover': {
                                        boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                                        transform: 'translateY(-2px)'
                                    },
                                    transition: 'all 0.3s ease-in-out'
                                },
                                children: attributes.buttonText
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 199,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 165,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                    lineNumber: 128,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 117,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
        lineNumber: 67,
        columnNumber: 5
    }, this);
};
_c = Banner;
const __TURBOPACK__default__export__ = Banner;
var _c;
__turbopack_context__.k.register(_c, "Banner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Video": (()=>Video),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/IconButton/IconButton.js [app-client] (ecmascript) <export default as IconButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PlayArrow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Pause.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
const Video = ({ element, className, style })=>{
    _s();
    const { attributes, styles } = element;
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showControls, setShowControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Build container styles
    const videoContainerStyles = {
        position: 'relative',
        width: '100%',
        maxWidth: attributes.maxWidth || '100%',
        margin: '0 auto',
        ...styles,
        ...style
    };
    // Get video embed URL
    const getEmbedUrl = ()=>{
        const { videoType, videoUrl, videoId } = attributes;
        if (videoType === 'youtube') {
            const id = videoId || extractYouTubeId(videoUrl);
            return `https://www.youtube.com/embed/${id}?autoplay=${attributes.autoplay ? 1 : 0}&mute=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
        }
        if (videoType === 'vimeo') {
            const id = videoId || extractVimeoId(videoUrl);
            return `https://player.vimeo.com/video/${id}?autoplay=${attributes.autoplay ? 1 : 0}&muted=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
        }
        return videoUrl;
    };
    // Extract YouTube video ID
    const extractYouTubeId = (url)=>{
        if (!url) return '';
        const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        return match ? match[1] : '';
    };
    // Extract Vimeo video ID
    const extractVimeoId = (url)=>{
        if (!url) return '';
        const match = url.match(/vimeo\.com\/(\d+)/);
        return match ? match[1] : '';
    };
    // Handle play/pause
    const handlePlayPause = ()=>{
        setIsPlaying(!isPlaying);
    };
    // Animation variants
    const videoVariants = {
        hidden: {
            opacity: 0,
            scale: 0.95
        },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    const overlayVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1
        },
        exit: {
            opacity: 0
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-video ${className || ''}`,
        style: videoContainerStyles,
        variants: videoVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        onMouseEnter: ()=>setShowControls(true),
        onMouseLeave: ()=>setShowControls(false),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                position: 'relative',
                width: '100%',
                paddingBottom: '56.25%',
                height: 0,
                overflow: 'hidden',
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
            },
            children: [
                attributes.videoType === 'mp4' ? // Native HTML5 video
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                    src: attributes.videoUrl,
                    autoPlay: attributes.autoplay,
                    muted: attributes.muted,
                    loop: attributes.loop,
                    controls: attributes.controls,
                    style: {
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                    },
                    poster: attributes.fallbackImage ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.fallbackImage) : undefined
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                    lineNumber: 109,
                    columnNumber: 11
                }, this) : // Embedded video (YouTube/Vimeo)
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: !isPlaying && attributes.fallbackImage ? // Fallback image with play button
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                        sx: {
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            cursor: 'pointer'
                        },
                        onClick: handlePlayPause,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.fallbackImage),
                                alt: "Video thumbnail",
                                fill: true,
                                style: {
                                    objectFit: 'cover'
                                },
                                quality: 85
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                lineNumber: 141,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                variants: overlayVariants,
                                initial: "hidden",
                                animate: "visible",
                                exit: "exit",
                                style: {
                                    position: 'absolute',
                                    top: '50%',
                                    left: '50%',
                                    transform: 'translate(-50%, -50%)'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                                    sx: {
                                        backgroundColor: 'rgba(0,0,0,0.7)',
                                        color: 'white',
                                        width: 80,
                                        height: 80,
                                        '&:hover': {
                                            backgroundColor: 'rgba(0,0,0,0.8)',
                                            transform: 'scale(1.1)'
                                        },
                                        transition: 'all 0.3s ease'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        sx: {
                                            fontSize: 40
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                        lineNumber: 175,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                    lineNumber: 162,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                lineNumber: 150,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 130,
                        columnNumber: 15
                    }, this) : // Embedded iframe
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                        src: getEmbedUrl(),
                        style: {
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            border: 'none'
                        },
                        allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
                        allowFullScreen: true,
                        loading: attributes.lazyLoading !== false ? 'lazy' : 'eager'
                    }, void 0, false, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 181,
                        columnNumber: 15
                    }, this)
                }, void 0, false),
                attributes.videoType === 'mp4' && showControls && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: overlayVariants,
                    initial: "hidden",
                    animate: "visible",
                    exit: "exit",
                    style: {
                        position: 'absolute',
                        bottom: 16,
                        left: 16
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                        onClick: handlePlayPause,
                        sx: {
                            backgroundColor: 'rgba(0,0,0,0.7)',
                            color: 'white',
                            '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.8)'
                            }
                        },
                        children: isPlaying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                            lineNumber: 222,
                            columnNumber: 28
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                            lineNumber: 222,
                            columnNumber: 40
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 212,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                    lineNumber: 201,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this);
};
_s(Video, "EG4wYCUBGOXPZ1s15EGTpyE/z5I=");
_c = Video;
const __TURBOPACK__default__export__ = Video;
var _c;
__turbopack_context__.k.register(_c, "Video");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html": (()=>Html),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Html = ({ element, className, style })=>{
    const { content, styles } = element;
    // Build styles
    const htmlStyles = {
        width: '100%',
        ...styles,
        ...style
    };
    // Animation variants
    const htmlVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-html ${className || ''}`,
        variants: htmlVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: htmlStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Html.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Html.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_c = Html;
const __TURBOPACK__default__export__ = Html;
var _c;
__turbopack_context__.k.register(_c, "Html");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Divider": (()=>Divider),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Divider/Divider.js [app-client] (ecmascript) <export default as Divider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Divider = ({ element, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const dividerStyles = {
        width: attributes.lineWidth || '100%',
        height: attributes.lineThickness || '1px',
        backgroundColor: attributes.lineColor || '#e0e0e0',
        border: 'none',
        margin: '2rem 0',
        ...styles,
        ...style
    };
    // Animation variants
    const dividerVariants = {
        hidden: {
            opacity: 0,
            scaleX: 0
        },
        visible: {
            opacity: 1,
            scaleX: 1,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-divider ${className || ''}`,
        sx: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            my: 2
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            variants: dividerVariants,
            initial: "hidden",
            whileInView: "visible",
            viewport: {
                once: true,
                amount: 0.1
            },
            style: {
                width: '100%'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                sx: {
                    ...dividerStyles,
                    '&::before, &::after': {
                        borderColor: attributes.lineColor || 'divider'
                    }
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
                lineNumber: 57,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
};
_c = Divider;
const __TURBOPACK__default__export__ = Divider;
var _c;
__turbopack_context__.k.register(_c, "Divider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Elements Export
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript) <export default as Row>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Row": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript) <export default as Column>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Column": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript) <export default as Text>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript) <export default as Heading>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heading": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript) <export default as PageBuilderImage>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderImage": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript) <export default as Button>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript) <export default as Banner>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Banner": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript) <export default as Video>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Video": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript) <export default as Html>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript) <export default as Divider>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Divider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Element Parser
__turbopack_context__.s({
    "ElementParser": (()=>ElementParser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
;
;
class ElementParser {
    config;
    constructor(config){
        this.config = config;
    }
    // Parse a DOM element into a PageBuilderElement
    parseElement(element) {
        try {
            const elementType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementType"])(element);
            if (!elementType) {
                return null;
            }
            // Check for custom parser
            if (this.config.customElementParsers?.[elementType]) {
                return this.config.customElementParsers[elementType](element);
            }
            // Use built-in parser
            return this.parseByType(element, elementType);
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error, element);
            return null;
        }
    }
    // Parse element by type
    parseByType(element, type) {
        const baseElement = this.createBaseElement(element, type);
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW:
                return this.parseRow(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN:
                return this.parseColumn(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT:
                return this.parseText(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING:
                return this.parseHeading(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE:
                return this.parseImage(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON:
                return this.parseButton(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER:
                return this.parseBanner(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO:
                return this.parseVideo(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML:
                return this.parseHtml(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER:
                return this.parseDivider(element, baseElement);
            default:
                return baseElement;
        }
    }
    // Create base element with common properties
    createBaseElement(element, type) {
        return {
            type,
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateElementId"])(),
            attributes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractAttributes"])(element),
            styles: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractStyles"])(element),
            rawHtml: element.outerHTML
        };
    }
    // Parse Row element
    parseRow(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
            attributes: {
                appearance: attributes.appearance || 'contained',
                enableParallax: attributes.enableParallax === 'true',
                parallaxSpeed: parseFloat(attributes.parallaxSpeed) || 0.5,
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                minHeight: attributes.minHeight,
                verticalAlignment: attributes.verticalAlignment || 'top'
            }
        };
    }
    // Parse Column element
    parseColumn(element, baseElement) {
        const attributes = baseElement.attributes;
        const styles = baseElement.styles;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
            attributes: {
                width: this.extractColumnWidth(element, styles),
                appearance: attributes.appearance || 'minimum-height',
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                minHeight: attributes.minHeight,
                verticalAlignment: attributes.verticalAlignment || 'top'
            }
        };
    }
    // Parse Text element
    parseText(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, true),
            attributes: {
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius
            }
        };
    }
    // Parse Heading element
    parseHeading(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, true),
            attributes: {
                headingType: this.extractHeadingType(element),
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius
            }
        };
    }
    // Parse Image element
    parseImage(element, baseElement) {
        const attributes = baseElement.attributes;
        const img = element.tagName === 'IMG' ? element : element.querySelector('img');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
            attributes: {
                src: img?.getAttribute('src') || attributes.src || '',
                alt: img?.getAttribute('alt') || attributes.alt || '',
                title: img?.getAttribute('title') || attributes.title,
                caption: this.extractImageCaption(element),
                link: this.extractImageLink(element),
                linkTarget: attributes.linkTarget || '_self',
                alignment: attributes.alignment || 'center',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius,
                lazyLoading: this.config.enableLazyLoading !== false
            }
        };
    }
    // Parse Button element
    parseButton(element, baseElement) {
        const attributes = baseElement.attributes;
        const link = element.tagName === 'A' ? element : element.querySelector('a');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, false),
            attributes: {
                buttonType: this.extractButtonType(element),
                link: link?.getAttribute('href') || attributes.link,
                linkTarget: link?.getAttribute('target') || attributes.linkTarget || '_self',
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius,
                backgroundColor: attributes.backgroundColor,
                textColor: attributes.textColor
            }
        };
    }
    // Parse Banner element
    parseBanner(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
            attributes: {
                appearance: attributes.appearance || 'poster',
                minHeight: attributes.minHeight || '300px',
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                showButton: attributes.showButton === 'true',
                buttonText: this.extractBannerButtonText(element),
                buttonType: attributes.buttonType || 'primary',
                buttonLink: this.extractBannerButtonLink(element),
                buttonTarget: attributes.buttonTarget || '_self',
                showOverlay: attributes.showOverlay === 'true',
                overlayColor: attributes.overlayColor,
                content: this.extractBannerContent(element),
                contentPlacement: attributes.contentPlacement || 'center'
            }
        };
    }
    // Parse Video element
    parseVideo(element, baseElement) {
        const attributes = baseElement.attributes;
        const video = element.querySelector('video');
        const iframe = element.querySelector('iframe');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
            attributes: {
                videoType: this.extractVideoType(element),
                videoUrl: video?.getAttribute('src') || iframe?.getAttribute('src') || attributes.videoUrl,
                videoId: attributes.videoId,
                maxWidth: attributes.maxWidth || '100%',
                autoplay: attributes.autoplay === 'true' || video?.hasAttribute('autoplay'),
                muted: attributes.muted === 'true' || video?.hasAttribute('muted'),
                loop: attributes.loop === 'true' || video?.hasAttribute('loop'),
                controls: attributes.controls === 'true' || video?.hasAttribute('controls'),
                lazyLoading: this.config.enableLazyLoading !== false,
                fallbackImage: this.extractVideoFallbackImage(element)
            }
        };
    }
    // Parse HTML element
    parseHtml(element, baseElement) {
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
            content: element.innerHTML
        };
    }
    // Parse Divider element
    parseDivider(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
            attributes: {
                lineColor: attributes.lineColor || '#e0e0e0',
                lineThickness: attributes.lineThickness || '1px',
                lineWidth: attributes.lineWidth || '100%'
            }
        };
    }
    // Helper methods
    parseBackgroundImage(backgroundImagesData) {
        if (!backgroundImagesData) return undefined;
        const images = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBackgroundImages"])(backgroundImagesData);
        return images[0]; // Use first image (desktop)
    }
    extractColumnWidth(element, styles) {
        // Try to get width from styles or data attributes
        if (styles.width) return styles.width;
        if (styles.flexBasis) return styles.flexBasis;
        // Check for Magento column classes
        const classList = Array.from(element.classList);
        for (const className of classList){
            if (className.includes('col-')) {
                const match = className.match(/col-(\d+)/);
                if (match) {
                    const cols = parseInt(match[1]);
                    return `${cols / 12 * 100}%`;
                }
            }
        }
        return '100%';
    }
    extractHeadingType(element) {
        const tagName = element.tagName.toLowerCase();
        if ([
            'h1',
            'h2',
            'h3',
            'h4',
            'h5',
            'h6'
        ].includes(tagName)) {
            return tagName;
        }
        return 'h2'; // Default
    }
    extractButtonType(element) {
        const classList = Array.from(element.classList);
        if (classList.includes('pagebuilder-button-secondary')) return 'secondary';
        if (classList.includes('pagebuilder-button-link')) return 'link';
        return 'primary';
    }
    extractImageCaption(element) {
        const caption = element.querySelector('figcaption');
        return caption?.textContent || undefined;
    }
    extractImageLink(element) {
        const link = element.querySelector('a');
        return link?.getAttribute('href') || undefined;
    }
    extractBannerContent(element) {
        const content = element.querySelector('.pagebuilder-banner-content');
        return content?.innerHTML || undefined;
    }
    extractBannerButtonText(element) {
        const button = element.querySelector('.pagebuilder-banner-button');
        return button?.textContent || undefined;
    }
    extractBannerButtonLink(element) {
        const button = element.querySelector('.pagebuilder-banner-button a');
        return button?.getAttribute('href') || undefined;
    }
    extractVideoType(element) {
        const iframe = element.querySelector('iframe');
        if (iframe?.src.includes('youtube.com')) return 'youtube';
        if (iframe?.src.includes('vimeo.com')) return 'vimeo';
        return 'mp4';
    }
    extractVideoFallbackImage(element) {
        const img = element.querySelector('img');
        return img?.getAttribute('src') || undefined;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Main Page Builder Parser
__turbopack_context__.s({
    "PageBuilderParser": (()=>PageBuilderParser),
    "defaultParser": (()=>defaultParser),
    "optimizePageBuilderContent": (()=>optimizePageBuilderContent),
    "parsePageBuilderContent": (()=>parsePageBuilderContent),
    "validatePageBuilderContent": (()=>validatePageBuilderContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)");
;
;
class PageBuilderParser {
    config;
    elementParser;
    constructor(config = {}){
        this.config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])(config);
        this.elementParser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementParser"](this.config);
    }
    // Parse HTML string into PageBuilderContent
    parse(html) {
        try {
            // Create a temporary DOM to parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            // Extract Page Builder elements
            const elements = this.parseElements(doc.body);
            return {
                elements,
                rawHtml: html,
                version: this.extractPageBuilderVersion(html)
            };
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return {
                elements: [],
                rawHtml: html
            };
        }
    }
    // Parse elements from a DOM node
    parseElements(container) {
        const elements = [];
        // Get direct children that are Page Builder elements
        const children = Array.from(container.children);
        for (const child of children){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPageBuilderElement"])(child)) {
                const element = this.elementParser.parseElement(child);
                if (element) {
                    // Parse children recursively
                    element.children = this.parseElements(child);
                    elements.push(element);
                }
            } else {
                // Check if this element contains Page Builder elements
                const nestedElements = this.parseElements(child);
                elements.push(...nestedElements);
            }
        }
        return elements;
    }
    // Extract Page Builder version from HTML
    extractPageBuilderVersion(html) {
        const versionMatch = html.match(/data-pb-version="([^"]+)"/);
        return versionMatch ? versionMatch[1] : undefined;
    }
    // Parse HTML from server-side (Node.js environment)
    static parseServerSide(html, config = {}) {
        // For server-side parsing, we need to use a different approach
        // since DOMParser is not available in Node.js
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Client-side parsing
        const parser = new PageBuilderParser(config);
        return parser.parse(html);
    }
    // Fallback regex-based parsing for server-side
    static parseWithRegex(html, config = {}) {
        const elements = [];
        try {
            // Extract Page Builder elements using regex patterns
            const rowPattern = /<div[^>]*data-content-type="row"[^>]*>(.*?)<\/div>/gs;
            const matches = html.matchAll(rowPattern);
            for (const match of matches){
                const element = PageBuilderParser.parseElementWithRegex(match[0]);
                if (element) {
                    elements.push(element);
                }
            }
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
        }
        return {
            elements,
            rawHtml: html
        };
    }
    // Parse single element with regex (server-side fallback)
    static parseElementWithRegex(elementHtml) {
        try {
            // Extract data-content-type
            const contentTypeMatch = elementHtml.match(/data-content-type="([^"]+)"/);
            if (!contentTypeMatch) return null;
            const contentType = contentTypeMatch[1];
            // Basic element structure
            const element = {
                type: contentType,
                id: `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                attributes: PageBuilderParser.extractAttributesWithRegex(elementHtml),
                styles: {},
                rawHtml: elementHtml
            };
            // Extract content for text/heading elements
            if (contentType === 'text' || contentType === 'heading') {
                const contentMatch = elementHtml.match(/>([^<]+)</);
                element.content = contentMatch ? contentMatch[1].trim() : '';
            }
            return element;
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return null;
        }
    }
    // Extract attributes with regex (server-side fallback)
    static extractAttributesWithRegex(html) {
        const attributes = {};
        // Extract data attributes
        const dataAttrPattern = /data-([^=]+)="([^"]+)"/g;
        let match;
        while((match = dataAttrPattern.exec(html)) !== null){
            const key = match[1].replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
            attributes[key] = match[2];
        }
        return attributes;
    }
    // Validate parsed content
    validateContent(content) {
        try {
            // Check if content has valid structure
            if (!content.elements || !Array.isArray(content.elements)) {
                return false;
            }
            // Validate each element
            for (const element of content.elements){
                if (!this.validateElement(element)) {
                    return false;
                }
            }
            return true;
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return false;
        }
    }
    // Validate single element
    validateElement(element) {
        // Check required properties
        if (!element.type || !element.id) {
            return false;
        }
        // Validate children recursively
        if (element.children) {
            for (const child of element.children){
                if (!this.validateElement(child)) {
                    return false;
                }
            }
        }
        return true;
    }
    // Clean and optimize parsed content
    optimizeContent(content) {
        return {
            ...content,
            elements: this.optimizeElements(content.elements)
        };
    }
    // Optimize elements (remove empty elements, merge similar elements, etc.)
    optimizeElements(elements) {
        return elements.filter((element)=>this.shouldKeepElement(element)).map((element)=>({
                ...element,
                children: element.children ? this.optimizeElements(element.children) : undefined
            }));
    }
    // Determine if element should be kept during optimization
    shouldKeepElement(element) {
        // Remove empty text elements
        if (element.type === 'text' && (!element.content || element.content.trim() === '')) {
            return false;
        }
        // Remove empty HTML elements
        if (element.type === 'html' && (!element.content || element.content.trim() === '')) {
            return false;
        }
        // Keep all other elements
        return true;
    }
    // Get configuration
    getConfig() {
        return this.config;
    }
    // Update configuration
    updateConfig(newConfig) {
        this.config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])({
            ...this.config,
            ...newConfig
        });
        this.elementParser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementParser"](this.config);
    }
}
const defaultParser = new PageBuilderParser();
const parsePageBuilderContent = (html, config)=>{
    return PageBuilderParser.parseServerSide(html, config);
};
const validatePageBuilderContent = (content)=>{
    return defaultParser.validateContent(content);
};
const optimizePageBuilderContent = (content)=>{
    return defaultParser.optimizeContent(content);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderRenderer": (()=>PageBuilderRenderer),
    "PageBuilderRendererSSR": (()=>PageBuilderRendererSSR),
    "RawHtmlRenderer": (()=>RawHtmlRenderer),
    "default": (()=>__TURBOPACK__default__export__),
    "usePageBuilderConfig": (()=>usePageBuilderConfig),
    "usePageBuilderContext": (()=>usePageBuilderContext),
    "usePageBuilderDeviceType": (()=>usePageBuilderDeviceType),
    "usePageBuilderEditing": (()=>usePageBuilderEditing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
// Import Page Builder components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Column$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript) <export default as Column>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Text$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript) <export default as Text>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heading$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript) <export default as Heading>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PageBuilderImage$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript) <export default as PageBuilderImage>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banner$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript) <export default as Banner>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Html$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript) <export default as Html>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript) <export default as Divider>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
// Page Builder Context
const PageBuilderContextProvider = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const usePageBuilderContext = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(PageBuilderContextProvider);
    if (!context) {
        throw new Error('usePageBuilderContext must be used within a PageBuilderRenderer');
    }
    return context;
};
_s(usePageBuilderContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
// Component mapping
const COMPONENT_MAP = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Column$3e$__["Column"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Text$3e$__["Text"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heading$3e$__["Heading"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PageBuilderImage$3e$__["PageBuilderImage"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banner$3e$__["Banner"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Html$3e$__["Html"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"]
};
// Individual element renderer
const ElementRenderer = ({ element, config })=>{
    // Get component from mapping or custom overrides
    const Component = config.componentOverrides?.[element.type] || COMPONENT_MAP[element.type];
    if (!Component) {
        // Fallback for unknown element types
        console.warn(`Unknown Page Builder element type: ${element.type}`);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                p: 2,
                border: '1px dashed #ccc',
                borderRadius: 1,
                backgroundColor: '#f5f5f5',
                textAlign: 'center',
                color: 'text.secondary'
            },
            children: [
                "Unknown element: ",
                element.type
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this);
    }
    // Render children if they exist
    const children = element.children?.map((child, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ElementRenderer, {
            element: child,
            config: config
        }, child.id || index, false, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 97,
            columnNumber: 5
        }, this));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
        element: element,
        className: `pagebuilder-element pagebuilder-${element.type}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
};
_c = ElementRenderer;
const PageBuilderRenderer = ({ content, config = {}, className, style, isEditing = false, deviceType = 'desktop' })=>{
    // Merge configuration
    const mergedConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])(config);
    // Parse content if it's a string
    let pageBuilderContent;
    if (typeof content === 'string') {
        // Import parser dynamically to avoid SSR issues
        const { parsePageBuilderContent } = __turbopack_context__.r("[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)");
        pageBuilderContent = parsePageBuilderContent(content, mergedConfig);
    } else {
        pageBuilderContent = content;
    }
    // Create context value
    const contextValue = {
        config: mergedConfig,
        isEditing,
        deviceType
    };
    // If no elements, return empty
    if (!pageBuilderContent.elements || pageBuilderContent.elements.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderContextProvider.Provider, {
        value: contextValue,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            className: `pagebuilder-content ${className || ''}`,
            sx: {
                width: '100%',
                ...style
            },
            children: pageBuilderContent.elements.map((element, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ElementRenderer, {
                    element: element,
                    config: mergedConfig
                }, element.id || index, false, {
                    fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
                    lineNumber: 155,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 147,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
_c1 = PageBuilderRenderer;
const PageBuilderRendererSSR = (props)=>{
    // For SSR, we need to handle the parsing differently
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Client-side: use normal renderer
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderRenderer, {
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 183,
        columnNumber: 10
    }, this);
};
_c2 = PageBuilderRendererSSR;
const RawHtmlRenderer = ({ html, className })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-raw-html ${className || ''}`,
        dangerouslySetInnerHTML: {
            __html: html
        },
        sx: {
            '& .pagebuilder-row': {
                display: 'flex',
                flexDirection: 'column',
                width: '100%'
            },
            '& .pagebuilder-column-group': {
                display: 'flex',
                flexWrap: 'wrap',
                width: '100%'
            },
            '& .pagebuilder-column': {
                flex: '1 1 auto',
                minWidth: 0
            }
        }
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 192,
        columnNumber: 5
    }, this);
};
_c3 = RawHtmlRenderer;
const usePageBuilderConfig = ()=>{
    _s1();
    const context = usePageBuilderContext();
    return context.config;
};
_s1(usePageBuilderConfig, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const usePageBuilderEditing = ()=>{
    _s2();
    const context = usePageBuilderContext();
    return context.isEditing || false;
};
_s2(usePageBuilderEditing, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const usePageBuilderDeviceType = ()=>{
    _s3();
    const context = usePageBuilderContext();
    return context.deviceType || 'desktop';
};
_s3(usePageBuilderDeviceType, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const __TURBOPACK__default__export__ = PageBuilderRenderer;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "ElementRenderer");
__turbopack_context__.k.register(_c1, "PageBuilderRenderer");
__turbopack_context__.k.register(_c2, "PageBuilderRendererSSR");
__turbopack_context__.k.register(_c3, "RawHtmlRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/parser/StyleParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Style Parser
__turbopack_context__.s({
    "StyleParser": (()=>StyleParser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)");
;
class StyleParser {
    // Parse inline styles from style attribute
    static parseInlineStyles(styleString) {
        const styles = {};
        if (!styleString) return styles;
        // Split by semicolon and process each declaration
        const declarations = styleString.split(';').filter((decl)=>decl.trim());
        for (const declaration of declarations){
            const [property, value] = declaration.split(':').map((s)=>s.trim());
            if (property && value) {
                const jsProperty = this.cssPropertyToJs(property);
                styles[jsProperty] = this.parseValue(value);
            }
        }
        return styles;
    }
    // Parse CSS from data-pb-style attribute (Magento specific)
    static parsePbStyles(pbStyleString) {
        const styles = {};
        if (!pbStyleString) return styles;
        try {
            // Magento stores styles as encoded CSS
            const decodedStyles = decodeURIComponent(pbStyleString);
            return this.parseInlineStyles(decodedStyles);
        } catch (error) {
            console.warn('Failed to parse pb-style:', error);
            return styles;
        }
    }
    // Parse background images from Magento's data-background-images
    static parseBackgroundImages(backgroundImagesData) {
        try {
            const data = JSON.parse(backgroundImagesData);
            return {
                desktop: data.desktop_image,
                mobile: data.mobile_image,
                tablet: data.tablet_image
            };
        } catch (error) {
            return {};
        }
    }
    // Convert CSS property name to JavaScript property name
    static cssPropertyToJs(cssProperty) {
        // Check if we have a mapping
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"][cssProperty]) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"][cssProperty];
        }
        // Convert kebab-case to camelCase
        return cssProperty.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
    }
    // Parse CSS value and convert to appropriate type
    static parseValue(value) {
        const trimmedValue = value.trim();
        // Handle numeric values
        if (/^-?\d+(\.\d+)?(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax)?$/.test(trimmedValue)) {
            return trimmedValue;
        }
        // Handle color values
        if (this.isColor(trimmedValue)) {
            return trimmedValue;
        }
        // Handle URLs
        if (trimmedValue.startsWith('url(')) {
            return trimmedValue;
        }
        // Handle keywords and other values
        return trimmedValue;
    }
    // Check if value is a color
    static isColor(value) {
        // Hex colors
        if (/^#([0-9A-F]{3}){1,2}$/i.test(value)) {
            return true;
        }
        // RGB/RGBA
        if (/^rgba?\(/.test(value)) {
            return true;
        }
        // HSL/HSLA
        if (/^hsla?\(/.test(value)) {
            return true;
        }
        // Named colors (basic check)
        const namedColors = [
            'transparent',
            'black',
            'white',
            'red',
            'green',
            'blue',
            'yellow',
            'orange',
            'purple',
            'pink',
            'gray',
            'grey'
        ];
        return namedColors.includes(value.toLowerCase());
    }
    // Generate responsive styles based on breakpoints
    static generateResponsiveStyles(styles, breakpoint = 'desktop') {
        const responsiveStyles = {
            ...styles
        };
        // Apply breakpoint-specific adjustments
        switch(breakpoint){
            case 'mobile':
                // Adjust font sizes for mobile
                if (responsiveStyles.fontSize) {
                    responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.8);
                }
                // Adjust padding/margins for mobile
                if (responsiveStyles.padding) {
                    responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.7);
                }
                if (responsiveStyles.margin) {
                    responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.7);
                }
                break;
            case 'tablet':
                // Adjust font sizes for tablet
                if (responsiveStyles.fontSize) {
                    responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.9);
                }
                // Adjust padding/margins for tablet
                if (responsiveStyles.padding) {
                    responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.85);
                }
                if (responsiveStyles.margin) {
                    responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.85);
                }
                break;
        }
        return responsiveStyles;
    }
    // Scale font size by factor
    static scaleFontSize(fontSize, factor) {
        const match = fontSize.match(/^(\d+(?:\.\d+)?)(px|em|rem|%)$/);
        if (match) {
            const value = parseFloat(match[1]);
            const unit = match[2];
            return `${(value * factor).toFixed(2)}${unit}`;
        }
        return fontSize;
    }
    // Scale spacing (padding/margin) by factor
    static scaleSpacing(spacing, factor) {
        // Handle multiple values (e.g., "10px 20px")
        const values = spacing.split(' ');
        const scaledValues = values.map((value)=>{
            const match = value.match(/^(\d+(?:\.\d+)?)(px|em|rem|%)$/);
            if (match) {
                const num = parseFloat(match[1]);
                const unit = match[2];
                return `${(num * factor).toFixed(2)}${unit}`;
            }
            return value;
        });
        return scaledValues.join(' ');
    }
    // Convert Magento styles to Material-UI sx prop format
    static toMuiSx(styles) {
        const muiStyles = {};
        Object.entries(styles).forEach(([key, value])=>{
            // Convert specific properties for MUI
            switch(key){
                case 'backgroundColor':
                    muiStyles.bgcolor = value;
                    break;
                case 'textAlign':
                    muiStyles.textAlign = value;
                    break;
                case 'fontSize':
                    muiStyles.fontSize = value;
                    break;
                case 'fontWeight':
                    muiStyles.fontWeight = value;
                    break;
                case 'color':
                    muiStyles.color = value;
                    break;
                case 'padding':
                    muiStyles.p = this.convertSpacingToMui(value);
                    break;
                case 'margin':
                    muiStyles.m = this.convertSpacingToMui(value);
                    break;
                case 'paddingTop':
                    muiStyles.pt = this.convertSpacingToMui(value);
                    break;
                case 'paddingRight':
                    muiStyles.pr = this.convertSpacingToMui(value);
                    break;
                case 'paddingBottom':
                    muiStyles.pb = this.convertSpacingToMui(value);
                    break;
                case 'paddingLeft':
                    muiStyles.pl = this.convertSpacingToMui(value);
                    break;
                case 'marginTop':
                    muiStyles.mt = this.convertSpacingToMui(value);
                    break;
                case 'marginRight':
                    muiStyles.mr = this.convertSpacingToMui(value);
                    break;
                case 'marginBottom':
                    muiStyles.mb = this.convertSpacingToMui(value);
                    break;
                case 'marginLeft':
                    muiStyles.ml = this.convertSpacingToMui(value);
                    break;
                case 'borderRadius':
                    muiStyles.borderRadius = value;
                    break;
                case 'border':
                    muiStyles.border = value;
                    break;
                case 'borderColor':
                    muiStyles.borderColor = value;
                    break;
                case 'borderWidth':
                    muiStyles.borderWidth = value;
                    break;
                default:
                    // Keep other properties as-is
                    muiStyles[key] = value;
            }
        });
        return muiStyles;
    }
    // Convert spacing value to MUI spacing units
    static convertSpacingToMui(value) {
        // If it's a pixel value, convert to MUI spacing units (8px = 1 unit)
        const pxMatch = value.match(/^(\d+)px$/);
        if (pxMatch) {
            const pixels = parseInt(pxMatch[1]);
            return Math.round(pixels / 8);
        }
        // Return as-is for other units
        return value;
    }
    // Clean styles by removing empty or invalid values
    static cleanStyles(styles) {
        const cleanedStyles = {};
        Object.entries(styles).forEach(([key, value])=>{
            if (value !== null && value !== undefined && value !== '') {
                cleanedStyles[key] = value;
            }
        });
        return cleanedStyles;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Module Exports
// Main renderer components
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)");
// Parser classes and functions
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$PageBuilderParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$StyleParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/StyleParser.ts [app-client] (ecmascript)");
// Individual element components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>");
// Constants and utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$PageBuilderParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$StyleParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/StyleParser.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/magento/client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento GraphQL Client with native fetch
__turbopack_context__.s({
    "MagentoGraphQLError": (()=>MagentoGraphQLError),
    "magentoClient": (()=>magentoClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
class MagentoGraphQLError extends Error {
    errors;
    response;
    constructor(message, errors = [], response){
        super(message), this.errors = errors, this.response = response;
        this.name = 'MagentoGraphQLError';
    }
}
class MagentoGraphQLClient {
    baseUrl;
    headers;
    cache = new Map();
    cacheTimeout = 5 * 60 * 1000;
    constructor(){
        this.baseUrl = ("TURBOPACK compile-time value", "http://magento2.local/graphql/") || 'http://magento2.local/graphql/';
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        // Add admin token if available (for server-side requests)
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    // Set customer token for authenticated requests
    setCustomerToken(token) {
        this.headers['Authorization'] = `Bearer ${token}`;
    }
    // Remove customer token
    removeCustomerToken() {
        delete this.headers['Authorization'];
    }
    // Generate cache key
    getCacheKey(query, variables) {
        return `${query}:${JSON.stringify(variables || {})}`;
    }
    // Check if cache is valid
    isCacheValid(timestamp) {
        return Date.now() - timestamp < this.cacheTimeout;
    }
    // Get from cache
    getFromCache(cacheKey) {
        const cached = this.cache.get(cacheKey);
        if (cached && this.isCacheValid(cached.timestamp)) {
            return cached.data;
        }
        return null;
    }
    // Set cache
    setCache(cacheKey, data) {
        this.cache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
    }
    // Main fetch method
    async fetch(query, variables, options = {}) {
        const { cache = true, headers: customHeaders = {} } = options;
        // Check cache first (only for queries, not mutations)
        const isQuery = query.trim().startsWith('query');
        const cacheKey = this.getCacheKey(query, variables);
        if (cache && isQuery) {
            const cachedData = this.getFromCache(cacheKey);
            if (cachedData) {
                return cachedData;
            }
        }
        const requestBody = {
            query,
            variables
        };
        const requestHeaders = {
            ...this.headers,
            ...customHeaders
        };
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: requestHeaders,
                body: JSON.stringify(requestBody),
                // Add Next.js specific options
                ...options.revalidate && {
                    next: {
                        revalidate: options.revalidate
                    }
                }
            });
            if (!response.ok) {
                throw new MagentoGraphQLError(`HTTP Error: ${response.status} ${response.statusText}`, [], response);
            }
            const result = await response.json();
            // Handle GraphQL errors
            if (result.errors && result.errors.length > 0) {
                const errorMessage = result.errors.map((error)=>error.message).join(', ');
                throw new MagentoGraphQLError(`GraphQL Error: ${errorMessage}`, result.errors, response);
            }
            // Cache successful queries
            if (cache && isQuery && result.data) {
                this.setCache(cacheKey, result.data);
            }
            return result.data;
        } catch (error) {
            if (error instanceof MagentoGraphQLError) {
                throw error;
            }
            // Handle network errors
            throw new MagentoGraphQLError(`Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`, []);
        }
    }
    // Clear cache
    clearCache() {
        this.cache.clear();
    }
    // Clear specific cache entry
    clearCacheEntry(query, variables) {
        const cacheKey = this.getCacheKey(query, variables);
        this.cache.delete(cacheKey);
    }
}
// Create singleton instance
const magentoClient = new MagentoGraphQLClient();
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/magento/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Main Magento GraphQL API functions
__turbopack_context__.s({
    "cacheManager": (()=>cacheManager),
    "customerAuth": (()=>customerAuth),
    "magentoGraphQLFetch": (()=>magentoGraphQLFetch),
    "magentoGraphQLMutate": (()=>magentoGraphQLMutate),
    "magentoGraphQLQuery": (()=>magentoGraphQLQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/client.ts [app-client] (ecmascript)");
;
;
async function magentoGraphQLFetch(query, variables, options) {
    try {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].fetch(query, variables, options);
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MagentoGraphQLError"]) {
            // Log error for debugging (only in development)
            if ("TURBOPACK compile-time truthy", 1) {
                console.error('Magento GraphQL Error:', {
                    message: error.message,
                    errors: error.errors,
                    query: query.substring(0, 100) + '...',
                    variables
                });
            }
            throw error;
        }
        throw error;
    }
}
async function magentoGraphQLMutate(mutation, variables, options) {
    return magentoGraphQLFetch(mutation, variables, {
        ...options,
        cache: false
    });
}
async function magentoGraphQLQuery(query, variables, revalidate = 3600 // 1 hour default
) {
    return magentoGraphQLFetch(query, variables, {
        cache: true,
        revalidate
    });
}
const customerAuth = {
    setToken: (token)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].setCustomerToken(token),
    removeToken: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].removeCustomerToken(),
    clearCache: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].clearCache()
};
const cacheManager = {
    clear: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].clearCache(),
    clearEntry: (query, variables)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["magentoClient"].clearCacheEntry(query, variables)
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/magento/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/magento/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/magento/queries/cms.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// CMS GraphQL Queries for Magento 2 (including Page Builder content)
// Fragment for CMS page
__turbopack_context__.s({
    "CMS_BLOCK_FRAGMENT": (()=>CMS_BLOCK_FRAGMENT),
    "CMS_PAGE_FRAGMENT": (()=>CMS_PAGE_FRAGMENT),
    "GET_ALL_CMS_PAGES": (()=>GET_ALL_CMS_PAGES),
    "GET_CMS_BLOCK": (()=>GET_CMS_BLOCK),
    "GET_CMS_BLOCKS": (()=>GET_CMS_BLOCKS),
    "GET_CMS_PAGE": (()=>GET_CMS_PAGE),
    "GET_CMS_PAGES": (()=>GET_CMS_PAGES),
    "GET_CMS_PAGE_BY_URL_KEY": (()=>GET_CMS_PAGE_BY_URL_KEY),
    "SEARCH_CMS_PAGES": (()=>SEARCH_CMS_PAGES)
});
const CMS_PAGE_FRAGMENT = `
  fragment CmsPageFragment on CmsPage {
    identifier
    url_key
    title
    content
    content_heading
    page_layout
    meta_title
    meta_description
    meta_keywords
    created_at
    updated_at
  }
`;
const CMS_BLOCK_FRAGMENT = `
  fragment CmsBlockFragment on CmsBlock {
    identifier
    title
    content
    created_at
    updated_at
  }
`;
const GET_CMS_PAGE = `
  query GetCmsPage($identifier: String!) {
    cmsPage(identifier: $identifier) {
      ...CmsPageFragment
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;
const GET_CMS_PAGE_BY_URL_KEY = `
  query GetCmsPageByUrlKey($urlKey: String!) {
    cmsPage(url_key: $urlKey) {
      ...CmsPageFragment
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;
const GET_CMS_PAGES = `
  query GetCmsPages($identifiers: [String!]) {
    cmsPages(identifiers: $identifiers) {
      items {
        ...CmsPageFragment
      }
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;
const GET_CMS_BLOCK = `
  query GetCmsBlock($identifier: String!) {
    cmsBlocks(identifiers: [$identifier]) {
      items {
        ...CmsBlockFragment
      }
    }
  }
  ${CMS_BLOCK_FRAGMENT}
`;
const GET_CMS_BLOCKS = `
  query GetCmsBlocks($identifiers: [String!]!) {
    cmsBlocks(identifiers: $identifiers) {
      items {
        ...CmsBlockFragment
      }
    }
  }
  ${CMS_BLOCK_FRAGMENT}
`;
const GET_ALL_CMS_PAGES = `
  query GetAllCmsPages {
    cmsPages {
      items {
        identifier
        url_key
        title
        meta_title
        meta_description
        created_at
        updated_at
      }
    }
  }
`;
const SEARCH_CMS_PAGES = `
  query SearchCmsPages($search: String!) {
    cmsPages(search: $search) {
      items {
        ...CmsPageFragment
      }
    }
  }
  ${CMS_PAGE_FRAGMENT}
`;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/magento/api/cms.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// CMS API functions using Magento GraphQL
__turbopack_context__.s({
    "cleanCmsContent": (()=>cleanCmsContent),
    "extractPageBuilderContent": (()=>extractPageBuilderContent),
    "getAllCmsPages": (()=>getAllCmsPages),
    "getCmsBlock": (()=>getCmsBlock),
    "getCmsBlockWithPageBuilder": (()=>getCmsBlockWithPageBuilder),
    "getCmsBlocks": (()=>getCmsBlocks),
    "getCmsPage": (()=>getCmsPage),
    "getCmsPageBreadcrumbs": (()=>getCmsPageBreadcrumbs),
    "getCmsPageByUrlKey": (()=>getCmsPageByUrlKey),
    "getCmsPageUrl": (()=>getCmsPageUrl),
    "getCmsPageWithPageBuilder": (()=>getCmsPageWithPageBuilder),
    "getCmsPages": (()=>getCmsPages),
    "hasPageBuilderContent": (()=>hasPageBuilderContent),
    "searchCmsPages": (()=>searchCmsPages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/magento/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/magento/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/queries/cms.ts [app-client] (ecmascript)");
;
;
async function getCmsPage(identifier, revalidate = 3600 // 1 hour
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_CMS_PAGE"], {
            identifier
        }, revalidate);
        return response.cmsPage;
    } catch (error) {
        console.error('Error fetching CMS page:', error);
        return null;
    }
}
async function getCmsPageByUrlKey(urlKey, revalidate = 3600 // 1 hour
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_CMS_PAGE_BY_URL_KEY"], {
            urlKey
        }, revalidate);
        return response.cmsPage;
    } catch (error) {
        console.error('Error fetching CMS page by URL key:', error);
        return null;
    }
}
async function getCmsPages(identifiers, revalidate = 3600 // 1 hour
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_CMS_PAGES"], {
            identifiers
        }, revalidate);
        return response.cmsPages.items;
    } catch (error) {
        console.error('Error fetching CMS pages:', error);
        return [];
    }
}
async function getCmsBlock(identifier, revalidate = 7200 // 2 hours
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_CMS_BLOCK"], {
            identifier
        }, revalidate);
        return response.cmsBlocks.items[0] || null;
    } catch (error) {
        console.error('Error fetching CMS block:', error);
        return null;
    }
}
async function getCmsBlocks(identifiers, revalidate = 7200 // 2 hours
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_CMS_BLOCKS"], {
            identifiers
        }, revalidate);
        return response.cmsBlocks.items;
    } catch (error) {
        console.error('Error fetching CMS blocks:', error);
        return [];
    }
}
async function getAllCmsPages(revalidate = 86400 // 24 hours
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GET_ALL_CMS_PAGES"], {}, revalidate);
        return response.cmsPages.items;
    } catch (error) {
        console.error('Error fetching all CMS pages:', error);
        return [];
    }
}
async function searchCmsPages(search, revalidate = 1800 // 30 minutes
) {
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["magentoGraphQLQuery"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$queries$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SEARCH_CMS_PAGES"], {
            search
        }, revalidate);
        return response.cmsPages.items;
    } catch (error) {
        console.error('Error searching CMS pages:', error);
        return [];
    }
}
function hasPageBuilderContent(content) {
    return content.includes('data-content-type') || content.includes('pagebuilder-') || content.includes('data-pb-style');
}
function extractPageBuilderContent(content) {
    // Remove any wrapper divs that might not be part of Page Builder
    const cleanContent = content.trim();
    // If it starts with Page Builder content, return as-is
    if (cleanContent.includes('data-content-type')) {
        return cleanContent;
    }
    // Try to find Page Builder content within the HTML
    const pbMatch = cleanContent.match(/<div[^>]*data-content-type[^>]*>.*<\/div>/s);
    if (pbMatch) {
        return pbMatch[0];
    }
    return cleanContent;
}
async function getCmsPageWithPageBuilder(identifier, revalidate = 3600) {
    const page = await getCmsPage(identifier, revalidate);
    if (!page) return null;
    const hasPageBuilder = hasPageBuilderContent(page.content);
    const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(page.content) : undefined;
    return {
        ...page,
        hasPageBuilder,
        pageBuilderContent
    };
}
async function getCmsBlockWithPageBuilder(identifier, revalidate = 7200) {
    const block = await getCmsBlock(identifier, revalidate);
    if (!block) return null;
    const hasPageBuilder = hasPageBuilderContent(block.content);
    const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(block.content) : undefined;
    return {
        ...block,
        hasPageBuilder,
        pageBuilderContent
    };
}
function cleanCmsContent(content) {
    return content.replace(/{{[^}]+}}/g, '') // Remove Magento directives
    .replace(/\{\{[^}]+\}\}/g, '') // Remove any remaining directives
    .trim();
}
function getCmsPageUrl(page) {
    return `/${page.url_key}`;
}
function getCmsPageBreadcrumbs(page) {
    return [
        {
            name: 'Home',
            url: '/'
        },
        {
            name: page.title,
            url: getCmsPageUrl(page)
        }
    ];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/cms/CmsPageRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CmsPageRenderer": (()=>CmsPageRenderer),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Container/Container.js [app-client] (ecmascript) <export default as Container>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Breadcrumbs$2f$Breadcrumbs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumbs$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Breadcrumbs/Breadcrumbs.js [app-client] (ecmascript) <export default as Breadcrumbs>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Link$2f$Link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Link$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Link/Link.js [app-client] (ecmascript) <export default as Link>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/api/cms.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const CmsPageRenderer = ({ page, showBreadcrumbs = true, showTitle = true, showContentHeading = true, className, style })=>{
    const hasPageBuilder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasPageBuilderContent"])(page.content);
    const pageBuilderContent = hasPageBuilder ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractPageBuilderContent"])(page.content) : null;
    // Animation variants
    const pageVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    const titleVariants = {
        hidden: {
            opacity: 0,
            y: 30
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                delay: 0.2,
                ease: 'easeOut'
            }
        }
    };
    const contentVariants = {
        hidden: {
            opacity: 0,
            y: 40
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                delay: 0.4,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `cms-page ${className || ''}`,
        style: style,
        variants: pageVariants,
        initial: "hidden",
        animate: "visible",
        children: [
            showBreadcrumbs && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                maxWidth: "lg",
                sx: {
                    py: 2
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Breadcrumbs$2f$Breadcrumbs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Breadcrumbs$3e$__["Breadcrumbs"], {
                    "aria-label": "breadcrumb",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Link$2f$Link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Link$3e$__["Link"], {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
                            href: "/",
                            color: "inherit",
                            children: "Home"
                        }, void 0, false, {
                            fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                            lineNumber: 81,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                            color: "text.primary",
                            children: page.title
                        }, void 0, false, {
                            fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                            lineNumber: 84,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                    lineNumber: 80,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                lineNumber: 79,
                columnNumber: 9
            }, this),
            showTitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                maxWidth: "lg",
                sx: {
                    py: 3
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: titleVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                        variant: "h1",
                        component: "h1",
                        sx: {
                            fontSize: {
                                xs: '2rem',
                                md: '2.5rem',
                                lg: '3rem'
                            },
                            fontWeight: 700,
                            mb: 2,
                            textAlign: 'center',
                            background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',
                            backgroundClip: 'text',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent'
                        },
                        children: page.title
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 93,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                    lineNumber: 92,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                lineNumber: 91,
                columnNumber: 9
            }, this),
            showContentHeading && page.content_heading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                maxWidth: "lg",
                sx: {
                    pb: 2
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: titleVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                        variant: "h2",
                        component: "h2",
                        sx: {
                            fontSize: {
                                xs: '1.5rem',
                                md: '2rem'
                            },
                            fontWeight: 600,
                            mb: 3,
                            textAlign: 'center',
                            color: 'text.secondary'
                        },
                        children: page.content_heading
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 117,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                    lineNumber: 116,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                lineNumber: 115,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: contentVariants,
                children: hasPageBuilder && pageBuilderContent ? // Render with Page Builder
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderRenderer"], {
                    content: pageBuilderContent,
                    config: {
                        enableLazyLoading: true,
                        imageOptimization: true
                    },
                    className: "cms-page-content"
                }, void 0, false, {
                    fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                    lineNumber: 138,
                    columnNumber: 11
                }, this) : // Render as raw HTML
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"], {
                    maxWidth: "lg",
                    sx: {
                        py: 3
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RawHtmlRenderer"], {
                        html: page.content,
                        className: "cms-page-content"
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 149,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                    lineNumber: 148,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                lineNumber: 135,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    display: 'none'
                },
                children: [
                    page.meta_title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "title",
                        content: page.meta_title
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 159,
                        columnNumber: 29
                    }, this),
                    page.meta_description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "description",
                        content: page.meta_description
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 160,
                        columnNumber: 35
                    }, this),
                    page.meta_keywords && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "keywords",
                        content: page.meta_keywords
                    }, void 0, false, {
                        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                        lineNumber: 161,
                        columnNumber: 32
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/cms/CmsPageRenderer.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
};
_c = CmsPageRenderer;
const __TURBOPACK__default__export__ = CmsPageRenderer;
var _c;
__turbopack_context__.k.register(_c, "CmsPageRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/cms/CmsBlockRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CmsBlockRenderer": (()=>CmsBlockRenderer),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/api/cms.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
const CmsBlockRenderer = ({ block, className, style, animate = true })=>{
    const hasPageBuilder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasPageBuilderContent"])(block.content);
    const pageBuilderContent = hasPageBuilder ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractPageBuilderContent"])(block.content) : null;
    // Animation variants
    const blockVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    const content = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `cms-block cms-block-${block.identifier} ${className || ''}`,
        style: style,
        children: hasPageBuilder && pageBuilderContent ? // Render with Page Builder
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderRenderer"], {
            content: pageBuilderContent,
            config: {
                enableLazyLoading: true,
                imageOptimization: true
            },
            className: "cms-block-content"
        }, void 0, false, {
            fileName: "[project]/src/components/cms/CmsBlockRenderer.tsx",
            lineNumber: 45,
            columnNumber: 9
        }, this) : // Render as raw HTML
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$PageBuilderRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RawHtmlRenderer"], {
            html: block.content,
            className: "cms-block-content"
        }, void 0, false, {
            fileName: "[project]/src/components/cms/CmsBlockRenderer.tsx",
            lineNumber: 55,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cms/CmsBlockRenderer.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
    if (animate) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            variants: blockVariants,
            initial: "hidden",
            whileInView: "visible",
            viewport: {
                once: true,
                amount: 0.1
            },
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/cms/CmsBlockRenderer.tsx",
            lineNumber: 65,
            columnNumber: 7
        }, this);
    }
    return content;
};
_c = CmsBlockRenderer;
const __TURBOPACK__default__export__ = CmsBlockRenderer;
var _c;
__turbopack_context__.k.register(_c, "CmsBlockRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/LoadingSkeleton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BannerSkeleton": (()=>BannerSkeleton),
    "CartItemSkeleton": (()=>CartItemSkeleton),
    "CategoryCardSkeleton": (()=>CategoryCardSkeleton),
    "CmsBlockSkeleton": (()=>CmsBlockSkeleton),
    "ListSkeleton": (()=>ListSkeleton),
    "OrderItemSkeleton": (()=>OrderItemSkeleton),
    "PageBuilderBannerSkeleton": (()=>PageBuilderBannerSkeleton),
    "PageBuilderColumnSkeleton": (()=>PageBuilderColumnSkeleton),
    "PageBuilderContentSkeleton": (()=>PageBuilderContentSkeleton),
    "PageBuilderRowSkeleton": (()=>PageBuilderRowSkeleton),
    "PageSkeleton": (()=>PageSkeleton),
    "ProductCardSkeleton": (()=>ProductCardSkeleton),
    "ProductGridSkeleton": (()=>ProductGridSkeleton),
    "TextSkeleton": (()=>TextSkeleton),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Skeleton/Skeleton.js [app-client] (ecmascript) <export default as Skeleton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Card/Card.js [app-client] (ecmascript) <export default as Card>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CardContent$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/CardContent/CardContent.js [app-client] (ecmascript) <export default as CardContent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Grid/Grid.js [app-client] (ecmascript) <export default as Grid>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$emotion$2f$react$2f$dist$2f$emotion$2d$react$2e$browser$2e$development$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@emotion/react/dist/emotion-react.browser.development.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
// Custom shimmer animation
const shimmer = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$emotion$2f$react$2f$dist$2f$emotion$2d$react$2e$browser$2e$development$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["keyframes"]`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;
// Custom pulse animation
const pulse = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$emotion$2f$react$2f$dist$2f$emotion$2d$react$2e$browser$2e$development$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["keyframes"]`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
`;
// Animation variants for motion
const containerVariants = {
    hidden: {
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1
        }
    }
};
const itemVariants = {
    hidden: {
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        }
    }
};
// Enhanced skeleton styles with animations
const getSkeletonSx = (animationType = 'shimmer')=>{
    const baseSx = {
        borderRadius: 2,
        transform: 'scale(1)',
        transition: 'transform 0.2s ease-in-out',
        '&:hover': {
            transform: 'scale(1.02)'
        }
    };
    switch(animationType){
        case 'shimmer':
            return {
                ...baseSx,
                background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',
                backgroundSize: '200px 100%',
                animation: `${shimmer} 2s infinite linear`
            };
        case 'pulse':
            return {
                ...baseSx,
                animation: `${pulse} 2s infinite ease-in-out`
            };
        default:
            return baseSx;
    }
};
const ProductCardSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: itemVariants,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
            sx: {
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                }
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "rectangular",
                    height: 200,
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 107,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CardContent$3e$__["CardContent"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 24,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 20,
                            width: "60%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mt: 1
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 120,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                mt: 1
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                variant: "text",
                                height: 28,
                                width: "40%",
                                animation: animationType === 'shimmer' ? false : animationType,
                                sx: getSkeletonSx(animationType)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 128,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 127,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 98,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 97,
        columnNumber: 5
    }, this);
};
_c = ProductCardSkeleton;
const ProductGridSkeleton = ({ count = 12, columns = 4, animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__["Grid"], {
            container: true,
            spacing: 3,
            children: Array.from({
                length: count
            }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__["Grid"], {
                    item: true,
                    xs: 12,
                    sm: 6,
                    md: 12 / columns,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductCardSkeleton, {
                        animationType: animationType
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 159,
                        columnNumber: 13
                    }, this)
                }, index, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 158,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 156,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 155,
        columnNumber: 5
    }, this);
};
_c1 = ProductGridSkeleton;
const CategoryCardSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: itemVariants,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Card$2f$Card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Card$3e$__["Card"], {
            sx: {
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                }
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "rectangular",
                    height: 150,
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$CardContent$2f$CardContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CardContent$3e$__["CardContent"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 24,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 189,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 16,
                            width: "80%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mt: 1
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 173,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 172,
        columnNumber: 5
    }, this);
};
_c2 = CategoryCardSkeleton;
const BannerSkeleton = ({ height = 300, animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            scale: 0.95
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 0.6,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
            variant: "rectangular",
            height: height,
            animation: animationType === 'shimmer' ? false : animationType,
            sx: {
                ...getSkeletonSx(animationType),
                borderRadius: 2
            }
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 219,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 214,
        columnNumber: 5
    }, this);
};
_c3 = BannerSkeleton;
const TextSkeleton = ({ lines = 3, width = '100%', animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            children: Array.from({
                length: lines
            }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                        variant: "text",
                        height: 20,
                        width: index === lines - 1 ? '60%' : width,
                        animation: animationType === 'shimmer' ? false : animationType,
                        sx: {
                            ...getSkeletonSx(animationType),
                            mb: 0.5
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 249,
                        columnNumber: 13
                    }, this)
                }, index, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 248,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 246,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 245,
        columnNumber: 5
    }, this);
};
_c4 = TextSkeleton;
const CartItemSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                display: 'flex',
                alignItems: 'center',
                p: 2,
                gap: 2
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "rectangular",
                    width: 80,
                    height: 80,
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 277,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    sx: {
                        flex: 1
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 24,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 285,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 20,
                            width: "60%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mt: 0.5
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 291,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 20,
                            width: "40%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mt: 0.5
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 298,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 284,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    sx: {
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'end'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 24,
                            width: 60,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 309,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "rectangular",
                            width: 40,
                            height: 32,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mt: 1
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 316,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 306,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 276,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 271,
        columnNumber: 5
    }, this);
};
_c5 = CartItemSkeleton;
const OrderItemSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                p: 2,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                mb: 2
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    sx: {
                        display: 'flex',
                        justifyContent: 'space-between',
                        mb: 1
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 20,
                            width: 120,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 343,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 20,
                            width: 80,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 350,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 342,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "text",
                    height: 16,
                    width: "100%",
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 358,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "text",
                    height: 16,
                    width: "80%",
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: {
                        ...getSkeletonSx(animationType),
                        mt: 0.5
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 365,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    sx: {
                        mt: 1,
                        display: 'flex',
                        gap: 2
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 16,
                            width: 60,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 373,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 16,
                            width: 80,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 380,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 372,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 339,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 334,
        columnNumber: 5
    }, this);
};
_c6 = OrderItemSkeleton;
const PageSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                p: 3
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                        variant: "text",
                        height: 40,
                        width: "30%",
                        animation: animationType === 'shimmer' ? false : animationType,
                        sx: {
                            ...getSkeletonSx(animationType),
                            mb: 2
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 402,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 401,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                        sx: {
                            display: 'flex',
                            gap: 1,
                            mb: 3
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                variant: "text",
                                height: 16,
                                width: 60,
                                animation: animationType === 'shimmer' ? false : animationType,
                                sx: getSkeletonSx(animationType)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 414,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                variant: "text",
                                height: 16,
                                width: 20,
                                animation: animationType === 'shimmer' ? false : animationType,
                                sx: getSkeletonSx(animationType)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 421,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                variant: "text",
                                height: 16,
                                width: 80,
                                animation: animationType === 'shimmer' ? false : animationType,
                                sx: getSkeletonSx(animationType)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 428,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 413,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 412,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__["Grid"], {
                    container: true,
                    spacing: 3,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__["Grid"], {
                            item: true,
                            xs: 12,
                            md: 3,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                variants: itemVariants,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                                    sx: {
                                        mb: 2
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                            variant: "text",
                                            height: 24,
                                            width: "60%",
                                            animation: animationType === 'shimmer' ? false : animationType,
                                            sx: getSkeletonSx(animationType)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                            lineNumber: 444,
                                            columnNumber: 17
                                        }, this),
                                        Array.from({
                                            length: 5
                                        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                                                variant: "text",
                                                height: 20,
                                                animation: animationType === 'shimmer' ? false : animationType,
                                                sx: {
                                                    ...getSkeletonSx(animationType),
                                                    mt: 1
                                                }
                                            }, index, false, {
                                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                                lineNumber: 454,
                                                columnNumber: 19
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                    lineNumber: 443,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 442,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 440,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Grid$2f$Grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Grid$3e$__["Grid"], {
                            item: true,
                            xs: 12,
                            md: 9,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProductGridSkeleton, {
                                count: 9,
                                columns: 3,
                                animationType: animationType
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                lineNumber: 469,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 467,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 439,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 399,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 398,
        columnNumber: 5
    }, this);
};
_c7 = PageSkeleton;
const ListSkeleton = ({ count = 5, height = 60, animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            children: Array.from({
                length: count
            }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                        sx: {
                            mb: 1
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "rectangular",
                            height: height,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: getSkeletonSx(animationType)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 499,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 498,
                        columnNumber: 13
                    }, this)
                }, index, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 497,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 495,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 494,
        columnNumber: 5
    }, this);
};
_c8 = ListSkeleton;
const PageBuilderRowSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                width: '100%',
                mb: 3
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                variant: "rectangular",
                height: 60,
                animation: animationType === 'shimmer' ? false : animationType,
                sx: getSkeletonSx(animationType)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                lineNumber: 524,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 523,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 518,
        columnNumber: 5
    }, this);
};
_c9 = PageBuilderRowSkeleton;
const PageBuilderColumnSkeleton = ({ width = '100%', height = 200, animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            scale: 0.95
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
            variant: "rectangular",
            height: height,
            animation: animationType === 'shimmer' ? false : animationType,
            sx: {
                ...getSkeletonSx(animationType),
                width
            }
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 546,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 541,
        columnNumber: 5
    }, this);
};
_c10 = PageBuilderColumnSkeleton;
const PageBuilderBannerSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            scale: 0.98
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 0.8,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                position: 'relative',
                width: '100%',
                mb: 3
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "rectangular",
                    height: 400,
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 569,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    sx: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        textAlign: 'center',
                        width: '80%'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 48,
                            width: "70%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mb: 2,
                                mx: 'auto'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 585,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "text",
                            height: 24,
                            width: "50%",
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mb: 3,
                                mx: 'auto'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 592,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                            variant: "rectangular",
                            height: 40,
                            width: 120,
                            animation: animationType === 'shimmer' ? false : animationType,
                            sx: {
                                ...getSkeletonSx(animationType),
                                mx: 'auto'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 599,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 575,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 568,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 563,
        columnNumber: 5
    }, this);
};
_c11 = PageBuilderBannerSkeleton;
const CmsBlockSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.5,
            ease: 'easeOut'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                width: '100%',
                p: 2,
                border: '1px solid #e0e0e0',
                borderRadius: 2,
                mb: 2
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "text",
                    height: 28,
                    width: "40%",
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: {
                        ...getSkeletonSx(animationType),
                        mb: 2
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 630,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Skeleton$3e$__["Skeleton"], {
                    variant: "rectangular",
                    height: 120,
                    animation: animationType === 'shimmer' ? false : animationType,
                    sx: getSkeletonSx(animationType)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 637,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 621,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 616,
        columnNumber: 5
    }, this);
};
_c12 = CmsBlockSkeleton;
const PageBuilderContentSkeleton = ({ animationType = 'shimmer' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                width: '100%'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderRowSkeleton, {
                            animationType: animationType
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 656,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                display: 'flex',
                                gap: 2,
                                mb: 3
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderColumnSkeleton, {
                                    width: "50%",
                                    animationType: animationType
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                    lineNumber: 658,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderColumnSkeleton, {
                                    width: "50%",
                                    animationType: animationType
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                                    lineNumber: 662,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                            lineNumber: 657,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 655,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderBannerSkeleton, {
                        animationType: animationType
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 671,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 670,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: itemVariants,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(TextSkeleton, {
                        lines: 4,
                        animationType: animationType
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                        lineNumber: 676,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
                    lineNumber: 675,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
            lineNumber: 653,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSkeleton.tsx",
        lineNumber: 652,
        columnNumber: 5
    }, this);
};
_c13 = PageBuilderContentSkeleton;
const __TURBOPACK__default__export__ = {
    ProductCardSkeleton,
    ProductGridSkeleton,
    CategoryCardSkeleton,
    BannerSkeleton,
    TextSkeleton,
    CartItemSkeleton,
    OrderItemSkeleton,
    PageSkeleton,
    ListSkeleton,
    PageBuilderRowSkeleton,
    PageBuilderColumnSkeleton,
    PageBuilderBannerSkeleton,
    CmsBlockSkeleton,
    PageBuilderContentSkeleton
};
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13;
__turbopack_context__.k.register(_c, "ProductCardSkeleton");
__turbopack_context__.k.register(_c1, "ProductGridSkeleton");
__turbopack_context__.k.register(_c2, "CategoryCardSkeleton");
__turbopack_context__.k.register(_c3, "BannerSkeleton");
__turbopack_context__.k.register(_c4, "TextSkeleton");
__turbopack_context__.k.register(_c5, "CartItemSkeleton");
__turbopack_context__.k.register(_c6, "OrderItemSkeleton");
__turbopack_context__.k.register(_c7, "PageSkeleton");
__turbopack_context__.k.register(_c8, "ListSkeleton");
__turbopack_context__.k.register(_c9, "PageBuilderRowSkeleton");
__turbopack_context__.k.register(_c10, "PageBuilderColumnSkeleton");
__turbopack_context__.k.register(_c11, "PageBuilderBannerSkeleton");
__turbopack_context__.k.register(_c12, "CmsBlockSkeleton");
__turbopack_context__.k.register(_c13, "PageBuilderContentSkeleton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/cms/CmsBlockLoader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CmsBlockLoader": (()=>CmsBlockLoader),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/magento/api/cms.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LoadingSkeleton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cms$2f$CmsBlockRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/cms/CmsBlockRenderer.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const CmsBlockLoader = ({ identifier, fallback, className, style, animate = true })=>{
    _s();
    const [block, setBlock] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CmsBlockLoader.useEffect": ()=>{
            const loadBlock = {
                "CmsBlockLoader.useEffect.loadBlock": async ()=>{
                    try {
                        setLoading(true);
                        setError(null);
                        const blockData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$magento$2f$api$2f$cms$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCmsBlockWithPageBuilder"])(identifier);
                        setBlock(blockData);
                    } catch (err) {
                        setError(err instanceof Error ? err.message : 'Failed to load CMS block');
                        console.error('Error loading CMS block:', err);
                    } finally{
                        setLoading(false);
                    }
                }
            }["CmsBlockLoader.useEffect.loadBlock"];
            loadBlock();
        }
    }["CmsBlockLoader.useEffect"], [
        identifier
    ]);
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            className: className,
            style: style,
            children: fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingSkeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CmsBlockSkeleton"], {
                animationType: "shimmer"
            }, void 0, false, {
                fileName: "[project]/src/components/cms/CmsBlockLoader.tsx",
                lineNumber: 52,
                columnNumber: 22
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/cms/CmsBlockLoader.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, this);
    }
    if (error || !block) {
        if ("TURBOPACK compile-time truthy", 1) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                className: className,
                style: style,
                sx: {
                    p: 2,
                    border: '1px dashed #f44336',
                    borderRadius: 1,
                    backgroundColor: '#ffebee',
                    color: '#c62828',
                    textAlign: 'center'
                },
                children: [
                    "CMS Block Error: ",
                    error || `Block "${identifier}" not found`
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/cms/CmsBlockLoader.tsx",
                lineNumber: 60,
                columnNumber: 9
            }, this);
        }
        "TURBOPACK unreachable";
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cms$2f$CmsBlockRenderer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        block: block,
        className: className,
        style: style,
        animate: animate
    }, void 0, false, {
        fileName: "[project]/src/components/cms/CmsBlockLoader.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
};
_s(CmsBlockLoader, "6YFjUvIl91aI/42LvU1ooWgx0SA=");
_c = CmsBlockLoader;
const __TURBOPACK__default__export__ = CmsBlockLoader;
var _c;
__turbopack_context__.k.register(_c, "CmsBlockLoader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_69c6268e._.js.map