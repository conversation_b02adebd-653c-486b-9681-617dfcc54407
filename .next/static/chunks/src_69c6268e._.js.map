{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/pagebuilder/types.ts"], "sourcesContent": ["// Magento 2 Page Builder Types for Next.js\n\nimport { ReactNode } from 'react';\n\n// Base Page Builder element interface\nexport interface PageBuilderElement {\n  type: PageBuilderElementType;\n  id: string;\n  attributes: Record<string, any>;\n  styles: Record<string, any>;\n  children?: PageBuilderElement[];\n  content?: string;\n  rawHtml?: string;\n}\n\n// Page Builder element types\nexport enum PageBuilderElementType {\n  ROW = 'row',\n  COLUMN = 'column',\n  TEXT = 'text',\n  HEADING = 'heading',\n  IMAGE = 'image',\n  BUTTON = 'button',\n  BANNER = 'banner',\n  SLIDER = 'slider',\n  PRODUCTS = 'products',\n  VIDEO = 'video',\n  MAP = 'map',\n  BLOCK = 'block',\n  HTML = 'html',\n  DIVIDER = 'divider',\n  TABS = 'tabs',\n  TAB_ITEM = 'tab-item',\n}\n\n// Row element specific types\nexport interface RowElement extends PageBuilderElement {\n  type: PageBuilderElementType.ROW;\n  attributes: {\n    appearance?: 'contained' | 'full-width' | 'full-bleed';\n    enableParallax?: boolean;\n    parallaxSpeed?: number;\n    backgroundColor?: string;\n    backgroundImage?: string;\n    backgroundSize?: 'cover' | 'contain' | 'auto';\n    backgroundPosition?: string;\n    backgroundAttachment?: 'scroll' | 'fixed';\n    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';\n    minHeight?: string;\n    verticalAlignment?: 'top' | 'middle' | 'bottom';\n  };\n}\n\n// Column element specific types\nexport interface ColumnElement extends PageBuilderElement {\n  type: PageBuilderElementType.COLUMN;\n  attributes: {\n    width?: string;\n    appearance?: 'full-height' | 'minimum-height';\n    backgroundColor?: string;\n    backgroundImage?: string;\n    backgroundSize?: 'cover' | 'contain' | 'auto';\n    backgroundPosition?: string;\n    backgroundAttachment?: 'scroll' | 'fixed';\n    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';\n    minHeight?: string;\n    verticalAlignment?: 'top' | 'middle' | 'bottom';\n  };\n}\n\n// Text element specific types\nexport interface TextElement extends PageBuilderElement {\n  type: PageBuilderElementType.TEXT;\n  content: string;\n  attributes: {\n    textAlign?: 'left' | 'center' | 'right' | 'justify';\n    border?: string;\n    borderColor?: string;\n    borderWidth?: string;\n    borderRadius?: string;\n  };\n}\n\n// Heading element specific types\nexport interface HeadingElement extends PageBuilderElement {\n  type: PageBuilderElementType.HEADING;\n  content: string;\n  attributes: {\n    headingType?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';\n    textAlign?: 'left' | 'center' | 'right';\n    border?: string;\n    borderColor?: string;\n    borderWidth?: string;\n    borderRadius?: string;\n  };\n}\n\n// Image element specific types\nexport interface ImageElement extends PageBuilderElement {\n  type: PageBuilderElementType.IMAGE;\n  attributes: {\n    src: string;\n    alt?: string;\n    title?: string;\n    caption?: string;\n    link?: string;\n    linkTarget?: '_self' | '_blank';\n    alignment?: 'left' | 'center' | 'right';\n    border?: string;\n    borderColor?: string;\n    borderWidth?: string;\n    borderRadius?: string;\n    lazyLoading?: boolean;\n  };\n}\n\n// Button element specific types\nexport interface ButtonElement extends PageBuilderElement {\n  type: PageBuilderElementType.BUTTON;\n  content: string;\n  attributes: {\n    buttonType?: 'primary' | 'secondary' | 'link';\n    link?: string;\n    linkTarget?: '_self' | '_blank';\n    textAlign?: 'left' | 'center' | 'right';\n    border?: string;\n    borderColor?: string;\n    borderWidth?: string;\n    borderRadius?: string;\n    backgroundColor?: string;\n    textColor?: string;\n  };\n}\n\n// Banner element specific types\nexport interface BannerElement extends PageBuilderElement {\n  type: PageBuilderElementType.BANNER;\n  attributes: {\n    appearance?: 'poster' | 'collage-left' | 'collage-centered' | 'collage-right';\n    minHeight?: string;\n    backgroundColor?: string;\n    backgroundImage?: string;\n    backgroundSize?: 'cover' | 'contain' | 'auto';\n    backgroundPosition?: string;\n    backgroundAttachment?: 'scroll' | 'fixed';\n    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';\n    showButton?: boolean;\n    buttonText?: string;\n    buttonType?: 'primary' | 'secondary' | 'link';\n    buttonLink?: string;\n    buttonTarget?: '_self' | '_blank';\n    showOverlay?: boolean;\n    overlayColor?: string;\n    content?: string;\n    contentPlacement?: 'left' | 'center' | 'right';\n  };\n}\n\n// Slider element specific types\nexport interface SliderElement extends PageBuilderElement {\n  type: PageBuilderElementType.SLIDER;\n  attributes: {\n    appearance?: 'default' | 'full-width';\n    autoplay?: boolean;\n    autoplaySpeed?: number;\n    fade?: boolean;\n    infiniteLoop?: boolean;\n    showArrows?: boolean;\n    showDots?: boolean;\n    minHeight?: string;\n  };\n  children: BannerElement[];\n}\n\n// Products element specific types\nexport interface ProductsElement extends PageBuilderElement {\n  type: PageBuilderElementType.PRODUCTS;\n  attributes: {\n    appearance?: 'grid' | 'carousel';\n    selectType?: 'product' | 'sku' | 'condition';\n    displayMode?: 'selected' | 'crossed' | 'related' | 'upsell';\n    productsCount?: number;\n    condition?: any[];\n    sortOrder?: 'position' | 'name' | 'sku' | 'price' | 'created_at';\n    carouselMode?: 'default' | 'continuous';\n    autoplay?: boolean;\n    autoplaySpeed?: number;\n    infiniteLoop?: boolean;\n    showArrows?: boolean;\n    showDots?: boolean;\n  };\n}\n\n// Video element specific types\nexport interface VideoElement extends PageBuilderElement {\n  type: PageBuilderElementType.VIDEO;\n  attributes: {\n    videoType?: 'youtube' | 'vimeo' | 'mp4';\n    videoUrl?: string;\n    videoId?: string;\n    maxWidth?: string;\n    autoplay?: boolean;\n    muted?: boolean;\n    loop?: boolean;\n    controls?: boolean;\n    lazyLoading?: boolean;\n    fallbackImage?: string;\n  };\n}\n\n// Map element specific types\nexport interface MapElement extends PageBuilderElement {\n  type: PageBuilderElementType.MAP;\n  attributes: {\n    height?: string;\n    showControls?: boolean;\n    locations?: Array<{\n      position: {\n        latitude: number;\n        longitude: number;\n      };\n      name?: string;\n      comment?: string;\n      phoneNumber?: string;\n      address?: string;\n      city?: string;\n      country?: string;\n      postcode?: string;\n    }>;\n  };\n}\n\n// Block element specific types\nexport interface BlockElement extends PageBuilderElement {\n  type: PageBuilderElementType.BLOCK;\n  attributes: {\n    blockId?: string;\n    template?: string;\n  };\n}\n\n// HTML element specific types\nexport interface HtmlElement extends PageBuilderElement {\n  type: PageBuilderElementType.HTML;\n  content: string;\n}\n\n// Divider element specific types\nexport interface DividerElement extends PageBuilderElement {\n  type: PageBuilderElementType.DIVIDER;\n  attributes: {\n    lineColor?: string;\n    lineThickness?: string;\n    lineWidth?: string;\n  };\n}\n\n// Tabs element specific types\nexport interface TabsElement extends PageBuilderElement {\n  type: PageBuilderElementType.TABS;\n  attributes: {\n    defaultActiveTab?: number;\n    tabsAlignment?: 'left' | 'center' | 'right';\n    tabsNavigation?: 'tabs' | 'pills';\n    minHeight?: string;\n  };\n  children: TabItemElement[];\n}\n\n// Tab item element specific types\nexport interface TabItemElement extends PageBuilderElement {\n  type: PageBuilderElementType.TAB_ITEM;\n  attributes: {\n    tabName?: string;\n    backgroundColor?: string;\n    backgroundImage?: string;\n    backgroundSize?: 'cover' | 'contain' | 'auto';\n    backgroundPosition?: string;\n    backgroundAttachment?: 'scroll' | 'fixed';\n    backgroundRepeat?: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';\n    minHeight?: string;\n    verticalAlignment?: 'top' | 'middle' | 'bottom';\n  };\n}\n\n// Page Builder content structure\nexport interface PageBuilderContent {\n  elements: PageBuilderElement[];\n  rawHtml: string;\n  version?: string;\n}\n\n// Parser configuration\nexport interface PageBuilderParserConfig {\n  enableLazyLoading?: boolean;\n  imageOptimization?: boolean;\n  customElementParsers?: Record<string, (element: Element) => PageBuilderElement>;\n  componentOverrides?: Record<PageBuilderElementType, React.ComponentType<any>>;\n}\n\n// Component props for Page Builder elements\nexport interface PageBuilderElementProps {\n  element: PageBuilderElement;\n  children?: ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\n// Renderer context\nexport interface PageBuilderContext {\n  config: PageBuilderParserConfig;\n  isEditing?: boolean;\n  deviceType?: 'mobile' | 'tablet' | 'desktop';\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;AAgBpC,IAAA,AAAK,gDAAA;;;;;;;;;;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/pagebuilder/constants.ts"], "sourcesContent": ["// Magento 2 Page Builder Constants\n\nimport { PageBuilderElementType } from './types';\n\n// Page Builder CSS class mappings\nexport const PAGE_BUILDER_CLASSES = {\n  // Row classes\n  ROW: 'pagebuilder-row',\n  ROW_CONTAINED: 'pagebuilder-row-contained',\n  ROW_FULL_WIDTH: 'pagebuilder-row-full-width',\n  ROW_FULL_BLEED: 'pagebuilder-row-full-bleed',\n  \n  // Column classes\n  COLUMN: 'pagebuilder-column',\n  COLUMN_GROUP: 'pagebuilder-column-group',\n  COLUMN_LINE: 'pagebuilder-column-line',\n  \n  // Content type classes\n  TEXT: 'pagebuilder-text',\n  HEADING: 'pagebuilder-heading',\n  IMAGE: 'pagebuilder-image',\n  BUTTON: 'pagebuilder-button-item',\n  BANNER: 'pagebuilder-banner-item',\n  SLIDER: 'pagebuilder-slider',\n  PRODUCTS: 'pagebuilder-products',\n  VIDEO: 'pagebuilder-video',\n  MAP: 'pagebuilder-map',\n  BLOCK: 'pagebuilder-block',\n  HTML: 'pagebuilder-html-code',\n  DIVIDER: 'pagebuilder-divider',\n  TABS: 'pagebuilder-tabs',\n  TAB_ITEM: 'pagebuilder-tab-item',\n} as const;\n\n// Page Builder data attributes\nexport const PAGE_BUILDER_ATTRIBUTES = {\n  // Common attributes\n  CONTENT_TYPE: 'data-content-type',\n  APPEARANCE: 'data-appearance',\n  ELEMENT: 'data-element',\n  \n  // Background attributes\n  BACKGROUND_IMAGES: 'data-background-images',\n  BACKGROUND_TYPE: 'data-background-type',\n  VIDEO_LOOP: 'data-video-loop',\n  VIDEO_PLAY_ONLY_VISIBLE: 'data-video-play-only-visible',\n  VIDEO_LAZY_LOAD: 'data-video-lazy-load',\n  VIDEO_FALLBACK_SRC: 'data-video-fallback-src',\n  \n  // Parallax attributes\n  ENABLE_PARALLAX: 'data-enable-parallax',\n  PARALLAX_SPEED: 'data-parallax-speed',\n  \n  // Banner attributes\n  SHOW_BUTTON: 'data-show-button',\n  SHOW_OVERLAY: 'data-show-overlay',\n  \n  // Slider attributes\n  AUTOPLAY: 'data-autoplay',\n  AUTOPLAY_SPEED: 'data-autoplay-speed',\n  FADE: 'data-fade',\n  INFINITE_LOOP: 'data-infinite-loop',\n  SHOW_ARROWS: 'data-show-arrows',\n  SHOW_DOTS: 'data-show-dots',\n  \n  // Products attributes\n  PRODUCTS_COUNT: 'data-products-count',\n  SORT_ORDER: 'data-sort-order',\n  CAROUSEL_MODE: 'data-carousel-mode',\n  \n  // Map attributes\n  SHOW_CONTROLS: 'data-show-controls',\n  \n  // Tabs attributes\n  DEFAULT_ACTIVE_TAB: 'data-default-active-tab',\n  TABS_ALIGNMENT: 'data-tabs-alignment',\n  TABS_NAVIGATION: 'data-tabs-navigation',\n} as const;\n\n// Element type mapping from CSS classes to PageBuilderElementType\nexport const ELEMENT_TYPE_MAPPING: Record<string, PageBuilderElementType> = {\n  [PAGE_BUILDER_CLASSES.ROW]: PageBuilderElementType.ROW,\n  [PAGE_BUILDER_CLASSES.COLUMN]: PageBuilderElementType.COLUMN,\n  [PAGE_BUILDER_CLASSES.TEXT]: PageBuilderElementType.TEXT,\n  [PAGE_BUILDER_CLASSES.HEADING]: PageBuilderElementType.HEADING,\n  [PAGE_BUILDER_CLASSES.IMAGE]: PageBuilderElementType.IMAGE,\n  [PAGE_BUILDER_CLASSES.BUTTON]: PageBuilderElementType.BUTTON,\n  [PAGE_BUILDER_CLASSES.BANNER]: PageBuilderElementType.BANNER,\n  [PAGE_BUILDER_CLASSES.SLIDER]: PageBuilderElementType.SLIDER,\n  [PAGE_BUILDER_CLASSES.PRODUCTS]: PageBuilderElementType.PRODUCTS,\n  [PAGE_BUILDER_CLASSES.VIDEO]: PageBuilderElementType.VIDEO,\n  [PAGE_BUILDER_CLASSES.MAP]: PageBuilderElementType.MAP,\n  [PAGE_BUILDER_CLASSES.BLOCK]: PageBuilderElementType.BLOCK,\n  [PAGE_BUILDER_CLASSES.HTML]: PageBuilderElementType.HTML,\n  [PAGE_BUILDER_CLASSES.DIVIDER]: PageBuilderElementType.DIVIDER,\n  [PAGE_BUILDER_CLASSES.TABS]: PageBuilderElementType.TABS,\n  [PAGE_BUILDER_CLASSES.TAB_ITEM]: PageBuilderElementType.TAB_ITEM,\n};\n\n// Content type mapping from data-content-type attribute\nexport const CONTENT_TYPE_MAPPING: Record<string, PageBuilderElementType> = {\n  'row': PageBuilderElementType.ROW,\n  'column-group': PageBuilderElementType.COLUMN,\n  'column': PageBuilderElementType.COLUMN,\n  'text': PageBuilderElementType.TEXT,\n  'heading': PageBuilderElementType.HEADING,\n  'image': PageBuilderElementType.IMAGE,\n  'button-item': PageBuilderElementType.BUTTON,\n  'banner': PageBuilderElementType.BANNER,\n  'slider': PageBuilderElementType.SLIDER,\n  'products': PageBuilderElementType.PRODUCTS,\n  'video': PageBuilderElementType.VIDEO,\n  'map': PageBuilderElementType.MAP,\n  'block': PageBuilderElementType.BLOCK,\n  'html': PageBuilderElementType.HTML,\n  'divider': PageBuilderElementType.DIVIDER,\n  'tabs': PageBuilderElementType.TABS,\n  'tab-item': PageBuilderElementType.TAB_ITEM,\n};\n\n// Default styles for Page Builder elements\nexport const DEFAULT_STYLES = {\n  ROW: {\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    boxSizing: 'border-box',\n  },\n  COLUMN: {\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    boxSizing: 'border-box',\n  },\n  TEXT: {\n    wordWrap: 'break-word',\n  },\n  HEADING: {\n    wordWrap: 'break-word',\n  },\n  IMAGE: {\n    maxWidth: '100%',\n    height: 'auto',\n  },\n  BUTTON: {\n    display: 'inline-block',\n    textDecoration: 'none',\n    cursor: 'pointer',\n  },\n  BANNER: {\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n  SLIDER: {\n    position: 'relative',\n  },\n  PRODUCTS: {\n    display: 'flex',\n    flexWrap: 'wrap',\n  },\n  VIDEO: {\n    position: 'relative',\n    width: '100%',\n  },\n  MAP: {\n    width: '100%',\n    height: '400px',\n  },\n  DIVIDER: {\n    width: '100%',\n    height: '1px',\n    backgroundColor: '#e0e0e0',\n  },\n  TABS: {\n    width: '100%',\n  },\n} as const;\n\n// Responsive breakpoints (matching Magento's defaults)\nexport const BREAKPOINTS = {\n  MOBILE: 768,\n  TABLET: 1024,\n  DESKTOP: 1200,\n} as const;\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  enableLazyLoading: true,\n  imageOptimization: true,\n  customElementParsers: {},\n  componentOverrides: {},\n} as const;\n\n// CSS property mappings for style parsing\nexport const CSS_PROPERTY_MAPPING = {\n  'background-color': 'backgroundColor',\n  'background-image': 'backgroundImage',\n  'background-size': 'backgroundSize',\n  'background-position': 'backgroundPosition',\n  'background-repeat': 'backgroundRepeat',\n  'background-attachment': 'backgroundAttachment',\n  'min-height': 'minHeight',\n  'text-align': 'textAlign',\n  'vertical-align': 'verticalAlign',\n  'border-color': 'borderColor',\n  'border-width': 'borderWidth',\n  'border-radius': 'borderRadius',\n  'border-style': 'borderStyle',\n  'margin-top': 'marginTop',\n  'margin-right': 'marginRight',\n  'margin-bottom': 'marginBottom',\n  'margin-left': 'marginLeft',\n  'padding-top': 'paddingTop',\n  'padding-right': 'paddingRight',\n  'padding-bottom': 'paddingBottom',\n  'padding-left': 'paddingLeft',\n} as const;\n\n// Video providers\nexport const VIDEO_PROVIDERS = {\n  YOUTUBE: 'youtube',\n  VIMEO: 'vimeo',\n  MP4: 'mp4',\n} as const;\n\n// Button types\nexport const BUTTON_TYPES = {\n  PRIMARY: 'primary',\n  SECONDARY: 'secondary',\n  LINK: 'link',\n} as const;\n\n// Alignment options\nexport const ALIGNMENT_OPTIONS = {\n  LEFT: 'left',\n  CENTER: 'center',\n  RIGHT: 'right',\n  JUSTIFY: 'justify',\n} as const;\n\n// Vertical alignment options\nexport const VERTICAL_ALIGNMENT_OPTIONS = {\n  TOP: 'top',\n  MIDDLE: 'middle',\n  BOTTOM: 'bottom',\n} as const;\n\n// Background size options\nexport const BACKGROUND_SIZE_OPTIONS = {\n  COVER: 'cover',\n  CONTAIN: 'contain',\n  AUTO: 'auto',\n} as const;\n\n// Background repeat options\nexport const BACKGROUND_REPEAT_OPTIONS = {\n  NO_REPEAT: 'no-repeat',\n  REPEAT: 'repeat',\n  REPEAT_X: 'repeat-x',\n  REPEAT_Y: 'repeat-y',\n} as const;\n\n// Background attachment options\nexport const BACKGROUND_ATTACHMENT_OPTIONS = {\n  SCROLL: 'scroll',\n  FIXED: 'fixed',\n} as const;\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;;;;;;;;;;;;AAEnC;;AAGO,MAAM,uBAAuB;IAClC,cAAc;IACd,KAAK;IACL,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAEhB,iBAAiB;IACjB,QAAQ;IACR,cAAc;IACd,aAAa;IAEb,uBAAuB;IACvB,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,OAAO;IACP,KAAK;IACL,OAAO;IACP,MAAM;IACN,SAAS;IACT,MAAM;IACN,UAAU;AACZ;AAGO,MAAM,0BAA0B;IACrC,oBAAoB;IACpB,cAAc;IACd,YAAY;IACZ,SAAS;IAET,wBAAwB;IACxB,mBAAmB;IACnB,iBAAiB;IACjB,YAAY;IACZ,yBAAyB;IACzB,iBAAiB;IACjB,oBAAoB;IAEpB,sBAAsB;IACtB,iBAAiB;IACjB,gBAAgB;IAEhB,oBAAoB;IACpB,aAAa;IACb,cAAc;IAEd,oBAAoB;IACpB,UAAU;IACV,gBAAgB;IAChB,MAAM;IACN,eAAe;IACf,aAAa;IACb,WAAW;IAEX,sBAAsB;IACtB,gBAAgB;IAChB,YAAY;IACZ,eAAe;IAEf,iBAAiB;IACjB,eAAe;IAEf,kBAAkB;IAClB,oBAAoB;IACpB,gBAAgB;IAChB,iBAAiB;AACnB;AAGO,MAAM,uBAA+D;IAC1E,CAAC,qBAAqB,GAAG,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,GAAG;IACtD,CAAC,qBAAqB,MAAM,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC5D,CAAC,qBAAqB,IAAI,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACxD,CAAC,qBAAqB,OAAO,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,OAAO;IAC9D,CAAC,qBAAqB,KAAK,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IAC1D,CAAC,qBAAqB,MAAM,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC5D,CAAC,qBAAqB,MAAM,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC5D,CAAC,qBAAqB,MAAM,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC5D,CAAC,qBAAqB,QAAQ,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,QAAQ;IAChE,CAAC,qBAAqB,KAAK,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IAC1D,CAAC,qBAAqB,GAAG,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,GAAG;IACtD,CAAC,qBAAqB,KAAK,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IAC1D,CAAC,qBAAqB,IAAI,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACxD,CAAC,qBAAqB,OAAO,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,OAAO;IAC9D,CAAC,qBAAqB,IAAI,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACxD,CAAC,qBAAqB,QAAQ,CAAC,EAAE,qIAAA,CAAA,yBAAsB,CAAC,QAAQ;AAClE;AAGO,MAAM,uBAA+D;IAC1E,OAAO,qIAAA,CAAA,yBAAsB,CAAC,GAAG;IACjC,gBAAgB,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC7C,UAAU,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IACvC,QAAQ,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACnC,WAAW,qIAAA,CAAA,yBAAsB,CAAC,OAAO;IACzC,SAAS,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IACrC,eAAe,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IAC5C,UAAU,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IACvC,UAAU,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IACvC,YAAY,qIAAA,CAAA,yBAAsB,CAAC,QAAQ;IAC3C,SAAS,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IACrC,OAAO,qIAAA,CAAA,yBAAsB,CAAC,GAAG;IACjC,SAAS,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IACrC,QAAQ,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACnC,WAAW,qIAAA,CAAA,yBAAsB,CAAC,OAAO;IACzC,QAAQ,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACnC,YAAY,qIAAA,CAAA,yBAAsB,CAAC,QAAQ;AAC7C;AAGO,MAAM,iBAAiB;IAC5B,KAAK;QACH,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,QAAQ;QACN,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,YAAY;QACZ,WAAW;IACb;IACA,MAAM;QACJ,UAAU;IACZ;IACA,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,UAAU;QACV,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;QACT,gBAAgB;QAChB,QAAQ;IACV;IACA,QAAQ;QACN,UAAU;QACV,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,YAAY;IACd;IACA,QAAQ;QACN,UAAU;IACZ;IACA,UAAU;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO;QACL,UAAU;QACV,OAAO;IACT;IACA,KAAK;QACH,OAAO;QACP,QAAQ;IACV;IACA,SAAS;QACP,OAAO;QACP,QAAQ;QACR,iBAAiB;IACnB;IACA,MAAM;QACJ,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB,CAAC;IACvB,oBAAoB,CAAC;AACvB;AAGO,MAAM,uBAAuB;IAClC,oBAAoB;IACpB,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,qBAAqB;IACrB,yBAAyB;IACzB,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;AAClB;AAGO,MAAM,kBAAkB;IAC7B,SAAS;IACT,OAAO;IACP,KAAK;AACP;AAGO,MAAM,eAAe;IAC1B,SAAS;IACT,WAAW;IACX,MAAM;AACR;AAGO,MAAM,oBAAoB;IAC/B,MAAM;IACN,QAAQ;IACR,OAAO;IACP,SAAS;AACX;AAGO,MAAM,6BAA6B;IACxC,KAAK;IACL,QAAQ;IACR,QAAQ;AACV;AAGO,MAAM,0BAA0B;IACrC,OAAO;IACP,SAAS;IACT,MAAM;AACR;AAGO,MAAM,4BAA4B;IACvC,WAAW;IACX,QAAQ;IACR,UAAU;IACV,UAAU;AACZ;AAGO,MAAM,gCAAgC;IAC3C,QAAQ;IACR,OAAO;AACT", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/pagebuilder/utils.ts"], "sourcesContent": ["// Magento 2 Page Builder Utilities\n\nimport { \n  PageBuilderElement, \n  PageBuilderElementType,\n  PageBuilderParserConfig \n} from './types';\nimport { \n  ELEMENT_TYPE_MAPPING, \n  CONTENT_TYPE_MAPPING, \n  CSS_PROPERTY_MAPPING,\n  DEFAULT_CONFIG \n} from './constants';\n\n// Generate unique ID for Page Builder elements\nexport function generateElementId(): string {\n  return `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n// Determine element type from DOM element\nexport function getElementType(element: Element): PageBuilderElementType | null {\n  // Check data-content-type attribute first\n  const contentType = element.getAttribute('data-content-type');\n  if (contentType && CONTENT_TYPE_MAPPING[contentType]) {\n    return CONTENT_TYPE_MAPPING[contentType];\n  }\n\n  // Check CSS classes\n  const classList = Array.from(element.classList);\n  for (const className of classList) {\n    if (ELEMENT_TYPE_MAPPING[className]) {\n      return ELEMENT_TYPE_MAPPING[className];\n    }\n  }\n\n  // Check for specific element patterns\n  if (element.tagName === 'IMG') {\n    return PageBuilderElementType.IMAGE;\n  }\n  \n  if (element.tagName === 'VIDEO' || element.tagName === 'IFRAME') {\n    return PageBuilderElementType.VIDEO;\n  }\n\n  if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {\n    return PageBuilderElementType.HEADING;\n  }\n\n  if (element.tagName === 'A' && element.classList.contains('pagebuilder-button-primary')) {\n    return PageBuilderElementType.BUTTON;\n  }\n\n  // Default to text for content elements\n  if (element.textContent && element.textContent.trim()) {\n    return PageBuilderElementType.TEXT;\n  }\n\n  return null;\n}\n\n// Extract attributes from DOM element\nexport function extractAttributes(element: Element): Record<string, any> {\n  const attributes: Record<string, any> = {};\n\n  // Extract data attributes\n  Array.from(element.attributes).forEach(attr => {\n    if (attr.name.startsWith('data-')) {\n      const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n      attributes[key] = attr.value;\n    }\n  });\n\n  // Extract common HTML attributes\n  if (element.getAttribute('id')) {\n    attributes.id = element.getAttribute('id');\n  }\n  \n  if (element.getAttribute('class')) {\n    attributes.className = element.getAttribute('class');\n  }\n\n  // Extract specific attributes based on element type\n  if (element.tagName === 'IMG') {\n    attributes.src = element.getAttribute('src');\n    attributes.alt = element.getAttribute('alt');\n    attributes.title = element.getAttribute('title');\n  }\n\n  if (element.tagName === 'A') {\n    attributes.href = element.getAttribute('href');\n    attributes.target = element.getAttribute('target');\n  }\n\n  if (element.tagName === 'VIDEO') {\n    attributes.src = element.getAttribute('src');\n    attributes.autoplay = element.hasAttribute('autoplay');\n    attributes.muted = element.hasAttribute('muted');\n    attributes.loop = element.hasAttribute('loop');\n    attributes.controls = element.hasAttribute('controls');\n  }\n\n  return attributes;\n}\n\n// Extract styles from DOM element\nexport function extractStyles(element: Element): Record<string, any> {\n  const styles: Record<string, any> = {};\n  \n  // Get computed styles\n  if (typeof window !== 'undefined') {\n    const computedStyles = window.getComputedStyle(element);\n    \n    // Extract relevant CSS properties\n    Object.entries(CSS_PROPERTY_MAPPING).forEach(([cssProperty, jsProperty]) => {\n      const value = computedStyles.getPropertyValue(cssProperty);\n      if (value && value !== 'initial' && value !== 'inherit') {\n        styles[jsProperty] = value;\n      }\n    });\n  }\n\n  // Extract inline styles\n  const inlineStyle = element.getAttribute('style');\n  if (inlineStyle) {\n    const styleDeclarations = inlineStyle.split(';');\n    styleDeclarations.forEach(declaration => {\n      const [property, value] = declaration.split(':').map(s => s.trim());\n      if (property && value) {\n        const jsProperty = CSS_PROPERTY_MAPPING[property as keyof typeof CSS_PROPERTY_MAPPING] || \n                          property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n        styles[jsProperty] = value;\n      }\n    });\n  }\n\n  return styles;\n}\n\n// Parse background images from data attribute\nexport function parseBackgroundImages(backgroundImagesData: string): string[] {\n  try {\n    const data = JSON.parse(backgroundImagesData);\n    if (data && data.desktop_image) {\n      return [data.desktop_image, data.mobile_image].filter(Boolean);\n    }\n    return [];\n  } catch {\n    return [];\n  }\n}\n\n// Convert CSS value to number\nexport function cssValueToNumber(value: string): number {\n  return parseFloat(value.replace(/[^\\d.-]/g, ''));\n}\n\n// Convert number to CSS value with unit\nexport function numberToCssValue(value: number, unit: string = 'px'): string {\n  return `${value}${unit}`;\n}\n\n// Check if element is a Page Builder element\nexport function isPageBuilderElement(element: Element): boolean {\n  return element.hasAttribute('data-content-type') || \n         Array.from(element.classList).some(className => \n           className.startsWith('pagebuilder-')\n         );\n}\n\n// Get element content (text or HTML)\nexport function getElementContent(element: Element, preserveHtml: boolean = false): string {\n  if (preserveHtml) {\n    return element.innerHTML;\n  }\n  return element.textContent || '';\n}\n\n// Clean HTML content\nexport function cleanHtmlContent(html: string): string {\n  // Remove Magento-specific attributes that aren't needed in React\n  return html\n    .replace(/data-pb-style=\"[^\"]*\"/g, '')\n    .replace(/data-element=\"[^\"]*\"/g, '')\n    .replace(/class=\"[^\"]*pagebuilder-[^\"]*\"/g, '')\n    .trim();\n}\n\n// Merge configurations\nexport function mergeConfig(\n  userConfig: Partial<PageBuilderParserConfig> = {}\n): PageBuilderParserConfig {\n  return {\n    ...DEFAULT_CONFIG,\n    ...userConfig,\n    customElementParsers: {\n      ...DEFAULT_CONFIG.customElementParsers,\n      ...userConfig.customElementParsers,\n    },\n    componentOverrides: {\n      ...DEFAULT_CONFIG.componentOverrides,\n      ...userConfig.componentOverrides,\n    },\n  };\n}\n\n// Convert Magento URL to Next.js optimized URL\nexport function optimizeImageUrl(url: string, width?: number, height?: number): string {\n  if (!url) return '';\n  \n  // If it's already a Next.js optimized URL, return as is\n  if (url.includes('/_next/image')) {\n    return url;\n  }\n\n  // Convert Magento media URLs\n  if (url.includes('/media/')) {\n    // Remove any existing resize parameters\n    const cleanUrl = url.split('?')[0];\n    \n    // Add Next.js image optimization parameters\n    const params = new URLSearchParams();\n    params.set('url', cleanUrl);\n    if (width) params.set('w', width.toString());\n    if (height) params.set('h', height.toString());\n    params.set('q', '75'); // Default quality\n    \n    return `/_next/image?${params.toString()}`;\n  }\n\n  return url;\n}\n\n// Validate Page Builder element structure\nexport function validateElement(element: PageBuilderElement): boolean {\n  if (!element.type || !element.id) {\n    return false;\n  }\n\n  // Type-specific validation\n  switch (element.type) {\n    case PageBuilderElementType.IMAGE:\n      return !!(element.attributes?.src);\n    \n    case PageBuilderElementType.BUTTON:\n      return !!(element.content || element.attributes?.buttonText);\n    \n    case PageBuilderElementType.VIDEO:\n      return !!(element.attributes?.videoUrl || element.attributes?.src);\n    \n    default:\n      return true;\n  }\n}\n\n// Get responsive breakpoint\nexport function getBreakpoint(width: number): 'mobile' | 'tablet' | 'desktop' {\n  if (width < 768) return 'mobile';\n  if (width < 1024) return 'tablet';\n  return 'desktop';\n}\n\n// Convert Magento column width to CSS grid\nexport function convertColumnWidth(width: string): string {\n  // Magento uses percentages like \"33.3333%\"\n  const percentage = parseFloat(width);\n  if (percentage > 0) {\n    return `${percentage}%`;\n  }\n  \n  // Fallback to equal distribution\n  return '1fr';\n}\n\n// Parse JSON safely\nexport function safeJsonParse<T>(json: string, fallback: T): T {\n  try {\n    return JSON.parse(json);\n  } catch {\n    return fallback;\n  }\n}\n\n// Debounce function for performance optimization\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Check if code is running in browser\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\n// Log Page Builder parsing errors in development\nexport function logParsingError(error: Error, element?: Element): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.error('Page Builder parsing error:', error);\n    if (element) {\n      console.error('Element:', element);\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;;;;;;;;;;;;;;;;AA6S7B;AA3SN;AAKA;;;AAQO,SAAS;IACd,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACtE;AAGO,SAAS,eAAe,OAAgB;IAC7C,0CAA0C;IAC1C,MAAM,cAAc,QAAQ,YAAY,CAAC;IACzC,IAAI,eAAe,yIAAA,CAAA,uBAAoB,CAAC,YAAY,EAAE;QACpD,OAAO,yIAAA,CAAA,uBAAoB,CAAC,YAAY;IAC1C;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,SAAS;IAC9C,KAAK,MAAM,aAAa,UAAW;QACjC,IAAI,yIAAA,CAAA,uBAAoB,CAAC,UAAU,EAAE;YACnC,OAAO,yIAAA,CAAA,uBAAoB,CAAC,UAAU;QACxC;IACF;IAEA,sCAAsC;IACtC,IAAI,QAAQ,OAAO,KAAK,OAAO;QAC7B,OAAO,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IACrC;IAEA,IAAI,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,KAAK,UAAU;QAC/D,OAAO,qIAAA,CAAA,yBAAsB,CAAC,KAAK;IACrC;IAEA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC,QAAQ,CAAC,QAAQ,OAAO,GAAG;QAClE,OAAO,qIAAA,CAAA,yBAAsB,CAAC,OAAO;IACvC;IAEA,IAAI,QAAQ,OAAO,KAAK,OAAO,QAAQ,SAAS,CAAC,QAAQ,CAAC,+BAA+B;QACvF,OAAO,qIAAA,CAAA,yBAAsB,CAAC,MAAM;IACtC;IAEA,uCAAuC;IACvC,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,IAAI,IAAI;QACrD,OAAO,qIAAA,CAAA,yBAAsB,CAAC,IAAI;IACpC;IAEA,OAAO;AACT;AAGO,SAAS,kBAAkB,OAAgB;IAChD,MAAM,aAAkC,CAAC;IAEzC,0BAA0B;IAC1B,MAAM,IAAI,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC,CAAA;QACrC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU;YACjC,MAAM,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,aAAa,CAAC,GAAG,SAAW,OAAO,WAAW;YACjG,UAAU,CAAC,IAAI,GAAG,KAAK,KAAK;QAC9B;IACF;IAEA,iCAAiC;IACjC,IAAI,QAAQ,YAAY,CAAC,OAAO;QAC9B,WAAW,EAAE,GAAG,QAAQ,YAAY,CAAC;IACvC;IAEA,IAAI,QAAQ,YAAY,CAAC,UAAU;QACjC,WAAW,SAAS,GAAG,QAAQ,YAAY,CAAC;IAC9C;IAEA,oDAAoD;IACpD,IAAI,QAAQ,OAAO,KAAK,OAAO;QAC7B,WAAW,GAAG,GAAG,QAAQ,YAAY,CAAC;QACtC,WAAW,GAAG,GAAG,QAAQ,YAAY,CAAC;QACtC,WAAW,KAAK,GAAG,QAAQ,YAAY,CAAC;IAC1C;IAEA,IAAI,QAAQ,OAAO,KAAK,KAAK;QAC3B,WAAW,IAAI,GAAG,QAAQ,YAAY,CAAC;QACvC,WAAW,MAAM,GAAG,QAAQ,YAAY,CAAC;IAC3C;IAEA,IAAI,QAAQ,OAAO,KAAK,SAAS;QAC/B,WAAW,GAAG,GAAG,QAAQ,YAAY,CAAC;QACtC,WAAW,QAAQ,GAAG,QAAQ,YAAY,CAAC;QAC3C,WAAW,KAAK,GAAG,QAAQ,YAAY,CAAC;QACxC,WAAW,IAAI,GAAG,QAAQ,YAAY,CAAC;QACvC,WAAW,QAAQ,GAAG,QAAQ,YAAY,CAAC;IAC7C;IAEA,OAAO;AACT;AAGO,SAAS,cAAc,OAAgB;IAC5C,MAAM,SAA8B,CAAC;IAErC,sBAAsB;IACtB,wCAAmC;QACjC,MAAM,iBAAiB,OAAO,gBAAgB,CAAC;QAE/C,kCAAkC;QAClC,OAAO,OAAO,CAAC,yIAAA,CAAA,uBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC,aAAa,WAAW;YACrE,MAAM,QAAQ,eAAe,gBAAgB,CAAC;YAC9C,IAAI,SAAS,UAAU,aAAa,UAAU,WAAW;gBACvD,MAAM,CAAC,WAAW,GAAG;YACvB;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,QAAQ,YAAY,CAAC;IACzC,IAAI,aAAa;QACf,MAAM,oBAAoB,YAAY,KAAK,CAAC;QAC5C,kBAAkB,OAAO,CAAC,CAAA;YACxB,MAAM,CAAC,UAAU,MAAM,GAAG,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAChE,IAAI,YAAY,OAAO;gBACrB,MAAM,aAAa,yIAAA,CAAA,uBAAoB,CAAC,SAA8C,IACpE,SAAS,OAAO,CAAC,aAAa,CAAC,GAAG,SAAW,OAAO,WAAW;gBACjF,MAAM,CAAC,WAAW,GAAG;YACvB;QACF;IACF;IAEA,OAAO;AACT;AAGO,SAAS,sBAAsB,oBAA4B;IAChE,IAAI;QACF,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,QAAQ,KAAK,aAAa,EAAE;YAC9B,OAAO;gBAAC,KAAK,aAAa;gBAAE,KAAK,YAAY;aAAC,CAAC,MAAM,CAAC;QACxD;QACA,OAAO,EAAE;IACX,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,WAAW,MAAM,OAAO,CAAC,YAAY;AAC9C;AAGO,SAAS,iBAAiB,KAAa,EAAE,OAAe,IAAI;IACjE,OAAO,GAAG,QAAQ,MAAM;AAC1B;AAGO,SAAS,qBAAqB,OAAgB;IACnD,OAAO,QAAQ,YAAY,CAAC,wBACrB,MAAM,IAAI,CAAC,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAA,YACjC,UAAU,UAAU,CAAC;AAEhC;AAGO,SAAS,kBAAkB,OAAgB,EAAE,eAAwB,KAAK;IAC/E,IAAI,cAAc;QAChB,OAAO,QAAQ,SAAS;IAC1B;IACA,OAAO,QAAQ,WAAW,IAAI;AAChC;AAGO,SAAS,iBAAiB,IAAY;IAC3C,iEAAiE;IACjE,OAAO,KACJ,OAAO,CAAC,0BAA0B,IAClC,OAAO,CAAC,yBAAyB,IACjC,OAAO,CAAC,mCAAmC,IAC3C,IAAI;AACT;AAGO,SAAS,YACd,aAA+C,CAAC,CAAC;IAEjD,OAAO;QACL,GAAG,yIAAA,CAAA,iBAAc;QACjB,GAAG,UAAU;QACb,sBAAsB;YACpB,GAAG,yIAAA,CAAA,iBAAc,CAAC,oBAAoB;YACtC,GAAG,WAAW,oBAAoB;QACpC;QACA,oBAAoB;YAClB,GAAG,yIAAA,CAAA,iBAAc,CAAC,kBAAkB;YACpC,GAAG,WAAW,kBAAkB;QAClC;IACF;AACF;AAGO,SAAS,iBAAiB,GAAW,EAAE,KAAc,EAAE,MAAe;IAC3E,IAAI,CAAC,KAAK,OAAO;IAEjB,wDAAwD;IACxD,IAAI,IAAI,QAAQ,CAAC,iBAAiB;QAChC,OAAO;IACT;IAEA,6BAA6B;IAC7B,IAAI,IAAI,QAAQ,CAAC,YAAY;QAC3B,wCAAwC;QACxC,MAAM,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;QAElC,4CAA4C;QAC5C,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,OAAO;QAClB,IAAI,OAAO,OAAO,GAAG,CAAC,KAAK,MAAM,QAAQ;QACzC,IAAI,QAAQ,OAAO,GAAG,CAAC,KAAK,OAAO,QAAQ;QAC3C,OAAO,GAAG,CAAC,KAAK,OAAO,kBAAkB;QAEzC,OAAO,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;IAC5C;IAEA,OAAO;AACT;AAGO,SAAS,gBAAgB,OAA2B;IACzD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;QAChC,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAQ,QAAQ,IAAI;QAClB,KAAK,qIAAA,CAAA,yBAAsB,CAAC,KAAK;YAC/B,OAAO,CAAC,CAAE,QAAQ,UAAU,EAAE;QAEhC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,MAAM;YAChC,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO,IAAI,QAAQ,UAAU,EAAE,UAAU;QAE7D,KAAK,qIAAA,CAAA,yBAAsB,CAAC,KAAK;YAC/B,OAAO,CAAC,CAAC,CAAC,QAAQ,UAAU,EAAE,YAAY,QAAQ,UAAU,EAAE,GAAG;QAEnE;YACE,OAAO;IACX;AACF;AAGO,SAAS,cAAc,KAAa;IACzC,IAAI,QAAQ,KAAK,OAAO;IACxB,IAAI,QAAQ,MAAM,OAAO;IACzB,OAAO;AACT;AAGO,SAAS,mBAAmB,KAAa;IAC9C,2CAA2C;IAC3C,MAAM,aAAa,WAAW;IAC9B,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,CAAC,CAAC;IACzB;IAEA,iCAAiC;IACjC,OAAO;AACT;AAGO,SAAS,cAAiB,IAAY,EAAE,QAAW;IACxD,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,aAAkB;AAC3B;AAGO,SAAS,gBAAgB,KAAY,EAAE,OAAiB;IAC7D,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,IAAI,SAAS;YACX,QAAQ,KAAK,CAAC,YAAY;QAC5B;IACF;AACF", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Row.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Container } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { RowElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface RowProps extends PageBuilderElementProps {\n  element: RowElement;\n}\n\nexport const Row: React.FC<RowProps> = ({ element, children, className, style }) => {\n  const { attributes, styles } = element;\n\n  // Determine container type based on appearance\n  const getContainerComponent = () => {\n    switch (attributes.appearance) {\n      case 'contained':\n        return Container;\n      case 'full-width':\n      case 'full-bleed':\n      default:\n        return Box;\n    }\n  };\n\n  const ContainerComponent = getContainerComponent();\n\n  // Build styles\n  const rowStyles: React.CSSProperties = {\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    width: '100%',\n    boxSizing: 'border-box',\n    ...styles,\n    ...style,\n  };\n\n  // Background styles\n  if (attributes.backgroundColor) {\n    rowStyles.backgroundColor = attributes.backgroundColor;\n  }\n\n  if (attributes.backgroundImage) {\n    rowStyles.backgroundImage = `url(${attributes.backgroundImage})`;\n    rowStyles.backgroundSize = attributes.backgroundSize || 'cover';\n    rowStyles.backgroundPosition = attributes.backgroundPosition || 'center center';\n    rowStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';\n    rowStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';\n  }\n\n  if (attributes.minHeight) {\n    rowStyles.minHeight = attributes.minHeight;\n  }\n\n  // Vertical alignment\n  if (attributes.verticalAlignment) {\n    switch (attributes.verticalAlignment) {\n      case 'top':\n        rowStyles.justifyContent = 'flex-start';\n        break;\n      case 'middle':\n        rowStyles.justifyContent = 'center';\n        break;\n      case 'bottom':\n        rowStyles.justifyContent = 'flex-end';\n        break;\n    }\n  }\n\n  // Parallax effect (if enabled and in browser)\n  const parallaxProps = attributes.enableParallax && typeof window !== 'undefined' ? {\n    initial: { y: 0 },\n    whileInView: { y: -20 },\n    transition: { \n      duration: 0.6,\n      ease: 'easeOut'\n    },\n    viewport: { once: false, amount: 0.3 }\n  } : {};\n\n  const containerProps = attributes.appearance === 'contained' ? {\n    maxWidth: 'lg' as const,\n    sx: { px: { xs: 2, sm: 3 } }\n  } : {\n    sx: { \n      width: '100%',\n      maxWidth: 'none',\n      px: attributes.appearance === 'full-bleed' ? 0 : { xs: 2, sm: 3 }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`pagebuilder-row ${className || ''}`}\n      style={rowStyles}\n      {...parallaxProps}\n    >\n      {/* Background overlay for better text readability */}\n      {attributes.backgroundImage && (\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.1)',\n            zIndex: 0,\n          }}\n        />\n      )}\n      \n      <ContainerComponent\n        {...containerProps}\n        sx={{\n          position: 'relative',\n          zIndex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          width: '100%',\n          height: '100%',\n          ...containerProps.sx,\n        }}\n      >\n        {children}\n      </ContainerComponent>\n    </motion.div>\n  );\n};\n\nexport default Row;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAJA;;;;AAWO,MAAM,MAA0B,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE;IAC7E,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAE/B,+CAA+C;IAC/C,MAAM,wBAAwB;QAC5B,OAAQ,WAAW,UAAU;YAC3B,KAAK;gBACH,OAAO,6MAAA,CAAA,YAAS;YAClB,KAAK;YACL,KAAK;YACL;gBACE,OAAO,2LAAA,CAAA,MAAG;QACd;IACF;IAEA,MAAM,qBAAqB;IAE3B,eAAe;IACf,MAAM,YAAiC;QACrC,UAAU;QACV,SAAS;QACT,eAAe;QACf,OAAO;QACP,WAAW;QACX,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,oBAAoB;IACpB,IAAI,WAAW,eAAe,EAAE;QAC9B,UAAU,eAAe,GAAG,WAAW,eAAe;IACxD;IAEA,IAAI,WAAW,eAAe,EAAE;QAC9B,UAAU,eAAe,GAAG,CAAC,IAAI,EAAE,WAAW,eAAe,CAAC,CAAC,CAAC;QAChE,UAAU,cAAc,GAAG,WAAW,cAAc,IAAI;QACxD,UAAU,kBAAkB,GAAG,WAAW,kBAAkB,IAAI;QAChE,UAAU,gBAAgB,GAAG,WAAW,gBAAgB,IAAI;QAC5D,UAAU,oBAAoB,GAAG,WAAW,oBAAoB,IAAI;IACtE;IAEA,IAAI,WAAW,SAAS,EAAE;QACxB,UAAU,SAAS,GAAG,WAAW,SAAS;IAC5C;IAEA,qBAAqB;IACrB,IAAI,WAAW,iBAAiB,EAAE;QAChC,OAAQ,WAAW,iBAAiB;YAClC,KAAK;gBACH,UAAU,cAAc,GAAG;gBAC3B;YACF,KAAK;gBACH,UAAU,cAAc,GAAG;gBAC3B;YACF,KAAK;gBACH,UAAU,cAAc,GAAG;gBAC3B;QACJ;IACF;IAEA,8CAA8C;IAC9C,MAAM,gBAAgB,WAAW,cAAc,IAAI,aAAkB,cAAc;QACjF,SAAS;YAAE,GAAG;QAAE;QAChB,aAAa;YAAE,GAAG,CAAC;QAAG;QACtB,YAAY;YACV,UAAU;YACV,MAAM;QACR;QACA,UAAU;YAAE,MAAM;YAAO,QAAQ;QAAI;IACvC,IAAI,CAAC;IAEL,MAAM,iBAAiB,WAAW,UAAU,KAAK,cAAc;QAC7D,UAAU;QACV,IAAI;YAAE,IAAI;gBAAE,IAAI;gBAAG,IAAI;YAAE;QAAE;IAC7B,IAAI;QACF,IAAI;YACF,OAAO;YACP,UAAU;YACV,IAAI,WAAW,UAAU,KAAK,eAAe,IAAI;gBAAE,IAAI;gBAAG,IAAI;YAAE;QAClE;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gBAAgB,EAAE,aAAa,IAAI;QAC/C,OAAO;QACN,GAAG,aAAa;;YAGhB,WAAW,eAAe,kBACzB,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;;;;;;0BAIJ,6LAAC;gBACE,GAAG,cAAc;gBAClB,IAAI;oBACF,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,eAAe;oBACf,OAAO;oBACP,QAAQ;oBACR,GAAG,eAAe,EAAE;gBACtB;0BAEC;;;;;;;;;;;;AAIT;KAvHa;uCAyHE", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Column.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { ColumnElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\nimport { convertColumnWidth } from '@/lib/pagebuilder/utils';\n\ninterface ColumnProps extends PageBuilderElementProps {\n  element: ColumnElement;\n}\n\nexport const Column: React.FC<ColumnProps> = ({ element, children, className, style }) => {\n  const { attributes, styles } = element;\n\n  // Build styles\n  const columnStyles: React.CSSProperties = {\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    boxSizing: 'border-box',\n    flex: '1 1 auto',\n    ...styles,\n    ...style,\n  };\n\n  // Width handling\n  if (attributes.width) {\n    const width = convertColumnWidth(attributes.width);\n    columnStyles.width = width;\n    columnStyles.flexBasis = width;\n    columnStyles.flexGrow = 0;\n    columnStyles.flexShrink = 0;\n  }\n\n  // Background styles\n  if (attributes.backgroundColor) {\n    columnStyles.backgroundColor = attributes.backgroundColor;\n  }\n\n  if (attributes.backgroundImage) {\n    columnStyles.backgroundImage = `url(${attributes.backgroundImage})`;\n    columnStyles.backgroundSize = attributes.backgroundSize || 'cover';\n    columnStyles.backgroundPosition = attributes.backgroundPosition || 'center center';\n    columnStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';\n    columnStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';\n  }\n\n  // Height handling\n  if (attributes.appearance === 'full-height') {\n    columnStyles.height = '100%';\n  } else if (attributes.minHeight) {\n    columnStyles.minHeight = attributes.minHeight;\n  }\n\n  // Vertical alignment\n  if (attributes.verticalAlignment) {\n    switch (attributes.verticalAlignment) {\n      case 'top':\n        columnStyles.justifyContent = 'flex-start';\n        break;\n      case 'middle':\n        columnStyles.justifyContent = 'center';\n        break;\n      case 'bottom':\n        columnStyles.justifyContent = 'flex-end';\n        break;\n    }\n  }\n\n  // Animation variants\n  const columnVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`pagebuilder-column ${className || ''}`}\n      style={columnStyles}\n      variants={columnVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      {/* Background overlay for better content readability */}\n      {attributes.backgroundImage && (\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0, 0, 0, 0.05)',\n            zIndex: 0,\n          }}\n        />\n      )}\n      \n      <Box\n        sx={{\n          position: 'relative',\n          zIndex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          width: '100%',\n          height: '100%',\n          padding: { xs: 1, sm: 2 },\n        }}\n      >\n        {children}\n      </Box>\n    </motion.div>\n  );\n};\n\nexport default Column;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAEA;AANA;;;;;AAYO,MAAM,SAAgC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE;IACnF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAE/B,eAAe;IACf,MAAM,eAAoC;QACxC,UAAU;QACV,SAAS;QACT,eAAe;QACf,WAAW;QACX,MAAM;QACN,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,iBAAiB;IACjB,IAAI,WAAW,KAAK,EAAE;QACpB,MAAM,QAAQ,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,KAAK;QACjD,aAAa,KAAK,GAAG;QACrB,aAAa,SAAS,GAAG;QACzB,aAAa,QAAQ,GAAG;QACxB,aAAa,UAAU,GAAG;IAC5B;IAEA,oBAAoB;IACpB,IAAI,WAAW,eAAe,EAAE;QAC9B,aAAa,eAAe,GAAG,WAAW,eAAe;IAC3D;IAEA,IAAI,WAAW,eAAe,EAAE;QAC9B,aAAa,eAAe,GAAG,CAAC,IAAI,EAAE,WAAW,eAAe,CAAC,CAAC,CAAC;QACnE,aAAa,cAAc,GAAG,WAAW,cAAc,IAAI;QAC3D,aAAa,kBAAkB,GAAG,WAAW,kBAAkB,IAAI;QACnE,aAAa,gBAAgB,GAAG,WAAW,gBAAgB,IAAI;QAC/D,aAAa,oBAAoB,GAAG,WAAW,oBAAoB,IAAI;IACzE;IAEA,kBAAkB;IAClB,IAAI,WAAW,UAAU,KAAK,eAAe;QAC3C,aAAa,MAAM,GAAG;IACxB,OAAO,IAAI,WAAW,SAAS,EAAE;QAC/B,aAAa,SAAS,GAAG,WAAW,SAAS;IAC/C;IAEA,qBAAqB;IACrB,IAAI,WAAW,iBAAiB,EAAE;QAChC,OAAQ,WAAW,iBAAiB;YAClC,KAAK;gBACH,aAAa,cAAc,GAAG;gBAC9B;YACF,KAAK;gBACH,aAAa,cAAc,GAAG;gBAC9B;YACF,KAAK;gBACH,aAAa,cAAc,GAAG;gBAC9B;QACJ;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mBAAmB,EAAE,aAAa,IAAI;QAClD,OAAO;QACP,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;YAGnC,WAAW,eAAe,kBACzB,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;;;;;;0BAIJ,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,eAAe;oBACf,OAAO;oBACP,QAAQ;oBACR,SAAS;wBAAE,IAAI;wBAAG,IAAI;oBAAE;gBAC1B;0BAEC;;;;;;;;;;;;AAIT;KA9Ga;uCAgHE", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Text.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Typography } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { TextElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface TextProps extends PageBuilderElementProps {\n  element: TextElement;\n}\n\nexport const Text: React.FC<TextProps> = ({ element, className, style }) => {\n  const { content, attributes, styles } = element;\n\n  // Build styles\n  const textStyles: React.CSSProperties = {\n    wordWrap: 'break-word',\n    lineHeight: 1.6,\n    ...styles,\n    ...style,\n  };\n\n  // Text alignment\n  if (attributes.textAlign) {\n    textStyles.textAlign = attributes.textAlign as any;\n  }\n\n  // Border styles\n  if (attributes.border) {\n    textStyles.border = attributes.border;\n  }\n  if (attributes.borderColor) {\n    textStyles.borderColor = attributes.borderColor;\n  }\n  if (attributes.borderWidth) {\n    textStyles.borderWidth = attributes.borderWidth;\n  }\n  if (attributes.borderRadius) {\n    textStyles.borderRadius = attributes.borderRadius;\n  }\n\n  // Animation variants\n  const textVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  // Check if content contains HTML\n  const isHtml = content && (content.includes('<') || content.includes('&'));\n\n  return (\n    <motion.div\n      className={`pagebuilder-text ${className || ''}`}\n      variants={textVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      {isHtml ? (\n        <Box\n          component=\"div\"\n          sx={textStyles}\n          dangerouslySetInnerHTML={{ __html: content }}\n        />\n      ) : (\n        <Typography\n          component=\"div\"\n          sx={textStyles}\n        >\n          {content}\n        </Typography>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Text;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAJA;;;;AAWO,MAAM,OAA4B,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IACrE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAExC,eAAe;IACf,MAAM,aAAkC;QACtC,UAAU;QACV,YAAY;QACZ,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,iBAAiB;IACjB,IAAI,WAAW,SAAS,EAAE;QACxB,WAAW,SAAS,GAAG,WAAW,SAAS;IAC7C;IAEA,gBAAgB;IAChB,IAAI,WAAW,MAAM,EAAE;QACrB,WAAW,MAAM,GAAG,WAAW,MAAM;IACvC;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,WAAW,WAAW,GAAG,WAAW,WAAW;IACjD;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,WAAW,WAAW,GAAG,WAAW,WAAW;IACjD;IACA,IAAI,WAAW,YAAY,EAAE;QAC3B,WAAW,YAAY,GAAG,WAAW,YAAY;IACnD;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM,SAAS,WAAW,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,IAAI;IAEzE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,iBAAiB,EAAE,aAAa,IAAI;QAChD,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEnC,uBACC,6LAAC,2LAAA,CAAA,MAAG;YACF,WAAU;YACV,IAAI;YACJ,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;iCAG7C,6LAAC,gNAAA,CAAA,aAAU;YACT,WAAU;YACV,IAAI;sBAEH;;;;;;;;;;;AAKX;KAtEa;uCAwEE", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Heading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Typography } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { HeadingElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface HeadingProps extends PageBuilderElementProps {\n  element: HeadingElement;\n}\n\nexport const Heading: React.FC<HeadingProps> = ({ element, className, style }) => {\n  const { content, attributes, styles } = element;\n\n  // Determine heading variant\n  const variant = attributes.headingType || 'h2';\n\n  // Build styles\n  const headingStyles: React.CSSProperties = {\n    wordWrap: 'break-word',\n    marginBottom: '1rem',\n    ...styles,\n    ...style,\n  };\n\n  // Text alignment\n  if (attributes.textAlign) {\n    headingStyles.textAlign = attributes.textAlign as any;\n  }\n\n  // Border styles\n  if (attributes.border) {\n    headingStyles.border = attributes.border;\n  }\n  if (attributes.borderColor) {\n    headingStyles.borderColor = attributes.borderColor;\n  }\n  if (attributes.borderWidth) {\n    headingStyles.borderWidth = attributes.borderWidth;\n  }\n  if (attributes.borderRadius) {\n    headingStyles.borderRadius = attributes.borderRadius;\n  }\n\n  // Animation variants\n  const headingVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.8,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  // Check if content contains HTML\n  const isHtml = content && (content.includes('<') || content.includes('&'));\n\n  return (\n    <motion.div\n      className={`pagebuilder-heading ${className || ''}`}\n      variants={headingVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      {isHtml ? (\n        <Typography\n          variant={variant as any}\n          component={variant}\n          sx={headingStyles}\n          dangerouslySetInnerHTML={{ __html: content }}\n        />\n      ) : (\n        <Typography\n          variant={variant as any}\n          component={variant}\n          sx={headingStyles}\n        >\n          {content}\n        </Typography>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Heading;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAWO,MAAM,UAAkC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IAC3E,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAExC,4BAA4B;IAC5B,MAAM,UAAU,WAAW,WAAW,IAAI;IAE1C,eAAe;IACf,MAAM,gBAAqC;QACzC,UAAU;QACV,cAAc;QACd,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,iBAAiB;IACjB,IAAI,WAAW,SAAS,EAAE;QACxB,cAAc,SAAS,GAAG,WAAW,SAAS;IAChD;IAEA,gBAAgB;IAChB,IAAI,WAAW,MAAM,EAAE;QACrB,cAAc,MAAM,GAAG,WAAW,MAAM;IAC1C;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,cAAc,WAAW,GAAG,WAAW,WAAW;IACpD;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,cAAc,WAAW,GAAG,WAAW,WAAW;IACpD;IACA,IAAI,WAAW,YAAY,EAAE;QAC3B,cAAc,YAAY,GAAG,WAAW,YAAY;IACtD;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM,SAAS,WAAW,CAAC,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,IAAI;IAEzE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,oBAAoB,EAAE,aAAa,IAAI;QACnD,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEnC,uBACC,6LAAC,gNAAA,CAAA,aAAU;YACT,SAAS;YACT,WAAW;YACX,IAAI;YACJ,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;iCAG7C,6LAAC,gNAAA,CAAA,aAAU;YACT,SAAS;YACT,WAAW;YACX,IAAI;sBAEH;;;;;;;;;;;AAKX;KA3Ea;uCA6EE", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Image.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ImageElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\nimport { optimizeImageUrl } from '@/lib/pagebuilder/utils';\n\ninterface ImageProps extends PageBuilderElementProps {\n  element: ImageElement;\n}\n\nexport const PageBuilderImage: React.FC<ImageProps> = ({ element, className, style }) => {\n  const { attributes, styles } = element;\n\n  // Build styles\n  const imageContainerStyles: React.CSSProperties = {\n    position: 'relative',\n    display: 'inline-block',\n    maxWidth: '100%',\n    ...styles,\n    ...style,\n  };\n\n  // Alignment\n  if (attributes.alignment) {\n    switch (attributes.alignment) {\n      case 'left':\n        imageContainerStyles.textAlign = 'left';\n        break;\n      case 'center':\n        imageContainerStyles.textAlign = 'center';\n        imageContainerStyles.margin = '0 auto';\n        break;\n      case 'right':\n        imageContainerStyles.textAlign = 'right';\n        imageContainerStyles.marginLeft = 'auto';\n        break;\n    }\n  }\n\n  // Border styles\n  const imageStyles: React.CSSProperties = {\n    maxWidth: '100%',\n    height: 'auto',\n  };\n\n  if (attributes.border) {\n    imageStyles.border = attributes.border;\n  }\n  if (attributes.borderColor) {\n    imageStyles.borderColor = attributes.borderColor;\n  }\n  if (attributes.borderWidth) {\n    imageStyles.borderWidth = attributes.borderWidth;\n  }\n  if (attributes.borderRadius) {\n    imageStyles.borderRadius = attributes.borderRadius;\n  }\n\n  // Animation variants\n  const imageVariants = {\n    hidden: { opacity: 0, scale: 0.95 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  // Optimize image URL\n  const optimizedSrc = optimizeImageUrl(attributes.src);\n\n  // Image component\n  const ImageComponent = (\n    <motion.div\n      variants={imageVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n      whileHover={{ scale: 1.02 }}\n      transition={{ duration: 0.3 }}\n    >\n      <Box\n        component=\"div\"\n        sx={{\n          position: 'relative',\n          display: 'inline-block',\n          ...imageStyles,\n        }}\n      >\n        <Image\n          src={optimizedSrc}\n          alt={attributes.alt || ''}\n          title={attributes.title}\n          width={800} // Default width, will be responsive\n          height={600} // Default height, will be responsive\n          style={{\n            width: '100%',\n            height: 'auto',\n            ...imageStyles,\n          }}\n          loading={attributes.lazyLoading !== false ? 'lazy' : 'eager'}\n          quality={85}\n        />\n        \n        {/* Caption */}\n        {attributes.caption && (\n          <Box\n            component=\"figcaption\"\n            sx={{\n              mt: 1,\n              fontSize: '0.875rem',\n              color: 'text.secondary',\n              textAlign: attributes.alignment || 'center',\n              fontStyle: 'italic',\n            }}\n          >\n            {attributes.caption}\n          </Box>\n        )}\n      </Box>\n    </motion.div>\n  );\n\n  return (\n    <Box\n      className={`pagebuilder-image ${className || ''}`}\n      component=\"figure\"\n      sx={{\n        margin: 0,\n        padding: 0,\n        ...imageContainerStyles,\n      }}\n    >\n      {attributes.link ? (\n        <Link\n          href={attributes.link}\n          target={attributes.linkTarget || '_self'}\n          style={{ textDecoration: 'none' }}\n        >\n          {ImageComponent}\n        </Link>\n      ) : (\n        ImageComponent\n      )}\n    </Box>\n  );\n};\n\nexport default PageBuilderImage;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAEA;AARA;;;;;;;AAcO,MAAM,mBAAyC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IAClF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAE/B,eAAe;IACf,MAAM,uBAA4C;QAChD,UAAU;QACV,SAAS;QACT,UAAU;QACV,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,YAAY;IACZ,IAAI,WAAW,SAAS,EAAE;QACxB,OAAQ,WAAW,SAAS;YAC1B,KAAK;gBACH,qBAAqB,SAAS,GAAG;gBACjC;YACF,KAAK;gBACH,qBAAqB,SAAS,GAAG;gBACjC,qBAAqB,MAAM,GAAG;gBAC9B;YACF,KAAK;gBACH,qBAAqB,SAAS,GAAG;gBACjC,qBAAqB,UAAU,GAAG;gBAClC;QACJ;IACF;IAEA,gBAAgB;IAChB,MAAM,cAAmC;QACvC,UAAU;QACV,QAAQ;IACV;IAEA,IAAI,WAAW,MAAM,EAAE;QACrB,YAAY,MAAM,GAAG,WAAW,MAAM;IACxC;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,YAAY,WAAW,GAAG,WAAW,WAAW;IAClD;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,YAAY,WAAW,GAAG,WAAW,WAAW;IAClD;IACA,IAAI,WAAW,YAAY,EAAE;QAC3B,YAAY,YAAY,GAAG,WAAW,YAAY;IACpD;IAEA,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAK;QAClC,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,GAAG;IAEpD,kBAAkB;IAClB,MAAM,+BACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY;YAAE,OAAO;QAAK;QAC1B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,WAAU;YACV,IAAI;gBACF,UAAU;gBACV,SAAS;gBACT,GAAG,WAAW;YAChB;;8BAEA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAK,WAAW,GAAG,IAAI;oBACvB,OAAO,WAAW,KAAK;oBACvB,OAAO;oBACP,QAAQ;oBACR,OAAO;wBACL,OAAO;wBACP,QAAQ;wBACR,GAAG,WAAW;oBAChB;oBACA,SAAS,WAAW,WAAW,KAAK,QAAQ,SAAS;oBACrD,SAAS;;;;;;gBAIV,WAAW,OAAO,kBACjB,6LAAC,2LAAA,CAAA,MAAG;oBACF,WAAU;oBACV,IAAI;wBACF,IAAI;wBACJ,UAAU;wBACV,OAAO;wBACP,WAAW,WAAW,SAAS,IAAI;wBACnC,WAAW;oBACb;8BAEC,WAAW,OAAO;;;;;;;;;;;;;;;;;IAO7B,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,WAAW,CAAC,kBAAkB,EAAE,aAAa,IAAI;QACjD,WAAU;QACV,IAAI;YACF,QAAQ;YACR,SAAS;YACT,GAAG,oBAAoB;QACzB;kBAEC,WAAW,IAAI,iBACd,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,WAAW,IAAI;YACrB,QAAQ,WAAW,UAAU,IAAI;YACjC,OAAO;gBAAE,gBAAgB;YAAO;sBAE/B;;;;;mBAGH;;;;;;AAIR;KA3Ia;uCA6IE", "debugId": null}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ButtonElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface ButtonProps extends PageBuilderElementProps {\n  element: ButtonElement;\n}\n\nexport const Button: React.FC<ButtonProps> = ({ element, className, style }) => {\n  const { content, attributes, styles } = element;\n\n  // Build container styles\n  const containerStyles: React.CSSProperties = {\n    display: 'flex',\n    ...styles,\n    ...style,\n  };\n\n  // Text alignment for container\n  if (attributes.textAlign) {\n    switch (attributes.textAlign) {\n      case 'left':\n        containerStyles.justifyContent = 'flex-start';\n        break;\n      case 'center':\n        containerStyles.justifyContent = 'center';\n        break;\n      case 'right':\n        containerStyles.justifyContent = 'flex-end';\n        break;\n    }\n  }\n\n  // Build button styles\n  const buttonStyles: React.CSSProperties = {\n    textTransform: 'none',\n    fontWeight: 500,\n    borderRadius: '8px',\n    padding: '12px 24px',\n    fontSize: '1rem',\n    transition: 'all 0.3s ease',\n  };\n\n  // Button type styling\n  let variant: 'contained' | 'outlined' | 'text' = 'contained';\n  let color: 'primary' | 'secondary' | 'inherit' = 'primary';\n\n  switch (attributes.buttonType) {\n    case 'primary':\n      variant = 'contained';\n      color = 'primary';\n      break;\n    case 'secondary':\n      variant = 'outlined';\n      color = 'primary';\n      break;\n    case 'link':\n      variant = 'text';\n      color = 'primary';\n      break;\n  }\n\n  // Custom colors\n  if (attributes.backgroundColor) {\n    buttonStyles.backgroundColor = attributes.backgroundColor;\n  }\n  if (attributes.textColor) {\n    buttonStyles.color = attributes.textColor;\n  }\n\n  // Border styles\n  if (attributes.border) {\n    buttonStyles.border = attributes.border;\n  }\n  if (attributes.borderColor) {\n    buttonStyles.borderColor = attributes.borderColor;\n  }\n  if (attributes.borderWidth) {\n    buttonStyles.borderWidth = attributes.borderWidth;\n  }\n  if (attributes.borderRadius) {\n    buttonStyles.borderRadius = attributes.borderRadius;\n  }\n\n  // Animation variants\n  const buttonVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    },\n    hover: {\n      scale: 1.05,\n      transition: { duration: 0.2 }\n    },\n    tap: {\n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  };\n\n  const ButtonComponent = (\n    <motion.div\n      variants={buttonVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      <MuiButton\n        variant={variant}\n        color={color}\n        sx={{\n          ...buttonStyles,\n          '&:hover': {\n            transform: 'translateY(-2px)',\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n          },\n        }}\n      >\n        {content || attributes.buttonText || 'Button'}\n      </MuiButton>\n    </motion.div>\n  );\n\n  return (\n    <Box\n      className={`pagebuilder-button ${className || ''}`}\n      sx={containerStyles}\n    >\n      {attributes.link ? (\n        <Link\n          href={attributes.link}\n          target={attributes.linkTarget || '_self'}\n          style={{ textDecoration: 'none' }}\n        >\n          {ButtonComponent}\n        </Link>\n      ) : (\n        ButtonComponent\n      )}\n    </Box>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAYO,MAAM,SAAgC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IACzE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAExC,yBAAyB;IACzB,MAAM,kBAAuC;QAC3C,SAAS;QACT,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,+BAA+B;IAC/B,IAAI,WAAW,SAAS,EAAE;QACxB,OAAQ,WAAW,SAAS;YAC1B,KAAK;gBACH,gBAAgB,cAAc,GAAG;gBACjC;YACF,KAAK;gBACH,gBAAgB,cAAc,GAAG;gBACjC;YACF,KAAK;gBACH,gBAAgB,cAAc,GAAG;gBACjC;QACJ;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAoC;QACxC,eAAe;QACf,YAAY;QACZ,cAAc;QACd,SAAS;QACT,UAAU;QACV,YAAY;IACd;IAEA,sBAAsB;IACtB,IAAI,UAA6C;IACjD,IAAI,QAA6C;IAEjD,OAAQ,WAAW,UAAU;QAC3B,KAAK;YACH,UAAU;YACV,QAAQ;YACR;QACF,KAAK;YACH,UAAU;YACV,QAAQ;YACR;QACF,KAAK;YACH,UAAU;YACV,QAAQ;YACR;IACJ;IAEA,gBAAgB;IAChB,IAAI,WAAW,eAAe,EAAE;QAC9B,aAAa,eAAe,GAAG,WAAW,eAAe;IAC3D;IACA,IAAI,WAAW,SAAS,EAAE;QACxB,aAAa,KAAK,GAAG,WAAW,SAAS;IAC3C;IAEA,gBAAgB;IAChB,IAAI,WAAW,MAAM,EAAE;QACrB,aAAa,MAAM,GAAG,WAAW,MAAM;IACzC;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,aAAa,WAAW,GAAG,WAAW,WAAW;IACnD;IACA,IAAI,WAAW,WAAW,EAAE;QAC1B,aAAa,WAAW,GAAG,WAAW,WAAW;IACnD;IACA,IAAI,WAAW,YAAY,EAAE;QAC3B,aAAa,YAAY,GAAG,WAAW,YAAY;IACrD;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,OAAO;YACL,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,KAAK;YACH,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,gCACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,YAAW;QACX,UAAS;QACT,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,6LAAC,oMAAA,CAAA,SAAS;YACR,SAAS;YACT,OAAO;YACP,IAAI;gBACF,GAAG,YAAY;gBACf,WAAW;oBACT,WAAW;oBACX,WAAW;gBACb;YACF;sBAEC,WAAW,WAAW,UAAU,IAAI;;;;;;;;;;;IAK3C,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,WAAW,CAAC,mBAAmB,EAAE,aAAa,IAAI;QAClD,IAAI;kBAEH,WAAW,IAAI,iBACd,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,WAAW,IAAI;YACrB,QAAQ,WAAW,UAAU,IAAI;YACjC,OAAO;gBAAE,gBAAgB;YAAO;sBAE/B;;;;;mBAGH;;;;;;AAIR;KA5Ia;uCA8IE", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Banner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Typography, But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { BannerElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\nimport { optimizeImageUrl } from '@/lib/pagebuilder/utils';\n\ninterface BannerProps extends PageBuilderElementProps {\n  element: BannerElement;\n}\n\nexport const Banner: React.FC<BannerProps> = ({ element, children, className, style }) => {\n  const { attributes, styles } = element;\n\n  // Build container styles\n  const bannerStyles: React.CSSProperties = {\n    position: 'relative',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    overflow: 'hidden',\n    minHeight: attributes.minHeight || '400px',\n    ...styles,\n    ...style,\n  };\n\n  // Background styles\n  if (attributes.backgroundColor) {\n    bannerStyles.backgroundColor = attributes.backgroundColor;\n  }\n\n  // Content placement\n  let contentAlignment = 'center';\n  if (attributes.contentPlacement) {\n    contentAlignment = attributes.contentPlacement;\n  }\n\n  // Animation variants\n  const bannerVariants = {\n    hidden: { opacity: 0 },\n    visible: { \n      opacity: 1,\n      transition: { \n        duration: 0.8,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const contentVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.8,\n        delay: 0.3,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`pagebuilder-banner ${className || ''}`}\n      style={bannerStyles}\n      variants={bannerVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      {/* Background Image */}\n      {attributes.backgroundImage && (\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 0,\n          }}\n        >\n          <Image\n            src={optimizeImageUrl(attributes.backgroundImage)}\n            alt=\"\"\n            fill\n            style={{\n              objectFit: attributes.backgroundSize === 'contain' ? 'contain' : 'cover',\n              objectPosition: attributes.backgroundPosition || 'center center',\n            }}\n            quality={85}\n            priority\n          />\n        </Box>\n      )}\n\n      {/* Overlay */}\n      {attributes.showOverlay && (\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: attributes.overlayColor || 'rgba(0, 0, 0, 0.4)',\n            zIndex: 1,\n          }}\n        />\n      )}\n\n      {/* Content */}\n      <Box\n        sx={{\n          position: 'relative',\n          zIndex: 2,\n          width: '100%',\n          maxWidth: '800px',\n          padding: { xs: 3, md: 6 },\n          textAlign: contentAlignment,\n          color: 'white',\n        }}\n      >\n        <motion.div\n          variants={contentVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.1 }}\n        >\n          {/* Banner Content */}\n          {attributes.content && (\n            <Box sx={{ mb: 3 }}>\n              {attributes.content.includes('<') ? (\n                <div dangerouslySetInnerHTML={{ __html: attributes.content }} />\n              ) : (\n                <Typography\n                  variant=\"h3\"\n                  component=\"h2\"\n                  sx={{\n                    fontWeight: 700,\n                    mb: 2,\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n                    fontSize: { xs: '2rem', md: '3rem' },\n                  }}\n                >\n                  {attributes.content}\n                </Typography>\n              )}\n            </Box>\n          )}\n\n          {/* Children content */}\n          {children && (\n            <Box sx={{ mb: 3 }}>\n              {children}\n            </Box>\n          )}\n\n          {/* Button */}\n          {attributes.showButton && attributes.buttonText && (\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              {attributes.buttonLink ? (\n                <Link\n                  href={attributes.buttonLink}\n                  target={attributes.buttonTarget || '_self'}\n                  style={{ textDecoration: 'none' }}\n                >\n                  <MuiButton\n                    variant={attributes.buttonType === 'secondary' ? 'outlined' : 'contained'}\n                    size=\"large\"\n                    sx={{\n                      fontSize: '1.125rem',\n                      fontWeight: 600,\n                      px: 4,\n                      py: 1.5,\n                      borderRadius: 2,\n                      textTransform: 'none',\n                      boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',\n                      color: attributes.buttonType === 'secondary' ? 'white' : undefined,\n                      borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,\n                      '&:hover': {\n                        boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',\n                        transform: 'translateY(-2px)',\n                      },\n                      transition: 'all 0.3s ease-in-out',\n                    }}\n                  >\n                    {attributes.buttonText}\n                  </MuiButton>\n                </Link>\n              ) : (\n                <MuiButton\n                  variant={attributes.buttonType === 'secondary' ? 'outlined' : 'contained'}\n                  size=\"large\"\n                  sx={{\n                    fontSize: '1.125rem',\n                    fontWeight: 600,\n                    px: 4,\n                    py: 1.5,\n                    borderRadius: 2,\n                    textTransform: 'none',\n                    boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',\n                    color: attributes.buttonType === 'secondary' ? 'white' : undefined,\n                    borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,\n                    '&:hover': {\n                      boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',\n                      transform: 'translateY(-2px)',\n                    },\n                    transition: 'all 0.3s ease-in-out',\n                  }}\n                >\n                  {attributes.buttonText}\n                </MuiButton>\n              )}\n            </motion.div>\n          )}\n        </motion.div>\n      </Box>\n    </motion.div>\n  );\n};\n\nexport default Banner;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AARA;;;;;;;AAcO,MAAM,SAAgC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE;IACnF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAE/B,yBAAyB;IACzB,MAAM,eAAoC;QACxC,UAAU;QACV,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,UAAU;QACV,WAAW,WAAW,SAAS,IAAI;QACnC,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,oBAAoB;IACpB,IAAI,WAAW,eAAe,EAAE;QAC9B,aAAa,eAAe,GAAG,WAAW,eAAe;IAC3D;IAEA,oBAAoB;IACpB,IAAI,mBAAmB;IACvB,IAAI,WAAW,gBAAgB,EAAE;QAC/B,mBAAmB,WAAW,gBAAgB;IAChD;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mBAAmB,EAAE,aAAa,IAAI;QAClD,OAAO;QACP,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;;YAGnC,WAAW,eAAe,kBACzB,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,QAAQ;gBACV;0BAEA,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,eAAe;oBAChD,KAAI;oBACJ,IAAI;oBACJ,OAAO;wBACL,WAAW,WAAW,cAAc,KAAK,YAAY,YAAY;wBACjE,gBAAgB,WAAW,kBAAkB,IAAI;oBACnD;oBACA,SAAS;oBACT,QAAQ;;;;;;;;;;;YAMb,WAAW,WAAW,kBACrB,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB,WAAW,YAAY,IAAI;oBAC5C,QAAQ;gBACV;;;;;;0BAKJ,6LAAC,2LAAA,CAAA,MAAG;gBACF,IAAI;oBACF,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,SAAS;wBAAE,IAAI;wBAAG,IAAI;oBAAE;oBACxB,WAAW;oBACX,OAAO;gBACT;0BAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;;wBAGnC,WAAW,OAAO,kBACjB,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,IAAI;4BAAE;sCACd,WAAW,OAAO,CAAC,QAAQ,CAAC,qBAC3B,6LAAC;gCAAI,yBAAyB;oCAAE,QAAQ,WAAW,OAAO;gCAAC;;;;;qDAE3D,6LAAC,gNAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,WAAU;gCACV,IAAI;oCACF,YAAY;oCACZ,IAAI;oCACJ,YAAY;oCACZ,UAAU;wCAAE,IAAI;wCAAQ,IAAI;oCAAO;gCACrC;0CAEC,WAAW,OAAO;;;;;;;;;;;wBAO1B,0BACC,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,IAAI;4BAAE;sCACd;;;;;;wBAKJ,WAAW,UAAU,IAAI,WAAW,UAAU,kBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAEvB,WAAW,UAAU,iBACpB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,WAAW,UAAU;gCAC3B,QAAQ,WAAW,YAAY,IAAI;gCACnC,OAAO;oCAAE,gBAAgB;gCAAO;0CAEhC,cAAA,6LAAC,oMAAA,CAAA,SAAS;oCACR,SAAS,WAAW,UAAU,KAAK,cAAc,aAAa;oCAC9D,MAAK;oCACL,IAAI;wCACF,UAAU;wCACV,YAAY;wCACZ,IAAI;wCACJ,IAAI;wCACJ,cAAc;wCACd,eAAe;wCACf,WAAW;wCACX,OAAO,WAAW,UAAU,KAAK,cAAc,UAAU;wCACzD,aAAa,WAAW,UAAU,KAAK,cAAc,UAAU;wCAC/D,WAAW;4CACT,WAAW;4CACX,WAAW;wCACb;wCACA,YAAY;oCACd;8CAEC,WAAW,UAAU;;;;;;;;;;qDAI1B,6LAAC,oMAAA,CAAA,SAAS;gCACR,SAAS,WAAW,UAAU,KAAK,cAAc,aAAa;gCAC9D,MAAK;gCACL,IAAI;oCACF,UAAU;oCACV,YAAY;oCACZ,IAAI;oCACJ,IAAI;oCACJ,cAAc;oCACd,eAAe;oCACf,WAAW;oCACX,OAAO,WAAW,UAAU,KAAK,cAAc,UAAU;oCACzD,aAAa,WAAW,UAAU,KAAK,cAAc,UAAU;oCAC/D,WAAW;wCACT,WAAW;wCACX,WAAW;oCACb;oCACA,YAAY;gCACd;0CAEC,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KArNa;uCAuNE", "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Video.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Box, IconButton } from '@mui/material';\nimport { PlayArrow, Pause } from '@mui/icons-material';\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { VideoElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\nimport { optimizeImageUrl } from '@/lib/pagebuilder/utils';\n\ninterface VideoProps extends PageBuilderElementProps {\n  element: VideoElement;\n}\n\nexport const Video: React.FC<VideoProps> = ({ element, className, style }) => {\n  const { attributes, styles } = element;\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [showControls, setShowControls] = useState(false);\n\n  // Build container styles\n  const videoContainerStyles: React.CSSProperties = {\n    position: 'relative',\n    width: '100%',\n    maxWidth: attributes.maxWidth || '100%',\n    margin: '0 auto',\n    ...styles,\n    ...style,\n  };\n\n  // Get video embed URL\n  const getEmbedUrl = () => {\n    const { videoType, videoUrl, videoId } = attributes;\n\n    if (videoType === 'youtube') {\n      const id = videoId || extractYouTubeId(videoUrl);\n      return `https://www.youtube.com/embed/${id}?autoplay=${attributes.autoplay ? 1 : 0}&mute=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;\n    }\n\n    if (videoType === 'vimeo') {\n      const id = videoId || extractVimeoId(videoUrl);\n      return `https://player.vimeo.com/video/${id}?autoplay=${attributes.autoplay ? 1 : 0}&muted=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;\n    }\n\n    return videoUrl;\n  };\n\n  // Extract YouTube video ID\n  const extractYouTubeId = (url?: string): string => {\n    if (!url) return '';\n    const match = url.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n    return match ? match[1] : '';\n  };\n\n  // Extract Vimeo video ID\n  const extractVimeoId = (url?: string): string => {\n    if (!url) return '';\n    const match = url.match(/vimeo\\.com\\/(\\d+)/);\n    return match ? match[1] : '';\n  };\n\n  // Handle play/pause\n  const handlePlayPause = () => {\n    setIsPlaying(!isPlaying);\n  };\n\n  // Animation variants\n  const videoVariants = {\n    hidden: { opacity: 0, scale: 0.95 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  return (\n    <motion.div\n      className={`pagebuilder-video ${className || ''}`}\n      style={videoContainerStyles}\n      variants={videoVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n      onMouseEnter={() => setShowControls(true)}\n      onMouseLeave={() => setShowControls(false)}\n    >\n      <Box\n        sx={{\n          position: 'relative',\n          width: '100%',\n          paddingBottom: '56.25%', // 16:9 aspect ratio\n          height: 0,\n          overflow: 'hidden',\n          borderRadius: 2,\n          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',\n        }}\n      >\n        {attributes.videoType === 'mp4' ? (\n          // Native HTML5 video\n          <video\n            src={attributes.videoUrl}\n            autoPlay={attributes.autoplay}\n            muted={attributes.muted}\n            loop={attributes.loop}\n            controls={attributes.controls}\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover',\n            }}\n            poster={attributes.fallbackImage ? optimizeImageUrl(attributes.fallbackImage) : undefined}\n          />\n        ) : (\n          // Embedded video (YouTube/Vimeo)\n          <>\n            {!isPlaying && attributes.fallbackImage ? (\n              // Fallback image with play button\n              <Box\n                sx={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  cursor: 'pointer',\n                }}\n                onClick={handlePlayPause}\n              >\n                <Image\n                  src={optimizeImageUrl(attributes.fallbackImage)}\n                  alt=\"Video thumbnail\"\n                  fill\n                  style={{ objectFit: 'cover' }}\n                  quality={85}\n                />\n                \n                {/* Play button overlay */}\n                <motion.div\n                  variants={overlayVariants}\n                  initial=\"hidden\"\n                  animate=\"visible\"\n                  exit=\"exit\"\n                  style={{\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                  }}\n                >\n                  <IconButton\n                    sx={{\n                      backgroundColor: 'rgba(0,0,0,0.7)',\n                      color: 'white',\n                      width: 80,\n                      height: 80,\n                      '&:hover': {\n                        backgroundColor: 'rgba(0,0,0,0.8)',\n                        transform: 'scale(1.1)',\n                      },\n                      transition: 'all 0.3s ease',\n                    }}\n                  >\n                    <PlayArrow sx={{ fontSize: 40 }} />\n                  </IconButton>\n                </motion.div>\n              </Box>\n            ) : (\n              // Embedded iframe\n              <iframe\n                src={getEmbedUrl()}\n                style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  border: 'none',\n                }}\n                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                allowFullScreen\n                loading={attributes.lazyLoading !== false ? 'lazy' : 'eager'}\n              />\n            )}\n          </>\n        )}\n\n        {/* Custom controls overlay */}\n        {attributes.videoType === 'mp4' && showControls && (\n          <motion.div\n            variants={overlayVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"exit\"\n            style={{\n              position: 'absolute',\n              bottom: 16,\n              left: 16,\n            }}\n          >\n            <IconButton\n              onClick={handlePlayPause}\n              sx={{\n                backgroundColor: 'rgba(0,0,0,0.7)',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: 'rgba(0,0,0,0.8)',\n                },\n              }}\n            >\n              {isPlaying ? <Pause /> : <PlayArrow />}\n            </IconButton>\n          </motion.div>\n        )}\n      </Box>\n    </motion.div>\n  );\n};\n\nexport default Video;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;;;AARA;;;;;;;;AAcO,MAAM,QAA8B,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;;IACvE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,yBAAyB;IACzB,MAAM,uBAA4C;QAChD,UAAU;QACV,OAAO;QACP,UAAU,WAAW,QAAQ,IAAI;QACjC,QAAQ;QACR,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,sBAAsB;IACtB,MAAM,cAAc;QAClB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;QAEzC,IAAI,cAAc,WAAW;YAC3B,MAAM,KAAK,WAAW,iBAAiB;YACvC,OAAO,CAAC,8BAA8B,EAAE,GAAG,UAAU,EAAE,WAAW,QAAQ,GAAG,IAAI,EAAE,MAAM,EAAE,WAAW,KAAK,GAAG,IAAI,EAAE,MAAM,EAAE,WAAW,IAAI,GAAG,IAAI,EAAE,UAAU,EAAE,WAAW,QAAQ,GAAG,IAAI,GAAG;QAC/L;QAEA,IAAI,cAAc,SAAS;YACzB,MAAM,KAAK,WAAW,eAAe;YACrC,OAAO,CAAC,+BAA+B,EAAE,GAAG,UAAU,EAAE,WAAW,QAAQ,GAAG,IAAI,EAAE,OAAO,EAAE,WAAW,KAAK,GAAG,IAAI,EAAE,MAAM,EAAE,WAAW,IAAI,GAAG,IAAI,EAAE,UAAU,EAAE,WAAW,QAAQ,GAAG,IAAI,GAAG;QACjM;QAEA,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,KAAK,OAAO;QACjB,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,KAAK,OAAO;QACjB,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAK;QAClC,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;IACrB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,kBAAkB,EAAE,aAAa,IAAI;QACjD,OAAO;QACP,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,cAAc,IAAM,gBAAgB;QACpC,cAAc,IAAM,gBAAgB;kBAEpC,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,QAAQ;gBACR,UAAU;gBACV,cAAc;gBACd,WAAW;YACb;;gBAEC,WAAW,SAAS,KAAK,QACxB,qBAAqB;8BACrB,6LAAC;oBACC,KAAK,WAAW,QAAQ;oBACxB,UAAU,WAAW,QAAQ;oBAC7B,OAAO,WAAW,KAAK;oBACvB,MAAM,WAAW,IAAI;oBACrB,UAAU,WAAW,QAAQ;oBAC7B,OAAO;wBACL,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR,WAAW;oBACb;oBACA,QAAQ,WAAW,aAAa,GAAG,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,aAAa,IAAI;;;;;2BAGlF,iCAAiC;8BACjC;8BACG,CAAC,aAAa,WAAW,aAAa,GACrC,kCAAkC;kCAClC,6LAAC,2LAAA,CAAA,MAAG;wBACF,IAAI;4BACF,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,QAAQ;wBACV;wBACA,SAAS;;0CAET,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,aAAa;gCAC9C,KAAI;gCACJ,IAAI;gCACJ,OAAO;oCAAE,WAAW;gCAAQ;gCAC5B,SAAS;;;;;;0CAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,MAAK;gCACL,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,MAAM;oCACN,WAAW;gCACb;0CAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;oCACT,IAAI;wCACF,iBAAiB;wCACjB,OAAO;wCACP,OAAO;wCACP,QAAQ;wCACR,WAAW;4CACT,iBAAiB;4CACjB,WAAW;wCACb;wCACA,YAAY;oCACd;8CAEA,cAAA,6LAAC,iKAAA,CAAA,UAAS;wCAAC,IAAI;4CAAE,UAAU;wCAAG;;;;;;;;;;;;;;;;;;;;;+BAKpC,kBAAkB;kCAClB,6LAAC;wBACC,KAAK;wBACL,OAAO;4BACL,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,QAAQ;wBACV;wBACA,OAAM;wBACN,eAAe;wBACf,SAAS,WAAW,WAAW,KAAK,QAAQ,SAAS;;;;;;;gBAO5D,WAAW,SAAS,KAAK,SAAS,8BACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,OAAO;wBACL,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;8BAEA,cAAA,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAS;wBACT,IAAI;4BACF,iBAAiB;4BACjB,OAAO;4BACP,WAAW;gCACT,iBAAiB;4BACnB;wBACF;kCAEC,0BAAY,6LAAC,6JAAA,CAAA,UAAK;;;;iDAAM,6LAAC,iKAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GAtNa;KAAA;uCAwNE", "debugId": null}}, {"offset": {"line": 2011, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Html.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { HtmlElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface HtmlProps extends PageBuilderElementProps {\n  element: HtmlElement;\n}\n\nexport const Html: React.FC<HtmlProps> = ({ element, className, style }) => {\n  const { content, styles } = element;\n\n  // Build styles\n  const htmlStyles: React.CSSProperties = {\n    width: '100%',\n    ...styles,\n    ...style,\n  };\n\n  // Animation variants\n  const htmlVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`pagebuilder-html ${className || ''}`}\n      variants={htmlVariants}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, amount: 0.1 }}\n    >\n      <Box\n        component=\"div\"\n        sx={htmlStyles}\n        dangerouslySetInnerHTML={{ __html: content }}\n      />\n    </motion.div>\n  );\n};\n\nexport default Html;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAWO,MAAM,OAA4B,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IACrE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IAE5B,eAAe;IACf,MAAM,aAAkC;QACtC,OAAO;QACP,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,iBAAiB,EAAE,aAAa,IAAI;QAChD,UAAU;QACV,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;kBAEpC,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,WAAU;YACV,IAAI;YACJ,yBAAyB;gBAAE,QAAQ;YAAQ;;;;;;;;;;;AAInD;KAtCa;uCAwCE", "debugId": null}}, {"offset": {"line": 2084, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/Divider.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Divider as MuiDivider } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { DividerElement, PageBuilderElementProps } from '@/lib/pagebuilder/types';\n\ninterface DividerProps extends PageBuilderElementProps {\n  element: DividerElement;\n}\n\nexport const Divider: React.FC<DividerProps> = ({ element, className, style }) => {\n  const { attributes, styles } = element;\n\n  // Build styles\n  const dividerStyles: React.CSSProperties = {\n    width: attributes.lineWidth || '100%',\n    height: attributes.lineThickness || '1px',\n    backgroundColor: attributes.lineColor || '#e0e0e0',\n    border: 'none',\n    margin: '2rem 0',\n    ...styles,\n    ...style,\n  };\n\n  // Animation variants\n  const dividerVariants = {\n    hidden: { opacity: 0, scaleX: 0 },\n    visible: { \n      opacity: 1, \n      scaleX: 1,\n      transition: { \n        duration: 0.8,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <Box\n      className={`pagebuilder-divider ${className || ''}`}\n      sx={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        width: '100%',\n        my: 2,\n      }}\n    >\n      <motion.div\n        variants={dividerVariants}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, amount: 0.1 }}\n        style={{ width: '100%' }}\n      >\n        <MuiDivider\n          sx={{\n            ...dividerStyles,\n            '&::before, &::after': {\n              borderColor: attributes.lineColor || 'divider',\n            },\n          }}\n        />\n      </motion.div>\n    </Box>\n  );\n};\n\nexport default Divider;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAJA;;;;AAWO,MAAM,UAAkC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IAC3E,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG;IAE/B,eAAe;IACf,MAAM,gBAAqC;QACzC,OAAO,WAAW,SAAS,IAAI;QAC/B,QAAQ,WAAW,aAAa,IAAI;QACpC,iBAAiB,WAAW,SAAS,IAAI;QACzC,QAAQ;QACR,QAAQ;QACR,GAAG,MAAM;QACT,GAAG,KAAK;IACV;IAEA,qBAAqB;IACrB,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,QAAQ;QAAE;QAChC,SAAS;YACP,SAAS;YACT,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,WAAW,CAAC,oBAAoB,EAAE,aAAa,IAAI;QACnD,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,OAAO;YACP,IAAI;QACN;kBAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU;YACV,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;YACpC,OAAO;gBAAE,OAAO;YAAO;sBAEvB,cAAA,6LAAC,uMAAA,CAAA,UAAU;gBACT,IAAI;oBACF,GAAG,aAAa;oBAChB,uBAAuB;wBACrB,aAAa,WAAW,SAAS,IAAI;oBACvC;gBACF;;;;;;;;;;;;;;;;AAKV;KAxDa;uCA0DE", "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/elements/index.ts"], "sourcesContent": ["// Page Builder Elements Export\n\nexport { default as Row } from './Row';\nexport { default as Column } from './Column';\nexport { default as Text } from './Text';\nexport { default as Heading } from './Heading';\nexport { default as PageBuilderImage } from './Image';\nexport { default as <PERSON><PERSON> } from './Button';\nexport { default as Banner } from './Banner';\nexport { default as Video } from './Video';\nexport { default as Html } from './Html';\nexport { default as Divider } from './Divider';\n\n// Re-export types for convenience\nexport type {\n  PageBuilderElementProps,\n  RowElement,\n  ColumnElement,\n  TextElement,\n  HeadingElement,\n  ImageElement,\n  ButtonElement,\n  BannerElement,\n  VideoElement,\n  HtmlElement,\n  DividerElement,\n} from '@/lib/pagebuilder/types';\n"], "names": [], "mappings": "AAAA,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/parser/ElementParser.ts"], "sourcesContent": ["// Page Builder Element Parser\n\nimport {\n  PageBuilderElement,\n  PageBuilderElementType,\n  RowElement,\n  ColumnElement,\n  TextElement,\n  HeadingElement,\n  ImageElement,\n  ButtonElement,\n  BannerElement,\n  VideoElement,\n  HtmlElement,\n  DividerElement,\n  PageBuilderParserConfig,\n} from '@/lib/pagebuilder/types';\nimport {\n  generateElementId,\n  getElementType,\n  extractAttributes,\n  extractStyles,\n  getElementContent,\n  parseBackgroundImages,\n  logParsingError,\n} from '@/lib/pagebuilder/utils';\n\nexport class ElementParser {\n  private config: PageBuilderParserConfig;\n\n  constructor(config: PageBuilderParserConfig) {\n    this.config = config;\n  }\n\n  // Parse a DOM element into a PageBuilderElement\n  parseElement(element: Element): PageBuilderElement | null {\n    try {\n      const elementType = getElementType(element);\n      if (!elementType) {\n        return null;\n      }\n\n      // Check for custom parser\n      if (this.config.customElementParsers?.[elementType]) {\n        return this.config.customElementParsers[elementType](element);\n      }\n\n      // Use built-in parser\n      return this.parseByType(element, elementType);\n    } catch (error) {\n      logParsingError(error as Error, element);\n      return null;\n    }\n  }\n\n  // Parse element by type\n  private parseByType(element: Element, type: PageBuilderElementType): PageBuilderElement | null {\n    const baseElement = this.createBaseElement(element, type);\n\n    switch (type) {\n      case PageBuilderElementType.ROW:\n        return this.parseRow(element, baseElement);\n      case PageBuilderElementType.COLUMN:\n        return this.parseColumn(element, baseElement);\n      case PageBuilderElementType.TEXT:\n        return this.parseText(element, baseElement);\n      case PageBuilderElementType.HEADING:\n        return this.parseHeading(element, baseElement);\n      case PageBuilderElementType.IMAGE:\n        return this.parseImage(element, baseElement);\n      case PageBuilderElementType.BUTTON:\n        return this.parseButton(element, baseElement);\n      case PageBuilderElementType.BANNER:\n        return this.parseBanner(element, baseElement);\n      case PageBuilderElementType.VIDEO:\n        return this.parseVideo(element, baseElement);\n      case PageBuilderElementType.HTML:\n        return this.parseHtml(element, baseElement);\n      case PageBuilderElementType.DIVIDER:\n        return this.parseDivider(element, baseElement);\n      default:\n        return baseElement;\n    }\n  }\n\n  // Create base element with common properties\n  private createBaseElement(element: Element, type: PageBuilderElementType): PageBuilderElement {\n    return {\n      type,\n      id: generateElementId(),\n      attributes: extractAttributes(element),\n      styles: extractStyles(element),\n      rawHtml: element.outerHTML,\n    };\n  }\n\n  // Parse Row element\n  private parseRow(element: Element, baseElement: PageBuilderElement): RowElement {\n    const attributes = baseElement.attributes;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.ROW,\n      attributes: {\n        appearance: attributes.appearance || 'contained',\n        enableParallax: attributes.enableParallax === 'true',\n        parallaxSpeed: parseFloat(attributes.parallaxSpeed) || 0.5,\n        backgroundColor: attributes.backgroundColor,\n        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),\n        backgroundSize: attributes.backgroundSize || 'cover',\n        backgroundPosition: attributes.backgroundPosition || 'center center',\n        backgroundAttachment: attributes.backgroundAttachment || 'scroll',\n        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',\n        minHeight: attributes.minHeight,\n        verticalAlignment: attributes.verticalAlignment || 'top',\n      },\n    } as RowElement;\n  }\n\n  // Parse Column element\n  private parseColumn(element: Element, baseElement: PageBuilderElement): ColumnElement {\n    const attributes = baseElement.attributes;\n    const styles = baseElement.styles;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.COLUMN,\n      attributes: {\n        width: this.extractColumnWidth(element, styles),\n        appearance: attributes.appearance || 'minimum-height',\n        backgroundColor: attributes.backgroundColor,\n        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),\n        backgroundSize: attributes.backgroundSize || 'cover',\n        backgroundPosition: attributes.backgroundPosition || 'center center',\n        backgroundAttachment: attributes.backgroundAttachment || 'scroll',\n        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',\n        minHeight: attributes.minHeight,\n        verticalAlignment: attributes.verticalAlignment || 'top',\n      },\n    } as ColumnElement;\n  }\n\n  // Parse Text element\n  private parseText(element: Element, baseElement: PageBuilderElement): TextElement {\n    const attributes = baseElement.attributes;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.TEXT,\n      content: getElementContent(element, true),\n      attributes: {\n        textAlign: attributes.textAlign || 'left',\n        border: attributes.border,\n        borderColor: attributes.borderColor,\n        borderWidth: attributes.borderWidth,\n        borderRadius: attributes.borderRadius,\n      },\n    } as TextElement;\n  }\n\n  // Parse Heading element\n  private parseHeading(element: Element, baseElement: PageBuilderElement): HeadingElement {\n    const attributes = baseElement.attributes;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.HEADING,\n      content: getElementContent(element, true),\n      attributes: {\n        headingType: this.extractHeadingType(element),\n        textAlign: attributes.textAlign || 'left',\n        border: attributes.border,\n        borderColor: attributes.borderColor,\n        borderWidth: attributes.borderWidth,\n        borderRadius: attributes.borderRadius,\n      },\n    } as HeadingElement;\n  }\n\n  // Parse Image element\n  private parseImage(element: Element, baseElement: PageBuilderElement): ImageElement {\n    const attributes = baseElement.attributes;\n    const img = element.tagName === 'IMG' ? element : element.querySelector('img');\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.IMAGE,\n      attributes: {\n        src: img?.getAttribute('src') || attributes.src || '',\n        alt: img?.getAttribute('alt') || attributes.alt || '',\n        title: img?.getAttribute('title') || attributes.title,\n        caption: this.extractImageCaption(element),\n        link: this.extractImageLink(element),\n        linkTarget: attributes.linkTarget || '_self',\n        alignment: attributes.alignment || 'center',\n        border: attributes.border,\n        borderColor: attributes.borderColor,\n        borderWidth: attributes.borderWidth,\n        borderRadius: attributes.borderRadius,\n        lazyLoading: this.config.enableLazyLoading !== false,\n      },\n    } as ImageElement;\n  }\n\n  // Parse Button element\n  private parseButton(element: Element, baseElement: PageBuilderElement): ButtonElement {\n    const attributes = baseElement.attributes;\n    const link = element.tagName === 'A' ? element : element.querySelector('a');\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.BUTTON,\n      content: getElementContent(element, false),\n      attributes: {\n        buttonType: this.extractButtonType(element),\n        link: link?.getAttribute('href') || attributes.link,\n        linkTarget: link?.getAttribute('target') || attributes.linkTarget || '_self',\n        textAlign: attributes.textAlign || 'left',\n        border: attributes.border,\n        borderColor: attributes.borderColor,\n        borderWidth: attributes.borderWidth,\n        borderRadius: attributes.borderRadius,\n        backgroundColor: attributes.backgroundColor,\n        textColor: attributes.textColor,\n      },\n    } as ButtonElement;\n  }\n\n  // Parse Banner element\n  private parseBanner(element: Element, baseElement: PageBuilderElement): BannerElement {\n    const attributes = baseElement.attributes;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.BANNER,\n      attributes: {\n        appearance: attributes.appearance || 'poster',\n        minHeight: attributes.minHeight || '300px',\n        backgroundColor: attributes.backgroundColor,\n        backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),\n        backgroundSize: attributes.backgroundSize || 'cover',\n        backgroundPosition: attributes.backgroundPosition || 'center center',\n        backgroundAttachment: attributes.backgroundAttachment || 'scroll',\n        backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',\n        showButton: attributes.showButton === 'true',\n        buttonText: this.extractBannerButtonText(element),\n        buttonType: attributes.buttonType || 'primary',\n        buttonLink: this.extractBannerButtonLink(element),\n        buttonTarget: attributes.buttonTarget || '_self',\n        showOverlay: attributes.showOverlay === 'true',\n        overlayColor: attributes.overlayColor,\n        content: this.extractBannerContent(element),\n        contentPlacement: attributes.contentPlacement || 'center',\n      },\n    } as BannerElement;\n  }\n\n  // Parse Video element\n  private parseVideo(element: Element, baseElement: PageBuilderElement): VideoElement {\n    const attributes = baseElement.attributes;\n    const video = element.querySelector('video');\n    const iframe = element.querySelector('iframe');\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.VIDEO,\n      attributes: {\n        videoType: this.extractVideoType(element),\n        videoUrl: video?.getAttribute('src') || iframe?.getAttribute('src') || attributes.videoUrl,\n        videoId: attributes.videoId,\n        maxWidth: attributes.maxWidth || '100%',\n        autoplay: attributes.autoplay === 'true' || video?.hasAttribute('autoplay'),\n        muted: attributes.muted === 'true' || video?.hasAttribute('muted'),\n        loop: attributes.loop === 'true' || video?.hasAttribute('loop'),\n        controls: attributes.controls === 'true' || video?.hasAttribute('controls'),\n        lazyLoading: this.config.enableLazyLoading !== false,\n        fallbackImage: this.extractVideoFallbackImage(element),\n      },\n    } as VideoElement;\n  }\n\n  // Parse HTML element\n  private parseHtml(element: Element, baseElement: PageBuilderElement): HtmlElement {\n    return {\n      ...baseElement,\n      type: PageBuilderElementType.HTML,\n      content: element.innerHTML,\n    } as HtmlElement;\n  }\n\n  // Parse Divider element\n  private parseDivider(element: Element, baseElement: PageBuilderElement): DividerElement {\n    const attributes = baseElement.attributes;\n    \n    return {\n      ...baseElement,\n      type: PageBuilderElementType.DIVIDER,\n      attributes: {\n        lineColor: attributes.lineColor || '#e0e0e0',\n        lineThickness: attributes.lineThickness || '1px',\n        lineWidth: attributes.lineWidth || '100%',\n      },\n    } as DividerElement;\n  }\n\n  // Helper methods\n  private parseBackgroundImage(backgroundImagesData?: string): string | undefined {\n    if (!backgroundImagesData) return undefined;\n    const images = parseBackgroundImages(backgroundImagesData);\n    return images[0]; // Use first image (desktop)\n  }\n\n  private extractColumnWidth(element: Element, styles: Record<string, any>): string {\n    // Try to get width from styles or data attributes\n    if (styles.width) return styles.width;\n    if (styles.flexBasis) return styles.flexBasis;\n    \n    // Check for Magento column classes\n    const classList = Array.from(element.classList);\n    for (const className of classList) {\n      if (className.includes('col-')) {\n        const match = className.match(/col-(\\d+)/);\n        if (match) {\n          const cols = parseInt(match[1]);\n          return `${(cols / 12) * 100}%`;\n        }\n      }\n    }\n    \n    return '100%';\n  }\n\n  private extractHeadingType(element: Element): 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' {\n    const tagName = element.tagName.toLowerCase();\n    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {\n      return tagName as any;\n    }\n    return 'h2'; // Default\n  }\n\n  private extractButtonType(element: Element): 'primary' | 'secondary' | 'link' {\n    const classList = Array.from(element.classList);\n    if (classList.includes('pagebuilder-button-secondary')) return 'secondary';\n    if (classList.includes('pagebuilder-button-link')) return 'link';\n    return 'primary';\n  }\n\n  private extractImageCaption(element: Element): string | undefined {\n    const caption = element.querySelector('figcaption');\n    return caption?.textContent || undefined;\n  }\n\n  private extractImageLink(element: Element): string | undefined {\n    const link = element.querySelector('a');\n    return link?.getAttribute('href') || undefined;\n  }\n\n  private extractBannerContent(element: Element): string | undefined {\n    const content = element.querySelector('.pagebuilder-banner-content');\n    return content?.innerHTML || undefined;\n  }\n\n  private extractBannerButtonText(element: Element): string | undefined {\n    const button = element.querySelector('.pagebuilder-banner-button');\n    return button?.textContent || undefined;\n  }\n\n  private extractBannerButtonLink(element: Element): string | undefined {\n    const button = element.querySelector('.pagebuilder-banner-button a');\n    return button?.getAttribute('href') || undefined;\n  }\n\n  private extractVideoType(element: Element): 'youtube' | 'vimeo' | 'mp4' {\n    const iframe = element.querySelector('iframe');\n    if (iframe?.src.includes('youtube.com')) return 'youtube';\n    if (iframe?.src.includes('vimeo.com')) return 'vimeo';\n    return 'mp4';\n  }\n\n  private extractVideoFallbackImage(element: Element): string | undefined {\n    const img = element.querySelector('img');\n    return img?.getAttribute('src') || undefined;\n  }\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;AAE9B;AAeA;;;AAUO,MAAM;IACH,OAAgC;IAExC,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,gDAAgD;IAChD,aAAa,OAAgB,EAA6B;QACxD,IAAI;YACF,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YACnC,IAAI,CAAC,aAAa;gBAChB,OAAO;YACT;YAEA,0BAA0B;YAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,YAAY,EAAE;gBACnD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,YAAY,CAAC;YACvD;YAEA,sBAAsB;YACtB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;QACnC,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,OAAgB;YAChC,OAAO;QACT;IACF;IAEA,wBAAwB;IAChB,YAAY,OAAgB,EAAE,IAA4B,EAA6B;QAC7F,MAAM,cAAc,IAAI,CAAC,iBAAiB,CAAC,SAAS;QAEpD,OAAQ;YACN,KAAK,qIAAA,CAAA,yBAAsB,CAAC,GAAG;gBAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS;YAChC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,MAAM;gBAChC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;YACnC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,IAAI;gBAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;YACjC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,OAAO;gBACjC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;YACpC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,KAAK;gBAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS;YAClC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,MAAM;gBAChC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;YACnC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,MAAM;gBAChC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;YACnC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,KAAK;gBAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS;YAClC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,IAAI;gBAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;YACjC,KAAK,qIAAA,CAAA,yBAAsB,CAAC,OAAO;gBACjC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;YACpC;gBACE,OAAO;QACX;IACF;IAEA,6CAA6C;IACrC,kBAAkB,OAAgB,EAAE,IAA4B,EAAsB;QAC5F,OAAO;YACL;YACA,IAAI,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD;YACpB,YAAY,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC9B,QAAQ,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD,EAAE;YACtB,SAAS,QAAQ,SAAS;QAC5B;IACF;IAEA,oBAAoB;IACZ,SAAS,OAAgB,EAAE,WAA+B,EAAc;QAC9E,MAAM,aAAa,YAAY,UAAU;QAEzC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,GAAG;YAChC,YAAY;gBACV,YAAY,WAAW,UAAU,IAAI;gBACrC,gBAAgB,WAAW,cAAc,KAAK;gBAC9C,eAAe,WAAW,WAAW,aAAa,KAAK;gBACvD,iBAAiB,WAAW,eAAe;gBAC3C,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,WAAW,gBAAgB;gBACtE,gBAAgB,WAAW,cAAc,IAAI;gBAC7C,oBAAoB,WAAW,kBAAkB,IAAI;gBACrD,sBAAsB,WAAW,oBAAoB,IAAI;gBACzD,kBAAkB,WAAW,gBAAgB,IAAI;gBACjD,WAAW,WAAW,SAAS;gBAC/B,mBAAmB,WAAW,iBAAiB,IAAI;YACrD;QACF;IACF;IAEA,uBAAuB;IACf,YAAY,OAAgB,EAAE,WAA+B,EAAiB;QACpF,MAAM,aAAa,YAAY,UAAU;QACzC,MAAM,SAAS,YAAY,MAAM;QAEjC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,MAAM;YACnC,YAAY;gBACV,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS;gBACxC,YAAY,WAAW,UAAU,IAAI;gBACrC,iBAAiB,WAAW,eAAe;gBAC3C,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,WAAW,gBAAgB;gBACtE,gBAAgB,WAAW,cAAc,IAAI;gBAC7C,oBAAoB,WAAW,kBAAkB,IAAI;gBACrD,sBAAsB,WAAW,oBAAoB,IAAI;gBACzD,kBAAkB,WAAW,gBAAgB,IAAI;gBACjD,WAAW,WAAW,SAAS;gBAC/B,mBAAmB,WAAW,iBAAiB,IAAI;YACrD;QACF;IACF;IAEA,qBAAqB;IACb,UAAU,OAAgB,EAAE,WAA+B,EAAe;QAChF,MAAM,aAAa,YAAY,UAAU;QAEzC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,IAAI;YACjC,SAAS,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;YACpC,YAAY;gBACV,WAAW,WAAW,SAAS,IAAI;gBACnC,QAAQ,WAAW,MAAM;gBACzB,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,cAAc,WAAW,YAAY;YACvC;QACF;IACF;IAEA,wBAAwB;IAChB,aAAa,OAAgB,EAAE,WAA+B,EAAkB;QACtF,MAAM,aAAa,YAAY,UAAU;QAEzC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,OAAO;YACpC,SAAS,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;YACpC,YAAY;gBACV,aAAa,IAAI,CAAC,kBAAkB,CAAC;gBACrC,WAAW,WAAW,SAAS,IAAI;gBACnC,QAAQ,WAAW,MAAM;gBACzB,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,cAAc,WAAW,YAAY;YACvC;QACF;IACF;IAEA,sBAAsB;IACd,WAAW,OAAgB,EAAE,WAA+B,EAAgB;QAClF,MAAM,aAAa,YAAY,UAAU;QACzC,MAAM,MAAM,QAAQ,OAAO,KAAK,QAAQ,UAAU,QAAQ,aAAa,CAAC;QAExE,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,KAAK;YAClC,YAAY;gBACV,KAAK,KAAK,aAAa,UAAU,WAAW,GAAG,IAAI;gBACnD,KAAK,KAAK,aAAa,UAAU,WAAW,GAAG,IAAI;gBACnD,OAAO,KAAK,aAAa,YAAY,WAAW,KAAK;gBACrD,SAAS,IAAI,CAAC,mBAAmB,CAAC;gBAClC,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBAC5B,YAAY,WAAW,UAAU,IAAI;gBACrC,WAAW,WAAW,SAAS,IAAI;gBACnC,QAAQ,WAAW,MAAM;gBACzB,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,cAAc,WAAW,YAAY;gBACrC,aAAa,IAAI,CAAC,MAAM,CAAC,iBAAiB,KAAK;YACjD;QACF;IACF;IAEA,uBAAuB;IACf,YAAY,OAAgB,EAAE,WAA+B,EAAiB;QACpF,MAAM,aAAa,YAAY,UAAU;QACzC,MAAM,OAAO,QAAQ,OAAO,KAAK,MAAM,UAAU,QAAQ,aAAa,CAAC;QAEvE,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,MAAM;YACnC,SAAS,CAAA,GAAA,qIAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;YACpC,YAAY;gBACV,YAAY,IAAI,CAAC,iBAAiB,CAAC;gBACnC,MAAM,MAAM,aAAa,WAAW,WAAW,IAAI;gBACnD,YAAY,MAAM,aAAa,aAAa,WAAW,UAAU,IAAI;gBACrE,WAAW,WAAW,SAAS,IAAI;gBACnC,QAAQ,WAAW,MAAM;gBACzB,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,cAAc,WAAW,YAAY;gBACrC,iBAAiB,WAAW,eAAe;gBAC3C,WAAW,WAAW,SAAS;YACjC;QACF;IACF;IAEA,uBAAuB;IACf,YAAY,OAAgB,EAAE,WAA+B,EAAiB;QACpF,MAAM,aAAa,YAAY,UAAU;QAEzC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,MAAM;YACnC,YAAY;gBACV,YAAY,WAAW,UAAU,IAAI;gBACrC,WAAW,WAAW,SAAS,IAAI;gBACnC,iBAAiB,WAAW,eAAe;gBAC3C,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,WAAW,gBAAgB;gBACtE,gBAAgB,WAAW,cAAc,IAAI;gBAC7C,oBAAoB,WAAW,kBAAkB,IAAI;gBACrD,sBAAsB,WAAW,oBAAoB,IAAI;gBACzD,kBAAkB,WAAW,gBAAgB,IAAI;gBACjD,YAAY,WAAW,UAAU,KAAK;gBACtC,YAAY,IAAI,CAAC,uBAAuB,CAAC;gBACzC,YAAY,WAAW,UAAU,IAAI;gBACrC,YAAY,IAAI,CAAC,uBAAuB,CAAC;gBACzC,cAAc,WAAW,YAAY,IAAI;gBACzC,aAAa,WAAW,WAAW,KAAK;gBACxC,cAAc,WAAW,YAAY;gBACrC,SAAS,IAAI,CAAC,oBAAoB,CAAC;gBACnC,kBAAkB,WAAW,gBAAgB,IAAI;YACnD;QACF;IACF;IAEA,sBAAsB;IACd,WAAW,OAAgB,EAAE,WAA+B,EAAgB;QAClF,MAAM,aAAa,YAAY,UAAU;QACzC,MAAM,QAAQ,QAAQ,aAAa,CAAC;QACpC,MAAM,SAAS,QAAQ,aAAa,CAAC;QAErC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,KAAK;YAClC,YAAY;gBACV,WAAW,IAAI,CAAC,gBAAgB,CAAC;gBACjC,UAAU,OAAO,aAAa,UAAU,QAAQ,aAAa,UAAU,WAAW,QAAQ;gBAC1F,SAAS,WAAW,OAAO;gBAC3B,UAAU,WAAW,QAAQ,IAAI;gBACjC,UAAU,WAAW,QAAQ,KAAK,UAAU,OAAO,aAAa;gBAChE,OAAO,WAAW,KAAK,KAAK,UAAU,OAAO,aAAa;gBAC1D,MAAM,WAAW,IAAI,KAAK,UAAU,OAAO,aAAa;gBACxD,UAAU,WAAW,QAAQ,KAAK,UAAU,OAAO,aAAa;gBAChE,aAAa,IAAI,CAAC,MAAM,CAAC,iBAAiB,KAAK;gBAC/C,eAAe,IAAI,CAAC,yBAAyB,CAAC;YAChD;QACF;IACF;IAEA,qBAAqB;IACb,UAAU,OAAgB,EAAE,WAA+B,EAAe;QAChF,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,IAAI;YACjC,SAAS,QAAQ,SAAS;QAC5B;IACF;IAEA,wBAAwB;IAChB,aAAa,OAAgB,EAAE,WAA+B,EAAkB;QACtF,MAAM,aAAa,YAAY,UAAU;QAEzC,OAAO;YACL,GAAG,WAAW;YACd,MAAM,qIAAA,CAAA,yBAAsB,CAAC,OAAO;YACpC,YAAY;gBACV,WAAW,WAAW,SAAS,IAAI;gBACnC,eAAe,WAAW,aAAa,IAAI;gBAC3C,WAAW,WAAW,SAAS,IAAI;YACrC;QACF;IACF;IAEA,iBAAiB;IACT,qBAAqB,oBAA6B,EAAsB;QAC9E,IAAI,CAAC,sBAAsB,OAAO;QAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD,EAAE;QACrC,OAAO,MAAM,CAAC,EAAE,EAAE,4BAA4B;IAChD;IAEQ,mBAAmB,OAAgB,EAAE,MAA2B,EAAU;QAChF,kDAAkD;QAClD,IAAI,OAAO,KAAK,EAAE,OAAO,OAAO,KAAK;QACrC,IAAI,OAAO,SAAS,EAAE,OAAO,OAAO,SAAS;QAE7C,mCAAmC;QACnC,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,SAAS;QAC9C,KAAK,MAAM,aAAa,UAAW;YACjC,IAAI,UAAU,QAAQ,CAAC,SAAS;gBAC9B,MAAM,QAAQ,UAAU,KAAK,CAAC;gBAC9B,IAAI,OAAO;oBACT,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE;oBAC9B,OAAO,GAAG,AAAC,OAAO,KAAM,IAAI,CAAC,CAAC;gBAChC;YACF;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,OAAgB,EAA2C;QACpF,MAAM,UAAU,QAAQ,OAAO,CAAC,WAAW;QAC3C,IAAI;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,UAAU;YAC1D,OAAO;QACT;QACA,OAAO,MAAM,UAAU;IACzB;IAEQ,kBAAkB,OAAgB,EAAoC;QAC5E,MAAM,YAAY,MAAM,IAAI,CAAC,QAAQ,SAAS;QAC9C,IAAI,UAAU,QAAQ,CAAC,iCAAiC,OAAO;QAC/D,IAAI,UAAU,QAAQ,CAAC,4BAA4B,OAAO;QAC1D,OAAO;IACT;IAEQ,oBAAoB,OAAgB,EAAsB;QAChE,MAAM,UAAU,QAAQ,aAAa,CAAC;QACtC,OAAO,SAAS,eAAe;IACjC;IAEQ,iBAAiB,OAAgB,EAAsB;QAC7D,MAAM,OAAO,QAAQ,aAAa,CAAC;QACnC,OAAO,MAAM,aAAa,WAAW;IACvC;IAEQ,qBAAqB,OAAgB,EAAsB;QACjE,MAAM,UAAU,QAAQ,aAAa,CAAC;QACtC,OAAO,SAAS,aAAa;IAC/B;IAEQ,wBAAwB,OAAgB,EAAsB;QACpE,MAAM,SAAS,QAAQ,aAAa,CAAC;QACrC,OAAO,QAAQ,eAAe;IAChC;IAEQ,wBAAwB,OAAgB,EAAsB;QACpE,MAAM,SAAS,QAAQ,aAAa,CAAC;QACrC,OAAO,QAAQ,aAAa,WAAW;IACzC;IAEQ,iBAAiB,OAAgB,EAA+B;QACtE,MAAM,SAAS,QAAQ,aAAa,CAAC;QACrC,IAAI,QAAQ,IAAI,SAAS,gBAAgB,OAAO;QAChD,IAAI,QAAQ,IAAI,SAAS,cAAc,OAAO;QAC9C,OAAO;IACT;IAEQ,0BAA0B,OAAgB,EAAsB;QACtE,MAAM,MAAM,QAAQ,aAAa,CAAC;QAClC,OAAO,KAAK,aAAa,UAAU;IACrC;AACF", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/parser/PageBuilderParser.ts"], "sourcesContent": ["// Main Page Builder Parser\n\nimport {\n  Page<PERSON><PERSON>erContent,\n  PageBuilderElement,\n  PageBuilderParserConfig,\n} from '@/lib/pagebuilder/types';\nimport { mergeConfig, isPageBuilderElement, logParsingError } from '@/lib/pagebuilder/utils';\nimport { ElementParser } from './ElementParser';\n\nexport class PageBuilderParser {\n  private config: PageBuilderParserConfig;\n  private elementParser: ElementParser;\n\n  constructor(config: Partial<PageBuilderParserConfig> = {}) {\n    this.config = mergeConfig(config);\n    this.elementParser = new ElementParser(this.config);\n  }\n\n  // Parse HTML string into PageBuilderContent\n  parse(html: string): PageBuilderContent {\n    try {\n      // Create a temporary DOM to parse the HTML\n      const parser = new DOMParser();\n      const doc = parser.parseFromString(html, 'text/html');\n      \n      // Extract Page Builder elements\n      const elements = this.parseElements(doc.body);\n      \n      return {\n        elements,\n        rawHtml: html,\n        version: this.extractPageBuilderVersion(html),\n      };\n    } catch (error) {\n      logParsingError(error as Error);\n      return {\n        elements: [],\n        rawHtml: html,\n      };\n    }\n  }\n\n  // Parse elements from a DOM node\n  private parseElements(container: Element): PageBuilderElement[] {\n    const elements: PageBuilderElement[] = [];\n    \n    // Get direct children that are Page Builder elements\n    const children = Array.from(container.children);\n    \n    for (const child of children) {\n      if (isPageBuilderElement(child)) {\n        const element = this.elementParser.parseElement(child);\n        if (element) {\n          // Parse children recursively\n          element.children = this.parseElements(child);\n          elements.push(element);\n        }\n      } else {\n        // Check if this element contains Page Builder elements\n        const nestedElements = this.parseElements(child);\n        elements.push(...nestedElements);\n      }\n    }\n    \n    return elements;\n  }\n\n  // Extract Page Builder version from HTML\n  private extractPageBuilderVersion(html: string): string | undefined {\n    const versionMatch = html.match(/data-pb-version=\"([^\"]+)\"/);\n    return versionMatch ? versionMatch[1] : undefined;\n  }\n\n  // Parse HTML from server-side (Node.js environment)\n  static parseServerSide(html: string, config: Partial<PageBuilderParserConfig> = {}): PageBuilderContent {\n    // For server-side parsing, we need to use a different approach\n    // since DOMParser is not available in Node.js\n    \n    if (typeof window === 'undefined') {\n      // Server-side parsing using regex and string manipulation\n      return PageBuilderParser.parseWithRegex(html, config);\n    }\n    \n    // Client-side parsing\n    const parser = new PageBuilderParser(config);\n    return parser.parse(html);\n  }\n\n  // Fallback regex-based parsing for server-side\n  private static parseWithRegex(html: string, config: Partial<PageBuilderParserConfig> = {}): PageBuilderContent {\n    const elements: PageBuilderElement[] = [];\n    \n    try {\n      // Extract Page Builder elements using regex patterns\n      const rowPattern = /<div[^>]*data-content-type=\"row\"[^>]*>(.*?)<\\/div>/gs;\n      const matches = html.matchAll(rowPattern);\n      \n      for (const match of matches) {\n        const element = PageBuilderParser.parseElementWithRegex(match[0]);\n        if (element) {\n          elements.push(element);\n        }\n      }\n    } catch (error) {\n      logParsingError(error as Error);\n    }\n    \n    return {\n      elements,\n      rawHtml: html,\n    };\n  }\n\n  // Parse single element with regex (server-side fallback)\n  private static parseElementWithRegex(elementHtml: string): PageBuilderElement | null {\n    try {\n      // Extract data-content-type\n      const contentTypeMatch = elementHtml.match(/data-content-type=\"([^\"]+)\"/);\n      if (!contentTypeMatch) return null;\n      \n      const contentType = contentTypeMatch[1];\n      \n      // Basic element structure\n      const element: PageBuilderElement = {\n        type: contentType as any,\n        id: `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        attributes: PageBuilderParser.extractAttributesWithRegex(elementHtml),\n        styles: {},\n        rawHtml: elementHtml,\n      };\n      \n      // Extract content for text/heading elements\n      if (contentType === 'text' || contentType === 'heading') {\n        const contentMatch = elementHtml.match(/>([^<]+)</);\n        element.content = contentMatch ? contentMatch[1].trim() : '';\n      }\n      \n      return element;\n    } catch (error) {\n      logParsingError(error as Error);\n      return null;\n    }\n  }\n\n  // Extract attributes with regex (server-side fallback)\n  private static extractAttributesWithRegex(html: string): Record<string, any> {\n    const attributes: Record<string, any> = {};\n    \n    // Extract data attributes\n    const dataAttrPattern = /data-([^=]+)=\"([^\"]+)\"/g;\n    let match;\n    \n    while ((match = dataAttrPattern.exec(html)) !== null) {\n      const key = match[1].replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n      attributes[key] = match[2];\n    }\n    \n    return attributes;\n  }\n\n  // Validate parsed content\n  validateContent(content: PageBuilderContent): boolean {\n    try {\n      // Check if content has valid structure\n      if (!content.elements || !Array.isArray(content.elements)) {\n        return false;\n      }\n      \n      // Validate each element\n      for (const element of content.elements) {\n        if (!this.validateElement(element)) {\n          return false;\n        }\n      }\n      \n      return true;\n    } catch (error) {\n      logParsingError(error as Error);\n      return false;\n    }\n  }\n\n  // Validate single element\n  private validateElement(element: PageBuilderElement): boolean {\n    // Check required properties\n    if (!element.type || !element.id) {\n      return false;\n    }\n    \n    // Validate children recursively\n    if (element.children) {\n      for (const child of element.children) {\n        if (!this.validateElement(child)) {\n          return false;\n        }\n      }\n    }\n    \n    return true;\n  }\n\n  // Clean and optimize parsed content\n  optimizeContent(content: PageBuilderContent): PageBuilderContent {\n    return {\n      ...content,\n      elements: this.optimizeElements(content.elements),\n    };\n  }\n\n  // Optimize elements (remove empty elements, merge similar elements, etc.)\n  private optimizeElements(elements: PageBuilderElement[]): PageBuilderElement[] {\n    return elements\n      .filter(element => this.shouldKeepElement(element))\n      .map(element => ({\n        ...element,\n        children: element.children ? this.optimizeElements(element.children) : undefined,\n      }));\n  }\n\n  // Determine if element should be kept during optimization\n  private shouldKeepElement(element: PageBuilderElement): boolean {\n    // Remove empty text elements\n    if (element.type === 'text' && (!element.content || element.content.trim() === '')) {\n      return false;\n    }\n    \n    // Remove empty HTML elements\n    if (element.type === 'html' && (!element.content || element.content.trim() === '')) {\n      return false;\n    }\n    \n    // Keep all other elements\n    return true;\n  }\n\n  // Get configuration\n  getConfig(): PageBuilderParserConfig {\n    return this.config;\n  }\n\n  // Update configuration\n  updateConfig(newConfig: Partial<PageBuilderParserConfig>): void {\n    this.config = mergeConfig({ ...this.config, ...newConfig });\n    this.elementParser = new ElementParser(this.config);\n  }\n}\n\n// Export default instance\nexport const defaultParser = new PageBuilderParser();\n\n// Export static methods for convenience\nexport const parsePageBuilderContent = (html: string, config?: Partial<PageBuilderParserConfig>) => {\n  return PageBuilderParser.parseServerSide(html, config);\n};\n\nexport const validatePageBuilderContent = (content: PageBuilderContent) => {\n  return defaultParser.validateContent(content);\n};\n\nexport const optimizePageBuilderContent = (content: PageBuilderContent) => {\n  return defaultParser.optimizeContent(content);\n};\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;AAO3B;AACA;;;AAEO,MAAM;IACH,OAAgC;IAChC,cAA6B;IAErC,YAAY,SAA2C,CAAC,CAAC,CAAE;QACzD,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,8JAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM;IACpD;IAEA,4CAA4C;IAC5C,MAAM,IAAY,EAAsB;QACtC,IAAI;YACF,2CAA2C;YAC3C,MAAM,SAAS,IAAI;YACnB,MAAM,MAAM,OAAO,eAAe,CAAC,MAAM;YAEzC,gCAAgC;YAChC,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI;YAE5C,OAAO;gBACL;gBACA,SAAS;gBACT,SAAS,IAAI,CAAC,yBAAyB,CAAC;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE;YAChB,OAAO;gBACL,UAAU,EAAE;gBACZ,SAAS;YACX;QACF;IACF;IAEA,iCAAiC;IACzB,cAAc,SAAkB,EAAwB;QAC9D,MAAM,WAAiC,EAAE;QAEzC,qDAAqD;QACrD,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,QAAQ;QAE9C,KAAK,MAAM,SAAS,SAAU;YAC5B,IAAI,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;gBAC/B,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;gBAChD,IAAI,SAAS;oBACX,6BAA6B;oBAC7B,QAAQ,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;oBACtC,SAAS,IAAI,CAAC;gBAChB;YACF,OAAO;gBACL,uDAAuD;gBACvD,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;gBAC1C,SAAS,IAAI,IAAI;YACnB;QACF;QAEA,OAAO;IACT;IAEA,yCAAyC;IACjC,0BAA0B,IAAY,EAAsB;QAClE,MAAM,eAAe,KAAK,KAAK,CAAC;QAChC,OAAO,eAAe,YAAY,CAAC,EAAE,GAAG;IAC1C;IAEA,oDAAoD;IACpD,OAAO,gBAAgB,IAAY,EAAE,SAA2C,CAAC,CAAC,EAAsB;QACtG,+DAA+D;QAC/D,8CAA8C;QAE9C,uCAAmC;;QAGnC;QAEA,sBAAsB;QACtB,MAAM,SAAS,IAAI,kBAAkB;QACrC,OAAO,OAAO,KAAK,CAAC;IACtB;IAEA,+CAA+C;IAC/C,OAAe,eAAe,IAAY,EAAE,SAA2C,CAAC,CAAC,EAAsB;QAC7G,MAAM,WAAiC,EAAE;QAEzC,IAAI;YACF,qDAAqD;YACrD,MAAM,aAAa;YACnB,MAAM,UAAU,KAAK,QAAQ,CAAC;YAE9B,KAAK,MAAM,SAAS,QAAS;gBAC3B,MAAM,UAAU,kBAAkB,qBAAqB,CAAC,KAAK,CAAC,EAAE;gBAChE,IAAI,SAAS;oBACX,SAAS,IAAI,CAAC;gBAChB;YACF;QACF,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE;QAClB;QAEA,OAAO;YACL;YACA,SAAS;QACX;IACF;IAEA,yDAAyD;IACzD,OAAe,sBAAsB,WAAmB,EAA6B;QACnF,IAAI;YACF,4BAA4B;YAC5B,MAAM,mBAAmB,YAAY,KAAK,CAAC;YAC3C,IAAI,CAAC,kBAAkB,OAAO;YAE9B,MAAM,cAAc,gBAAgB,CAAC,EAAE;YAEvC,0BAA0B;YAC1B,MAAM,UAA8B;gBAClC,MAAM;gBACN,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACjE,YAAY,kBAAkB,0BAA0B,CAAC;gBACzD,QAAQ,CAAC;gBACT,SAAS;YACX;YAEA,4CAA4C;YAC5C,IAAI,gBAAgB,UAAU,gBAAgB,WAAW;gBACvD,MAAM,eAAe,YAAY,KAAK,CAAC;gBACvC,QAAQ,OAAO,GAAG,eAAe,YAAY,CAAC,EAAE,CAAC,IAAI,KAAK;YAC5D;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE;YAChB,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,OAAe,2BAA2B,IAAY,EAAuB;QAC3E,MAAM,aAAkC,CAAC;QAEzC,0BAA0B;QAC1B,MAAM,kBAAkB;QACxB,IAAI;QAEJ,MAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,KAAK,MAAM,KAAM;YACpD,MAAM,MAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,SAAW,OAAO,WAAW;YAC3E,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;QAC5B;QAEA,OAAO;IACT;IAEA,0BAA0B;IAC1B,gBAAgB,OAA2B,EAAW;QACpD,IAAI;YACF,uCAAuC;YACvC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,QAAQ,GAAG;gBACzD,OAAO;YACT;YAEA,wBAAwB;YACxB,KAAK,MAAM,WAAW,QAAQ,QAAQ,CAAE;gBACtC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU;oBAClC,OAAO;gBACT;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE;YAChB,OAAO;QACT;IACF;IAEA,0BAA0B;IAClB,gBAAgB,OAA2B,EAAW;QAC5D,4BAA4B;QAC5B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YAChC,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,QAAQ,QAAQ,EAAE;YACpB,KAAK,MAAM,SAAS,QAAQ,QAAQ,CAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ;oBAChC,OAAO;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,gBAAgB,OAA2B,EAAsB;QAC/D,OAAO;YACL,GAAG,OAAO;YACV,UAAU,IAAI,CAAC,gBAAgB,CAAC,QAAQ,QAAQ;QAClD;IACF;IAEA,0EAA0E;IAClE,iBAAiB,QAA8B,EAAwB;QAC7E,OAAO,SACJ,MAAM,CAAC,CAAA,UAAW,IAAI,CAAC,iBAAiB,CAAC,UACzC,GAAG,CAAC,CAAA,UAAW,CAAC;gBACf,GAAG,OAAO;gBACV,UAAU,QAAQ,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,QAAQ,IAAI;YACzE,CAAC;IACL;IAEA,0DAA0D;IAClD,kBAAkB,OAA2B,EAAW;QAC9D,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,OAAO,EAAE,GAAG;YAClF,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,KAAK,UAAU,CAAC,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,OAAO,EAAE,GAAG;YAClF,OAAO;QACT;QAEA,0BAA0B;QAC1B,OAAO;IACT;IAEA,oBAAoB;IACpB,YAAqC;QACnC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,uBAAuB;IACvB,aAAa,SAA2C,EAAQ;QAC9D,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,SAAS;QAAC;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,8JAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,MAAM;IACpD;AACF;AAGO,MAAM,gBAAgB,IAAI;AAG1B,MAAM,0BAA0B,CAAC,MAAc;IACpD,OAAO,kBAAkB,eAAe,CAAC,MAAM;AACjD;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO,cAAc,eAAe,CAAC;AACvC;AAEO,MAAM,6BAA6B,CAAC;IACzC,OAAO,cAAc,eAAe,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 2900, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/PageBuilderRenderer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext } from 'react';\nimport { Box } from '@mui/material';\nimport {\n  PageBuilderContent,\n  PageBuilderElement,\n  PageBuilderElementType,\n  PageBuilderContext,\n  PageBuilderParserConfig,\n} from '@/lib/pagebuilder/types';\nimport { mergeConfig } from '@/lib/pagebuilder/utils';\n\n// Import Page Builder components\nimport {\n  Row,\n  Column,\n  Text,\n  Heading,\n  PageBuilderImage,\n  Button,\n  Banner,\n  Video,\n  Html,\n  Divider,\n} from './elements';\n\n// Page Builder Context\nconst PageBuilderContextProvider = createContext<PageBuilderContext | null>(null);\n\n// Hook to use Page Builder context\nexport const usePageBuilderContext = (): PageBuilderContext => {\n  const context = useContext(PageBuilderContextProvider);\n  if (!context) {\n    throw new Error('usePageBuilderContext must be used within a PageBuilderRenderer');\n  }\n  return context;\n};\n\n// Component mapping\nconst COMPONENT_MAP = {\n  [PageBuilderElementType.ROW]: Row,\n  [PageBuilderElementType.COLUMN]: Column,\n  [PageBuilderElementType.TEXT]: Text,\n  [PageBuilderElementType.HEADING]: Heading,\n  [PageBuilderElementType.IMAGE]: PageBuilderImage,\n  [PageBuilderElementType.BUTTON]: Button,\n  [PageBuilderElementType.BANNER]: Banner,\n  [PageBuilderElementType.VIDEO]: Video,\n  [PageBuilderElementType.HTML]: Html,\n  [PageBuilderElementType.DIVIDER]: Divider,\n  // Add more components as needed\n};\n\n// Props for PageBuilderRenderer\ninterface PageBuilderRendererProps {\n  content: PageBuilderContent | string;\n  config?: Partial<PageBuilderParserConfig>;\n  className?: string;\n  style?: React.CSSProperties;\n  isEditing?: boolean;\n  deviceType?: 'mobile' | 'tablet' | 'desktop';\n}\n\n// Props for ElementRenderer\ninterface ElementRendererProps {\n  element: PageBuilderElement;\n  config: PageBuilderParserConfig;\n}\n\n// Individual element renderer\nconst ElementRenderer: React.FC<ElementRendererProps> = ({ element, config }) => {\n  // Get component from mapping or custom overrides\n  const Component = config.componentOverrides?.[element.type] || COMPONENT_MAP[element.type];\n\n  if (!Component) {\n    // Fallback for unknown element types\n    console.warn(`Unknown Page Builder element type: ${element.type}`);\n    return (\n      <Box\n        sx={{\n          p: 2,\n          border: '1px dashed #ccc',\n          borderRadius: 1,\n          backgroundColor: '#f5f5f5',\n          textAlign: 'center',\n          color: 'text.secondary',\n        }}\n      >\n        Unknown element: {element.type}\n      </Box>\n    );\n  }\n\n  // Render children if they exist\n  const children = element.children?.map((child, index) => (\n    <ElementRenderer key={child.id || index} element={child} config={config} />\n  ));\n\n  return (\n    <Component\n      element={element}\n      className={`pagebuilder-element pagebuilder-${element.type}`}\n    >\n      {children}\n    </Component>\n  );\n};\n\n// Main Page Builder Renderer component\nexport const PageBuilderRenderer: React.FC<PageBuilderRendererProps> = ({\n  content,\n  config = {},\n  className,\n  style,\n  isEditing = false,\n  deviceType = 'desktop',\n}) => {\n  // Merge configuration\n  const mergedConfig = mergeConfig(config);\n\n  // Parse content if it's a string\n  let pageBuilderContent: PageBuilderContent;\n  \n  if (typeof content === 'string') {\n    // Import parser dynamically to avoid SSR issues\n    const { parsePageBuilderContent } = require('./parser/PageBuilderParser');\n    pageBuilderContent = parsePageBuilderContent(content, mergedConfig);\n  } else {\n    pageBuilderContent = content;\n  }\n\n  // Create context value\n  const contextValue: PageBuilderContext = {\n    config: mergedConfig,\n    isEditing,\n    deviceType,\n  };\n\n  // If no elements, return empty\n  if (!pageBuilderContent.elements || pageBuilderContent.elements.length === 0) {\n    return null;\n  }\n\n  return (\n    <PageBuilderContextProvider.Provider value={contextValue}>\n      <Box\n        className={`pagebuilder-content ${className || ''}`}\n        sx={{\n          width: '100%',\n          ...style,\n        }}\n      >\n        {pageBuilderContent.elements.map((element, index) => (\n          <ElementRenderer\n            key={element.id || index}\n            element={element}\n            config={mergedConfig}\n          />\n        ))}\n      </Box>\n    </PageBuilderContextProvider.Provider>\n  );\n};\n\n// Server-side safe renderer (for SSR)\nexport const PageBuilderRendererSSR: React.FC<PageBuilderRendererProps> = (props) => {\n  // For SSR, we need to handle the parsing differently\n  if (typeof window === 'undefined' && typeof props.content === 'string') {\n    // Server-side: parse with regex-based parser\n    const { parsePageBuilderContent } = require('./parser/PageBuilderParser');\n    const parsedContent = parsePageBuilderContent(props.content, props.config);\n    \n    return (\n      <PageBuilderRenderer\n        {...props}\n        content={parsedContent}\n      />\n    );\n  }\n\n  // Client-side: use normal renderer\n  return <PageBuilderRenderer {...props} />;\n};\n\n// Utility component for rendering raw HTML (fallback)\nexport const RawHtmlRenderer: React.FC<{ html: string; className?: string }> = ({\n  html,\n  className,\n}) => {\n  return (\n    <Box\n      className={`pagebuilder-raw-html ${className || ''}`}\n      dangerouslySetInnerHTML={{ __html: html }}\n      sx={{\n        '& .pagebuilder-row': {\n          display: 'flex',\n          flexDirection: 'column',\n          width: '100%',\n        },\n        '& .pagebuilder-column-group': {\n          display: 'flex',\n          flexWrap: 'wrap',\n          width: '100%',\n        },\n        '& .pagebuilder-column': {\n          flex: '1 1 auto',\n          minWidth: 0,\n        },\n        // Add more CSS for Page Builder elements\n      }}\n    />\n  );\n};\n\n// Hook for accessing Page Builder configuration\nexport const usePageBuilderConfig = (): PageBuilderParserConfig => {\n  const context = usePageBuilderContext();\n  return context.config;\n};\n\n// Hook for checking if in editing mode\nexport const usePageBuilderEditing = (): boolean => {\n  const context = usePageBuilderContext();\n  return context.isEditing || false;\n};\n\n// Hook for getting device type\nexport const usePageBuilderDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {\n  const context = usePageBuilderContext();\n  return context.deviceType || 'desktop';\n};\n\nexport default PageBuilderRenderer;\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAOA;AAEA,iCAAiC;AACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;AA2BA,uBAAuB;AACvB,MAAM,2CAA6B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA6B;AAGrE,MAAM,wBAAwB;;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQb,oBAAoB;AACpB,MAAM,gBAAgB;IACpB,CAAC,qIAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,EAAE,yLAAA,CAAA,MAAG;IACjC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC,EAAE,+LAAA,CAAA,SAAM;IACvC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,EAAE,2LAAA,CAAA,OAAI;IACnC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,OAAO,CAAC,EAAE,iMAAA,CAAA,UAAO;IACzC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE,wMAAA,CAAA,mBAAgB;IAChD,CAAC,qIAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC,EAAE,+LAAA,CAAA,SAAM;IACvC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC,EAAE,+LAAA,CAAA,SAAM;IACvC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC,EAAE,6LAAA,CAAA,QAAK;IACrC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,EAAE,2LAAA,CAAA,OAAI;IACnC,CAAC,qIAAA,CAAA,yBAAsB,CAAC,OAAO,CAAC,EAAE,iMAAA,CAAA,UAAO;AAE3C;AAkBA,8BAA8B;AAC9B,MAAM,kBAAkD,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE;IAC1E,iDAAiD;IACjD,MAAM,YAAY,OAAO,kBAAkB,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,aAAa,CAAC,QAAQ,IAAI,CAAC;IAE1F,IAAI,CAAC,WAAW;QACd,qCAAqC;QACrC,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,QAAQ,IAAI,EAAE;QACjE,qBACE,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,GAAG;gBACH,QAAQ;gBACR,cAAc;gBACd,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;;gBACD;gBACmB,QAAQ,IAAI;;;;;;;IAGpC;IAEA,gCAAgC;IAChC,MAAM,WAAW,QAAQ,QAAQ,EAAE,IAAI,CAAC,OAAO,sBAC7C,6LAAC;YAAwC,SAAS;YAAO,QAAQ;WAA3C,MAAM,EAAE,IAAI;;;;;IAGpC,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,CAAC,gCAAgC,EAAE,QAAQ,IAAI,EAAE;kBAE3D;;;;;;AAGP;KApCM;AAuCC,MAAM,sBAA0D,CAAC,EACtE,OAAO,EACP,SAAS,CAAC,CAAC,EACX,SAAS,EACT,KAAK,EACL,YAAY,KAAK,EACjB,aAAa,SAAS,EACvB;IACC,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,iCAAiC;IACjC,IAAI;IAEJ,IAAI,OAAO,YAAY,UAAU;QAC/B,gDAAgD;QAChD,MAAM,EAAE,uBAAuB,EAAE;QACjC,qBAAqB,wBAAwB,SAAS;IACxD,OAAO;QACL,qBAAqB;IACvB;IAEA,uBAAuB;IACvB,MAAM,eAAmC;QACvC,QAAQ;QACR;QACA;IACF;IAEA,+BAA+B;IAC/B,IAAI,CAAC,mBAAmB,QAAQ,IAAI,mBAAmB,QAAQ,CAAC,MAAM,KAAK,GAAG;QAC5E,OAAO;IACT;IAEA,qBACE,6LAAC,2BAA2B,QAAQ;QAAC,OAAO;kBAC1C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,WAAW,CAAC,oBAAoB,EAAE,aAAa,IAAI;YACnD,IAAI;gBACF,OAAO;gBACP,GAAG,KAAK;YACV;sBAEC,mBAAmB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oBAEC,SAAS;oBACT,QAAQ;mBAFH,QAAQ,EAAE,IAAI;;;;;;;;;;;;;;;AAQ/B;MArDa;AAwDN,MAAM,yBAA6D,CAAC;IACzE,qDAAqD;IACrD,uCAAwE;;IAWxE;IAEA,mCAAmC;IACnC,qBAAO,6LAAC;QAAqB,GAAG,KAAK;;;;;;AACvC;MAjBa;AAoBN,MAAM,kBAAkE,CAAC,EAC9E,IAAI,EACJ,SAAS,EACV;IACC,qBACE,6LAAC,2LAAA,CAAA,MAAG;QACF,WAAW,CAAC,qBAAqB,EAAE,aAAa,IAAI;QACpD,yBAAyB;YAAE,QAAQ;QAAK;QACxC,IAAI;YACF,sBAAsB;gBACpB,SAAS;gBACT,eAAe;gBACf,OAAO;YACT;YACA,+BAA+B;gBAC7B,SAAS;gBACT,UAAU;gBACV,OAAO;YACT;YACA,yBAAyB;gBACvB,MAAM;gBACN,UAAU;YACZ;QAEF;;;;;;AAGN;MA3Ba;AA8BN,MAAM,uBAAuB;;IAClC,MAAM,UAAU;IAChB,OAAO,QAAQ,MAAM;AACvB;IAHa;;QACK;;;AAKX,MAAM,wBAAwB;;IACnC,MAAM,UAAU;IAChB,OAAO,QAAQ,SAAS,IAAI;AAC9B;IAHa;;QACK;;;AAKX,MAAM,2BAA2B;;IACtC,MAAM,UAAU;IAChB,OAAO,QAAQ,UAAU,IAAI;AAC/B;IAHa;;QACK;;;uCAIH", "debugId": null}}, {"offset": {"line": 3144, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/parser/StyleParser.ts"], "sourcesContent": ["// Page Builder Style Parser\n\nimport { CSS_PROPERTY_MAPPING } from '@/lib/pagebuilder/constants';\n\nexport class StyleParser {\n  // Parse inline styles from style attribute\n  static parseInlineStyles(styleString: string): Record<string, any> {\n    const styles: Record<string, any> = {};\n    \n    if (!styleString) return styles;\n    \n    // Split by semicolon and process each declaration\n    const declarations = styleString.split(';').filter(decl => decl.trim());\n    \n    for (const declaration of declarations) {\n      const [property, value] = declaration.split(':').map(s => s.trim());\n      \n      if (property && value) {\n        const jsProperty = this.cssPropertyToJs(property);\n        styles[jsProperty] = this.parseValue(value);\n      }\n    }\n    \n    return styles;\n  }\n\n  // Parse CSS from data-pb-style attribute (Magento specific)\n  static parsePbStyles(pbStyleString: string): Record<string, any> {\n    const styles: Record<string, any> = {};\n    \n    if (!pbStyleString) return styles;\n    \n    try {\n      // Magento stores styles as encoded CSS\n      const decodedStyles = decodeURIComponent(pbStyleString);\n      return this.parseInlineStyles(decodedStyles);\n    } catch (error) {\n      console.warn('Failed to parse pb-style:', error);\n      return styles;\n    }\n  }\n\n  // Parse background images from Magento's data-background-images\n  static parseBackgroundImages(backgroundImagesData: string): {\n    desktop?: string;\n    mobile?: string;\n    tablet?: string;\n  } {\n    try {\n      const data = JSON.parse(backgroundImagesData);\n      return {\n        desktop: data.desktop_image,\n        mobile: data.mobile_image,\n        tablet: data.tablet_image,\n      };\n    } catch (error) {\n      return {};\n    }\n  }\n\n  // Convert CSS property name to JavaScript property name\n  private static cssPropertyToJs(cssProperty: string): string {\n    // Check if we have a mapping\n    if (CSS_PROPERTY_MAPPING[cssProperty as keyof typeof CSS_PROPERTY_MAPPING]) {\n      return CSS_PROPERTY_MAPPING[cssProperty as keyof typeof CSS_PROPERTY_MAPPING];\n    }\n    \n    // Convert kebab-case to camelCase\n    return cssProperty.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n  }\n\n  // Parse CSS value and convert to appropriate type\n  private static parseValue(value: string): any {\n    const trimmedValue = value.trim();\n    \n    // Handle numeric values\n    if (/^-?\\d+(\\.\\d+)?(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax)?$/.test(trimmedValue)) {\n      return trimmedValue;\n    }\n    \n    // Handle color values\n    if (this.isColor(trimmedValue)) {\n      return trimmedValue;\n    }\n    \n    // Handle URLs\n    if (trimmedValue.startsWith('url(')) {\n      return trimmedValue;\n    }\n    \n    // Handle keywords and other values\n    return trimmedValue;\n  }\n\n  // Check if value is a color\n  private static isColor(value: string): boolean {\n    // Hex colors\n    if (/^#([0-9A-F]{3}){1,2}$/i.test(value)) {\n      return true;\n    }\n    \n    // RGB/RGBA\n    if (/^rgba?\\(/.test(value)) {\n      return true;\n    }\n    \n    // HSL/HSLA\n    if (/^hsla?\\(/.test(value)) {\n      return true;\n    }\n    \n    // Named colors (basic check)\n    const namedColors = [\n      'transparent', 'black', 'white', 'red', 'green', 'blue',\n      'yellow', 'orange', 'purple', 'pink', 'gray', 'grey'\n    ];\n    \n    return namedColors.includes(value.toLowerCase());\n  }\n\n  // Generate responsive styles based on breakpoints\n  static generateResponsiveStyles(\n    styles: Record<string, any>,\n    breakpoint: 'mobile' | 'tablet' | 'desktop' = 'desktop'\n  ): Record<string, any> {\n    const responsiveStyles: Record<string, any> = { ...styles };\n    \n    // Apply breakpoint-specific adjustments\n    switch (breakpoint) {\n      case 'mobile':\n        // Adjust font sizes for mobile\n        if (responsiveStyles.fontSize) {\n          responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.8);\n        }\n        \n        // Adjust padding/margins for mobile\n        if (responsiveStyles.padding) {\n          responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.7);\n        }\n        if (responsiveStyles.margin) {\n          responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.7);\n        }\n        break;\n        \n      case 'tablet':\n        // Adjust font sizes for tablet\n        if (responsiveStyles.fontSize) {\n          responsiveStyles.fontSize = this.scaleFontSize(responsiveStyles.fontSize, 0.9);\n        }\n        \n        // Adjust padding/margins for tablet\n        if (responsiveStyles.padding) {\n          responsiveStyles.padding = this.scaleSpacing(responsiveStyles.padding, 0.85);\n        }\n        if (responsiveStyles.margin) {\n          responsiveStyles.margin = this.scaleSpacing(responsiveStyles.margin, 0.85);\n        }\n        break;\n    }\n    \n    return responsiveStyles;\n  }\n\n  // Scale font size by factor\n  private static scaleFontSize(fontSize: string, factor: number): string {\n    const match = fontSize.match(/^(\\d+(?:\\.\\d+)?)(px|em|rem|%)$/);\n    if (match) {\n      const value = parseFloat(match[1]);\n      const unit = match[2];\n      return `${(value * factor).toFixed(2)}${unit}`;\n    }\n    return fontSize;\n  }\n\n  // Scale spacing (padding/margin) by factor\n  private static scaleSpacing(spacing: string, factor: number): string {\n    // Handle multiple values (e.g., \"10px 20px\")\n    const values = spacing.split(' ');\n    const scaledValues = values.map(value => {\n      const match = value.match(/^(\\d+(?:\\.\\d+)?)(px|em|rem|%)$/);\n      if (match) {\n        const num = parseFloat(match[1]);\n        const unit = match[2];\n        return `${(num * factor).toFixed(2)}${unit}`;\n      }\n      return value;\n    });\n    \n    return scaledValues.join(' ');\n  }\n\n  // Convert Magento styles to Material-UI sx prop format\n  static toMuiSx(styles: Record<string, any>): Record<string, any> {\n    const muiStyles: Record<string, any> = {};\n    \n    Object.entries(styles).forEach(([key, value]) => {\n      // Convert specific properties for MUI\n      switch (key) {\n        case 'backgroundColor':\n          muiStyles.bgcolor = value;\n          break;\n        case 'textAlign':\n          muiStyles.textAlign = value;\n          break;\n        case 'fontSize':\n          muiStyles.fontSize = value;\n          break;\n        case 'fontWeight':\n          muiStyles.fontWeight = value;\n          break;\n        case 'color':\n          muiStyles.color = value;\n          break;\n        case 'padding':\n          muiStyles.p = this.convertSpacingToMui(value);\n          break;\n        case 'margin':\n          muiStyles.m = this.convertSpacingToMui(value);\n          break;\n        case 'paddingTop':\n          muiStyles.pt = this.convertSpacingToMui(value);\n          break;\n        case 'paddingRight':\n          muiStyles.pr = this.convertSpacingToMui(value);\n          break;\n        case 'paddingBottom':\n          muiStyles.pb = this.convertSpacingToMui(value);\n          break;\n        case 'paddingLeft':\n          muiStyles.pl = this.convertSpacingToMui(value);\n          break;\n        case 'marginTop':\n          muiStyles.mt = this.convertSpacingToMui(value);\n          break;\n        case 'marginRight':\n          muiStyles.mr = this.convertSpacingToMui(value);\n          break;\n        case 'marginBottom':\n          muiStyles.mb = this.convertSpacingToMui(value);\n          break;\n        case 'marginLeft':\n          muiStyles.ml = this.convertSpacingToMui(value);\n          break;\n        case 'borderRadius':\n          muiStyles.borderRadius = value;\n          break;\n        case 'border':\n          muiStyles.border = value;\n          break;\n        case 'borderColor':\n          muiStyles.borderColor = value;\n          break;\n        case 'borderWidth':\n          muiStyles.borderWidth = value;\n          break;\n        default:\n          // Keep other properties as-is\n          muiStyles[key] = value;\n      }\n    });\n    \n    return muiStyles;\n  }\n\n  // Convert spacing value to MUI spacing units\n  private static convertSpacingToMui(value: string): string | number {\n    // If it's a pixel value, convert to MUI spacing units (8px = 1 unit)\n    const pxMatch = value.match(/^(\\d+)px$/);\n    if (pxMatch) {\n      const pixels = parseInt(pxMatch[1]);\n      return Math.round(pixels / 8);\n    }\n    \n    // Return as-is for other units\n    return value;\n  }\n\n  // Clean styles by removing empty or invalid values\n  static cleanStyles(styles: Record<string, any>): Record<string, any> {\n    const cleanedStyles: Record<string, any> = {};\n    \n    Object.entries(styles).forEach(([key, value]) => {\n      if (value !== null && value !== undefined && value !== '') {\n        cleanedStyles[key] = value;\n      }\n    });\n    \n    return cleanedStyles;\n  }\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAE5B;;AAEO,MAAM;IACX,2CAA2C;IAC3C,OAAO,kBAAkB,WAAmB,EAAuB;QACjE,MAAM,SAA8B,CAAC;QAErC,IAAI,CAAC,aAAa,OAAO;QAEzB,kDAAkD;QAClD,MAAM,eAAe,YAAY,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;QAEpE,KAAK,MAAM,eAAe,aAAc;YACtC,MAAM,CAAC,UAAU,MAAM,GAAG,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAEhE,IAAI,YAAY,OAAO;gBACrB,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC;gBACxC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC;QACF;QAEA,OAAO;IACT;IAEA,4DAA4D;IAC5D,OAAO,cAAc,aAAqB,EAAuB;QAC/D,MAAM,SAA8B,CAAC;QAErC,IAAI,CAAC,eAAe,OAAO;QAE3B,IAAI;YACF,uCAAuC;YACvC,MAAM,gBAAgB,mBAAmB;YACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,6BAA6B;YAC1C,OAAO;QACT;IACF;IAEA,gEAAgE;IAChE,OAAO,sBAAsB,oBAA4B,EAIvD;QACA,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,OAAO;gBACL,SAAS,KAAK,aAAa;gBAC3B,QAAQ,KAAK,YAAY;gBACzB,QAAQ,KAAK,YAAY;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IAEA,wDAAwD;IACxD,OAAe,gBAAgB,WAAmB,EAAU;QAC1D,6BAA6B;QAC7B,IAAI,yIAAA,CAAA,uBAAoB,CAAC,YAAiD,EAAE;YAC1E,OAAO,yIAAA,CAAA,uBAAoB,CAAC,YAAiD;QAC/E;QAEA,kCAAkC;QAClC,OAAO,YAAY,OAAO,CAAC,aAAa,CAAC,GAAG,SAAW,OAAO,WAAW;IAC3E;IAEA,kDAAkD;IAClD,OAAe,WAAW,KAAa,EAAO;QAC5C,MAAM,eAAe,MAAM,IAAI;QAE/B,wBAAwB;QACxB,IAAI,qEAAqE,IAAI,CAAC,eAAe;YAC3F,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe;YAC9B,OAAO;QACT;QAEA,cAAc;QACd,IAAI,aAAa,UAAU,CAAC,SAAS;YACnC,OAAO;QACT;QAEA,mCAAmC;QACnC,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAe,QAAQ,KAAa,EAAW;QAC7C,aAAa;QACb,IAAI,yBAAyB,IAAI,CAAC,QAAQ;YACxC,OAAO;QACT;QAEA,WAAW;QACX,IAAI,WAAW,IAAI,CAAC,QAAQ;YAC1B,OAAO;QACT;QAEA,WAAW;QACX,IAAI,WAAW,IAAI,CAAC,QAAQ;YAC1B,OAAO;QACT;QAEA,6BAA6B;QAC7B,MAAM,cAAc;YAClB;YAAe;YAAS;YAAS;YAAO;YAAS;YACjD;YAAU;YAAU;YAAU;YAAQ;YAAQ;SAC/C;QAED,OAAO,YAAY,QAAQ,CAAC,MAAM,WAAW;IAC/C;IAEA,kDAAkD;IAClD,OAAO,yBACL,MAA2B,EAC3B,aAA8C,SAAS,EAClC;QACrB,MAAM,mBAAwC;YAAE,GAAG,MAAM;QAAC;QAE1D,wCAAwC;QACxC,OAAQ;YACN,KAAK;gBACH,+BAA+B;gBAC/B,IAAI,iBAAiB,QAAQ,EAAE;oBAC7B,iBAAiB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,QAAQ,EAAE;gBAC5E;gBAEA,oCAAoC;gBACpC,IAAI,iBAAiB,OAAO,EAAE;oBAC5B,iBAAiB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,OAAO,EAAE;gBACzE;gBACA,IAAI,iBAAiB,MAAM,EAAE;oBAC3B,iBAAiB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,MAAM,EAAE;gBACvE;gBACA;YAEF,KAAK;gBACH,+BAA+B;gBAC/B,IAAI,iBAAiB,QAAQ,EAAE;oBAC7B,iBAAiB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,QAAQ,EAAE;gBAC5E;gBAEA,oCAAoC;gBACpC,IAAI,iBAAiB,OAAO,EAAE;oBAC5B,iBAAiB,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,OAAO,EAAE;gBACzE;gBACA,IAAI,iBAAiB,MAAM,EAAE;oBAC3B,iBAAiB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,MAAM,EAAE;gBACvE;gBACA;QACJ;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,OAAe,cAAc,QAAgB,EAAE,MAAc,EAAU;QACrE,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,IAAI,OAAO;YACT,MAAM,QAAQ,WAAW,KAAK,CAAC,EAAE;YACjC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC,QAAQ,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM;QAChD;QACA,OAAO;IACT;IAEA,2CAA2C;IAC3C,OAAe,aAAa,OAAe,EAAE,MAAc,EAAU;QACnE,6CAA6C;QAC7C,MAAM,SAAS,QAAQ,KAAK,CAAC;QAC7B,MAAM,eAAe,OAAO,GAAG,CAAC,CAAA;YAC9B,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,IAAI,OAAO;gBACT,MAAM,MAAM,WAAW,KAAK,CAAC,EAAE;gBAC/B,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,OAAO,GAAG,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM;YAC9C;YACA,OAAO;QACT;QAEA,OAAO,aAAa,IAAI,CAAC;IAC3B;IAEA,uDAAuD;IACvD,OAAO,QAAQ,MAA2B,EAAuB;QAC/D,MAAM,YAAiC,CAAC;QAExC,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,sCAAsC;YACtC,OAAQ;gBACN,KAAK;oBACH,UAAU,OAAO,GAAG;oBACpB;gBACF,KAAK;oBACH,UAAU,SAAS,GAAG;oBACtB;gBACF,KAAK;oBACH,UAAU,QAAQ,GAAG;oBACrB;gBACF,KAAK;oBACH,UAAU,UAAU,GAAG;oBACvB;gBACF,KAAK;oBACH,UAAU,KAAK,GAAG;oBAClB;gBACF,KAAK;oBACH,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACvC;gBACF,KAAK;oBACH,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACvC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACxC;gBACF,KAAK;oBACH,UAAU,YAAY,GAAG;oBACzB;gBACF,KAAK;oBACH,UAAU,MAAM,GAAG;oBACnB;gBACF,KAAK;oBACH,UAAU,WAAW,GAAG;oBACxB;gBACF,KAAK;oBACH,UAAU,WAAW,GAAG;oBACxB;gBACF;oBACE,8BAA8B;oBAC9B,SAAS,CAAC,IAAI,GAAG;YACrB;QACF;QAEA,OAAO;IACT;IAEA,6CAA6C;IAC7C,OAAe,oBAAoB,KAAa,EAAmB;QACjE,qEAAqE;QACrE,MAAM,UAAU,MAAM,KAAK,CAAC;QAC5B,IAAI,SAAS;YACX,MAAM,SAAS,SAAS,OAAO,CAAC,EAAE;YAClC,OAAO,KAAK,KAAK,CAAC,SAAS;QAC7B;QAEA,+BAA+B;QAC/B,OAAO;IACT;IAEA,mDAAmD;IACnD,OAAO,YAAY,MAA2B,EAAuB;QACnE,MAAM,gBAAqC,CAAC;QAE5C,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;gBACzD,aAAa,CAAC,IAAI,GAAG;YACvB;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3412, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/pagebuilder/index.ts"], "sourcesContent": ["// Page Builder Module Exports\n\n// Main renderer components\nexport {\n  PageBuilderRenderer,\n  PageBuilderRendererSSR,\n  RawHtmlRenderer,\n  usePageBuilderContext,\n  usePageBuilderConfig,\n  usePageBuilderEditing,\n  usePageBuilderDeviceType,\n} from './PageBuilderRenderer';\n\n// Parser classes and functions\nexport {\n  PageBuilderParser,\n  defaultParser,\n  parsePageBuilderContent,\n  validatePageBuilderContent,\n  optimizePageBuilderContent,\n} from './parser/PageBuilderParser';\n\nexport { ElementParser } from './parser/ElementParser';\nexport { StyleParser } from './parser/StyleParser';\n\n// Individual element components\nexport {\n  Row,\n  Column,\n  Text,\n  Heading,\n  PageBuilderImage,\n  Button,\n  Banner,\n  Video,\n  Html,\n  Divider,\n} from './elements';\n\n// Types\nexport type {\n  PageBuilderContent,\n  PageBuilderElement,\n  PageBuilderElementType,\n  PageBuilderElementProps,\n  PageBuilderParserConfig,\n  PageBuilderContext,\n  RowElement,\n  ColumnElement,\n  TextElement,\n  HeadingElement,\n  ImageElement,\n  ButtonElement,\n  BannerElement,\n  VideoElement,\n  HtmlElement,\n  DividerElement,\n} from '@/lib/pagebuilder/types';\n\n// Constants and utilities\nexport {\n  PAGE_BUILDER_CLASSES,\n  PAGE_BUILDER_ATTRIBUTES,\n  ELEMENT_TYPE_MAPPING,\n  CONTENT_TYPE_MAPPING,\n  DEFAULT_STYLES,\n  BREAKPOINTS,\n  DEFAULT_CONFIG,\n} from '@/lib/pagebuilder/constants';\n\nexport {\n  generateElementId,\n  getElementType,\n  extractAttributes,\n  extractStyles,\n  parseBackgroundImages,\n  cssValueToNumber,\n  numberToCssValue,\n  isPageBuilderElement,\n  getElementContent,\n  cleanHtmlContent,\n  mergeConfig,\n  optimizeImageUrl,\n  validateElement,\n  getBreakpoint,\n  convertColumnWidth,\n  safeJsonParse,\n  debounce,\n  isBrowser,\n  logParsingError,\n} from '@/lib/pagebuilder/utils';\n"], "names": [], "mappings": "AAAA,8BAA8B;AAE9B,2BAA2B;;AAC3B;AAUA,+BAA+B;AAC/B;AAQA;AACA;AAEA,gCAAgC;AAChC;AAiCA,0BAA0B;AAC1B;AAUA", "debugId": null}}, {"offset": {"line": 3456, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/client.ts"], "sourcesContent": ["// Magento GraphQL Client with native fetch\ninterface GraphQLResponse<T> {\n  data: T;\n  errors?: Array<{\n    message: string;\n    locations?: Array<{ line: number; column: number }>;\n    path?: Array<string | number>;\n    extensions?: Record<string, any>;\n  }>;\n}\n\ninterface GraphQLRequest {\n  query: string;\n  variables?: Record<string, any>;\n  operationName?: string;\n}\n\nclass MagentoGraphQLError extends Error {\n  constructor(\n    message: string,\n    public errors: GraphQLResponse<any>['errors'] = [],\n    public response?: Response\n  ) {\n    super(message);\n    this.name = 'MagentoGraphQLError';\n  }\n}\n\nclass MagentoGraphQLClient {\n  private baseUrl: string;\n  private headers: Record<string, string>;\n  private cache: Map<string, { data: any; timestamp: number }> = new Map();\n  private cacheTimeout = 5 * 60 * 1000; // 5 minutes\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_MAGENTO_GRAPHQL_URL || 'http://magento2.local/graphql/';\n    this.headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    };\n\n    // Add admin token if available (for server-side requests)\n    if (typeof window === 'undefined' && process.env.MAGENTO_ADMIN_TOKEN) {\n      this.headers['Authorization'] = `Bearer ${process.env.MAGENTO_ADMIN_TOKEN}`;\n    }\n  }\n\n  // Set customer token for authenticated requests\n  setCustomerToken(token: string) {\n    this.headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  // Remove customer token\n  removeCustomerToken() {\n    delete this.headers['Authorization'];\n  }\n\n  // Generate cache key\n  private getCacheKey(query: string, variables?: Record<string, any>): string {\n    return `${query}:${JSON.stringify(variables || {})}`;\n  }\n\n  // Check if cache is valid\n  private isCacheValid(timestamp: number): boolean {\n    return Date.now() - timestamp < this.cacheTimeout;\n  }\n\n  // Get from cache\n  private getFromCache(cacheKey: string): any | null {\n    const cached = this.cache.get(cacheKey);\n    if (cached && this.isCacheValid(cached.timestamp)) {\n      return cached.data;\n    }\n    return null;\n  }\n\n  // Set cache\n  private setCache(cacheKey: string, data: any): void {\n    this.cache.set(cacheKey, {\n      data,\n      timestamp: Date.now(),\n    });\n  }\n\n  // Main fetch method\n  async fetch<T>(\n    query: string,\n    variables?: Record<string, any>,\n    options: {\n      cache?: boolean;\n      revalidate?: number;\n      headers?: Record<string, string>;\n    } = {}\n  ): Promise<T> {\n    const { cache = true, headers: customHeaders = {} } = options;\n    \n    // Check cache first (only for queries, not mutations)\n    const isQuery = query.trim().startsWith('query');\n    const cacheKey = this.getCacheKey(query, variables);\n    \n    if (cache && isQuery) {\n      const cachedData = this.getFromCache(cacheKey);\n      if (cachedData) {\n        return cachedData;\n      }\n    }\n\n    const requestBody: GraphQLRequest = {\n      query,\n      variables,\n    };\n\n    const requestHeaders = {\n      ...this.headers,\n      ...customHeaders,\n    };\n\n    try {\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: requestHeaders,\n        body: JSON.stringify(requestBody),\n        // Add Next.js specific options\n        ...(options.revalidate && { next: { revalidate: options.revalidate } }),\n      });\n\n      if (!response.ok) {\n        throw new MagentoGraphQLError(\n          `HTTP Error: ${response.status} ${response.statusText}`,\n          [],\n          response\n        );\n      }\n\n      const result: GraphQLResponse<T> = await response.json();\n\n      // Handle GraphQL errors\n      if (result.errors && result.errors.length > 0) {\n        const errorMessage = result.errors.map(error => error.message).join(', ');\n        throw new MagentoGraphQLError(\n          `GraphQL Error: ${errorMessage}`,\n          result.errors,\n          response\n        );\n      }\n\n      // Cache successful queries\n      if (cache && isQuery && result.data) {\n        this.setCache(cacheKey, result.data);\n      }\n\n      return result.data;\n    } catch (error) {\n      if (error instanceof MagentoGraphQLError) {\n        throw error;\n      }\n      \n      // Handle network errors\n      throw new MagentoGraphQLError(\n        `Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        []\n      );\n    }\n  }\n\n  // Clear cache\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  // Clear specific cache entry\n  clearCacheEntry(query: string, variables?: Record<string, any>): void {\n    const cacheKey = this.getCacheKey(query, variables);\n    this.cache.delete(cacheKey);\n  }\n}\n\n// Create singleton instance\nconst magentoClient = new MagentoGraphQLClient();\n\n// Export the client and error class\nexport { magentoClient, MagentoGraphQLError };\nexport type { GraphQLResponse, GraphQLRequest };\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAmCxB;AAlBnB,MAAM,4BAA4B;;;IAChC,YACE,OAAe,EACf,AAAO,SAAyC,EAAE,EAClD,AAAO,QAAmB,CAC1B;QACA,KAAK,CAAC,eAHC,SAAA,aACA,WAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,MAAM;IACI,QAAgB;IAChB,QAAgC;IAChC,QAAuD,IAAI,MAAM;IACjE,eAAe,IAAI,KAAK,KAAK;IAErC,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,sEAA+C;QAC9D,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB;YAChB,UAAU;QACZ;QAEA,0DAA0D;QAC1D,uCAAsE;;QAEtE;IACF;IAEA,gDAAgD;IAChD,iBAAiB,KAAa,EAAE;QAC9B,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACnD;IAEA,wBAAwB;IACxB,sBAAsB;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB;IACtC;IAEA,qBAAqB;IACb,YAAY,KAAa,EAAE,SAA+B,EAAU;QAC1E,OAAO,GAAG,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,aAAa,CAAC,IAAI;IACtD;IAEA,0BAA0B;IAClB,aAAa,SAAiB,EAAW;QAC/C,OAAO,KAAK,GAAG,KAAK,YAAY,IAAI,CAAC,YAAY;IACnD;IAEA,iBAAiB;IACT,aAAa,QAAgB,EAAc;QACjD,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,OAAO,SAAS,GAAG;YACjD,OAAO,OAAO,IAAI;QACpB;QACA,OAAO;IACT;IAEA,YAAY;IACJ,SAAS,QAAgB,EAAE,IAAS,EAAQ;QAClD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;YACvB;YACA,WAAW,KAAK,GAAG;QACrB;IACF;IAEA,oBAAoB;IACpB,MAAM,MACJ,KAAa,EACb,SAA+B,EAC/B,UAII,CAAC,CAAC,EACM;QACZ,MAAM,EAAE,QAAQ,IAAI,EAAE,SAAS,gBAAgB,CAAC,CAAC,EAAE,GAAG;QAEtD,sDAAsD;QACtD,MAAM,UAAU,MAAM,IAAI,GAAG,UAAU,CAAC;QACxC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,OAAO;QAEzC,IAAI,SAAS,SAAS;YACpB,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;YACrC,IAAI,YAAY;gBACd,OAAO;YACT;QACF;QAEA,MAAM,cAA8B;YAClC;YACA;QACF;QAEA,MAAM,iBAAiB;YACrB,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,aAAa;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;gBACrB,+BAA+B;gBAC/B,GAAI,QAAQ,UAAU,IAAI;oBAAE,MAAM;wBAAE,YAAY,QAAQ,UAAU;oBAAC;gBAAE,CAAC;YACxE;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,oBACR,CAAC,YAAY,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACvD,EAAE,EACF;YAEJ;YAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;YAEtD,wBAAwB;YACxB,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,eAAe,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,MAAM,OAAO,EAAE,IAAI,CAAC;gBACpE,MAAM,IAAI,oBACR,CAAC,eAAe,EAAE,cAAc,EAChC,OAAO,MAAM,EACb;YAEJ;YAEA,2BAA2B;YAC3B,IAAI,SAAS,WAAW,OAAO,IAAI,EAAE;gBACnC,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO,IAAI;YACrC;YAEA,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,qBAAqB;gBACxC,MAAM;YACR;YAEA,wBAAwB;YACxB,MAAM,IAAI,oBACR,CAAC,eAAe,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB,EAC5E,EAAE;QAEN;IACF;IAEA,cAAc;IACd,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,6BAA6B;IAC7B,gBAAgB,KAAa,EAAE,SAA+B,EAAQ;QACpE,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,OAAO;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;AACF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 3593, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/index.ts"], "sourcesContent": ["// Main Magento GraphQL API functions\nimport { magentoClient, MagentoGraphQLError } from './client';\n\n// Re-export client and error for convenience\nexport { magentoClient, MagentoGraphQLError };\n\n// Helper function for GraphQL queries with better error handling\nexport async function magentoGraphQLFetch<T>(\n  query: string,\n  variables?: Record<string, any>,\n  options?: {\n    cache?: boolean;\n    revalidate?: number;\n    headers?: Record<string, string>;\n  }\n): Promise<T> {\n  try {\n    return await magentoClient.fetch<T>(query, variables, options);\n  } catch (error) {\n    if (error instanceof MagentoGraphQLError) {\n      // Log error for debugging (only in development)\n      if (process.env.NODE_ENV === 'development') {\n        console.error('Magento GraphQL Error:', {\n          message: error.message,\n          errors: error.errors,\n          query: query.substring(0, 100) + '...',\n          variables,\n        });\n      }\n      throw error;\n    }\n    throw error;\n  }\n}\n\n// Helper for mutations (no caching)\nexport async function magentoGraphQLMutate<T>(\n  mutation: string,\n  variables?: Record<string, any>,\n  options?: {\n    headers?: Record<string, string>;\n  }\n): Promise<T> {\n  return magentoGraphQLFetch<T>(mutation, variables, {\n    ...options,\n    cache: false,\n  });\n}\n\n// Helper for queries with ISR (Incremental Static Regeneration)\nexport async function magentoGraphQLQuery<T>(\n  query: string,\n  variables?: Record<string, any>,\n  revalidate: number = 3600 // 1 hour default\n): Promise<T> {\n  return magentoGraphQLFetch<T>(query, variables, {\n    cache: true,\n    revalidate,\n  });\n}\n\n// Customer token management\nexport const customerAuth = {\n  setToken: (token: string) => magentoClient.setCustomerToken(token),\n  removeToken: () => magentoClient.removeCustomerToken(),\n  clearCache: () => magentoClient.clearCache(),\n};\n\n// Cache management\nexport const cacheManager = {\n  clear: () => magentoClient.clearCache(),\n  clearEntry: (query: string, variables?: Record<string, any>) => \n    magentoClient.clearCacheEntry(query, variables),\n};\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;;;AAqB3B;AApBV;;;AAMO,eAAe,oBACpB,KAAa,EACb,SAA+B,EAC/B,OAIC;IAED,IAAI;QACF,OAAO,MAAM,kIAAA,CAAA,gBAAa,CAAC,KAAK,CAAI,OAAO,WAAW;IACxD,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,kIAAA,CAAA,sBAAmB,EAAE;YACxC,gDAAgD;YAChD,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,0BAA0B;oBACtC,SAAS,MAAM,OAAO;oBACtB,QAAQ,MAAM,MAAM;oBACpB,OAAO,MAAM,SAAS,CAAC,GAAG,OAAO;oBACjC;gBACF;YACF;YACA,MAAM;QACR;QACA,MAAM;IACR;AACF;AAGO,eAAe,qBACpB,QAAgB,EAChB,SAA+B,EAC/B,OAEC;IAED,OAAO,oBAAuB,UAAU,WAAW;QACjD,GAAG,OAAO;QACV,OAAO;IACT;AACF;AAGO,eAAe,oBACpB,KAAa,EACb,SAA+B,EAC/B,aAAqB,KAAK,iBAAiB;AAAlB;IAEzB,OAAO,oBAAuB,OAAO,WAAW;QAC9C,OAAO;QACP;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,UAAU,CAAC,QAAkB,kIAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;IAC5D,aAAa,IAAM,kIAAA,CAAA,gBAAa,CAAC,mBAAmB;IACpD,YAAY,IAAM,kIAAA,CAAA,gBAAa,CAAC,UAAU;AAC5C;AAGO,MAAM,eAAe;IAC1B,OAAO,IAAM,kIAAA,CAAA,gBAAa,CAAC,UAAU;IACrC,YAAY,CAAC,OAAe,YAC1B,kIAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,OAAO;AACzC", "debugId": null}}, {"offset": {"line": 3664, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/queries/cms.ts"], "sourcesContent": ["// CMS GraphQL Queries for Magento 2 (including Page Builder content)\n\n// Fragment for CMS page\nexport const CMS_PAGE_FRAGMENT = `\n  fragment CmsPageFragment on CmsPage {\n    identifier\n    url_key\n    title\n    content\n    content_heading\n    page_layout\n    meta_title\n    meta_description\n    meta_keywords\n    created_at\n    updated_at\n  }\n`;\n\n// Fragment for CMS block\nexport const CMS_BLOCK_FRAGMENT = `\n  fragment CmsBlockFragment on CmsBlock {\n    identifier\n    title\n    content\n    created_at\n    updated_at\n  }\n`;\n\n// Get CMS page by identifier\nexport const GET_CMS_PAGE = `\n  query GetCmsPage($identifier: String!) {\n    cmsPage(identifier: $identifier) {\n      ...CmsPageFragment\n    }\n  }\n  ${CMS_PAGE_FRAGMENT}\n`;\n\n// Get CMS page by URL key\nexport const GET_CMS_PAGE_BY_URL_KEY = `\n  query GetCmsPageByUrlKey($urlKey: String!) {\n    cmsPage(url_key: $urlKey) {\n      ...CmsPageFragment\n    }\n  }\n  ${CMS_PAGE_FRAGMENT}\n`;\n\n// Get multiple CMS pages\nexport const GET_CMS_PAGES = `\n  query GetCmsPages($identifiers: [String!]) {\n    cmsPages(identifiers: $identifiers) {\n      items {\n        ...CmsPageFragment\n      }\n    }\n  }\n  ${CMS_PAGE_FRAGMENT}\n`;\n\n// Get CMS block by identifier\nexport const GET_CMS_BLOCK = `\n  query GetCmsBlock($identifier: String!) {\n    cmsBlocks(identifiers: [$identifier]) {\n      items {\n        ...CmsBlockFragment\n      }\n    }\n  }\n  ${CMS_BLOCK_FRAGMENT}\n`;\n\n// Get multiple CMS blocks\nexport const GET_CMS_BLOCKS = `\n  query GetCmsBlocks($identifiers: [String!]!) {\n    cmsBlocks(identifiers: $identifiers) {\n      items {\n        ...CmsBlockFragment\n      }\n    }\n  }\n  ${CMS_BLOCK_FRAGMENT}\n`;\n\n// Get all CMS pages (for sitemap/navigation)\nexport const GET_ALL_CMS_PAGES = `\n  query GetAllCmsPages {\n    cmsPages {\n      items {\n        identifier\n        url_key\n        title\n        meta_title\n        meta_description\n        created_at\n        updated_at\n      }\n    }\n  }\n`;\n\n// Search CMS pages\nexport const SEARCH_CMS_PAGES = `\n  query SearchCmsPages($search: String!) {\n    cmsPages(search: $search) {\n      items {\n        ...CmsPageFragment\n      }\n    }\n  }\n  ${CMS_PAGE_FRAGMENT}\n`;\n"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,wBAAwB;;;;;;;;;;;;AACjB,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;AAclC,CAAC;AAGM,MAAM,qBAAqB,CAAC;;;;;;;;AAQnC,CAAC;AAGM,MAAM,eAAe,CAAC;;;;;;EAM3B,EAAE,kBAAkB;AACtB,CAAC;AAGM,MAAM,0BAA0B,CAAC;;;;;;EAMtC,EAAE,kBAAkB;AACtB,CAAC;AAGM,MAAM,gBAAgB,CAAC;;;;;;;;EAQ5B,EAAE,kBAAkB;AACtB,CAAC;AAGM,MAAM,gBAAgB,CAAC;;;;;;;;EAQ5B,EAAE,mBAAmB;AACvB,CAAC;AAGM,MAAM,iBAAiB,CAAC;;;;;;;;EAQ7B,EAAE,mBAAmB;AACvB,CAAC;AAGM,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;;AAclC,CAAC;AAGM,MAAM,mBAAmB,CAAC;;;;;;;;EAQ/B,EAAE,kBAAkB;AACtB,CAAC", "debugId": null}}, {"offset": {"line": 3781, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/lib/magento/api/cms.ts"], "sourcesContent": ["// CMS API functions using Magento GraphQL\n\nimport { magentoGraphQLQuery } from '../index';\nimport {\n  GET_CMS_PAGE,\n  GET_CMS_PAGE_BY_URL_KEY,\n  GET_CMS_PAGES,\n  GET_CMS_BLOCK,\n  GET_CMS_BLOCKS,\n  GET_ALL_CMS_PAGES,\n  SEARCH_CMS_PAGES,\n} from '../queries/cms';\n\n// CMS Page interface\nexport interface CmsPage {\n  identifier: string;\n  url_key: string;\n  title: string;\n  content: string;\n  content_heading?: string;\n  page_layout?: string;\n  meta_title?: string;\n  meta_description?: string;\n  meta_keywords?: string;\n  created_at: string;\n  updated_at: string;\n}\n\n// CMS Block interface\nexport interface CmsBlock {\n  identifier: string;\n  title: string;\n  content: string;\n  created_at: string;\n  updated_at: string;\n}\n\n// Get CMS page by identifier\nexport async function getCmsPage(\n  identifier: string,\n  revalidate: number = 3600 // 1 hour\n): Promise<CmsPage | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsPage: CmsPage;\n    }>(\n      GET_CMS_PAGE,\n      { identifier },\n      revalidate\n    );\n\n    return response.cmsPage;\n  } catch (error) {\n    console.error('Error fetching CMS page:', error);\n    return null;\n  }\n}\n\n// Get CMS page by URL key\nexport async function getCmsPageByUrlKey(\n  urlKey: string,\n  revalidate: number = 3600 // 1 hour\n): Promise<CmsPage | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsPage: CmsPage;\n    }>(\n      GET_CMS_PAGE_BY_URL_KEY,\n      { urlKey },\n      revalidate\n    );\n\n    return response.cmsPage;\n  } catch (error) {\n    console.error('Error fetching CMS page by URL key:', error);\n    return null;\n  }\n}\n\n// Get multiple CMS pages\nexport async function getCmsPages(\n  identifiers: string[],\n  revalidate: number = 3600 // 1 hour\n): Promise<CmsPage[]> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsPages: { items: CmsPage[] };\n    }>(\n      GET_CMS_PAGES,\n      { identifiers },\n      revalidate\n    );\n\n    return response.cmsPages.items;\n  } catch (error) {\n    console.error('Error fetching CMS pages:', error);\n    return [];\n  }\n}\n\n// Get CMS block by identifier\nexport async function getCmsBlock(\n  identifier: string,\n  revalidate: number = 7200 // 2 hours\n): Promise<CmsBlock | null> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsBlocks: { items: CmsBlock[] };\n    }>(\n      GET_CMS_BLOCK,\n      { identifier },\n      revalidate\n    );\n\n    return response.cmsBlocks.items[0] || null;\n  } catch (error) {\n    console.error('Error fetching CMS block:', error);\n    return null;\n  }\n}\n\n// Get multiple CMS blocks\nexport async function getCmsBlocks(\n  identifiers: string[],\n  revalidate: number = 7200 // 2 hours\n): Promise<CmsBlock[]> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsBlocks: { items: CmsBlock[] };\n    }>(\n      GET_CMS_BLOCKS,\n      { identifiers },\n      revalidate\n    );\n\n    return response.cmsBlocks.items;\n  } catch (error) {\n    console.error('Error fetching CMS blocks:', error);\n    return [];\n  }\n}\n\n// Get all CMS pages (for sitemap/navigation)\nexport async function getAllCmsPages(\n  revalidate: number = 86400 // 24 hours\n): Promise<Partial<CmsPage>[]> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsPages: { items: Partial<CmsPage>[] };\n    }>(\n      GET_ALL_CMS_PAGES,\n      {},\n      revalidate\n    );\n\n    return response.cmsPages.items;\n  } catch (error) {\n    console.error('Error fetching all CMS pages:', error);\n    return [];\n  }\n}\n\n// Search CMS pages\nexport async function searchCmsPages(\n  search: string,\n  revalidate: number = 1800 // 30 minutes\n): Promise<CmsPage[]> {\n  try {\n    const response = await magentoGraphQLQuery<{\n      cmsPages: { items: CmsPage[] };\n    }>(\n      SEARCH_CMS_PAGES,\n      { search },\n      revalidate\n    );\n\n    return response.cmsPages.items;\n  } catch (error) {\n    console.error('Error searching CMS pages:', error);\n    return [];\n  }\n}\n\n// Check if content contains Page Builder\nexport function hasPageBuilderContent(content: string): boolean {\n  return content.includes('data-content-type') || \n         content.includes('pagebuilder-') ||\n         content.includes('data-pb-style');\n}\n\n// Extract Page Builder content from CMS content\nexport function extractPageBuilderContent(content: string): string {\n  // Remove any wrapper divs that might not be part of Page Builder\n  const cleanContent = content.trim();\n  \n  // If it starts with Page Builder content, return as-is\n  if (cleanContent.includes('data-content-type')) {\n    return cleanContent;\n  }\n  \n  // Try to find Page Builder content within the HTML\n  const pbMatch = cleanContent.match(/<div[^>]*data-content-type[^>]*>.*<\\/div>/s);\n  if (pbMatch) {\n    return pbMatch[0];\n  }\n  \n  return cleanContent;\n}\n\n// Get CMS page with Page Builder content parsed\nexport async function getCmsPageWithPageBuilder(\n  identifier: string,\n  revalidate: number = 3600\n): Promise<(CmsPage & { hasPageBuilder: boolean; pageBuilderContent?: string }) | null> {\n  const page = await getCmsPage(identifier, revalidate);\n  \n  if (!page) return null;\n  \n  const hasPageBuilder = hasPageBuilderContent(page.content);\n  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(page.content) : undefined;\n  \n  return {\n    ...page,\n    hasPageBuilder,\n    pageBuilderContent,\n  };\n}\n\n// Get CMS block with Page Builder content parsed\nexport async function getCmsBlockWithPageBuilder(\n  identifier: string,\n  revalidate: number = 7200\n): Promise<(CmsBlock & { hasPageBuilder: boolean; pageBuilderContent?: string }) | null> {\n  const block = await getCmsBlock(identifier, revalidate);\n  \n  if (!block) return null;\n  \n  const hasPageBuilder = hasPageBuilderContent(block.content);\n  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(block.content) : undefined;\n  \n  return {\n    ...block,\n    hasPageBuilder,\n    pageBuilderContent,\n  };\n}\n\n// Utility function to clean CMS content for display\nexport function cleanCmsContent(content: string): string {\n  return content\n    .replace(/{{[^}]+}}/g, '') // Remove Magento directives\n    .replace(/\\{\\{[^}]+\\}\\}/g, '') // Remove any remaining directives\n    .trim();\n}\n\n// Generate CMS page URL\nexport function getCmsPageUrl(page: CmsPage): string {\n  return `/${page.url_key}`;\n}\n\n// Generate breadcrumbs for CMS page\nexport function getCmsPageBreadcrumbs(page: CmsPage): Array<{ name: string; url: string }> {\n  return [\n    { name: 'Home', url: '/' },\n    { name: page.title, url: getCmsPageUrl(page) },\n  ];\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;;;;;;;;;;;;AAE1C;AAAA;AACA;;;AAmCO,eAAe,WACpB,UAAkB,EAClB,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,eAAY,EACZ;YAAE;QAAW,GACb;QAGF,OAAO,SAAS,OAAO;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAGO,eAAe,mBACpB,MAAc,EACd,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,0BAAuB,EACvB;YAAE;QAAO,GACT;QAGF,OAAO,SAAS,OAAO;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,eAAe,YACpB,WAAqB,EACrB,aAAqB,KAAK,SAAS;AAAV;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,gBAAa,EACb;YAAE;QAAY,GACd;QAGF,OAAO,SAAS,QAAQ,CAAC,KAAK;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAGO,eAAe,YACpB,UAAkB,EAClB,aAAqB,KAAK,UAAU;AAAX;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,gBAAa,EACb;YAAE;QAAW,GACb;QAGF,OAAO,SAAS,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGO,eAAe,aACpB,WAAqB,EACrB,aAAqB,KAAK,UAAU;AAAX;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,iBAAc,EACd;YAAE;QAAY,GACd;QAGF,OAAO,SAAS,SAAS,CAAC,KAAK;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAGO,eAAe,eACpB,aAAqB,MAAM,WAAW;AAAZ;IAE1B,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,oBAAiB,EACjB,CAAC,GACD;QAGF,OAAO,SAAS,QAAQ,CAAC,KAAK;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAGO,eAAe,eACpB,MAAc,EACd,aAAqB,KAAK,aAAa;AAAd;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAGvC,0IAAA,CAAA,mBAAgB,EAChB;YAAE;QAAO,GACT;QAGF,OAAO,SAAS,QAAQ,CAAC,KAAK;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAGO,SAAS,sBAAsB,OAAe;IACnD,OAAO,QAAQ,QAAQ,CAAC,wBACjB,QAAQ,QAAQ,CAAC,mBACjB,QAAQ,QAAQ,CAAC;AAC1B;AAGO,SAAS,0BAA0B,OAAe;IACvD,iEAAiE;IACjE,MAAM,eAAe,QAAQ,IAAI;IAEjC,uDAAuD;IACvD,IAAI,aAAa,QAAQ,CAAC,sBAAsB;QAC9C,OAAO;IACT;IAEA,mDAAmD;IACnD,MAAM,UAAU,aAAa,KAAK,CAAC;IACnC,IAAI,SAAS;QACX,OAAO,OAAO,CAAC,EAAE;IACnB;IAEA,OAAO;AACT;AAGO,eAAe,0BACpB,UAAkB,EAClB,aAAqB,IAAI;IAEzB,MAAM,OAAO,MAAM,WAAW,YAAY;IAE1C,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,iBAAiB,sBAAsB,KAAK,OAAO;IACzD,MAAM,qBAAqB,iBAAiB,0BAA0B,KAAK,OAAO,IAAI;IAEtF,OAAO;QACL,GAAG,IAAI;QACP;QACA;IACF;AACF;AAGO,eAAe,2BACpB,UAAkB,EAClB,aAAqB,IAAI;IAEzB,MAAM,QAAQ,MAAM,YAAY,YAAY;IAE5C,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,iBAAiB,sBAAsB,MAAM,OAAO;IAC1D,MAAM,qBAAqB,iBAAiB,0BAA0B,MAAM,OAAO,IAAI;IAEvF,OAAO;QACL,GAAG,KAAK;QACR;QACA;IACF;AACF;AAGO,SAAS,gBAAgB,OAAe;IAC7C,OAAO,QACJ,OAAO,CAAC,cAAc,IAAI,4BAA4B;KACtD,OAAO,CAAC,kBAAkB,IAAI,kCAAkC;KAChE,IAAI;AACT;AAGO,SAAS,cAAc,IAAa;IACzC,OAAO,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE;AAC3B;AAGO,SAAS,sBAAsB,IAAa;IACjD,OAAO;QACL;YAAE,MAAM;YAAQ,KAAK;QAAI;QACzB;YAAE,MAAM,KAAK,KAAK;YAAE,KAAK,cAAc;QAAM;KAC9C;AACH", "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/cms/CmsPageRenderer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box, Typography, Container, Breadcrumbs, Link } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport NextLink from 'next/link';\nimport { PageBuilderRenderer, RawHtmlRenderer } from '@/components/pagebuilder';\nimport { CmsPage, hasPageBuilderContent, extractPageBuilderContent } from '@/lib/magento/api/cms';\n\ninterface CmsPageRendererProps {\n  page: CmsPage;\n  showBreadcrumbs?: boolean;\n  showTitle?: boolean;\n  showContentHeading?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nexport const CmsPageRenderer: React.FC<CmsPageRendererProps> = ({\n  page,\n  showBreadcrumbs = true,\n  showTitle = true,\n  showContentHeading = true,\n  className,\n  style,\n}) => {\n  const hasPageBuilder = hasPageBuilderContent(page.content);\n  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(page.content) : null;\n\n  // Animation variants\n  const pageVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const titleVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.8,\n        delay: 0.2,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const contentVariants = {\n    hidden: { opacity: 0, y: 40 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.8,\n        delay: 0.4,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`cms-page ${className || ''}`}\n      style={style}\n      variants={pageVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Breadcrumbs */}\n      {showBreadcrumbs && (\n        <Container maxWidth=\"lg\" sx={{ py: 2 }}>\n          <Breadcrumbs aria-label=\"breadcrumb\">\n            <Link component={NextLink} href=\"/\" color=\"inherit\">\n              Home\n            </Link>\n            <Typography color=\"text.primary\">{page.title}</Typography>\n          </Breadcrumbs>\n        </Container>\n      )}\n\n      {/* Page Title */}\n      {showTitle && (\n        <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n          <motion.div variants={titleVariants}>\n            <Typography\n              variant=\"h1\"\n              component=\"h1\"\n              sx={{\n                fontSize: { xs: '2rem', md: '2.5rem', lg: '3rem' },\n                fontWeight: 700,\n                mb: 2,\n                textAlign: 'center',\n                background: 'linear-gradient(45deg, #2196f3 30%, #21cbf3 90%)',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n              }}\n            >\n              {page.title}\n            </Typography>\n          </motion.div>\n        </Container>\n      )}\n\n      {/* Content Heading */}\n      {showContentHeading && page.content_heading && (\n        <Container maxWidth=\"lg\" sx={{ pb: 2 }}>\n          <motion.div variants={titleVariants}>\n            <Typography\n              variant=\"h2\"\n              component=\"h2\"\n              sx={{\n                fontSize: { xs: '1.5rem', md: '2rem' },\n                fontWeight: 600,\n                mb: 3,\n                textAlign: 'center',\n                color: 'text.secondary',\n              }}\n            >\n              {page.content_heading}\n            </Typography>\n          </motion.div>\n        </Container>\n      )}\n\n      {/* Page Content */}\n      <motion.div variants={contentVariants}>\n        {hasPageBuilder && pageBuilderContent ? (\n          // Render with Page Builder\n          <PageBuilderRenderer\n            content={pageBuilderContent}\n            config={{\n              enableLazyLoading: true,\n              imageOptimization: true,\n            }}\n            className=\"cms-page-content\"\n          />\n        ) : (\n          // Render as raw HTML\n          <Container maxWidth=\"lg\" sx={{ py: 3 }}>\n            <RawHtmlRenderer\n              html={page.content}\n              className=\"cms-page-content\"\n            />\n          </Container>\n        )}\n      </motion.div>\n\n      {/* SEO Meta Information (hidden, for reference) */}\n      <Box sx={{ display: 'none' }}>\n        {page.meta_title && <meta name=\"title\" content={page.meta_title} />}\n        {page.meta_description && <meta name=\"description\" content={page.meta_description} />}\n        {page.meta_keywords && <meta name=\"keywords\" content={page.meta_keywords} />}\n      </Box>\n    </motion.div>\n  );\n};\n\nexport default CmsPageRenderer;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;AAkBO,MAAM,kBAAkD,CAAC,EAC9D,IAAI,EACJ,kBAAkB,IAAI,EACtB,YAAY,IAAI,EAChB,qBAAqB,IAAI,EACzB,SAAS,EACT,KAAK,EACN;IACC,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,OAAO;IACzD,MAAM,qBAAqB,iBAAiB,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,OAAO,IAAI;IAEtF,qBAAqB;IACrB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QACxC,OAAO;QACP,UAAU;QACV,SAAQ;QACR,SAAQ;;YAGP,iCACC,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;0BACnC,cAAA,6LAAC,mNAAA,CAAA,cAAW;oBAAC,cAAW;;sCACtB,6LAAC,8LAAA,CAAA,OAAI;4BAAC,WAAW,+JAAA,CAAA,UAAQ;4BAAE,MAAK;4BAAI,OAAM;sCAAU;;;;;;sCAGpD,6LAAC,gNAAA,CAAA,aAAU;4BAAC,OAAM;sCAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;;YAMjD,2BACC,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;0BACnC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,IAAI;4BACF,UAAU;gCAAE,IAAI;gCAAQ,IAAI;gCAAU,IAAI;4BAAO;4BACjD,YAAY;4BACZ,IAAI;4BACJ,WAAW;4BACX,YAAY;4BACZ,gBAAgB;4BAChB,sBAAsB;4BACtB,qBAAqB;wBACvB;kCAEC,KAAK,KAAK;;;;;;;;;;;;;;;;YAOlB,sBAAsB,KAAK,eAAe,kBACzC,6LAAC,6MAAA,CAAA,YAAS;gBAAC,UAAS;gBAAK,IAAI;oBAAE,IAAI;gBAAE;0BACnC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC,gNAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,WAAU;wBACV,IAAI;4BACF,UAAU;gCAAE,IAAI;gCAAU,IAAI;4BAAO;4BACrC,YAAY;4BACZ,IAAI;4BACJ,WAAW;4BACX,OAAO;wBACT;kCAEC,KAAK,eAAe;;;;;;;;;;;;;;;;0BAO7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACnB,kBAAkB,qBACjB,2BAA2B;8BAC3B,6LAAC,2JAAA,CAAA,sBAAmB;oBAClB,SAAS;oBACT,QAAQ;wBACN,mBAAmB;wBACnB,mBAAmB;oBACrB;oBACA,WAAU;;;;;2BAGZ,qBAAqB;8BACrB,6LAAC,6MAAA,CAAA,YAAS;oBAAC,UAAS;oBAAK,IAAI;wBAAE,IAAI;oBAAE;8BACnC,cAAA,6LAAC,2JAAA,CAAA,kBAAe;wBACd,MAAM,KAAK,OAAO;wBAClB,WAAU;;;;;;;;;;;;;;;;0BAOlB,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,SAAS;gBAAO;;oBACxB,KAAK,UAAU,kBAAI,6LAAC;wBAAK,MAAK;wBAAQ,SAAS,KAAK,UAAU;;;;;;oBAC9D,KAAK,gBAAgB,kBAAI,6LAAC;wBAAK,MAAK;wBAAc,SAAS,KAAK,gBAAgB;;;;;;oBAChF,KAAK,aAAa,kBAAI,6LAAC;wBAAK,MAAK;wBAAW,SAAS,KAAK,aAAa;;;;;;;;;;;;;;;;;;AAIhF;KAlJa;uCAoJE", "debugId": null}}, {"offset": {"line": 4236, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/cms/CmsBlockRenderer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { motion } from 'framer-motion';\nimport { PageBuilderRenderer, RawHtmlRenderer } from '@/components/pagebuilder';\nimport { CmsBlock, hasPageBuilderContent, extractPageBuilderContent } from '@/lib/magento/api/cms';\n\ninterface CmsBlockRendererProps {\n  block: CmsBlock;\n  className?: string;\n  style?: React.CSSProperties;\n  animate?: boolean;\n}\n\nexport const CmsBlockRenderer: React.FC<CmsBlockRendererProps> = ({\n  block,\n  className,\n  style,\n  animate = true,\n}) => {\n  const hasPageBuilder = hasPageBuilderContent(block.content);\n  const pageBuilderContent = hasPageBuilder ? extractPageBuilderContent(block.content) : null;\n\n  // Animation variants\n  const blockVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { \n        duration: 0.6,\n        ease: 'easeOut'\n      }\n    }\n  };\n\n  const content = (\n    <Box\n      className={`cms-block cms-block-${block.identifier} ${className || ''}`}\n      style={style}\n    >\n      {hasPageBuilder && pageBuilderContent ? (\n        // Render with Page Builder\n        <PageBuilderRenderer\n          content={pageBuilderContent}\n          config={{\n            enableLazyLoading: true,\n            imageOptimization: true,\n          }}\n          className=\"cms-block-content\"\n        />\n      ) : (\n        // Render as raw HTML\n        <RawHtmlRenderer\n          html={block.content}\n          className=\"cms-block-content\"\n        />\n      )}\n    </Box>\n  );\n\n  if (animate) {\n    return (\n      <motion.div\n        variants={blockVariants}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, amount: 0.1 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\nexport default CmsBlockRenderer;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AACA;AANA;;;;;;AAeO,MAAM,mBAAoD,CAAC,EAChE,KAAK,EACL,SAAS,EACT,KAAK,EACL,UAAU,IAAI,EACf;IACC,MAAM,iBAAiB,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM,OAAO;IAC1D,MAAM,qBAAqB,iBAAiB,CAAA,GAAA,sIAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,OAAO,IAAI;IAEvF,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,wBACJ,6LAAC,2LAAA,CAAA,MAAG;QACF,WAAW,CAAC,oBAAoB,EAAE,MAAM,UAAU,CAAC,CAAC,EAAE,aAAa,IAAI;QACvE,OAAO;kBAEN,kBAAkB,qBACjB,2BAA2B;sBAC3B,6LAAC,2JAAA,CAAA,sBAAmB;YAClB,SAAS;YACT,QAAQ;gBACN,mBAAmB;gBACnB,mBAAmB;YACrB;YACA,WAAU;;;;;mBAGZ,qBAAqB;sBACrB,6LAAC,2JAAA,CAAA,kBAAe;YACd,MAAM,MAAM,OAAO;YACnB,WAAU;;;;;;;;;;;IAMlB,IAAI,SAAS;QACX,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU;YACV,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;sBAEnC;;;;;;IAGP;IAEA,OAAO;AACT;KA7Da;uCA+DE", "debugId": null}}, {"offset": {"line": 4330, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/ui/LoadingSkeleton.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Skeleton,\n  Box,\n  Card,\n  CardContent,\n  Grid,\n  keyframes,\n} from '@mui/material';\nimport { motion } from 'framer-motion';\n\n// Custom shimmer animation\nconst shimmer = keyframes`\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n`;\n\n// Custom pulse animation\nconst pulse = keyframes`\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.4;\n  }\n  100% {\n    opacity: 1;\n  }\n`;\n\n// Animation variants for motion\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.5,\n      ease: 'easeOut',\n    },\n  },\n};\n\n// Enhanced skeleton styles with animations\nconst getSkeletonSx = (\n  animationType: 'shimmer' | 'pulse' | 'wave' = 'shimmer'\n) => {\n  const baseSx = {\n    borderRadius: 2,\n    transform: 'scale(1)',\n    transition: 'transform 0.2s ease-in-out',\n    '&:hover': {\n      transform: 'scale(1.02)',\n    },\n  };\n\n  switch (animationType) {\n    case 'shimmer':\n      return {\n        ...baseSx,\n        background:\n          'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)',\n        backgroundSize: '200px 100%',\n        animation: `${shimmer} 2s infinite linear`,\n      };\n    case 'pulse':\n      return {\n        ...baseSx,\n        animation: `${pulse} 2s infinite ease-in-out`,\n      };\n    default:\n      return baseSx;\n  }\n};\n\n// Product card skeleton with enhanced animations\nexport const ProductCardSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div variants={itemVariants}>\n      <Card\n        sx={{\n          transition: 'all 0.3s ease-in-out',\n          '&:hover': {\n            transform: 'translateY(-4px)',\n            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n          },\n        }}\n      >\n        <Skeleton\n          variant=\"rectangular\"\n          height={200}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n        <CardContent>\n          <Skeleton\n            variant=\"text\"\n            height={24}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={20}\n            width=\"60%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mt: 1 }}\n          />\n          <Box sx={{ mt: 1 }}>\n            <Skeleton\n              variant=\"text\"\n              height={28}\n              width=\"40%\"\n              animation={animationType === 'shimmer' ? false : animationType}\n              sx={getSkeletonSx(animationType)}\n            />\n          </Box>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n};\n\n// Product grid skeleton with staggered animations\ninterface ProductGridSkeletonProps {\n  count?: number;\n  columns?: number;\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}\n\nexport const ProductGridSkeleton: React.FC<ProductGridSkeletonProps> = ({\n  count = 12,\n  columns = 4,\n  animationType = 'shimmer',\n}) => {\n  return (\n    <motion.div variants={containerVariants} initial=\"hidden\" animate=\"visible\">\n      <Grid container spacing={3}>\n        {Array.from({ length: count }).map((_, index) => (\n          <Grid item xs={12} sm={6} md={12 / columns} key={index}>\n            <ProductCardSkeleton animationType={animationType} />\n          </Grid>\n        ))}\n      </Grid>\n    </motion.div>\n  );\n};\n\n// Category card skeleton with enhanced animations\nexport const CategoryCardSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div variants={itemVariants}>\n      <Card\n        sx={{\n          transition: 'all 0.3s ease-in-out',\n          '&:hover': {\n            transform: 'translateY(-4px)',\n            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n          },\n        }}\n      >\n        <Skeleton\n          variant=\"rectangular\"\n          height={150}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n        <CardContent>\n          <Skeleton\n            variant=\"text\"\n            height={24}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={16}\n            width=\"80%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mt: 1 }}\n          />\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n};\n\n// Banner skeleton with enhanced animations\nexport const BannerSkeleton: React.FC<{\n  height?: number;\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ height = 300, animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      <Skeleton\n        variant=\"rectangular\"\n        height={height}\n        animation={animationType === 'shimmer' ? false : animationType}\n        sx={{\n          ...getSkeletonSx(animationType),\n          borderRadius: 2,\n        }}\n      />\n    </motion.div>\n  );\n};\n\n// Text skeleton with multiple lines and enhanced animations\ninterface TextSkeletonProps {\n  lines?: number;\n  width?: string | number;\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}\n\nexport const TextSkeleton: React.FC<TextSkeletonProps> = ({\n  lines = 3,\n  width = '100%',\n  animationType = 'shimmer',\n}) => {\n  return (\n    <motion.div variants={containerVariants} initial=\"hidden\" animate=\"visible\">\n      <Box>\n        {Array.from({ length: lines }).map((_, index) => (\n          <motion.div key={index} variants={itemVariants}>\n            <Skeleton\n              variant=\"text\"\n              height={20}\n              width={index === lines - 1 ? '60%' : width}\n              animation={animationType === 'shimmer' ? false : animationType}\n              sx={{\n                ...getSkeletonSx(animationType),\n                mb: 0.5,\n              }}\n            />\n          </motion.div>\n        ))}\n      </Box>\n    </motion.div>\n  );\n};\n\n// Cart item skeleton with enhanced animations\nexport const CartItemSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n    >\n      <Box sx={{ display: 'flex', alignItems: 'center', p: 2, gap: 2 }}>\n        <Skeleton\n          variant=\"rectangular\"\n          width={80}\n          height={80}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n        <Box sx={{ flex: 1 }}>\n          <Skeleton\n            variant=\"text\"\n            height={24}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={20}\n            width=\"60%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={20}\n            width=\"40%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}\n          />\n        </Box>\n        <Box\n          sx={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}\n        >\n          <Skeleton\n            variant=\"text\"\n            height={24}\n            width={60}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"rectangular\"\n            width={40}\n            height={32}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mt: 1 }}\n          />\n        </Box>\n      </Box>\n    </motion.div>\n  );\n};\n\n// Order item skeleton with enhanced animations\nexport const OrderItemSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n    >\n      <Box\n        sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, mb: 2 }}\n      >\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n          <Skeleton\n            variant=\"text\"\n            height={20}\n            width={120}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={20}\n            width={80}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n        </Box>\n        <Skeleton\n          variant=\"text\"\n          height={16}\n          width=\"100%\"\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n        <Skeleton\n          variant=\"text\"\n          height={16}\n          width=\"80%\"\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={{ ...getSkeletonSx(animationType), mt: 0.5 }}\n        />\n        <Box sx={{ mt: 1, display: 'flex', gap: 2 }}>\n          <Skeleton\n            variant=\"text\"\n            height={16}\n            width={60}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={16}\n            width={80}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={getSkeletonSx(animationType)}\n          />\n        </Box>\n      </Box>\n    </motion.div>\n  );\n};\n\n// Page skeleton for full page loading with enhanced animations\nexport const PageSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div variants={containerVariants} initial=\"hidden\" animate=\"visible\">\n      <Box sx={{ p: 3 }}>\n        {/* Header */}\n        <motion.div variants={itemVariants}>\n          <Skeleton\n            variant=\"text\"\n            height={40}\n            width=\"30%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mb: 2 }}\n          />\n        </motion.div>\n\n        {/* Breadcrumbs */}\n        <motion.div variants={itemVariants}>\n          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>\n            <Skeleton\n              variant=\"text\"\n              height={16}\n              width={60}\n              animation={animationType === 'shimmer' ? false : animationType}\n              sx={getSkeletonSx(animationType)}\n            />\n            <Skeleton\n              variant=\"text\"\n              height={16}\n              width={20}\n              animation={animationType === 'shimmer' ? false : animationType}\n              sx={getSkeletonSx(animationType)}\n            />\n            <Skeleton\n              variant=\"text\"\n              height={16}\n              width={80}\n              animation={animationType === 'shimmer' ? false : animationType}\n              sx={getSkeletonSx(animationType)}\n            />\n          </Box>\n        </motion.div>\n\n        {/* Content */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={3}>\n            {/* Sidebar */}\n            <motion.div variants={itemVariants}>\n              <Box sx={{ mb: 2 }}>\n                <Skeleton\n                  variant=\"text\"\n                  height={24}\n                  width=\"60%\"\n                  animation={\n                    animationType === 'shimmer' ? false : animationType\n                  }\n                  sx={getSkeletonSx(animationType)}\n                />\n                {Array.from({ length: 5 }).map((_, index) => (\n                  <Skeleton\n                    key={index}\n                    variant=\"text\"\n                    height={20}\n                    animation={\n                      animationType === 'shimmer' ? false : animationType\n                    }\n                    sx={{ ...getSkeletonSx(animationType), mt: 1 }}\n                  />\n                ))}\n              </Box>\n            </motion.div>\n          </Grid>\n          <Grid item xs={12} md={9}>\n            {/* Main content */}\n            <ProductGridSkeleton\n              count={9}\n              columns={3}\n              animationType={animationType}\n            />\n          </Grid>\n        </Grid>\n      </Box>\n    </motion.div>\n  );\n};\n\n// Generic list skeleton with enhanced animations\ninterface ListSkeletonProps {\n  count?: number;\n  height?: number;\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}\n\nexport const ListSkeleton: React.FC<ListSkeletonProps> = ({\n  count = 5,\n  height = 60,\n  animationType = 'shimmer',\n}) => {\n  return (\n    <motion.div variants={containerVariants} initial=\"hidden\" animate=\"visible\">\n      <Box>\n        {Array.from({ length: count }).map((_, index) => (\n          <motion.div key={index} variants={itemVariants}>\n            <Box sx={{ mb: 1 }}>\n              <Skeleton\n                variant=\"rectangular\"\n                height={height}\n                animation={animationType === 'shimmer' ? false : animationType}\n                sx={getSkeletonSx(animationType)}\n              />\n            </Box>\n          </motion.div>\n        ))}\n      </Box>\n    </motion.div>\n  );\n};\n\n// Page Builder specific skeletons\nexport const PageBuilderRowSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      <Box sx={{ width: '100%', mb: 3 }}>\n        <Skeleton\n          variant=\"rectangular\"\n          height={60}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n      </Box>\n    </motion.div>\n  );\n};\n\nexport const PageBuilderColumnSkeleton: React.FC<{\n  width?: string;\n  height?: number;\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ width = '100%', height = 200, animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n    >\n      <Skeleton\n        variant=\"rectangular\"\n        height={height}\n        animation={animationType === 'shimmer' ? false : animationType}\n        sx={{\n          ...getSkeletonSx(animationType),\n          width,\n        }}\n      />\n    </motion.div>\n  );\n};\n\nexport const PageBuilderBannerSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.98 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.8, ease: 'easeOut' }}\n    >\n      <Box sx={{ position: 'relative', width: '100%', mb: 3 }}>\n        <Skeleton\n          variant=\"rectangular\"\n          height={400}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n        <Box\n          sx={{\n            position: 'absolute',\n            top: '50%',\n            left: '50%',\n            transform: 'translate(-50%, -50%)',\n            textAlign: 'center',\n            width: '80%',\n          }}\n        >\n          <Skeleton\n            variant=\"text\"\n            height={48}\n            width=\"70%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mb: 2, mx: 'auto' }}\n          />\n          <Skeleton\n            variant=\"text\"\n            height={24}\n            width=\"50%\"\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mb: 3, mx: 'auto' }}\n          />\n          <Skeleton\n            variant=\"rectangular\"\n            height={40}\n            width={120}\n            animation={animationType === 'shimmer' ? false : animationType}\n            sx={{ ...getSkeletonSx(animationType), mx: 'auto' }}\n          />\n        </Box>\n      </Box>\n    </motion.div>\n  );\n};\n\nexport const CmsBlockSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, ease: 'easeOut' }}\n    >\n      <Box\n        sx={{\n          width: '100%',\n          p: 2,\n          border: '1px solid #e0e0e0',\n          borderRadius: 2,\n          mb: 2,\n        }}\n      >\n        <Skeleton\n          variant=\"text\"\n          height={28}\n          width=\"40%\"\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={{ ...getSkeletonSx(animationType), mb: 2 }}\n        />\n        <Skeleton\n          variant=\"rectangular\"\n          height={120}\n          animation={animationType === 'shimmer' ? false : animationType}\n          sx={getSkeletonSx(animationType)}\n        />\n      </Box>\n    </motion.div>\n  );\n};\n\nexport const PageBuilderContentSkeleton: React.FC<{\n  animationType?: 'shimmer' | 'pulse' | 'wave';\n}> = ({ animationType = 'shimmer' }) => {\n  return (\n    <motion.div variants={containerVariants} initial=\"hidden\" animate=\"visible\">\n      <Box sx={{ width: '100%' }}>\n        {/* Row with columns */}\n        <motion.div variants={itemVariants}>\n          <PageBuilderRowSkeleton animationType={animationType} />\n          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>\n            <PageBuilderColumnSkeleton\n              width=\"50%\"\n              animationType={animationType}\n            />\n            <PageBuilderColumnSkeleton\n              width=\"50%\"\n              animationType={animationType}\n            />\n          </Box>\n        </motion.div>\n\n        {/* Banner */}\n        <motion.div variants={itemVariants}>\n          <PageBuilderBannerSkeleton animationType={animationType} />\n        </motion.div>\n\n        {/* Text content */}\n        <motion.div variants={itemVariants}>\n          <TextSkeleton lines={4} animationType={animationType} />\n        </motion.div>\n      </Box>\n    </motion.div>\n  );\n};\n\nexport default {\n  ProductCardSkeleton,\n  ProductGridSkeleton,\n  CategoryCardSkeleton,\n  BannerSkeleton,\n  TextSkeleton,\n  CartItemSkeleton,\n  OrderItemSkeleton,\n  PageSkeleton,\n  ListSkeleton,\n  PageBuilderRowSkeleton,\n  PageBuilderColumnSkeleton,\n  PageBuilderBannerSkeleton,\n  CmsBlockSkeleton,\n  PageBuilderContentSkeleton,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAXA;;;;AAaA,2BAA2B;AAC3B,MAAM,UAAU,kNAAA,CAAA,YAAS,CAAC;;;;;;;AAO1B,CAAC;AAED,yBAAyB;AACzB,MAAM,QAAQ,kNAAA,CAAA,YAAS,CAAC;;;;;;;;;;AAUxB,CAAC;AAED,gCAAgC;AAChC,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAEA,2CAA2C;AAC3C,MAAM,gBAAgB,CACpB,gBAA8C,SAAS;IAEvD,MAAM,SAAS;QACb,cAAc;QACd,WAAW;QACX,YAAY;QACZ,WAAW;YACT,WAAW;QACb;IACF;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,MAAM;gBACT,YACE;gBACF,gBAAgB;gBAChB,WAAW,GAAG,QAAQ,mBAAmB,CAAC;YAC5C;QACF,KAAK;YACH,OAAO;gBACL,GAAG,MAAM;gBACT,WAAW,GAAG,MAAM,wBAAwB,CAAC;YAC/C;QACF;YACE,OAAO;IACX;AACF;AAGO,MAAM,sBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;kBACpB,cAAA,6LAAC,8LAAA,CAAA,OAAI;YACH,IAAI;gBACF,YAAY;gBACZ,WAAW;oBACT,WAAW;oBACX,WAAW;gBACb;YACF;;8BAEA,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;8BAEpB,6LAAC,mNAAA,CAAA,cAAW;;sCACV,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAE;;;;;;sCAE/C,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,IAAI;4BAAE;sCACf,cAAA,6LAAC,0MAAA,CAAA,WAAQ;gCACP,SAAQ;gCACR,QAAQ;gCACR,OAAM;gCACN,WAAW,kBAAkB,YAAY,QAAQ;gCACjD,IAAI,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;KA/Ca;AAwDN,MAAM,sBAA0D,CAAC,EACtE,QAAQ,EAAE,EACV,UAAU,CAAC,EACX,gBAAgB,SAAS,EAC1B;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;QAAmB,SAAQ;QAAS,SAAQ;kBAChE,cAAA,6LAAC,8LAAA,CAAA,OAAI;YAAC,SAAS;YAAC,SAAS;sBACtB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,8LAAA,CAAA,OAAI;oBAAC,IAAI;oBAAC,IAAI;oBAAI,IAAI;oBAAG,IAAI,KAAK;8BACjC,cAAA,6LAAC;wBAAoB,eAAe;;;;;;mBADW;;;;;;;;;;;;;;;AAO3D;MAhBa;AAmBN,MAAM,uBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;kBACpB,cAAA,6LAAC,8LAAA,CAAA,OAAI;YACH,IAAI;gBACF,YAAY;gBACZ,WAAW;oBACT,WAAW;oBACX,WAAW;gBACb;YACF;;8BAEA,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;8BAEpB,6LAAC,mNAAA,CAAA,cAAW;;sCACV,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;;;;;AAMzD;MAtCa;AAyCN,MAAM,iBAGR,CAAC,EAAE,SAAS,GAAG,EAAE,gBAAgB,SAAS,EAAE;IAC/C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,0MAAA,CAAA,WAAQ;YACP,SAAQ;YACR,QAAQ;YACR,WAAW,kBAAkB,YAAY,QAAQ;YACjD,IAAI;gBACF,GAAG,cAAc,cAAc;gBAC/B,cAAc;YAChB;;;;;;;;;;;AAIR;MArBa;AA8BN,MAAM,eAA4C,CAAC,EACxD,QAAQ,CAAC,EACT,QAAQ,MAAM,EACd,gBAAgB,SAAS,EAC1B;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;QAAmB,SAAQ;QAAS,SAAQ;kBAChE,cAAA,6LAAC,2LAAA,CAAA,MAAG;sBACD,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAa,UAAU;8BAChC,cAAA,6LAAC,0MAAA,CAAA,WAAQ;wBACP,SAAQ;wBACR,QAAQ;wBACR,OAAO,UAAU,QAAQ,IAAI,QAAQ;wBACrC,WAAW,kBAAkB,YAAY,QAAQ;wBACjD,IAAI;4BACF,GAAG,cAAc,cAAc;4BAC/B,IAAI;wBACN;;;;;;mBATa;;;;;;;;;;;;;;;AAgB3B;MAzBa;AA4BN,MAAM,mBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,GAAG;gBAAG,KAAK;YAAE;;8BAC7D,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;8BAEpB,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,MAAM;oBAAE;;sCACjB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAI;;;;;;sCAEjD,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAI;;;;;;;;;;;;8BAGnD,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBAAE,SAAS;wBAAQ,eAAe;wBAAU,YAAY;oBAAM;;sCAElE,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,OAAO;4BACP,QAAQ;4BACR,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAE;;;;;;;;;;;;;;;;;;;;;;;AAMzD;MA5Da;AA+DN,MAAM,oBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBAAE,GAAG;gBAAG,QAAQ;gBAAG,aAAa;gBAAW,cAAc;gBAAG,IAAI;YAAE;;8BAEtE,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,gBAAgB;wBAAiB,IAAI;oBAAE;;sCACjE,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;;;;;;;8BAGtB,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,OAAM;oBACN,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;8BAEpB,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,OAAM;oBACN,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI;wBAAE,GAAG,cAAc,cAAc;wBAAE,IAAI;oBAAI;;;;;;8BAEjD,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,IAAI;wBAAG,SAAS;wBAAQ,KAAK;oBAAE;;sCACxC,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;sCAEpB,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAM9B;MA7Da;AAgEN,MAAM,eAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;QAAmB,SAAQ;QAAS,SAAQ;kBAChE,cAAA,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,GAAG;YAAE;;8BAEd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC,0MAAA,CAAA,WAAQ;wBACP,SAAQ;wBACR,QAAQ;wBACR,OAAM;wBACN,WAAW,kBAAkB,YAAY,QAAQ;wBACjD,IAAI;4BAAE,GAAG,cAAc,cAAc;4BAAE,IAAI;wBAAE;;;;;;;;;;;8BAKjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,SAAS;4BAAQ,KAAK;4BAAG,IAAI;wBAAE;;0CACxC,6LAAC,0MAAA,CAAA,WAAQ;gCACP,SAAQ;gCACR,QAAQ;gCACR,OAAO;gCACP,WAAW,kBAAkB,YAAY,QAAQ;gCACjD,IAAI,cAAc;;;;;;0CAEpB,6LAAC,0MAAA,CAAA,WAAQ;gCACP,SAAQ;gCACR,QAAQ;gCACR,OAAO;gCACP,WAAW,kBAAkB,YAAY,QAAQ;gCACjD,IAAI,cAAc;;;;;;0CAEpB,6LAAC,0MAAA,CAAA,WAAQ;gCACP,SAAQ;gCACR,QAAQ;gCACR,OAAO;gCACP,WAAW,kBAAkB,YAAY,QAAQ;gCACjD,IAAI,cAAc;;;;;;;;;;;;;;;;;8BAMxB,6LAAC,8LAAA,CAAA,OAAI;oBAAC,SAAS;oBAAC,SAAS;;sCACvB,6LAAC,8LAAA,CAAA,OAAI;4BAAC,IAAI;4BAAC,IAAI;4BAAI,IAAI;sCAErB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,6LAAC,2LAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,IAAI;oCAAE;;sDACf,6LAAC,0MAAA,CAAA,WAAQ;4CACP,SAAQ;4CACR,QAAQ;4CACR,OAAM;4CACN,WACE,kBAAkB,YAAY,QAAQ;4CAExC,IAAI,cAAc;;;;;;wCAEnB,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,0MAAA,CAAA,WAAQ;gDAEP,SAAQ;gDACR,QAAQ;gDACR,WACE,kBAAkB,YAAY,QAAQ;gDAExC,IAAI;oDAAE,GAAG,cAAc,cAAc;oDAAE,IAAI;gDAAE;+CANxC;;;;;;;;;;;;;;;;;;;;;sCAYf,6LAAC,8LAAA,CAAA,OAAI;4BAAC,IAAI;4BAAC,IAAI;4BAAI,IAAI;sCAErB,cAAA,6LAAC;gCACC,OAAO;gCACP,SAAS;gCACT,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;MArFa;AA8FN,MAAM,eAA4C,CAAC,EACxD,QAAQ,CAAC,EACT,SAAS,EAAE,EACX,gBAAgB,SAAS,EAC1B;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;QAAmB,SAAQ;QAAS,SAAQ;kBAChE,cAAA,6LAAC,2LAAA,CAAA,MAAG;sBACD,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAa,UAAU;8BAChC,cAAA,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,IAAI;wBAAE;kCACf,cAAA,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI,cAAc;;;;;;;;;;;mBANP;;;;;;;;;;;;;;;AAc3B;MAvBa;AA0BN,MAAM,yBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,OAAO;gBAAQ,IAAI;YAAE;sBAC9B,cAAA,6LAAC,0MAAA,CAAA,WAAQ;gBACP,SAAQ;gBACR,QAAQ;gBACR,WAAW,kBAAkB,YAAY,QAAQ;gBACjD,IAAI,cAAc;;;;;;;;;;;;;;;;AAK5B;MAnBa;AAqBN,MAAM,4BAIR,CAAC,EAAE,QAAQ,MAAM,EAAE,SAAS,GAAG,EAAE,gBAAgB,SAAS,EAAE;IAC/D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,0MAAA,CAAA,WAAQ;YACP,SAAQ;YACR,QAAQ;YACR,WAAW,kBAAkB,YAAY,QAAQ;YACjD,IAAI;gBACF,GAAG,cAAc,cAAc;gBAC/B;YACF;;;;;;;;;;;AAIR;OAtBa;AAwBN,MAAM,4BAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,UAAU;gBAAY,OAAO;gBAAQ,IAAI;YAAE;;8BACpD,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;8BAEpB,6LAAC,2LAAA,CAAA,MAAG;oBACF,IAAI;wBACF,UAAU;wBACV,KAAK;wBACL,MAAM;wBACN,WAAW;wBACX,WAAW;wBACX,OAAO;oBACT;;sCAEA,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;gCAAG,IAAI;4BAAO;;;;;;sCAE3D,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAM;4BACN,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;gCAAG,IAAI;4BAAO;;;;;;sCAE3D,6LAAC,0MAAA,CAAA,WAAQ;4BACP,SAAQ;4BACR,QAAQ;4BACR,OAAO;4BACP,WAAW,kBAAkB,YAAY,QAAQ;4BACjD,IAAI;gCAAE,GAAG,cAAc,cAAc;gCAAE,IAAI;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAM9D;OAnDa;AAqDN,MAAM,mBAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE7C,cAAA,6LAAC,2LAAA,CAAA,MAAG;YACF,IAAI;gBACF,OAAO;gBACP,GAAG;gBACH,QAAQ;gBACR,cAAc;gBACd,IAAI;YACN;;8BAEA,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,OAAM;oBACN,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI;wBAAE,GAAG,cAAc,cAAc;wBAAE,IAAI;oBAAE;;;;;;8BAE/C,6LAAC,0MAAA,CAAA,WAAQ;oBACP,SAAQ;oBACR,QAAQ;oBACR,WAAW,kBAAkB,YAAY,QAAQ;oBACjD,IAAI,cAAc;;;;;;;;;;;;;;;;;AAK5B;OAlCa;AAoCN,MAAM,6BAER,CAAC,EAAE,gBAAgB,SAAS,EAAE;IACjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QAAC,UAAU;QAAmB,SAAQ;QAAS,SAAQ;kBAChE,cAAA,6LAAC,2LAAA,CAAA,MAAG;YAAC,IAAI;gBAAE,OAAO;YAAO;;8BAEvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;;sCACpB,6LAAC;4BAAuB,eAAe;;;;;;sCACvC,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,KAAK;gCAAG,IAAI;4BAAE;;8CACxC,6LAAC;oCACC,OAAM;oCACN,eAAe;;;;;;8CAEjB,6LAAC;oCACC,OAAM;oCACN,eAAe;;;;;;;;;;;;;;;;;;8BAMrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC;wBAA0B,eAAe;;;;;;;;;;;8BAI5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;8BACpB,cAAA,6LAAC;wBAAa,OAAO;wBAAG,eAAe;;;;;;;;;;;;;;;;;;;;;;AAKjD;OAjCa;uCAmCE;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 5565, "column": 0}, "map": {"version": 3, "sources": ["file:///var/www/html/next/src/components/cms/CmsBlockLoader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { Box } from '@mui/material';\nimport { getCmsBlockWithPageBuilder, CmsBlock } from '@/lib/magento/api/cms';\nimport { CmsBlockSkeleton } from '@/components/ui/LoadingSkeleton';\nimport CmsBlockRenderer from './CmsBlockRenderer';\n\ninterface CmsBlockLoaderProps {\n  identifier: string;\n  fallback?: React.ReactNode;\n  className?: string;\n  style?: React.CSSProperties;\n  animate?: boolean;\n}\n\nexport const CmsBlockLoader: React.FC<CmsBlockLoaderProps> = ({\n  identifier,\n  fallback,\n  className,\n  style,\n  animate = true,\n}) => {\n  const [block, setBlock] = useState<CmsBlock | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const loadBlock = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const blockData = await getCmsBlockWithPageBuilder(identifier);\n        setBlock(blockData);\n      } catch (err) {\n        setError(\n          err instanceof Error ? err.message : 'Failed to load CMS block'\n        );\n        console.error('Error loading CMS block:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadBlock();\n  }, [identifier]);\n\n  if (loading) {\n    return (\n      <Box className={className} style={style}>\n        {fallback || <CmsBlockSkeleton animationType=\"shimmer\" />}\n      </Box>\n    );\n  }\n\n  if (error || !block) {\n    if (process.env.NODE_ENV === 'development') {\n      return (\n        <Box\n          className={className}\n          style={style}\n          sx={{\n            p: 2,\n            border: '1px dashed #f44336',\n            borderRadius: 1,\n            backgroundColor: '#ffebee',\n            color: '#c62828',\n            textAlign: 'center',\n          }}\n        >\n          CMS Block Error: {error || `Block \"${identifier}\" not found`}\n        </Box>\n      );\n    }\n    return null;\n  }\n\n  return (\n    <CmsBlockRenderer\n      block={block}\n      className={className}\n      style={style}\n      animate={animate}\n    />\n  );\n};\n\nexport default CmsBlockLoader;\n"], "names": [], "mappings": ";;;;AAyDQ;;AAvDR;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAgBO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,EACV,QAAQ,EACR,SAAS,EACT,KAAK,EACL,UAAU,IAAI,EACf;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,MAAM,YAAY,MAAM,CAAA,GAAA,sIAAA,CAAA,6BAA0B,AAAD,EAAE;wBACnD,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SACE,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAEvC,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG;QAAC;KAAW;IAEf,IAAI,SAAS;QACX,qBACE,6LAAC,2LAAA,CAAA,MAAG;YAAC,WAAW;YAAW,OAAO;sBAC/B,0BAAY,6LAAC,8IAAA,CAAA,mBAAgB;gBAAC,eAAc;;;;;;;;;;;IAGnD;IAEA,IAAI,SAAS,CAAC,OAAO;QACnB,wCAA4C;YAC1C,qBACE,6LAAC,2LAAA,CAAA,MAAG;gBACF,WAAW;gBACX,OAAO;gBACP,IAAI;oBACF,GAAG;oBACH,QAAQ;oBACR,cAAc;oBACd,iBAAiB;oBACjB,OAAO;oBACP,WAAW;gBACb;;oBACD;oBACmB,SAAS,CAAC,OAAO,EAAE,WAAW,WAAW,CAAC;;;;;;;QAGlE;;IAEF;IAEA,qBACE,6LAAC,gJAAA,CAAA,UAAgB;QACf,OAAO;QACP,WAAW;QACX,OAAO;QACP,SAAS;;;;;;AAGf;GAtEa;KAAA;uCAwEE", "debugId": null}}]}