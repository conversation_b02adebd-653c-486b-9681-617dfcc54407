(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Types for Next.js
__turbopack_context__.s({
    "PageBuilderElementType": (()=>PageBuilderElementType)
});
var PageBuilderElementType = /*#__PURE__*/ function(PageBuilderElementType) {
    PageBuilderElementType["ROW"] = "row";
    PageBuilderElementType["COLUMN"] = "column";
    PageBuilderElementType["TEXT"] = "text";
    PageBuilderElementType["HEADING"] = "heading";
    PageBuilderElementType["IMAGE"] = "image";
    PageBuilderElementType["BUTTON"] = "button";
    PageBuilderElementType["BANNER"] = "banner";
    PageBuilderElementType["SLIDER"] = "slider";
    PageBuilderElementType["PRODUCTS"] = "products";
    PageBuilderElementType["VIDEO"] = "video";
    PageBuilderElementType["MAP"] = "map";
    PageBuilderElementType["BLOCK"] = "block";
    PageBuilderElementType["HTML"] = "html";
    PageBuilderElementType["DIVIDER"] = "divider";
    PageBuilderElementType["TABS"] = "tabs";
    PageBuilderElementType["TAB_ITEM"] = "tab-item";
    return PageBuilderElementType;
}({});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Constants
__turbopack_context__.s({
    "ALIGNMENT_OPTIONS": (()=>ALIGNMENT_OPTIONS),
    "BACKGROUND_ATTACHMENT_OPTIONS": (()=>BACKGROUND_ATTACHMENT_OPTIONS),
    "BACKGROUND_REPEAT_OPTIONS": (()=>BACKGROUND_REPEAT_OPTIONS),
    "BACKGROUND_SIZE_OPTIONS": (()=>BACKGROUND_SIZE_OPTIONS),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "BUTTON_TYPES": (()=>BUTTON_TYPES),
    "CONTENT_TYPE_MAPPING": (()=>CONTENT_TYPE_MAPPING),
    "CSS_PROPERTY_MAPPING": (()=>CSS_PROPERTY_MAPPING),
    "DEFAULT_CONFIG": (()=>DEFAULT_CONFIG),
    "DEFAULT_STYLES": (()=>DEFAULT_STYLES),
    "ELEMENT_TYPE_MAPPING": (()=>ELEMENT_TYPE_MAPPING),
    "PAGE_BUILDER_ATTRIBUTES": (()=>PAGE_BUILDER_ATTRIBUTES),
    "PAGE_BUILDER_CLASSES": (()=>PAGE_BUILDER_CLASSES),
    "VERTICAL_ALIGNMENT_OPTIONS": (()=>VERTICAL_ALIGNMENT_OPTIONS),
    "VIDEO_PROVIDERS": (()=>VIDEO_PROVIDERS)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
;
const PAGE_BUILDER_CLASSES = {
    // Row classes
    ROW: 'pagebuilder-row',
    ROW_CONTAINED: 'pagebuilder-row-contained',
    ROW_FULL_WIDTH: 'pagebuilder-row-full-width',
    ROW_FULL_BLEED: 'pagebuilder-row-full-bleed',
    // Column classes
    COLUMN: 'pagebuilder-column',
    COLUMN_GROUP: 'pagebuilder-column-group',
    COLUMN_LINE: 'pagebuilder-column-line',
    // Content type classes
    TEXT: 'pagebuilder-text',
    HEADING: 'pagebuilder-heading',
    IMAGE: 'pagebuilder-image',
    BUTTON: 'pagebuilder-button-item',
    BANNER: 'pagebuilder-banner-item',
    SLIDER: 'pagebuilder-slider',
    PRODUCTS: 'pagebuilder-products',
    VIDEO: 'pagebuilder-video',
    MAP: 'pagebuilder-map',
    BLOCK: 'pagebuilder-block',
    HTML: 'pagebuilder-html-code',
    DIVIDER: 'pagebuilder-divider',
    TABS: 'pagebuilder-tabs',
    TAB_ITEM: 'pagebuilder-tab-item'
};
const PAGE_BUILDER_ATTRIBUTES = {
    // Common attributes
    CONTENT_TYPE: 'data-content-type',
    APPEARANCE: 'data-appearance',
    ELEMENT: 'data-element',
    // Background attributes
    BACKGROUND_IMAGES: 'data-background-images',
    BACKGROUND_TYPE: 'data-background-type',
    VIDEO_LOOP: 'data-video-loop',
    VIDEO_PLAY_ONLY_VISIBLE: 'data-video-play-only-visible',
    VIDEO_LAZY_LOAD: 'data-video-lazy-load',
    VIDEO_FALLBACK_SRC: 'data-video-fallback-src',
    // Parallax attributes
    ENABLE_PARALLAX: 'data-enable-parallax',
    PARALLAX_SPEED: 'data-parallax-speed',
    // Banner attributes
    SHOW_BUTTON: 'data-show-button',
    SHOW_OVERLAY: 'data-show-overlay',
    // Slider attributes
    AUTOPLAY: 'data-autoplay',
    AUTOPLAY_SPEED: 'data-autoplay-speed',
    FADE: 'data-fade',
    INFINITE_LOOP: 'data-infinite-loop',
    SHOW_ARROWS: 'data-show-arrows',
    SHOW_DOTS: 'data-show-dots',
    // Products attributes
    PRODUCTS_COUNT: 'data-products-count',
    SORT_ORDER: 'data-sort-order',
    CAROUSEL_MODE: 'data-carousel-mode',
    // Map attributes
    SHOW_CONTROLS: 'data-show-controls',
    // Tabs attributes
    DEFAULT_ACTIVE_TAB: 'data-default-active-tab',
    TABS_ALIGNMENT: 'data-tabs-alignment',
    TABS_NAVIGATION: 'data-tabs-navigation'
};
const ELEMENT_TYPE_MAPPING = {
    [PAGE_BUILDER_CLASSES.ROW]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
    [PAGE_BUILDER_CLASSES.COLUMN]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    [PAGE_BUILDER_CLASSES.TEXT]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
    [PAGE_BUILDER_CLASSES.HEADING]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
    [PAGE_BUILDER_CLASSES.IMAGE]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
    [PAGE_BUILDER_CLASSES.BUTTON]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
    [PAGE_BUILDER_CLASSES.BANNER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
    [PAGE_BUILDER_CLASSES.SLIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].SLIDER,
    [PAGE_BUILDER_CLASSES.PRODUCTS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].PRODUCTS,
    [PAGE_BUILDER_CLASSES.VIDEO]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
    [PAGE_BUILDER_CLASSES.MAP]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].MAP,
    [PAGE_BUILDER_CLASSES.BLOCK]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BLOCK,
    [PAGE_BUILDER_CLASSES.HTML]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
    [PAGE_BUILDER_CLASSES.DIVIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
    [PAGE_BUILDER_CLASSES.TABS]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TABS,
    [PAGE_BUILDER_CLASSES.TAB_ITEM]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TAB_ITEM
};
const CONTENT_TYPE_MAPPING = {
    'row': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
    'column-group': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    'column': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
    'text': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
    'heading': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
    'image': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
    'button-item': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
    'banner': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
    'slider': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].SLIDER,
    'products': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].PRODUCTS,
    'video': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
    'map': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].MAP,
    'block': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BLOCK,
    'html': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
    'divider': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
    'tabs': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TABS,
    'tab-item': __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TAB_ITEM
};
const DEFAULT_STYLES = {
    ROW: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        boxSizing: 'border-box'
    },
    COLUMN: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        boxSizing: 'border-box'
    },
    TEXT: {
        wordWrap: 'break-word'
    },
    HEADING: {
        wordWrap: 'break-word'
    },
    IMAGE: {
        maxWidth: '100%',
        height: 'auto'
    },
    BUTTON: {
        display: 'inline-block',
        textDecoration: 'none',
        cursor: 'pointer'
    },
    BANNER: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
    },
    SLIDER: {
        position: 'relative'
    },
    PRODUCTS: {
        display: 'flex',
        flexWrap: 'wrap'
    },
    VIDEO: {
        position: 'relative',
        width: '100%'
    },
    MAP: {
        width: '100%',
        height: '400px'
    },
    DIVIDER: {
        width: '100%',
        height: '1px',
        backgroundColor: '#e0e0e0'
    },
    TABS: {
        width: '100%'
    }
};
const BREAKPOINTS = {
    MOBILE: 768,
    TABLET: 1024,
    DESKTOP: 1200
};
const DEFAULT_CONFIG = {
    enableLazyLoading: true,
    imageOptimization: true,
    customElementParsers: {},
    componentOverrides: {}
};
const CSS_PROPERTY_MAPPING = {
    'background-color': 'backgroundColor',
    'background-image': 'backgroundImage',
    'background-size': 'backgroundSize',
    'background-position': 'backgroundPosition',
    'background-repeat': 'backgroundRepeat',
    'background-attachment': 'backgroundAttachment',
    'min-height': 'minHeight',
    'text-align': 'textAlign',
    'vertical-align': 'verticalAlign',
    'border-color': 'borderColor',
    'border-width': 'borderWidth',
    'border-radius': 'borderRadius',
    'border-style': 'borderStyle',
    'margin-top': 'marginTop',
    'margin-right': 'marginRight',
    'margin-bottom': 'marginBottom',
    'margin-left': 'marginLeft',
    'padding-top': 'paddingTop',
    'padding-right': 'paddingRight',
    'padding-bottom': 'paddingBottom',
    'padding-left': 'paddingLeft'
};
const VIDEO_PROVIDERS = {
    YOUTUBE: 'youtube',
    VIMEO: 'vimeo',
    MP4: 'mp4'
};
const BUTTON_TYPES = {
    PRIMARY: 'primary',
    SECONDARY: 'secondary',
    LINK: 'link'
};
const ALIGNMENT_OPTIONS = {
    LEFT: 'left',
    CENTER: 'center',
    RIGHT: 'right',
    JUSTIFY: 'justify'
};
const VERTICAL_ALIGNMENT_OPTIONS = {
    TOP: 'top',
    MIDDLE: 'middle',
    BOTTOM: 'bottom'
};
const BACKGROUND_SIZE_OPTIONS = {
    COVER: 'cover',
    CONTAIN: 'contain',
    AUTO: 'auto'
};
const BACKGROUND_REPEAT_OPTIONS = {
    NO_REPEAT: 'no-repeat',
    REPEAT: 'repeat',
    REPEAT_X: 'repeat-x',
    REPEAT_Y: 'repeat-y'
};
const BACKGROUND_ATTACHMENT_OPTIONS = {
    SCROLL: 'scroll',
    FIXED: 'fixed'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Magento 2 Page Builder Utilities
__turbopack_context__.s({
    "cleanHtmlContent": (()=>cleanHtmlContent),
    "convertColumnWidth": (()=>convertColumnWidth),
    "cssValueToNumber": (()=>cssValueToNumber),
    "debounce": (()=>debounce),
    "extractAttributes": (()=>extractAttributes),
    "extractStyles": (()=>extractStyles),
    "generateElementId": (()=>generateElementId),
    "getBreakpoint": (()=>getBreakpoint),
    "getElementContent": (()=>getElementContent),
    "getElementType": (()=>getElementType),
    "isBrowser": (()=>isBrowser),
    "isPageBuilderElement": (()=>isPageBuilderElement),
    "logParsingError": (()=>logParsingError),
    "mergeConfig": (()=>mergeConfig),
    "numberToCssValue": (()=>numberToCssValue),
    "optimizeImageUrl": (()=>optimizeImageUrl),
    "parseBackgroundImages": (()=>parseBackgroundImages),
    "safeJsonParse": (()=>safeJsonParse),
    "validateElement": (()=>validateElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/constants.ts [app-client] (ecmascript)");
;
;
function generateElementId() {
    return `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
function getElementType(element) {
    // Check data-content-type attribute first
    const contentType = element.getAttribute('data-content-type');
    if (contentType && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_TYPE_MAPPING"][contentType]) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_TYPE_MAPPING"][contentType];
    }
    // Check CSS classes
    const classList = Array.from(element.classList);
    for (const className of classList){
        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_TYPE_MAPPING"][className]) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_TYPE_MAPPING"][className];
        }
    }
    // Check for specific element patterns
    if (element.tagName === 'IMG') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE;
    }
    if (element.tagName === 'VIDEO' || element.tagName === 'IFRAME') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO;
    }
    if ([
        'H1',
        'H2',
        'H3',
        'H4',
        'H5',
        'H6'
    ].includes(element.tagName)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING;
    }
    if (element.tagName === 'A' && element.classList.contains('pagebuilder-button-primary')) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON;
    }
    // Default to text for content elements
    if (element.textContent && element.textContent.trim()) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT;
    }
    return null;
}
function extractAttributes(element) {
    const attributes = {};
    // Extract data attributes
    Array.from(element.attributes).forEach((attr)=>{
        if (attr.name.startsWith('data-')) {
            const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
            attributes[key] = attr.value;
        }
    });
    // Extract common HTML attributes
    if (element.getAttribute('id')) {
        attributes.id = element.getAttribute('id');
    }
    if (element.getAttribute('class')) {
        attributes.className = element.getAttribute('class');
    }
    // Extract specific attributes based on element type
    if (element.tagName === 'IMG') {
        attributes.src = element.getAttribute('src');
        attributes.alt = element.getAttribute('alt');
        attributes.title = element.getAttribute('title');
    }
    if (element.tagName === 'A') {
        attributes.href = element.getAttribute('href');
        attributes.target = element.getAttribute('target');
    }
    if (element.tagName === 'VIDEO') {
        attributes.src = element.getAttribute('src');
        attributes.autoplay = element.hasAttribute('autoplay');
        attributes.muted = element.hasAttribute('muted');
        attributes.loop = element.hasAttribute('loop');
        attributes.controls = element.hasAttribute('controls');
    }
    return attributes;
}
function extractStyles(element) {
    const styles = {};
    // Get computed styles
    if ("TURBOPACK compile-time truthy", 1) {
        const computedStyles = window.getComputedStyle(element);
        // Extract relevant CSS properties
        Object.entries(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"]).forEach(([cssProperty, jsProperty])=>{
            const value = computedStyles.getPropertyValue(cssProperty);
            if (value && value !== 'initial' && value !== 'inherit') {
                styles[jsProperty] = value;
            }
        });
    }
    // Extract inline styles
    const inlineStyle = element.getAttribute('style');
    if (inlineStyle) {
        const styleDeclarations = inlineStyle.split(';');
        styleDeclarations.forEach((declaration)=>{
            const [property, value] = declaration.split(':').map((s)=>s.trim());
            if (property && value) {
                const jsProperty = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CSS_PROPERTY_MAPPING"][property] || property.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
                styles[jsProperty] = value;
            }
        });
    }
    return styles;
}
function parseBackgroundImages(backgroundImagesData) {
    try {
        const data = JSON.parse(backgroundImagesData);
        if (data && data.desktop_image) {
            return [
                data.desktop_image,
                data.mobile_image
            ].filter(Boolean);
        }
        return [];
    } catch  {
        return [];
    }
}
function cssValueToNumber(value) {
    return parseFloat(value.replace(/[^\d.-]/g, ''));
}
function numberToCssValue(value, unit = 'px') {
    return `${value}${unit}`;
}
function isPageBuilderElement(element) {
    return element.hasAttribute('data-content-type') || Array.from(element.classList).some((className)=>className.startsWith('pagebuilder-'));
}
function getElementContent(element, preserveHtml = false) {
    if (preserveHtml) {
        return element.innerHTML;
    }
    return element.textContent || '';
}
function cleanHtmlContent(html) {
    // Remove Magento-specific attributes that aren't needed in React
    return html.replace(/data-pb-style="[^"]*"/g, '').replace(/data-element="[^"]*"/g, '').replace(/class="[^"]*pagebuilder-[^"]*"/g, '').trim();
}
function mergeConfig(userConfig = {}) {
    return {
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"],
        ...userConfig,
        customElementParsers: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"].customElementParsers,
            ...userConfig.customElementParsers
        },
        componentOverrides: {
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_CONFIG"].componentOverrides,
            ...userConfig.componentOverrides
        }
    };
}
function optimizeImageUrl(url, width, height) {
    if (!url) return '';
    // If it's already a Next.js optimized URL, return as is
    if (url.includes('/_next/image')) {
        return url;
    }
    // Convert Magento media URLs
    if (url.includes('/media/')) {
        // Remove any existing resize parameters
        const cleanUrl = url.split('?')[0];
        // Add Next.js image optimization parameters
        const params = new URLSearchParams();
        params.set('url', cleanUrl);
        if (width) params.set('w', width.toString());
        if (height) params.set('h', height.toString());
        params.set('q', '75'); // Default quality
        return `/_next/image?${params.toString()}`;
    }
    return url;
}
function validateElement(element) {
    if (!element.type || !element.id) {
        return false;
    }
    // Type-specific validation
    switch(element.type){
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE:
            return !!element.attributes?.src;
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON:
            return !!(element.content || element.attributes?.buttonText);
        case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO:
            return !!(element.attributes?.videoUrl || element.attributes?.src);
        default:
            return true;
    }
}
function getBreakpoint(width) {
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
}
function convertColumnWidth(width) {
    // Magento uses percentages like "33.3333%"
    const percentage = parseFloat(width);
    if (percentage > 0) {
        return `${percentage}%`;
    }
    // Fallback to equal distribution
    return '1fr';
}
function safeJsonParse(json, fallback) {
    try {
        return JSON.parse(json);
    } catch  {
        return fallback;
    }
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function isBrowser() {
    return "object" !== 'undefined';
}
function logParsingError(error, element) {
    if ("TURBOPACK compile-time truthy", 1) {
        console.error('Page Builder parsing error:', error);
        if (element) {
            console.error('Element:', element);
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Row": (()=>Row),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Container/Container.js [app-client] (ecmascript) <export default as Container>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Row = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Determine container type based on appearance
    const getContainerComponent = ()=>{
        switch(attributes.appearance){
            case 'contained':
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Container$2f$Container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Container$3e$__["Container"];
            case 'full-width':
            case 'full-bleed':
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"];
        }
    };
    const ContainerComponent = getContainerComponent();
    // Build styles
    const rowStyles = {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        boxSizing: 'border-box',
        ...styles,
        ...style
    };
    // Background styles
    if (attributes.backgroundColor) {
        rowStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.backgroundImage) {
        rowStyles.backgroundImage = `url(${attributes.backgroundImage})`;
        rowStyles.backgroundSize = attributes.backgroundSize || 'cover';
        rowStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
        rowStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
        rowStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
    }
    if (attributes.minHeight) {
        rowStyles.minHeight = attributes.minHeight;
    }
    // Vertical alignment
    if (attributes.verticalAlignment) {
        switch(attributes.verticalAlignment){
            case 'top':
                rowStyles.justifyContent = 'flex-start';
                break;
            case 'middle':
                rowStyles.justifyContent = 'center';
                break;
            case 'bottom':
                rowStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Parallax effect (if enabled and in browser)
    const parallaxProps = attributes.enableParallax && "object" !== 'undefined' ? {
        initial: {
            y: 0
        },
        whileInView: {
            y: -20
        },
        transition: {
            duration: 0.6,
            ease: 'easeOut'
        },
        viewport: {
            once: false,
            amount: 0.3
        }
    } : {};
    const containerProps = attributes.appearance === 'contained' ? {
        maxWidth: 'lg',
        sx: {
            px: {
                xs: 2,
                sm: 3
            }
        }
    } : {
        sx: {
            width: '100%',
            maxWidth: 'none',
            px: attributes.appearance === 'full-bleed' ? 0 : {
                xs: 2,
                sm: 3
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-row ${className || ''}`,
        style: rowStyles,
        ...parallaxProps,
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    zIndex: 0
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
                lineNumber: 102,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ContainerComponent, {
                ...containerProps,
                sx: {
                    position: 'relative',
                    zIndex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    height: '100%',
                    ...containerProps.sx
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Row.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
};
_c = Row;
const __TURBOPACK__default__export__ = Row;
var _c;
__turbopack_context__.k.register(_c, "Row");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Column": (()=>Column),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
const Column = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const columnStyles = {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        boxSizing: 'border-box',
        flex: '1 1 auto',
        ...styles,
        ...style
    };
    // Width handling
    if (attributes.width) {
        const width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertColumnWidth"])(attributes.width);
        columnStyles.width = width;
        columnStyles.flexBasis = width;
        columnStyles.flexGrow = 0;
        columnStyles.flexShrink = 0;
    }
    // Background styles
    if (attributes.backgroundColor) {
        columnStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.backgroundImage) {
        columnStyles.backgroundImage = `url(${attributes.backgroundImage})`;
        columnStyles.backgroundSize = attributes.backgroundSize || 'cover';
        columnStyles.backgroundPosition = attributes.backgroundPosition || 'center center';
        columnStyles.backgroundRepeat = attributes.backgroundRepeat || 'no-repeat';
        columnStyles.backgroundAttachment = attributes.backgroundAttachment || 'scroll';
    }
    // Height handling
    if (attributes.appearance === 'full-height') {
        columnStyles.height = '100%';
    } else if (attributes.minHeight) {
        columnStyles.minHeight = attributes.minHeight;
    }
    // Vertical alignment
    if (attributes.verticalAlignment) {
        switch(attributes.verticalAlignment){
            case 'top':
                columnStyles.justifyContent = 'flex-start';
                break;
            case 'middle':
                columnStyles.justifyContent = 'center';
                break;
            case 'bottom':
                columnStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Animation variants
    const columnVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-column ${className || ''}`,
        style: columnStyles,
        variants: columnVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    zIndex: 0
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
                lineNumber: 95,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'relative',
                    zIndex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    height: '100%',
                    padding: {
                        xs: 1,
                        sm: 2
                    }
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
                lineNumber: 108,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Column.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
};
_c = Column;
const __TURBOPACK__default__export__ = Column;
var _c;
__turbopack_context__.k.register(_c, "Column");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>Text),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Text = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Build styles
    const textStyles = {
        wordWrap: 'break-word',
        lineHeight: 1.6,
        ...styles,
        ...style
    };
    // Text alignment
    if (attributes.textAlign) {
        textStyles.textAlign = attributes.textAlign;
    }
    // Border styles
    if (attributes.border) {
        textStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        textStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        textStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        textStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const textVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    // Check if content contains HTML
    const isHtml = content && (content.includes('<') || content.includes('&'));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-text ${className || ''}`,
        variants: textVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: isHtml ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: textStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
            lineNumber: 67,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            component: "div",
            sx: textStyles,
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
            lineNumber: 73,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Text.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
};
_c = Text;
const __TURBOPACK__default__export__ = Text;
var _c;
__turbopack_context__.k.register(_c, "Text");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heading": (()=>Heading),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Heading = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Determine heading variant
    const variant = attributes.headingType || 'h2';
    // Build styles
    const headingStyles = {
        wordWrap: 'break-word',
        marginBottom: '1rem',
        ...styles,
        ...style
    };
    // Text alignment
    if (attributes.textAlign) {
        headingStyles.textAlign = attributes.textAlign;
    }
    // Border styles
    if (attributes.border) {
        headingStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        headingStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        headingStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        headingStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const headingVariants = {
        hidden: {
            opacity: 0,
            y: 30
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    // Check if content contains HTML
    const isHtml = content && (content.includes('<') || content.includes('&'));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-heading ${className || ''}`,
        variants: headingVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: isHtml ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            variant: variant,
            component: variant,
            sx: headingStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
            lineNumber: 70,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
            variant: variant,
            component: variant,
            sx: headingStyles,
            children: content
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
            lineNumber: 77,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Heading.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
};
_c = Heading;
const __TURBOPACK__default__export__ = Heading;
var _c;
__turbopack_context__.k.register(_c, "Heading");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderImage": (()=>PageBuilderImage),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const PageBuilderImage = ({ element, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const imageContainerStyles = {
        position: 'relative',
        display: 'inline-block',
        maxWidth: '100%',
        ...styles,
        ...style
    };
    // Alignment
    if (attributes.alignment) {
        switch(attributes.alignment){
            case 'left':
                imageContainerStyles.textAlign = 'left';
                break;
            case 'center':
                imageContainerStyles.textAlign = 'center';
                imageContainerStyles.margin = '0 auto';
                break;
            case 'right':
                imageContainerStyles.textAlign = 'right';
                imageContainerStyles.marginLeft = 'auto';
                break;
        }
    }
    // Border styles
    const imageStyles = {
        maxWidth: '100%',
        height: 'auto'
    };
    if (attributes.border) {
        imageStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        imageStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        imageStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        imageStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const imageVariants = {
        hidden: {
            opacity: 0,
            scale: 0.95
        },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    // Optimize image URL
    const optimizedSrc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.src);
    // Image component
    const ImageComponent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: imageVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        whileHover: {
            scale: 1.02
        },
        transition: {
            duration: 0.3
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: {
                position: 'relative',
                display: 'inline-block',
                ...imageStyles
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: optimizedSrc,
                    alt: attributes.alt || '',
                    title: attributes.title,
                    width: 800,
                    height: 600,
                    style: {
                        width: '100%',
                        height: 'auto',
                        ...imageStyles
                    },
                    loading: attributes.lazyLoading !== false ? 'lazy' : 'eager',
                    quality: 85
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                attributes.caption && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                    component: "figcaption",
                    sx: {
                        mt: 1,
                        fontSize: '0.875rem',
                        color: 'text.secondary',
                        textAlign: attributes.alignment || 'center',
                        fontStyle: 'italic'
                    },
                    children: attributes.caption
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
                    lineNumber: 114,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-image ${className || ''}`,
        component: "figure",
        sx: {
            margin: 0,
            padding: 0,
            ...imageContainerStyles
        },
        children: attributes.link ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: attributes.link,
            target: attributes.linkTarget || '_self',
            style: {
                textDecoration: 'none'
            },
            children: ImageComponent
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
            lineNumber: 142,
            columnNumber: 9
        }, this) : ImageComponent
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Image.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
};
_c = PageBuilderImage;
const __TURBOPACK__default__export__ = PageBuilderImage;
var _c;
__turbopack_context__.k.register(_c, "PageBuilderImage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Button/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
'use client';
;
;
;
;
const Button = ({ element, className, style })=>{
    const { content, attributes, styles } = element;
    // Build container styles
    const containerStyles = {
        display: 'flex',
        ...styles,
        ...style
    };
    // Text alignment for container
    if (attributes.textAlign) {
        switch(attributes.textAlign){
            case 'left':
                containerStyles.justifyContent = 'flex-start';
                break;
            case 'center':
                containerStyles.justifyContent = 'center';
                break;
            case 'right':
                containerStyles.justifyContent = 'flex-end';
                break;
        }
    }
    // Build button styles
    const buttonStyles = {
        textTransform: 'none',
        fontWeight: 500,
        borderRadius: '8px',
        padding: '12px 24px',
        fontSize: '1rem',
        transition: 'all 0.3s ease'
    };
    // Button type styling
    let variant = 'contained';
    let color = 'primary';
    switch(attributes.buttonType){
        case 'primary':
            variant = 'contained';
            color = 'primary';
            break;
        case 'secondary':
            variant = 'outlined';
            color = 'primary';
            break;
        case 'link':
            variant = 'text';
            color = 'primary';
            break;
    }
    // Custom colors
    if (attributes.backgroundColor) {
        buttonStyles.backgroundColor = attributes.backgroundColor;
    }
    if (attributes.textColor) {
        buttonStyles.color = attributes.textColor;
    }
    // Border styles
    if (attributes.border) {
        buttonStyles.border = attributes.border;
    }
    if (attributes.borderColor) {
        buttonStyles.borderColor = attributes.borderColor;
    }
    if (attributes.borderWidth) {
        buttonStyles.borderWidth = attributes.borderWidth;
    }
    if (attributes.borderRadius) {
        buttonStyles.borderRadius = attributes.borderRadius;
    }
    // Animation variants
    const buttonVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        },
        hover: {
            scale: 1.05,
            transition: {
                duration: 0.2
            }
        },
        tap: {
            scale: 0.95,
            transition: {
                duration: 0.1
            }
        }
    };
    const ButtonComponent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: buttonVariants,
        initial: "hidden",
        whileInView: "visible",
        whileHover: "hover",
        whileTap: "tap",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
            variant: variant,
            color: color,
            sx: {
                ...buttonStyles,
                '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                }
            },
            children: content || attributes.buttonText || 'Button'
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
            lineNumber: 119,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
        lineNumber: 111,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-button ${className || ''}`,
        sx: containerStyles,
        children: attributes.link ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: attributes.link,
            target: attributes.linkTarget || '_self',
            style: {
                textDecoration: 'none'
            },
            children: ButtonComponent
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
            lineNumber: 141,
            columnNumber: 9
        }, this) : ButtonComponent
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Button.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
};
_c = Button;
const __TURBOPACK__default__export__ = Button;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Banner": (()=>Banner),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Button/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
const Banner = ({ element, children, className, style })=>{
    const { attributes, styles } = element;
    // Build container styles
    const bannerStyles = {
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        minHeight: attributes.minHeight || '400px',
        ...styles,
        ...style
    };
    // Background styles
    if (attributes.backgroundColor) {
        bannerStyles.backgroundColor = attributes.backgroundColor;
    }
    // Content placement
    let contentAlignment = 'center';
    if (attributes.contentPlacement) {
        contentAlignment = attributes.contentPlacement;
    }
    // Animation variants
    const bannerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    const contentVariants = {
        hidden: {
            opacity: 0,
            y: 50
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.8,
                delay: 0.3,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-banner ${className || ''}`,
        style: bannerStyles,
        variants: bannerVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: [
            attributes.backgroundImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 0
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.backgroundImage),
                    alt: "",
                    fill: true,
                    style: {
                        objectFit: attributes.backgroundSize === 'contain' ? 'contain' : 'cover',
                        objectPosition: attributes.backgroundPosition || 'center center'
                    },
                    quality: 85,
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                    lineNumber: 87,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 77,
                columnNumber: 9
            }, this),
            attributes.showOverlay && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: attributes.overlayColor || 'rgba(0, 0, 0, 0.4)',
                    zIndex: 1
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                sx: {
                    position: 'relative',
                    zIndex: 2,
                    width: '100%',
                    maxWidth: '800px',
                    padding: {
                        xs: 3,
                        md: 6
                    },
                    textAlign: contentAlignment,
                    color: 'white'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: contentVariants,
                    initial: "hidden",
                    whileInView: "visible",
                    viewport: {
                        once: true,
                        amount: 0.1
                    },
                    children: [
                        attributes.content && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                mb: 3
                            },
                            children: attributes.content.includes('<') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                dangerouslySetInnerHTML: {
                                    __html: attributes.content
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 138,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                variant: "h3",
                                component: "h2",
                                sx: {
                                    fontWeight: 700,
                                    mb: 2,
                                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                                    fontSize: {
                                        xs: '2rem',
                                        md: '3rem'
                                    }
                                },
                                children: attributes.content
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 140,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 136,
                            columnNumber: 13
                        }, this),
                        children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                            sx: {
                                mb: 3
                            },
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 158,
                            columnNumber: 13
                        }, this),
                        attributes.showButton && attributes.buttonText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            whileHover: {
                                scale: 1.05
                            },
                            whileTap: {
                                scale: 0.95
                            },
                            children: attributes.buttonLink ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: attributes.buttonLink,
                                target: attributes.buttonTarget || '_self',
                                style: {
                                    textDecoration: 'none'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    variant: attributes.buttonType === 'secondary' ? 'outlined' : 'contained',
                                    size: "large",
                                    sx: {
                                        fontSize: '1.125rem',
                                        fontWeight: 600,
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: 2,
                                        textTransform: 'none',
                                        boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                                        color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                        borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                        '&:hover': {
                                            boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                                            transform: 'translateY(-2px)'
                                        },
                                        transition: 'all 0.3s ease-in-out'
                                    },
                                    children: attributes.buttonText
                                }, void 0, false, {
                                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                    lineNumber: 175,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 170,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                variant: attributes.buttonType === 'secondary' ? 'outlined' : 'contained',
                                size: "large",
                                sx: {
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    boxShadow: '0 4px 14px 0 rgba(0,0,0,0.2)',
                                    color: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                    borderColor: attributes.buttonType === 'secondary' ? 'white' : undefined,
                                    '&:hover': {
                                        boxShadow: '0 6px 20px 0 rgba(0,0,0,0.3)',
                                        transform: 'translateY(-2px)'
                                    },
                                    transition: 'all 0.3s ease-in-out'
                                },
                                children: attributes.buttonText
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                                lineNumber: 199,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                            lineNumber: 165,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                    lineNumber: 128,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
                lineNumber: 117,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/pagebuilder/elements/Banner.tsx",
        lineNumber: 67,
        columnNumber: 5
    }, this);
};
_c = Banner;
const __TURBOPACK__default__export__ = Banner;
var _c;
__turbopack_context__.k.register(_c, "Banner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Video": (()=>Video),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/IconButton/IconButton.js [app-client] (ecmascript) <export default as IconButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/PlayArrow.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@mui/icons-material/esm/Pause.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
const Video = ({ element, className, style })=>{
    _s();
    const { attributes, styles } = element;
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showControls, setShowControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Build container styles
    const videoContainerStyles = {
        position: 'relative',
        width: '100%',
        maxWidth: attributes.maxWidth || '100%',
        margin: '0 auto',
        ...styles,
        ...style
    };
    // Get video embed URL
    const getEmbedUrl = ()=>{
        const { videoType, videoUrl, videoId } = attributes;
        if (videoType === 'youtube') {
            const id = videoId || extractYouTubeId(videoUrl);
            return `https://www.youtube.com/embed/${id}?autoplay=${attributes.autoplay ? 1 : 0}&mute=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
        }
        if (videoType === 'vimeo') {
            const id = videoId || extractVimeoId(videoUrl);
            return `https://player.vimeo.com/video/${id}?autoplay=${attributes.autoplay ? 1 : 0}&muted=${attributes.muted ? 1 : 0}&loop=${attributes.loop ? 1 : 0}&controls=${attributes.controls ? 1 : 0}`;
        }
        return videoUrl;
    };
    // Extract YouTube video ID
    const extractYouTubeId = (url)=>{
        if (!url) return '';
        const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        return match ? match[1] : '';
    };
    // Extract Vimeo video ID
    const extractVimeoId = (url)=>{
        if (!url) return '';
        const match = url.match(/vimeo\.com\/(\d+)/);
        return match ? match[1] : '';
    };
    // Handle play/pause
    const handlePlayPause = ()=>{
        setIsPlaying(!isPlaying);
    };
    // Animation variants
    const videoVariants = {
        hidden: {
            opacity: 0,
            scale: 0.95
        },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    const overlayVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1
        },
        exit: {
            opacity: 0
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-video ${className || ''}`,
        style: videoContainerStyles,
        variants: videoVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        onMouseEnter: ()=>setShowControls(true),
        onMouseLeave: ()=>setShowControls(false),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                position: 'relative',
                width: '100%',
                paddingBottom: '56.25%',
                height: 0,
                overflow: 'hidden',
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
            },
            children: [
                attributes.videoType === 'mp4' ? // Native HTML5 video
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                    src: attributes.videoUrl,
                    autoPlay: attributes.autoplay,
                    muted: attributes.muted,
                    loop: attributes.loop,
                    controls: attributes.controls,
                    style: {
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                    },
                    poster: attributes.fallbackImage ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.fallbackImage) : undefined
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                    lineNumber: 109,
                    columnNumber: 11
                }, this) : // Embedded video (YouTube/Vimeo)
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: !isPlaying && attributes.fallbackImage ? // Fallback image with play button
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
                        sx: {
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            cursor: 'pointer'
                        },
                        onClick: handlePlayPause,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["optimizeImageUrl"])(attributes.fallbackImage),
                                alt: "Video thumbnail",
                                fill: true,
                                style: {
                                    objectFit: 'cover'
                                },
                                quality: 85
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                lineNumber: 141,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                variants: overlayVariants,
                                initial: "hidden",
                                animate: "visible",
                                exit: "exit",
                                style: {
                                    position: 'absolute',
                                    top: '50%',
                                    left: '50%',
                                    transform: 'translate(-50%, -50%)'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                                    sx: {
                                        backgroundColor: 'rgba(0,0,0,0.7)',
                                        color: 'white',
                                        width: 80,
                                        height: 80,
                                        '&:hover': {
                                            backgroundColor: 'rgba(0,0,0,0.8)',
                                            transform: 'scale(1.1)'
                                        },
                                        transition: 'all 0.3s ease'
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        sx: {
                                            fontSize: 40
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                        lineNumber: 175,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                    lineNumber: 162,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                                lineNumber: 150,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 130,
                        columnNumber: 15
                    }, this) : // Embedded iframe
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                        src: getEmbedUrl(),
                        style: {
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            border: 'none'
                        },
                        allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
                        allowFullScreen: true,
                        loading: attributes.lazyLoading !== false ? 'lazy' : 'eager'
                    }, void 0, false, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 181,
                        columnNumber: 15
                    }, this)
                }, void 0, false),
                attributes.videoType === 'mp4' && showControls && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    variants: overlayVariants,
                    initial: "hidden",
                    animate: "visible",
                    exit: "exit",
                    style: {
                        position: 'absolute',
                        bottom: 16,
                        left: 16
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                        onClick: handlePlayPause,
                        sx: {
                            backgroundColor: 'rgba(0,0,0,0.7)',
                            color: 'white',
                            '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.8)'
                            }
                        },
                        children: isPlaying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Pause$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                            lineNumber: 222,
                            columnNumber: 28
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$PlayArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                            lineNumber: 222,
                            columnNumber: 40
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                        lineNumber: 212,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
                    lineNumber: 201,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Video.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this);
};
_s(Video, "EG4wYCUBGOXPZ1s15EGTpyE/z5I=");
_c = Video;
const __TURBOPACK__default__export__ = Video;
var _c;
__turbopack_context__.k.register(_c, "Video");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html": (()=>Html),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Html = ({ element, className, style })=>{
    const { content, styles } = element;
    // Build styles
    const htmlStyles = {
        width: '100%',
        ...styles,
        ...style
    };
    // Animation variants
    const htmlVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `pagebuilder-html ${className || ''}`,
        variants: htmlVariants,
        initial: "hidden",
        whileInView: "visible",
        viewport: {
            once: true,
            amount: 0.1
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            component: "div",
            sx: htmlStyles,
            dangerouslySetInnerHTML: {
                __html: content
            }
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Html.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Html.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
};
_c = Html;
const __TURBOPACK__default__export__ = Html;
var _c;
__turbopack_context__.k.register(_c, "Html");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Divider": (()=>Divider),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Divider/Divider.js [app-client] (ecmascript) <export default as Divider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
const Divider = ({ element, className, style })=>{
    const { attributes, styles } = element;
    // Build styles
    const dividerStyles = {
        width: attributes.lineWidth || '100%',
        height: attributes.lineThickness || '1px',
        backgroundColor: attributes.lineColor || '#e0e0e0',
        border: 'none',
        margin: '2rem 0',
        ...styles,
        ...style
    };
    // Animation variants
    const dividerVariants = {
        hidden: {
            opacity: 0,
            scaleX: 0
        },
        visible: {
            opacity: 1,
            scaleX: 1,
            transition: {
                duration: 0.8,
                ease: 'easeOut'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-divider ${className || ''}`,
        sx: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            my: 2
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            variants: dividerVariants,
            initial: "hidden",
            whileInView: "visible",
            viewport: {
                once: true,
                amount: 0.1
            },
            style: {
                width: '100%'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Divider$2f$Divider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"], {
                sx: {
                    ...dividerStyles,
                    '&::before, &::after': {
                        borderColor: attributes.lineColor || 'divider'
                    }
                }
            }, void 0, false, {
                fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
                lineNumber: 57,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
            lineNumber: 50,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/elements/Divider.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
};
_c = Divider;
const __TURBOPACK__default__export__ = Divider;
var _c;
__turbopack_context__.k.register(_c, "Divider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Elements Export
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript) <export default as Row>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Row": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript) <export default as Column>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Column": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript) <export default as Text>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Text": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript) <export default as Heading>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heading": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript) <export default as PageBuilderImage>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderImage": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript) <export default as Button>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript) <export default as Banner>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Banner": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript) <export default as Video>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Video": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript) <export default as Html>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Html": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript) <export default as Divider>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Divider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript)");
}}),
"[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Page Builder Element Parser
__turbopack_context__.s({
    "ElementParser": (()=>ElementParser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
;
;
class ElementParser {
    config;
    constructor(config){
        this.config = config;
    }
    // Parse a DOM element into a PageBuilderElement
    parseElement(element) {
        try {
            const elementType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementType"])(element);
            if (!elementType) {
                return null;
            }
            // Check for custom parser
            if (this.config.customElementParsers?.[elementType]) {
                return this.config.customElementParsers[elementType](element);
            }
            // Use built-in parser
            return this.parseByType(element, elementType);
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error, element);
            return null;
        }
    }
    // Parse element by type
    parseByType(element, type) {
        const baseElement = this.createBaseElement(element, type);
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW:
                return this.parseRow(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN:
                return this.parseColumn(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT:
                return this.parseText(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING:
                return this.parseHeading(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE:
                return this.parseImage(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON:
                return this.parseButton(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER:
                return this.parseBanner(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO:
                return this.parseVideo(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML:
                return this.parseHtml(element, baseElement);
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER:
                return this.parseDivider(element, baseElement);
            default:
                return baseElement;
        }
    }
    // Create base element with common properties
    createBaseElement(element, type) {
        return {
            type,
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateElementId"])(),
            attributes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractAttributes"])(element),
            styles: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractStyles"])(element),
            rawHtml: element.outerHTML
        };
    }
    // Parse Row element
    parseRow(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW,
            attributes: {
                appearance: attributes.appearance || 'contained',
                enableParallax: attributes.enableParallax === 'true',
                parallaxSpeed: parseFloat(attributes.parallaxSpeed) || 0.5,
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                minHeight: attributes.minHeight,
                verticalAlignment: attributes.verticalAlignment || 'top'
            }
        };
    }
    // Parse Column element
    parseColumn(element, baseElement) {
        const attributes = baseElement.attributes;
        const styles = baseElement.styles;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN,
            attributes: {
                width: this.extractColumnWidth(element, styles),
                appearance: attributes.appearance || 'minimum-height',
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                minHeight: attributes.minHeight,
                verticalAlignment: attributes.verticalAlignment || 'top'
            }
        };
    }
    // Parse Text element
    parseText(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, true),
            attributes: {
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius
            }
        };
    }
    // Parse Heading element
    parseHeading(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, true),
            attributes: {
                headingType: this.extractHeadingType(element),
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius
            }
        };
    }
    // Parse Image element
    parseImage(element, baseElement) {
        const attributes = baseElement.attributes;
        const img = element.tagName === 'IMG' ? element : element.querySelector('img');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE,
            attributes: {
                src: img?.getAttribute('src') || attributes.src || '',
                alt: img?.getAttribute('alt') || attributes.alt || '',
                title: img?.getAttribute('title') || attributes.title,
                caption: this.extractImageCaption(element),
                link: this.extractImageLink(element),
                linkTarget: attributes.linkTarget || '_self',
                alignment: attributes.alignment || 'center',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius,
                lazyLoading: this.config.enableLazyLoading !== false
            }
        };
    }
    // Parse Button element
    parseButton(element, baseElement) {
        const attributes = baseElement.attributes;
        const link = element.tagName === 'A' ? element : element.querySelector('a');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON,
            content: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementContent"])(element, false),
            attributes: {
                buttonType: this.extractButtonType(element),
                link: link?.getAttribute('href') || attributes.link,
                linkTarget: link?.getAttribute('target') || attributes.linkTarget || '_self',
                textAlign: attributes.textAlign || 'left',
                border: attributes.border,
                borderColor: attributes.borderColor,
                borderWidth: attributes.borderWidth,
                borderRadius: attributes.borderRadius,
                backgroundColor: attributes.backgroundColor,
                textColor: attributes.textColor
            }
        };
    }
    // Parse Banner element
    parseBanner(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER,
            attributes: {
                appearance: attributes.appearance || 'poster',
                minHeight: attributes.minHeight || '300px',
                backgroundColor: attributes.backgroundColor,
                backgroundImage: this.parseBackgroundImage(attributes.backgroundImages),
                backgroundSize: attributes.backgroundSize || 'cover',
                backgroundPosition: attributes.backgroundPosition || 'center center',
                backgroundAttachment: attributes.backgroundAttachment || 'scroll',
                backgroundRepeat: attributes.backgroundRepeat || 'no-repeat',
                showButton: attributes.showButton === 'true',
                buttonText: this.extractBannerButtonText(element),
                buttonType: attributes.buttonType || 'primary',
                buttonLink: this.extractBannerButtonLink(element),
                buttonTarget: attributes.buttonTarget || '_self',
                showOverlay: attributes.showOverlay === 'true',
                overlayColor: attributes.overlayColor,
                content: this.extractBannerContent(element),
                contentPlacement: attributes.contentPlacement || 'center'
            }
        };
    }
    // Parse Video element
    parseVideo(element, baseElement) {
        const attributes = baseElement.attributes;
        const video = element.querySelector('video');
        const iframe = element.querySelector('iframe');
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO,
            attributes: {
                videoType: this.extractVideoType(element),
                videoUrl: video?.getAttribute('src') || iframe?.getAttribute('src') || attributes.videoUrl,
                videoId: attributes.videoId,
                maxWidth: attributes.maxWidth || '100%',
                autoplay: attributes.autoplay === 'true' || video?.hasAttribute('autoplay'),
                muted: attributes.muted === 'true' || video?.hasAttribute('muted'),
                loop: attributes.loop === 'true' || video?.hasAttribute('loop'),
                controls: attributes.controls === 'true' || video?.hasAttribute('controls'),
                lazyLoading: this.config.enableLazyLoading !== false,
                fallbackImage: this.extractVideoFallbackImage(element)
            }
        };
    }
    // Parse HTML element
    parseHtml(element, baseElement) {
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML,
            content: element.innerHTML
        };
    }
    // Parse Divider element
    parseDivider(element, baseElement) {
        const attributes = baseElement.attributes;
        return {
            ...baseElement,
            type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER,
            attributes: {
                lineColor: attributes.lineColor || '#e0e0e0',
                lineThickness: attributes.lineThickness || '1px',
                lineWidth: attributes.lineWidth || '100%'
            }
        };
    }
    // Helper methods
    parseBackgroundImage(backgroundImagesData) {
        if (!backgroundImagesData) return undefined;
        const images = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseBackgroundImages"])(backgroundImagesData);
        return images[0]; // Use first image (desktop)
    }
    extractColumnWidth(element, styles) {
        // Try to get width from styles or data attributes
        if (styles.width) return styles.width;
        if (styles.flexBasis) return styles.flexBasis;
        // Check for Magento column classes
        const classList = Array.from(element.classList);
        for (const className of classList){
            if (className.includes('col-')) {
                const match = className.match(/col-(\d+)/);
                if (match) {
                    const cols = parseInt(match[1]);
                    return `${cols / 12 * 100}%`;
                }
            }
        }
        return '100%';
    }
    extractHeadingType(element) {
        const tagName = element.tagName.toLowerCase();
        if ([
            'h1',
            'h2',
            'h3',
            'h4',
            'h5',
            'h6'
        ].includes(tagName)) {
            return tagName;
        }
        return 'h2'; // Default
    }
    extractButtonType(element) {
        const classList = Array.from(element.classList);
        if (classList.includes('pagebuilder-button-secondary')) return 'secondary';
        if (classList.includes('pagebuilder-button-link')) return 'link';
        return 'primary';
    }
    extractImageCaption(element) {
        const caption = element.querySelector('figcaption');
        return caption?.textContent || undefined;
    }
    extractImageLink(element) {
        const link = element.querySelector('a');
        return link?.getAttribute('href') || undefined;
    }
    extractBannerContent(element) {
        const content = element.querySelector('.pagebuilder-banner-content');
        return content?.innerHTML || undefined;
    }
    extractBannerButtonText(element) {
        const button = element.querySelector('.pagebuilder-banner-button');
        return button?.textContent || undefined;
    }
    extractBannerButtonLink(element) {
        const button = element.querySelector('.pagebuilder-banner-button a');
        return button?.getAttribute('href') || undefined;
    }
    extractVideoType(element) {
        const iframe = element.querySelector('iframe');
        if (iframe?.src.includes('youtube.com')) return 'youtube';
        if (iframe?.src.includes('vimeo.com')) return 'vimeo';
        return 'mp4';
    }
    extractVideoFallbackImage(element) {
        const img = element.querySelector('img');
        return img?.getAttribute('src') || undefined;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Main Page Builder Parser
__turbopack_context__.s({
    "PageBuilderParser": (()=>PageBuilderParser),
    "defaultParser": (()=>defaultParser),
    "optimizePageBuilderContent": (()=>optimizePageBuilderContent),
    "parsePageBuilderContent": (()=>parsePageBuilderContent),
    "validatePageBuilderContent": (()=>validatePageBuilderContent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/parser/ElementParser.ts [app-client] (ecmascript)");
;
;
class PageBuilderParser {
    config;
    elementParser;
    constructor(config = {}){
        this.config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])(config);
        this.elementParser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementParser"](this.config);
    }
    // Parse HTML string into PageBuilderContent
    parse(html) {
        try {
            // Create a temporary DOM to parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            // Extract Page Builder elements
            const elements = this.parseElements(doc.body);
            return {
                elements,
                rawHtml: html,
                version: this.extractPageBuilderVersion(html)
            };
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return {
                elements: [],
                rawHtml: html
            };
        }
    }
    // Parse elements from a DOM node
    parseElements(container) {
        const elements = [];
        // Get direct children that are Page Builder elements
        const children = Array.from(container.children);
        for (const child of children){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPageBuilderElement"])(child)) {
                const element = this.elementParser.parseElement(child);
                if (element) {
                    // Parse children recursively
                    element.children = this.parseElements(child);
                    elements.push(element);
                }
            } else {
                // Check if this element contains Page Builder elements
                const nestedElements = this.parseElements(child);
                elements.push(...nestedElements);
            }
        }
        return elements;
    }
    // Extract Page Builder version from HTML
    extractPageBuilderVersion(html) {
        const versionMatch = html.match(/data-pb-version="([^"]+)"/);
        return versionMatch ? versionMatch[1] : undefined;
    }
    // Parse HTML from server-side (Node.js environment)
    static parseServerSide(html, config = {}) {
        // For server-side parsing, we need to use a different approach
        // since DOMParser is not available in Node.js
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Client-side parsing
        const parser = new PageBuilderParser(config);
        return parser.parse(html);
    }
    // Fallback regex-based parsing for server-side
    static parseWithRegex(html, config = {}) {
        const elements = [];
        try {
            // Extract Page Builder elements using regex patterns
            const rowPattern = /<div[^>]*data-content-type="row"[^>]*>(.*?)<\/div>/gs;
            const matches = html.matchAll(rowPattern);
            for (const match of matches){
                const element = PageBuilderParser.parseElementWithRegex(match[0]);
                if (element) {
                    elements.push(element);
                }
            }
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
        }
        return {
            elements,
            rawHtml: html
        };
    }
    // Parse single element with regex (server-side fallback)
    static parseElementWithRegex(elementHtml) {
        try {
            // Extract data-content-type
            const contentTypeMatch = elementHtml.match(/data-content-type="([^"]+)"/);
            if (!contentTypeMatch) return null;
            const contentType = contentTypeMatch[1];
            // Basic element structure
            const element = {
                type: contentType,
                id: `pb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                attributes: PageBuilderParser.extractAttributesWithRegex(elementHtml),
                styles: {},
                rawHtml: elementHtml
            };
            // Extract content for text/heading elements
            if (contentType === 'text' || contentType === 'heading') {
                const contentMatch = elementHtml.match(/>([^<]+)</);
                element.content = contentMatch ? contentMatch[1].trim() : '';
            }
            return element;
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return null;
        }
    }
    // Extract attributes with regex (server-side fallback)
    static extractAttributesWithRegex(html) {
        const attributes = {};
        // Extract data attributes
        const dataAttrPattern = /data-([^=]+)="([^"]+)"/g;
        let match;
        while((match = dataAttrPattern.exec(html)) !== null){
            const key = match[1].replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());
            attributes[key] = match[2];
        }
        return attributes;
    }
    // Validate parsed content
    validateContent(content) {
        try {
            // Check if content has valid structure
            if (!content.elements || !Array.isArray(content.elements)) {
                return false;
            }
            // Validate each element
            for (const element of content.elements){
                if (!this.validateElement(element)) {
                    return false;
                }
            }
            return true;
        } catch (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logParsingError"])(error);
            return false;
        }
    }
    // Validate single element
    validateElement(element) {
        // Check required properties
        if (!element.type || !element.id) {
            return false;
        }
        // Validate children recursively
        if (element.children) {
            for (const child of element.children){
                if (!this.validateElement(child)) {
                    return false;
                }
            }
        }
        return true;
    }
    // Clean and optimize parsed content
    optimizeContent(content) {
        return {
            ...content,
            elements: this.optimizeElements(content.elements)
        };
    }
    // Optimize elements (remove empty elements, merge similar elements, etc.)
    optimizeElements(elements) {
        return elements.filter((element)=>this.shouldKeepElement(element)).map((element)=>({
                ...element,
                children: element.children ? this.optimizeElements(element.children) : undefined
            }));
    }
    // Determine if element should be kept during optimization
    shouldKeepElement(element) {
        // Remove empty text elements
        if (element.type === 'text' && (!element.content || element.content.trim() === '')) {
            return false;
        }
        // Remove empty HTML elements
        if (element.type === 'html' && (!element.content || element.content.trim() === '')) {
            return false;
        }
        // Keep all other elements
        return true;
    }
    // Get configuration
    getConfig() {
        return this.config;
    }
    // Update configuration
    updateConfig(newConfig) {
        this.config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])({
            ...this.config,
            ...newConfig
        });
        this.elementParser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$parser$2f$ElementParser$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementParser"](this.config);
    }
}
const defaultParser = new PageBuilderParser();
const parsePageBuilderContent = (html, config)=>{
    return PageBuilderParser.parseServerSide(html, config);
};
const validatePageBuilderContent = (content)=>{
    return defaultParser.validateContent(content);
};
const optimizePageBuilderContent = (content)=>{
    return defaultParser.optimizeContent(content);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/pagebuilder/PageBuilderRenderer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageBuilderRenderer": (()=>PageBuilderRenderer),
    "PageBuilderRendererSSR": (()=>PageBuilderRendererSSR),
    "RawHtmlRenderer": (()=>RawHtmlRenderer),
    "default": (()=>__TURBOPACK__default__export__),
    "usePageBuilderConfig": (()=>usePageBuilderConfig),
    "usePageBuilderContext": (()=>usePageBuilderContext),
    "usePageBuilderDeviceType": (()=>usePageBuilderDeviceType),
    "usePageBuilderEditing": (()=>usePageBuilderEditing)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__ = __turbopack_context__.i("[project]/node_modules/@mui/material/esm/Box/Box.js [app-client] (ecmascript) <export default as Box>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pagebuilder/utils.ts [app-client] (ecmascript)");
// Import Page Builder components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Row.tsx [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Column$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Column.tsx [app-client] (ecmascript) <export default as Column>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Text$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Text.tsx [app-client] (ecmascript) <export default as Text>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heading$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Heading.tsx [app-client] (ecmascript) <export default as Heading>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PageBuilderImage$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Image.tsx [app-client] (ecmascript) <export default as PageBuilderImage>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Button.tsx [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banner$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Banner.tsx [app-client] (ecmascript) <export default as Banner>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Video.tsx [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Html$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Html.tsx [app-client] (ecmascript) <export default as Html>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__ = __turbopack_context__.i("[project]/src/components/pagebuilder/elements/Divider.tsx [app-client] (ecmascript) <export default as Divider>");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
// Page Builder Context
const PageBuilderContextProvider = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const usePageBuilderContext = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(PageBuilderContextProvider);
    if (!context) {
        throw new Error('usePageBuilderContext must be used within a PageBuilderRenderer');
    }
    return context;
};
_s(usePageBuilderContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
// Component mapping
const COMPONENT_MAP = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].ROW]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Row$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].COLUMN]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Column$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Column$3e$__["Column"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].TEXT]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Text$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Text$3e$__["Text"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HEADING]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Heading$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heading$3e$__["Heading"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].IMAGE]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Image$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PageBuilderImage$3e$__["PageBuilderImage"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BUTTON]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].BANNER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Banner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Banner$3e$__["Banner"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].VIDEO]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Video$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].HTML]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Html$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Html$3e$__["Html"],
    [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageBuilderElementType"].DIVIDER]: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pagebuilder$2f$elements$2f$Divider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Divider$3e$__["Divider"]
};
// Individual element renderer
const ElementRenderer = ({ element, config })=>{
    // Get component from mapping or custom overrides
    const Component = config.componentOverrides?.[element.type] || COMPONENT_MAP[element.type];
    if (!Component) {
        // Fallback for unknown element types
        console.warn(`Unknown Page Builder element type: ${element.type}`);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            sx: {
                p: 2,
                border: '1px dashed #ccc',
                borderRadius: 1,
                backgroundColor: '#f5f5f5',
                textAlign: 'center',
                color: 'text.secondary'
            },
            children: [
                "Unknown element: ",
                element.type
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this);
    }
    // Render children if they exist
    const children = element.children?.map((child, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ElementRenderer, {
            element: child,
            config: config
        }, child.id || index, false, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 97,
            columnNumber: 5
        }, this));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
        element: element,
        className: `pagebuilder-element pagebuilder-${element.type}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
};
_c = ElementRenderer;
const PageBuilderRenderer = ({ content, config = {}, className, style, isEditing = false, deviceType = 'desktop' })=>{
    // Merge configuration
    const mergedConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pagebuilder$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeConfig"])(config);
    // Parse content if it's a string
    let pageBuilderContent;
    if (typeof content === 'string') {
        // Import parser dynamically to avoid SSR issues
        const { parsePageBuilderContent } = __turbopack_context__.r("[project]/src/components/pagebuilder/parser/PageBuilderParser.ts [app-client] (ecmascript)");
        pageBuilderContent = parsePageBuilderContent(content, mergedConfig);
    } else {
        pageBuilderContent = content;
    }
    // Create context value
    const contextValue = {
        config: mergedConfig,
        isEditing,
        deviceType
    };
    // If no elements, return empty
    if (!pageBuilderContent.elements || pageBuilderContent.elements.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderContextProvider.Provider, {
        value: contextValue,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
            className: `pagebuilder-content ${className || ''}`,
            sx: {
                width: '100%',
                ...style
            },
            children: pageBuilderContent.elements.map((element, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ElementRenderer, {
                    element: element,
                    config: mergedConfig
                }, element.id || index, false, {
                    fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
                    lineNumber: 155,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
            lineNumber: 147,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
_c1 = PageBuilderRenderer;
const PageBuilderRendererSSR = (props)=>{
    // For SSR, we need to handle the parsing differently
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Client-side: use normal renderer
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PageBuilderRenderer, {
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 183,
        columnNumber: 10
    }, this);
};
_c2 = PageBuilderRendererSSR;
const RawHtmlRenderer = ({ html, className })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$esm$2f$Box$2f$Box$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Box$3e$__["Box"], {
        className: `pagebuilder-raw-html ${className || ''}`,
        dangerouslySetInnerHTML: {
            __html: html
        },
        sx: {
            '& .pagebuilder-row': {
                display: 'flex',
                flexDirection: 'column',
                width: '100%'
            },
            '& .pagebuilder-column-group': {
                display: 'flex',
                flexWrap: 'wrap',
                width: '100%'
            },
            '& .pagebuilder-column': {
                flex: '1 1 auto',
                minWidth: 0
            }
        }
    }, void 0, false, {
        fileName: "[project]/src/components/pagebuilder/PageBuilderRenderer.tsx",
        lineNumber: 192,
        columnNumber: 5
    }, this);
};
_c3 = RawHtmlRenderer;
const usePageBuilderConfig = ()=>{
    _s1();
    const context = usePageBuilderContext();
    return context.config;
};
_s1(usePageBuilderConfig, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const usePageBuilderEditing = ()=>{
    _s2();
    const context = usePageBuilderContext();
    return context.isEditing || false;
};
_s2(usePageBuilderEditing, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const usePageBuilderDeviceType = ()=>{
    _s3();
    const context = usePageBuilderContext();
    return context.deviceType || 'desktop';
};
_s3(usePageBuilderDeviceType, "xFQvSdCmJe9+kkMD4uLyzD+KLt0=", false, function() {
    return [
        usePageBuilderContext
    ];
});
const __TURBOPACK__default__export__ = PageBuilderRenderer;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "ElementRenderer");
__turbopack_context__.k.register(_c1, "PageBuilderRenderer");
__turbopack_context__.k.register(_c2, "PageBuilderRendererSSR");
__turbopack_context__.k.register(_c3, "RawHtmlRenderer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_a5f2041b._.js.map