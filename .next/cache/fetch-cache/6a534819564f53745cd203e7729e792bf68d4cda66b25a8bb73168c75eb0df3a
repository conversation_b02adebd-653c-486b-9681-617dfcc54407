{"kind": "FETCH", "data": {"headers": {"cache-control": "max-age=0, must-revalidate, no-cache, no-store", "connection": "Keep-Alive", "content-length": "570", "content-type": "application/json", "date": "Sat, 12 Jul 2025 20:53:13 GMT", "expires": "Fri, 12 Jul 2024 20:53:13 GMT", "keep-alive": "timeout=5, max=99", "pragma": "no-cache", "server": "Apache/2.4.58 (<PERSON><PERSON><PERSON><PERSON>)", "set-cookie": "private_content_version=27939bda3eecda9f10106aa4acc05305; expires=Tue, 10 Jul 2035 20:53:13 GMT; Max-Age=315360000; path=/; SameSite=Lax", "x-content-type-options": "nosniff", "x-frame-options": "SAMEORIGIN", "x-magento-cache-id": "11308f61e8df24014cf15cdb9eaa9cb2748d9251d1647b72a866127b79329499", "x-magento-tags": "FPC", "x-xss-protection": "1; mode=block"}, "body": "eyJkYXRhIjp7InN0b3JlQ29uZmlnIjp7ImlkIjoxLCJjb2RlIjoiZGVmYXVsdCIsInN0b3JlX25hbWUiOiJEZWZhdWx0IFN0b3JlIFZpZXciLCJsb2NhbGUiOiJlbl9VUyIsImJhc2VfY3VycmVuY3lfY29kZSI6IlVTRCIsImRlZmF1bHRfZGlzcGxheV9jdXJyZW5jeV9jb2RlIjoiVVNEIiwidGltZXpvbmUiOiJBc2lhXC9Lb2xrYXRhIiwiYmFzZV91cmwiOiJodHRwOlwvXC9tYWdlbnRvMi5sb2NhbFwvIiwiYmFzZV9tZWRpYV91cmwiOiJodHRwOlwvXC9tYWdlbnRvMi5sb2NhbFwvbWVkaWFcLyIsImNtc19ob21lX3BhZ2UiOiJob21lIiwiY21zX25vX3JvdXRlIjoibm8tcm91dGUiLCJkZWZhdWx0X3RpdGxlIjoiTWFnZW50byBDb21tZXJjZSIsImRlZmF1bHRfZGVzY3JpcHRpb24iOm51bGwsImRlZmF1bHRfa2V5d29yZHMiOm51bGwsImhlYWRlcl9sb2dvX3NyYyI6bnVsbCwibG9nb19hbHQiOm51bGwsIndlbGNvbWUiOiJEZWZhdWx0IHdlbGNvbWUgbXNnISIsImNvcHlyaWdodCI6IkNvcHlyaWdodCBcdTAwYTkgMjAxMy1wcmVzZW50IE1hZ2VudG8sIEluYy4gQWxsIHJpZ2h0cyByZXNlcnZlZC4ifX19", "status": 200, "url": "http://magento2.local/graphql/"}, "revalidate": 86400, "tags": []}