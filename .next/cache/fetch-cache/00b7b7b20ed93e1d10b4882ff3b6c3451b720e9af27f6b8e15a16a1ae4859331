{"kind": "FETCH", "data": {"headers": {"cache-control": "max-age=0, must-revalidate, no-cache, no-store", "connection": "Keep-Alive", "content-length": "118", "content-type": "application/json", "date": "Sat, 12 Jul 2025 20:53:10 GMT", "expires": "Fri, 12 Jul 2024 20:53:13 GMT", "keep-alive": "timeout=5, max=100", "pragma": "no-cache", "server": "Apache/2.4.58 (<PERSON><PERSON><PERSON><PERSON>)", "set-cookie": "private_content_version=c385c71f8d4723d0de1f1f4d5de18ade; expires=Tue, 10 Jul 2035 20:53:13 GMT; Max-Age=315360000; path=/; SameSite=Lax", "x-content-type-options": "nosniff", "x-frame-options": "SAMEORIGIN", "x-magento-cache-id": "11308f61e8df24014cf15cdb9eaa9cb2748d9251d1647b72a866127b79329499", "x-magento-tags": "FPC", "x-xss-protection": "1; mode=block"}, "body": "eyJkYXRhIjp7InN0b3JlQ29uZmlnIjp7ImNtc19ob21lX3BhZ2UiOiJob21lIiwic3RvcmVfbmFtZSI6IkRlZmF1bHQgU3RvcmUgVmlldyIsImRlZmF1bHRfdGl0bGUiOiJNYWdlbnRvIENvbW1lcmNlIn19fQ==", "status": 200, "url": "http://magento2.local/graphql/"}, "revalidate": 86400, "tags": []}