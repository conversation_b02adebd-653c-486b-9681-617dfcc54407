{"version": 3, "file": "main.cjs", "sources": ["index.js"], "sourcesContent": ["function maybe(thunk) {\n  try { return thunk() } catch (_) {}\n}\n\nvar safeGlobal = (\n  maybe(function() { return globalThis }) ||\n  maybe(function() { return window }) ||\n  maybe(function() { return self }) ||\n  maybe(function() { return global }) ||\n  // We don't expect the Function constructor ever to be invoked at runtime, as\n  // long as at least one of globalThis, window, self, or global is defined, so\n  // we are under no obligation to make it easy for static analysis tools to\n  // detect syntactic usage of the Function constructor. If you think you can\n  // improve your static analysis to detect this obfuscation, think again. This\n  // is an arms race you cannot win, at least not in JavaScript.\n  maybe(function() { return maybe.constructor(\"return this\")() })\n);\n\nvar needToRemove = false;\n\nexport function install() {\n  if (safeGlobal &&\n      !maybe(function() { return process.env.NODE_ENV }) &&\n      !maybe(function() { return process })) {\n    Object.defineProperty(safeGlobal, \"process\", {\n      value: {\n        env: {\n          // This default needs to be \"production\" instead of \"development\", to\n          // avoid the problem https://github.com/graphql/graphql-js/pull/2894\n          // will eventually solve, once merged and released.\n          NODE_ENV: \"production\",\n        },\n      },\n      // Let anyone else change global.process as they see fit, but hide it from\n      // Object.keys(global) enumeration.\n      configurable: true,\n      enumerable: false,\n      writable: true,\n    });\n    needToRemove = true;\n  }\n}\n\n// Call install() at least once, when this module is imported.\ninstall();\n\nexport function remove() {\n  if (needToRemove) {\n    delete safeGlobal.process;\n    needToRemove = false;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,KAAK,CAAC,KAAK,EAAE;AACtB,EAAE,IAAI,EAAE,OAAO,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AACrC,CAAC;AACD;AACA,IAAI,UAAU;AACd,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,UAAU,EAAE,CAAC;AACzC,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,MAAM,EAAE,CAAC;AACrC,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,IAAI,EAAE,CAAC;AACnC,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,MAAM,EAAE,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;AACjE,CAAC,CAAC;AACF;AACA,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB;AACO,SAAS,OAAO,GAAG;AAC1B,EAAE,IAAI,UAAU;AAChB,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;AACxD,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,OAAO,EAAE,CAAC,EAAE;AAC7C,IAAI,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE;AACb;AACA;AACA;AACA,UAAU,QAAQ,EAAE,YAAY;AAChC,SAAS;AACT,OAAO;AACP;AACA;AACA,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,YAAY,GAAG,IAAI,CAAC;AACxB,GAAG;AACH,CAAC;AACD;AACA;AACA,OAAO,EAAE,CAAC;AACV;AACO,SAAS,MAAM,GAAG;AACzB,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC;AAC9B,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB,GAAG;AACH;;;;;"}