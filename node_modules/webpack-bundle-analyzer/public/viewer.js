/*! For license information please see viewer.js.LICENSE.txt */
(()=>{var e={184:(e,t)=>{var n;
/*!
  Copyright (c) 2018 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var i={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var o=typeof n;if("string"===o||"number"===o)e.push(n);else if(Array.isArray(n)){if(n.length){var a=r.apply(null,n);a&&e.push(a)}}else if("object"===o)if(n.toString===Object.prototype.toString)for(var s in n)i.call(n,s)&&n[s]&&e.push(s);else e.push(n.toString())}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(n=function(){return r}.apply(t,[]))||(e.exports=n)}()},908:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Button__button{background:#fff;border:1px solid #aaa;border-radius:4px;cursor:pointer;display:inline-block;font:var(--main-font);outline:none;padding:5px 7px;transition:background .3s ease;white-space:nowrap}.Button__button:focus,.Button__button:hover{background:#ffefd7}.Button__button.Button__active{background:orange;color:#000}.Button__button[disabled]{cursor:default}","",{version:3,sources:["webpack://./client/components/Button.css"],names:[],mappings:"AAAA,gBACE,eAAgB,CAChB,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CACf,oBAAqB,CACrB,qBAAsB,CACtB,YAAa,CACb,eAAgB,CAChB,8BAA+B,CAC/B,kBACF,CAEA,4CAEE,kBACF,CAEA,+BACE,iBAAmB,CACnB,UACF,CAEA,0BACE,cACF",sourcesContent:[".button {\n  background: #fff;\n  border: 1px solid #aaa;\n  border-radius: 4px;\n  cursor: pointer;\n  display: inline-block;\n  font: var(--main-font);\n  outline: none;\n  padding: 5px 7px;\n  transition: background .3s ease;\n  white-space: nowrap;\n}\n\n.button:focus,\n.button:hover {\n  background: #ffefd7;\n}\n\n.button.active {\n  background: #ffa500;\n  color: #000;\n}\n\n.button[disabled] {\n  cursor: default;\n}\n"],sourceRoot:""}]),a.locals={button:"Button__button",active:"Button__active"};const s=a},396:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Checkbox__label{display:inline-block}.Checkbox__checkbox,.Checkbox__label{cursor:pointer}.Checkbox__itemText{margin-left:3px;position:relative;top:-2px;vertical-align:middle}","",{version:3,sources:["webpack://./client/components/Checkbox.css"],names:[],mappings:"AAAA,iBAEE,oBACF,CAEA,qCAJE,cAMF,CAEA,oBACE,eAAgB,CAChB,iBAAkB,CAClB,QAAS,CACT,qBACF",sourcesContent:[".label {\n  cursor: pointer;\n  display: inline-block;\n}\n\n.checkbox {\n  cursor: pointer;\n}\n\n.itemText {\n  margin-left: 3px;\n  position: relative;\n  top: -2px;\n  vertical-align: middle;\n}\n"],sourceRoot:""}]),a.locals={label:"Checkbox__label",checkbox:"Checkbox__checkbox",itemText:"Checkbox__itemText"};const s=a},213:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".CheckboxList__container{font:var(--main-font);white-space:nowrap}.CheckboxList__label{font-size:11px;font-weight:700;margin-bottom:7px}.CheckboxList__item+.CheckboxList__item{margin-top:1px}","",{version:3,sources:["webpack://./client/components/CheckboxList.css"],names:[],mappings:"AAAA,yBACE,qBAAsB,CACtB,kBACF,CAEA,qBACE,cAAe,CACf,eAAiB,CACjB,iBACF,CAEA,wCACE,cACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-size: 11px;\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.item + .item {\n  margin-top: 1px;\n}\n"],sourceRoot:""}]),a.locals={container:"CheckboxList__container",label:"CheckboxList__label",item:"CheckboxList__item"};const s=a},580:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".ContextMenu__container{background:#fff;border:1px solid #aaa;border-radius:4px;font:var(--main-font);list-style:none;opacity:1;padding:0;position:absolute;transition:opacity .2s ease,visibility .2s ease;visibility:visible;white-space:nowrap}.ContextMenu__hidden{opacity:0;visibility:hidden}","",{version:3,sources:["webpack://./client/components/ContextMenu.css"],names:[],mappings:"AAAA,wBAKE,eAAgB,CAChB,qBAAsB,CAFtB,iBAAkB,CAHlB,qBAAsB,CAMtB,eAAgB,CAChB,SAAU,CALV,SAAU,CADV,iBAAkB,CASlB,+CAAiD,CADjD,kBAAmB,CADnB,kBAGF,CAEA,qBACE,SAAU,CACV,iBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  position: absolute;\n  padding: 0;\n  border-radius: 4px;\n  background: #fff;\n  border: 1px solid #aaa;\n  list-style: none;\n  opacity: 1;\n  white-space: nowrap;\n  visibility: visible;\n  transition: opacity .2s ease, visibility .2s ease;\n}\n\n.hidden {\n  opacity: 0;\n  visibility: hidden;\n}\n"],sourceRoot:""}]),a.locals={container:"ContextMenu__container",hidden:"ContextMenu__hidden"};const s=a},270:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".ContextMenuItem__item{cursor:pointer;margin:0;padding:8px 14px;-webkit-user-select:none;user-select:none}.ContextMenuItem__item:hover{background:#ffefd7}.ContextMenuItem__disabled{color:grey;cursor:default}.ContextMenuItem__item.ContextMenuItem__disabled:hover{background:transparent}","",{version:3,sources:["webpack://./client/components/ContextMenuItem.css"],names:[],mappings:"AAAA,uBACE,cAAe,CACf,QAAS,CACT,gBAAiB,CACjB,wBAAiB,CAAjB,gBACF,CAEA,6BACE,kBACF,CAEA,2BAEE,UAAW,CADX,cAEF,CAEA,uDACE,sBACF",sourcesContent:[".item {\n  cursor: pointer;\n  margin: 0;\n  padding: 8px 14px;\n  user-select: none;\n}\n\n.item:hover {\n  background: #ffefd7;\n}\n\n.disabled {\n  cursor: default;\n  color: gray;\n}\n\n.item.disabled:hover {\n  background: transparent;\n}\n"],sourceRoot:""}]),a.locals={item:"ContextMenuItem__item",disabled:"ContextMenuItem__disabled"};const s=a},172:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Dropdown__container{font:var(--main-font);white-space:nowrap}.Dropdown__label{font-size:11px;font-weight:700;margin-bottom:7px}.Dropdown__input{border:1px solid #aaa;border-radius:4px;color:#7f7f7f;display:block;height:27px;width:100%}.Dropdown__option{cursor:pointer;padding:4px 0}","",{version:3,sources:["webpack://./client/components/Dropdown.css"],names:[],mappings:"AAAA,qBACE,qBAAsB,CACtB,kBACF,CAEA,iBACE,cAAe,CACf,eAAiB,CACjB,iBACF,CAEA,iBACE,qBAAsB,CACtB,iBAAkB,CAGlB,aAAc,CAFd,aAAc,CAGd,WAAY,CAFZ,UAGF,CAEA,kBAEE,cAAe,CADf,aAEF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-size: 11px;\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.input {\n  border: 1px solid #aaa;\n  border-radius: 4px;\n  display: block;\n  width: 100%;\n  color: #7f7f7f;\n  height: 27px;\n}\n\n.option {\n  padding: 4px 0;\n  cursor: pointer;\n}\n"],sourceRoot:""}]),a.locals={container:"Dropdown__container",label:"Dropdown__label",input:"Dropdown__input",option:"Dropdown__option"};const s=a},746:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Icon__icon{background:no-repeat 50%/contain;display:inline-block}","",{version:3,sources:["webpack://./client/components/Icon.css"],names:[],mappings:"AAAA,YACE,gCAAoC,CACpC,oBACF",sourcesContent:[".icon {\n  background: no-repeat center/contain;\n  display: inline-block;\n}\n"],sourceRoot:""}]),a.locals={icon:"Icon__icon"};const s=a},697:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var i=n(15),r=n.n(i),o=n(645),a=n.n(o),s=n(667),u=n.n(s),l=n(911),c=n(752),h=n(570),f=n(868),d=a()(r()),p=u()(l.Z),g=u()(c.Z),b=u()(h.Z),v=u()(f.Z);d.push([e.id,".ModuleItem__container{background:no-repeat 0;cursor:pointer;margin-bottom:4px;padding-left:18px;position:relative;white-space:nowrap}.ModuleItem__container.ModuleItem__module{background-image:url("+p+");background-position-x:1px}.ModuleItem__container.ModuleItem__folder{background-image:url("+g+")}.ModuleItem__container.ModuleItem__chunk{background-image:url("+b+")}.ModuleItem__container.ModuleItem__invisible:hover:before{background:url("+v+') no-repeat 0;content:"";height:100%;left:0;position:absolute;top:1px;width:13px}',"",{version:3,sources:["webpack://./client/components/ModuleItem.css"],names:[],mappings:"AAAA,uBACE,sBAAiC,CACjC,cAAe,CACf,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,kBACF,CAEA,0CACE,wDAAkD,CAClD,yBACF,CAEA,0CACE,wDACF,CAEA,yCACE,wDACF,CAEA,0DACE,8DAAqE,CACrE,UAAW,CACX,WAAY,CACZ,MAAO,CAEP,iBAAkB,CADlB,OAAQ,CAER,UACF",sourcesContent:[".container {\n  background: no-repeat left center;\n  cursor: pointer;\n  margin-bottom: 4px;\n  padding-left: 18px;\n  position: relative;\n  white-space: nowrap;\n}\n\n.container.module {\n  background-image: url('../assets/icon-module.svg');\n  background-position-x: 1px;\n}\n\n.container.folder {\n  background-image: url('../assets/icon-folder.svg');\n}\n\n.container.chunk {\n  background-image: url('../assets/icon-chunk.svg');\n}\n\n.container.invisible:hover::before {\n  background: url('../assets/icon-invisible.svg') no-repeat left center;\n  content: \"\";\n  height: 100%;\n  left: 0;\n  top: 1px;\n  position: absolute;\n  width: 13px;\n}\n"],sourceRoot:""}]),d.locals={container:"ModuleItem__container",module:"ModuleItem__module",folder:"ModuleItem__folder",chunk:"ModuleItem__chunk",invisible:"ModuleItem__invisible"};const m=d},784:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".ModulesList__container{font:var(--main-font)}","",{version:3,sources:["webpack://./client/components/ModulesList.css"],names:[],mappings:"AAAA,wBACE,qBACF",sourcesContent:[".container {\n  font: var(--main-font);\n}\n"],sourceRoot:""}]),a.locals={container:"ModulesList__container"};const s=a},393:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".ModulesTreemap__container{align-items:stretch;display:flex;height:100%;position:relative;width:100%}.ModulesTreemap__map{flex:1}.ModulesTreemap__sidebarGroup{font:var(--main-font);margin-bottom:20px}.ModulesTreemap__showOption{margin-top:5px}.ModulesTreemap__activeSize{font-weight:700}.ModulesTreemap__foundModulesInfo{display:flex;font:var(--main-font);margin:8px 0 0}.ModulesTreemap__foundModulesInfoItem+.ModulesTreemap__foundModulesInfoItem{margin-left:15px}.ModulesTreemap__foundModulesContainer{margin-top:15px;max-height:600px;overflow:auto}.ModulesTreemap__foundModulesChunk+.ModulesTreemap__foundModulesChunk{margin-top:15px}.ModulesTreemap__foundModulesChunkName{cursor:pointer;font:var(--main-font);font-weight:700;margin-bottom:7px}.ModulesTreemap__foundModulesList{margin-left:7px}","",{version:3,sources:["webpack://./client/components/ModulesTreemap.css"],names:[],mappings:"AAAA,2BACE,mBAAoB,CACpB,YAAa,CACb,WAAY,CACZ,iBAAkB,CAClB,UACF,CAEA,qBACE,MACF,CAEA,8BACE,qBAAsB,CACtB,kBACF,CAEA,4BACE,cACF,CAEA,4BACE,eACF,CAEA,kCACE,YAAa,CACb,qBAAsB,CACtB,cACF,CAEA,4EACE,gBACF,CAEA,uCACE,eAAgB,CAChB,gBAAiB,CACjB,aACF,CAEA,sEACE,eACF,CAEA,uCACE,cAAe,CACf,qBAAsB,CACtB,eAAiB,CACjB,iBACF,CAEA,kCACE,eACF",sourcesContent:[".container {\n  align-items: stretch;\n  display: flex;\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n\n.map {\n  flex: 1;\n}\n\n.sidebarGroup {\n  font: var(--main-font);\n  margin-bottom: 20px;\n}\n\n.showOption {\n  margin-top: 5px;\n}\n\n.activeSize {\n  font-weight: bold;\n}\n\n.foundModulesInfo {\n  display: flex;\n  font: var(--main-font);\n  margin: 8px 0 0;\n}\n\n.foundModulesInfoItem + .foundModulesInfoItem {\n  margin-left: 15px;\n}\n\n.foundModulesContainer {\n  margin-top: 15px;\n  max-height: 600px;\n  overflow: auto;\n}\n\n.foundModulesChunk + .foundModulesChunk {\n  margin-top: 15px;\n}\n\n.foundModulesChunkName {\n  cursor: pointer;\n  font: var(--main-font);\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.foundModulesList {\n  margin-left: 7px;\n}\n"],sourceRoot:""}]),a.locals={container:"ModulesTreemap__container",map:"ModulesTreemap__map",sidebarGroup:"ModulesTreemap__sidebarGroup",showOption:"ModulesTreemap__showOption",activeSize:"ModulesTreemap__activeSize",foundModulesInfo:"ModulesTreemap__foundModulesInfo",foundModulesInfoItem:"ModulesTreemap__foundModulesInfoItem",foundModulesContainer:"ModulesTreemap__foundModulesContainer",foundModulesChunk:"ModulesTreemap__foundModulesChunk",foundModulesChunkName:"ModulesTreemap__foundModulesChunkName",foundModulesList:"ModulesTreemap__foundModulesList"};const s=a},976:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Search__container{font:var(--main-font);white-space:nowrap}.Search__label{font-weight:700;margin-bottom:7px}.Search__row{display:flex}.Search__input{border:1px solid #aaa;border-radius:4px;display:block;flex:1;padding:5px}.Search__clear{flex:0 0 auto;line-height:1;margin-left:3px;padding:5px 8px 7px}","",{version:3,sources:["webpack://./client/components/Search.css"],names:[],mappings:"AAAA,mBACE,qBAAsB,CACtB,kBACF,CAEA,eACE,eAAiB,CACjB,iBACF,CAEA,aACE,YACF,CAEA,eACE,qBAAsB,CACtB,iBAAkB,CAClB,aAAc,CACd,MAAO,CACP,WACF,CAEA,eACE,aAAc,CACd,aAAc,CACd,eAAgB,CAChB,mBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-weight: bold;\n  margin-bottom: 7px;\n}\n\n.row {\n  display: flex;\n}\n\n.input {\n  border: 1px solid #aaa;\n  border-radius: 4px;\n  display: block;\n  flex: 1;\n  padding: 5px;\n}\n\n.clear {\n  flex: 0 0 auto;\n  line-height: 1;\n  margin-left: 3px;\n  padding: 5px 8px 7px;\n}\n"],sourceRoot:""}]),a.locals={container:"Search__container",label:"Search__label",row:"Search__row",input:"Search__input",clear:"Search__clear"};const s=a},826:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Sidebar__container{background:#fff;border:none;border-right:1px solid #aaa;box-sizing:border-box;max-width:calc(50% - 10px);opacity:.95;z-index:1}.Sidebar__container:not(.Sidebar__hidden){min-width:200px}.Sidebar__container:not(.Sidebar__pinned){bottom:0;position:absolute;top:0;transition:transform .2s ease}.Sidebar__container.Sidebar__pinned{position:relative}.Sidebar__container.Sidebar__left{left:0}.Sidebar__container.Sidebar__left.Sidebar__hidden{transform:translateX(calc(-100% + 7px))}.Sidebar__content{box-sizing:border-box;height:100%;overflow-y:auto;padding:25px 20px 20px;width:100%}.Sidebar__empty.Sidebar__pinned .Sidebar__content{padding:0}.Sidebar__pinButton,.Sidebar__toggleButton{cursor:pointer;height:26px;line-height:0;position:absolute;top:10px;width:27px}.Sidebar__pinButton{right:47px}.Sidebar__toggleButton{padding-left:6px;right:15px}.Sidebar__hidden .Sidebar__toggleButton{right:-35px;transition:transform .2s ease}.Sidebar__hidden .Sidebar__toggleButton:hover{transform:translateX(4px)}.Sidebar__resizer{bottom:0;cursor:col-resize;position:absolute;right:0;top:0;width:7px}","",{version:3,sources:["webpack://./client/components/Sidebar.css"],names:[],mappings:"AAEA,oBACE,eAAgB,CAEhB,WAA4B,CAA5B,2BAA4B,CAC5B,qBAAsB,CACtB,0BAA2B,CAC3B,WAAa,CACb,SACF,CAEA,0CACE,eACF,CAEA,0CACE,QAAS,CACT,iBAAkB,CAClB,KAAM,CACN,6BACF,CAEA,oCACE,iBACF,CAEA,kCACE,MACF,CAEA,kDACE,uCACF,CAEA,kBACE,qBAAsB,CACtB,WAAY,CACZ,eAAgB,CAChB,sBAAuB,CACvB,UACF,CAEA,kDACE,SACF,CAEA,2CAEE,cAAe,CACf,WAAY,CACZ,aAAc,CACd,iBAAkB,CAClB,QAAS,CACT,UACF,CAEA,oBACE,UACF,CAEA,uBACE,gBAAiB,CACjB,UACF,CAEA,wCACE,WAAY,CACZ,6BACF,CAEA,8CACE,yBACF,CAEA,kBACE,QAAS,CACT,iBAAkB,CAClB,iBAAkB,CAClB,OAAQ,CACR,KAAM,CACN,SACF",sourcesContent:["@value toggleTime: 200ms;\n\n.container {\n  background: #fff;\n  border: none;\n  border-right: 1px solid #aaa;\n  box-sizing: border-box;\n  max-width: calc(50% - 10px);\n  opacity: 0.95;\n  z-index: 1;\n}\n\n.container:not(.hidden) {\n  min-width: 200px;\n}\n\n.container:not(.pinned) {\n  bottom: 0;\n  position: absolute;\n  top: 0;\n  transition: transform toggleTime ease;\n}\n\n.container.pinned {\n  position: relative;\n}\n\n.container.left {\n  left: 0;\n}\n\n.container.left.hidden {\n  transform: translateX(calc(-100% + 7px));\n}\n\n.content {\n  box-sizing: border-box;\n  height: 100%;\n  overflow-y: auto;\n  padding: 25px 20px 20px;\n  width: 100%;\n}\n\n.empty.pinned .content {\n  padding: 0;\n}\n\n.pinButton,\n.toggleButton {\n  cursor: pointer;\n  height: 26px;\n  line-height: 0;\n  position: absolute;\n  top: 10px;\n  width: 27px;\n}\n\n.pinButton {\n  right: 47px;\n}\n\n.toggleButton {\n  padding-left: 6px;\n  right: 15px;\n}\n\n.hidden .toggleButton {\n  right: -35px;\n  transition: transform .2s ease;\n}\n\n.hidden .toggleButton:hover {\n  transform: translateX(4px);\n}\n\n.resizer {\n  bottom: 0;\n  cursor: col-resize;\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 7px;\n}\n"],sourceRoot:""}]),a.locals={toggleTime:".2s",container:"Sidebar__container",hidden:"Sidebar__hidden",pinned:"Sidebar__pinned",left:"Sidebar__left",content:"Sidebar__content",empty:"Sidebar__empty",pinButton:"Sidebar__pinButton",toggleButton:"Sidebar__toggleButton",resizer:"Sidebar__resizer"};const s=a},897:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Switcher__container{font:var(--main-font);white-space:nowrap}.Switcher__label{font-size:11px;font-weight:700;margin-bottom:7px}.Switcher__item+.Switcher__item{margin-left:5px}","",{version:3,sources:["webpack://./client/components/Switcher.css"],names:[],mappings:"AAAA,qBACE,qBAAsB,CACtB,kBACF,CAEA,iBAEE,cAAe,CADf,eAAiB,CAEjB,iBACF,CAEA,gCACE,eACF",sourcesContent:[".container {\n  font: var(--main-font);\n  white-space: nowrap;\n}\n\n.label {\n  font-weight: bold;\n  font-size: 11px;\n  margin-bottom: 7px;\n}\n\n.item + .item {\n  margin-left: 5px;\n}\n"],sourceRoot:""}]),a.locals={container:"Switcher__container",label:"Switcher__label",item:"Switcher__item"};const s=a},527:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,".Tooltip__container{background:#fff;border:1px solid #aaa;border-radius:4px;font:var(--main-font);opacity:.9;padding:5px 10px;position:absolute;transition:opacity .2s ease,visibility .2s ease;visibility:visible;white-space:nowrap}.Tooltip__hidden{opacity:0;visibility:hidden}","",{version:3,sources:["webpack://./client/components/Tooltip.css"],names:[],mappings:"AAAA,oBAKE,eAAgB,CAChB,qBAAsB,CAFtB,iBAAkB,CAHlB,qBAAsB,CAMtB,UAAY,CAJZ,gBAAiB,CADjB,iBAAkB,CAQlB,+CAAiD,CADjD,kBAAmB,CADnB,kBAGF,CAEA,iBACE,SAAU,CACV,iBACF",sourcesContent:[".container {\n  font: var(--main-font);\n  position: absolute;\n  padding: 5px 10px;\n  border-radius: 4px;\n  background: #fff;\n  border: 1px solid #aaa;\n  opacity: 0.9;\n  white-space: nowrap;\n  visibility: visible;\n  transition: opacity .2s ease, visibility .2s ease;\n}\n\n.hidden {\n  opacity: 0;\n  visibility: hidden;\n}\n"],sourceRoot:""}]),a.locals={container:"Tooltip__container",hidden:"Tooltip__hidden"};const s=a},194:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var i=n(15),r=n.n(i),o=n(645),a=n.n(o)()(r());a.push([e.id,":root{--main-font:normal 11px Verdana,sans-serif}#app,body,html{height:100%;margin:0;overflow:hidden;padding:0;width:100%}body.resizing{-webkit-user-select:none!important;user-select:none!important}body.resizing *{pointer-events:none}body.resizing.col{cursor:col-resize!important}","",{version:3,sources:["webpack://./client/viewer.css"],names:[],mappings:"AAAA,MACE,0CACF,CAEA,eAGE,WAAY,CACZ,QAAS,CACT,eAAgB,CAChB,SAAU,CACV,UACF,CAEA,cACE,kCAA4B,CAA5B,0BACF,CAEA,gBACE,mBACF,CAEA,kBACE,2BACF",sourcesContent:[":root {\n  --main-font: normal 11px Verdana, sans-serif;\n}\n\n:global html,\n:global body,\n:global #app {\n  height: 100%;\n  margin: 0;\n  overflow: hidden;\n  padding: 0;\n  width: 100%;\n}\n\n:global body.resizing {\n  user-select: none !important;\n}\n\n:global body.resizing * {\n  pointer-events: none;\n}\n\n:global body.resizing.col {\n  cursor: col-resize !important;\n}\n"],sourceRoot:""}]);const s=a},645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,i){"string"==typeof e&&(e=[[null,e,""]]);var r={};if(i)for(var o=0;o<this.length;o++){var a=this[o][0];null!=a&&(r[a]=!0)}for(var s=0;s<e.length;s++){var u=[].concat(e[s]);i&&r[u[0]]||(n&&(u[2]?u[2]="".concat(n," and ").concat(u[2]):u[2]=n),t.push(u))}},t}},15:e=>{"use strict";function t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null==n)return;var i,r,o=[],a=!0,s=!1;try{for(n=n.call(e);!(a=(i=n.next()).done)&&(o.push(i.value),!t||o.length!==t);a=!0);}catch(u){s=!0,r=u}finally{try{a||null==n.return||n.return()}finally{if(s)throw r}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return n(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}e.exports=function(e){var n=t(e,4),i=n[1],r=n[3];if("function"==typeof btoa){var o=btoa(unescape(encodeURIComponent(JSON.stringify(r)))),a="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(o),s="/*# ".concat(a," */"),u=r.sources.map((function(e){return"/*# sourceURL=".concat(r.sourceRoot||"").concat(e," */")}));return[i].concat(u).concat([s]).join("\n")}return[i].join("\n")}},667:e=>{"use strict";e.exports=function(e,t){return t||(t={}),"string"!=typeof(e=e&&e.__esModule?e.default:e)?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e)}},296:e=>{function t(e,t,n){var i,r,o,a,s;function u(){var l=Date.now()-a;l<t&&l>=0?i=setTimeout(u,t-l):(i=null,n||(s=e.apply(o,r),o=r=null))}null==t&&(t=100);var l=function(){o=this,r=arguments,a=Date.now();var l=n&&!i;return i||(i=setTimeout(u,t)),l&&(s=e.apply(o,r),o=r=null),s};return l.clear=function(){i&&(clearTimeout(i),i=null)},l.flush=function(){i&&(s=e.apply(o,r),o=r=null,clearTimeout(i),i=null)},l}t.debounce=t,e.exports=t},150:e=>{"use strict";e.exports=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}},755:function(e){e.exports=function(){"use strict";var e=/^(b|B)$/,t={iec:{bits:["b","Kib","Mib","Gib","Tib","Pib","Eib","Zib","Yib"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["b","Kb","Mb","Gb","Tb","Pb","Eb","Zb","Yb"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},n={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]},i={floor:Math.floor,ceil:Math.ceil};function r(r){var o,a,s,u,l,c,h,f,d,p,g,b,v,m,y,C,w,_,x,A,S=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},M=[],T=0;if(isNaN(r))throw new TypeError("Invalid number");if(s=!0===S.bits,y=!0===S.unix,b=!0===S.pad,a=S.base||2,v=void 0!==S.round?S.round:y?1:2,h=void 0!==S.locale?S.locale:"",f=S.localeOptions||{},C=void 0!==S.separator?S.separator:"",w=void 0!==S.spacer?S.spacer:y?"":" ",x=S.symbols||{},_=2===a&&S.standard||"jedec",g=S.output||"string",l=!0===S.fullform,c=S.fullforms instanceof Array?S.fullforms:[],o=void 0!==S.exponent?S.exponent:-1,A=i[S.roundingMethod]||Math.round,u=a>2?1e3:1024,(d=(p=Number(r))<0)&&(p=-p),(-1===o||isNaN(o))&&(o=Math.floor(Math.log(p)/Math.log(u)))<0&&(o=0),o>8&&(o=8),"exponent"===g)return o;if(0===p)M[0]=0,m=M[1]=y?"":t[_][s?"bits":"bytes"][o];else{T=p/(2===a?Math.pow(2,10*o):Math.pow(1e3,o)),s&&(T*=8)>=u&&o<8&&(T/=u,o++);var k=Math.pow(10,o>0?v:0);M[0]=A(T*k)/k,M[0]===u&&o<8&&void 0===S.exponent&&(M[0]=1,o++),m=M[1]=10===a&&1===o?s?"kb":"kB":t[_][s?"bits":"bytes"][o],y&&(M[1]="jedec"===_?M[1].charAt(0):o>0?M[1].replace(/B$/,""):M[1],e.test(M[1])&&(M[0]=Math.floor(M[0]),M[1]=""))}if(d&&(M[0]=-M[0]),M[1]=x[M[1]]||M[1],!0===h?M[0]=M[0].toLocaleString():h.length>0?M[0]=M[0].toLocaleString(h,f):C.length>0&&(M[0]=M[0].toString().replace(".",C)),b&&!1===Number.isInteger(M[0])&&v>0){var z=C||".",D=M[0].toString().split(z),B=D[1]||"",L=B.length,E=v-L;M[0]="".concat(D[0]).concat(z).concat(B.padEnd(L+E,"0"))}return l&&(M[1]=c[o]?c[o]:n[_][o]+(s?"bit":"byte")+(1===M[0]?"":"s")),"array"===g?M:"object"===g?{value:M[0],symbol:M[1],exponent:o,unit:m}:M.join(w)}return r.partial=function(e){return function(t){return r(t,e)}},r}()},379:(e,t,n)=>{"use strict";var i,r=function(){return void 0===i&&(i=Boolean(window&&document&&document.all&&!window.atob)),i},o=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(i){n=null}e[t]=n}return e[t]}}(),a=[];function s(e){for(var t=-1,n=0;n<a.length;n++)if(a[n].identifier===e){t=n;break}return t}function u(e,t){for(var n={},i=[],r=0;r<e.length;r++){var o=e[r],u=t.base?o[0]+t.base:o[0],l=n[u]||0,c="".concat(u," ").concat(l);n[u]=l+1;var h=s(c),f={css:o[1],media:o[2],sourceMap:o[3]};-1!==h?(a[h].references++,a[h].updater(f)):a.push({identifier:c,updater:b(f,t),references:1}),i.push(c)}return i}function l(e){var t=document.createElement("style"),i=e.attributes||{};if(void 0===i.nonce){var r=n.nc;r&&(i.nonce=r)}if(Object.keys(i).forEach((function(e){t.setAttribute(e,i[e])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var c,h=(c=[],function(e,t){return c[e]=t,c.filter(Boolean).join("\n")});function f(e,t,n,i){var r=n?"":i.media?"@media ".concat(i.media," {").concat(i.css,"}"):i.css;if(e.styleSheet)e.styleSheet.cssText=h(t,r);else{var o=document.createTextNode(r),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(o,a[t]):e.appendChild(o)}}function d(e,t,n){var i=n.css,r=n.media,o=n.sourceMap;if(r?e.setAttribute("media",r):e.removeAttribute("media"),o&&"undefined"!=typeof btoa&&(i+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),e.styleSheet)e.styleSheet.cssText=i;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(i))}}var p=null,g=0;function b(e,t){var n,i,r;if(t.singleton){var o=g++;n=p||(p=l(t)),i=f.bind(null,n,o,!1),r=f.bind(null,n,o,!0)}else n=l(t),i=d.bind(null,n,t),r=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return i(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;i(e=t)}else r()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=r());var n=u(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var i=0;i<n.length;i++){var r=s(n[i]);a[r].references--}for(var o=u(e,t),l=0;l<n.length;l++){var c=s(n[l]);0===a[c].references&&(a[c].updater(),a.splice(c,1))}n=o}}}},570:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});const i="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMCAwdjExLjI1YzAgLjQxNC4zMzYuNzUuNzUuNzVoMTAuNWEuNzUuNzUgMCAwIDAgLjc1LS43NVYwSDB6IiBmaWxsPSIjRkM2IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNMCAwcy4xNTYgMyAxLjEyNSAzaDkuNzVDMTEuODQ1IDMgMTIgMCAxMiAwSDB6IiBmaWxsPSIjQ0NBMzUyIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNi43NSAxLjVoLS4zNzVMNiAyLjVsLS4zNzUtMUg1LjI1TDUuODEzIDMgNS4yNSA0LjVoLjM3NUw2IDMuNWwuMzc1IDFoLjM3NUw2LjE4NyAzeiIgZmlsbD0iIzk5N0EzRCIvPjxjaXJjbGUgY3g9Ii43NSIgY3k9Ii43NSIgcj0iMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNS4yNSAzLjc1KSIgZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PGNpcmNsZSBjeD0iLjc1IiBjeT0iLjc1IiByPSIxIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1LjI1IC43NSkiIGZpbGw9IiNGRkYiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvZz48L3N2Zz4="},752:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});const i="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSI+PHBhdGggZD0iTTExLjcgMS4zMzNINS44NUw0LjU1IDBIMS4zQy41ODUgMCAwIC42IDAgMS4zMzNWNGgxM1YyLjY2N2MwLS43MzMtLjU4NS0xLjMzNC0xLjMtMS4zMzR6IiBmaWxsPSIjRkZBMDAwIi8+PHBhdGggZD0iTTExLjcgMUgxLjNDLjU4NSAxIDAgMS41NzkgMCAyLjI4NnY2LjQyOEMwIDkuNDIxLjU4NSAxMCAxLjMgMTBoMTAuNGMuNzE1IDAgMS4zLS41NzkgMS4zLTEuMjg2VjIuMjg2QzEzIDEuNTc5IDEyLjQxNSAxIDExLjcgMXoiIGZpbGw9IiNGRkNBMjgiLz48L2c+PC9zdmc+"},868:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});const i="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjMyNy4wNjRMOC40MzMgMS45NTdhNi4wMjUgNi4wMjUgMCAwIDAtMS45NTItLjM0MkMyLjkxMiAxLjYxNS4wMTkgNS4xOTYuMDE5IDUuMTk2czEuMDk4IDEuMzU4IDIuNzc0IDIuNDAxTC45NiA5LjQzMWwuOTM2LjkzNkwxMS4yNjMgMWwtLjkzNi0uOTM2ek00LjA1IDYuMzRhMi42ODYgMi42ODYgMCAwIDEgMy41NzQtMy41NzRMNC4wNSA2LjM0em02LjQ0OC0zLjMzYTEyLjM0NCAxMi4zNDQgMCAwIDEgMi40NDQgMi4xODZzLTIuODkzIDMuNTgtNi40NjEgMy41OGMtLjUzIDAtMS4wNDQtLjA3OC0xLjUzNy0uMjEzbC43ODgtLjc4OEEyLjY4NCAyLjY4NCAwIDAgMCA5LjA2IDQuNDQ4bDEuNDM4LTEuNDM5eiIgZmlsbD0iIzIzMUYyMCIgZmlsbC1vcGFjaXR5PSIuNTk3Ii8+PC9zdmc+"},911:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});const i="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTEiIGhlaWdodD0iMTMiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNjI1IDBBMS42MyAxLjYzIDAgMCAwIDAgMS42MjV2OS43NUExLjYzIDEuNjMgMCAwIDAgMS42MjUgMTNoNy41ODNhMS42MyAxLjYzIDAgMCAwIDEuNjI1LTEuNjI1VjMuNTY3TDcuMjY2IDBIMS42MjV6bTAgMS4wODNINi41djMuMjVoMy4yNXY3LjA0MmEuNTM1LjUzNSAwIDAgMS0uNTQyLjU0MkgxLjYyNWEuNTM1LjUzNSAwIDAgMS0uNTQyLS41NDJ2LTkuNzVjMC0uMzA1LjIzNy0uNTQyLjU0Mi0uNTQyem01Ljk1OC43NjZMOC45ODQgMy4yNWgtMS40di0xLjR6TTMuMjUgNi41djEuMDgzaDQuMzMzVjYuNUgzLjI1em0wIDIuMTY3VjkuNzVINi41VjguNjY3SDMuMjV6IiBmaWxsLW9wYWNpdHk9Ii40MDMiLz48L3N2Zz4="}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={id:i,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{"use strict";var e,t,i,r,o,a={},s=[],u=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function l(e,t){for(var n in t)e[n]=t[n];return e}function c(e){var t=e.parentNode;t&&t.removeChild(e)}function h(e,t,n){var i,r,o,a=arguments,s={};for(o in t)"key"==o?i=t[o]:"ref"==o?r=t[o]:s[o]=t[o];if(arguments.length>3)for(n=[n],o=3;o<arguments.length;o++)n.push(a[o]);if(null!=n&&(s.children=n),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===s[o]&&(s[o]=e.defaultProps[o]);return f(e,s,i,r,null)}function f(t,n,i,r,o){var a={type:t,props:n,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++e.__v:o};return null!=e.vnode&&e.vnode(a),a}function d(){return{current:null}}function p(e){return e.children}function g(e,t){this.props=e,this.context=t}function b(e,t){if(null==t)return e.__?b(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?b(e):null}function v(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return v(e)}}function m(n){(!n.__d&&(n.__d=!0)&&t.push(n)&&!y.__r++||r!==e.debounceRendering)&&((r=e.debounceRendering)||i)(y)}function y(){for(var e;y.__r=t.length;)e=t.sort((function(e,t){return e.__v.__b-t.__v.__b})),t=[],e.some((function(e){var t,n,i,r,o,a;e.__d&&(o=(r=(t=e).__v).__e,(a=t.__P)&&(n=[],(i=l({},r)).__v=r.__v+1,k(a,r,i,t.__n,void 0!==a.ownerSVGElement,null!=r.__h?[o]:null,n,null==o?b(r):o,r.__h),z(n,r),r.__e!=o&&v(r)))}))}function C(e,t,n,i,r,o,u,l,c,h){var d,g,v,m,y,C,_,A=i&&i.__k||s,S=A.length;for(n.__k=[],d=0;d<t.length;d++)if(null!=(m=n.__k[d]=null==(m=t[d])||"boolean"==typeof m?null:"string"==typeof m||"number"==typeof m||"bigint"==typeof m?f(null,m,null,null,m):Array.isArray(m)?f(p,{children:m},null,null,null):m.__b>0?f(m.type,m.props,m.key,null,m.__v):m)){if(m.__=n,m.__b=n.__b+1,null===(v=A[d])||v&&m.key==v.key&&m.type===v.type)A[d]=void 0;else for(g=0;g<S;g++){if((v=A[g])&&m.key==v.key&&m.type===v.type){A[g]=void 0;break}v=null}k(e,m,v=v||a,r,o,u,l,c,h),y=m.__e,(g=m.ref)&&v.ref!=g&&(_||(_=[]),v.ref&&_.push(v.ref,null,m),_.push(g,m.__c||y,m)),null!=y?(null==C&&(C=y),"function"==typeof m.type&&null!=m.__k&&m.__k===v.__k?m.__d=c=w(m,c,e):c=x(e,m,v,A,y,c),h||"option"!==n.type?"function"==typeof n.type&&(n.__d=c):e.value=""):c&&v.__e==c&&c.parentNode!=e&&(c=b(v))}for(n.__e=C,d=S;d--;)null!=A[d]&&("function"==typeof n.type&&null!=A[d].__e&&A[d].__e==n.__d&&(n.__d=b(i,d+1)),L(A[d],A[d]));if(_)for(d=0;d<_.length;d++)B(_[d],_[++d],_[++d])}function w(e,t,n){var i,r;for(i=0;i<e.__k.length;i++)(r=e.__k[i])&&(r.__=e,t="function"==typeof r.type?w(r,t,n):x(n,r,r,e.__k,r.__e,t));return t}function _(e,t){return t=t||[],null==e||"boolean"==typeof e||(Array.isArray(e)?e.some((function(e){_(e,t)})):t.push(e)),t}function x(e,t,n,i,r,o){var a,s,u;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||r!=o||null==r.parentNode)e:if(null==o||o.parentNode!==e)e.appendChild(r),a=null;else{for(s=o,u=0;(s=s.nextSibling)&&u<i.length;u+=2)if(s==r)break e;e.insertBefore(r,o),a=o}return void 0!==a?a:r.nextSibling}function A(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||u.test(t)?n:n+"px"}function S(e,t,n,i,r){var o;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)n&&t in n||A(e.style,t,"");if(n)for(t in n)i&&n[t]===i[t]||A(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?i||e.addEventListener(t,o?T:M,o):e.removeEventListener(t,o?T:M,o);else if("dangerouslySetInnerHTML"!==t){if(r)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function M(t){this.l[t.type+!1](e.event?e.event(t):t)}function T(t){this.l[t.type+!0](e.event?e.event(t):t)}function k(t,n,i,r,o,a,s,u,c){var h,f,d,b,v,m,y,w,_,x,A,S=n.type;if(void 0!==n.constructor)return null;null!=i.__h&&(c=i.__h,u=n.__e=i.__e,n.__h=null,a=[u]),(h=e.__b)&&h(n);try{e:if("function"==typeof S){if(w=n.props,_=(h=S.contextType)&&r[h.__c],x=h?_?_.props.value:h.__:r,i.__c?y=(f=n.__c=i.__c).__=f.__E:("prototype"in S&&S.prototype.render?n.__c=f=new S(w,x):(n.__c=f=new g(w,x),f.constructor=S,f.render=E),_&&_.sub(f),f.props=w,f.state||(f.state={}),f.context=x,f.__n=r,d=f.__d=!0,f.__h=[]),null==f.__s&&(f.__s=f.state),null!=S.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=l({},f.__s)),l(f.__s,S.getDerivedStateFromProps(w,f.__s))),b=f.props,v=f.state,d)null==S.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==S.getDerivedStateFromProps&&w!==b&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(w,x),!f.__e&&null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(w,f.__s,x)||n.__v===i.__v){f.props=w,f.state=f.__s,n.__v!==i.__v&&(f.__d=!1),f.__v=n,n.__e=i.__e,n.__k=i.__k,n.__k.forEach((function(e){e&&(e.__=n)})),f.__h.length&&s.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(w,f.__s,x),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(b,v,m)}))}f.context=x,f.props=w,f.state=f.__s,(h=e.__r)&&h(n),f.__d=!1,f.__v=n,f.__P=t,h=f.render(f.props,f.state,f.context),f.state=f.__s,null!=f.getChildContext&&(r=l(l({},r),f.getChildContext())),d||null==f.getSnapshotBeforeUpdate||(m=f.getSnapshotBeforeUpdate(b,v)),A=null!=h&&h.type===p&&null==h.key?h.props.children:h,C(t,Array.isArray(A)?A:[A],n,i,r,o,a,s,u,c),f.base=n.__e,n.__h=null,f.__h.length&&s.push(f),y&&(f.__E=f.__=null),f.__e=!1}else null==a&&n.__v===i.__v?(n.__k=i.__k,n.__e=i.__e):n.__e=D(i.__e,n,i,r,o,a,s,c);(h=e.diffed)&&h(n)}catch(t){n.__v=null,(c||null!=a)&&(n.__e=u,n.__h=!!c,a[a.indexOf(u)]=null),e.__e(t,n,i)}}function z(t,n){e.__c&&e.__c(n,t),t.some((function(n){try{t=n.__h,n.__h=[],t.some((function(e){e.call(n)}))}catch(t){e.__e(t,n.__v)}}))}function D(e,t,n,i,r,o,u,l){var h,f,d,p,g=n.props,b=t.props,v=t.type,m=0;if("svg"===v&&(r=!0),null!=o)for(;m<o.length;m++)if((h=o[m])&&(h===e||(v?h.localName==v:3==h.nodeType))){e=h,o[m]=null;break}if(null==e){if(null===v)return document.createTextNode(b);e=r?document.createElementNS("http://www.w3.org/2000/svg",v):document.createElement(v,b.is&&b),o=null,l=!1}if(null===v)g===b||l&&e.data===b||(e.data=b);else{if(o=o&&s.slice.call(e.childNodes),f=(g=n.props||a).dangerouslySetInnerHTML,d=b.dangerouslySetInnerHTML,!l){if(null!=o)for(g={},p=0;p<e.attributes.length;p++)g[e.attributes[p].name]=e.attributes[p].value;(d||f)&&(d&&(f&&d.__html==f.__html||d.__html===e.innerHTML)||(e.innerHTML=d&&d.__html||""))}if(function(e,t,n,i,r){var o;for(o in n)"children"===o||"key"===o||o in t||S(e,o,null,n[o],i);for(o in t)r&&"function"!=typeof t[o]||"children"===o||"key"===o||"value"===o||"checked"===o||n[o]===t[o]||S(e,o,t[o],n[o],i)}(e,b,g,r,l),d)t.__k=[];else if(m=t.props.children,C(e,Array.isArray(m)?m:[m],t,n,i,r&&"foreignObject"!==v,o,u,e.firstChild,l),null!=o)for(m=o.length;m--;)null!=o[m]&&c(o[m]);l||("value"in b&&void 0!==(m=b.value)&&(m!==e.value||"progress"===v&&!m)&&S(e,"value",m,g.value,!1),"checked"in b&&void 0!==(m=b.checked)&&m!==e.checked&&S(e,"checked",m,g.checked,!1))}return e}function B(t,n,i){try{"function"==typeof t?t(n):t.current=n}catch(t){e.__e(t,i)}}function L(t,n,i){var r,o,a;if(e.unmount&&e.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||B(r,null,n)),i||"function"==typeof t.type||(i=null!=(o=t.__e)),t.__e=t.__d=void 0,null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){e.__e(t,n)}r.base=r.__P=null}if(r=t.__k)for(a=0;a<r.length;a++)r[a]&&L(r[a],n,i);null!=o&&c(o)}function E(e,t,n){return this.constructor(e,n)}function j(t,n,i){var r,o,u;e.__&&e.__(t,n),o=(r="function"==typeof i)?null:i&&i.__k||n.__k,u=[],k(n,t=(!r&&i||n).__k=h(p,null,[t]),o||a,a,void 0!==n.ownerSVGElement,!r&&i?[i]:o?null:n.firstChild?s.slice.call(n.childNodes):null,u,!r&&i?i:o?o.__e:n.firstChild,r),z(u,t)}function O(e,t){j(e,t,O)}function I(e,t,n){var i,r,o,a=arguments,s=l({},e.props);for(o in t)"key"==o?i=t[o]:"ref"==o?r=t[o]:s[o]=t[o];if(arguments.length>3)for(n=[n],o=3;o<arguments.length;o++)n.push(a[o]);return null!=n&&(s.children=n),f(e.type,s,i||e.key,r||e.ref,null)}function N(e,t,n,i){n&&Object.defineProperty(e,t,{enumerable:n.enumerable,configurable:n.configurable,writable:n.writable,value:n.initializer?n.initializer.call(i):void 0})}function P(e,t,n,i,r){var o={};return Object.keys(i).forEach((function(e){o[e]=i[e]})),o.enumerable=!!o.enumerable,o.configurable=!!o.configurable,("value"in o||o.initializer)&&(o.writable=!0),o=n.slice().reverse().reduce((function(n,i){return i(e,t,n)||n}),o),r&&void 0!==o.initializer&&(o.value=o.initializer?o.initializer.call(r):void 0,o.initializer=void 0),void 0===o.initializer&&(Object.defineProperty(e,t,o),o=null),o}e={__e:function(e,t){for(var n,i,r;t=t.__;)if((n=t.__c)&&!n.__)try{if((i=n.constructor)&&null!=i.getDerivedStateFromError&&(n.setState(i.getDerivedStateFromError(e)),r=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),r=n.__d),r)return n.__E=n}catch(t){e=t}throw e},__v:0},g.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=l({},this.state),"function"==typeof e&&(e=e(l({},n),this.props)),e&&l(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),m(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),m(this))},g.prototype.render=p,t=[],i="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,y.__r=0,o=0;const F=[];Object.freeze(F);const R={};function G(){return++at.mobxGuid}function H(e){throw U(!1,e),"X"}function U(e,t){if(!e)throw new Error("[mobx] "+(t||"An invariant failed, however the error is obfuscated because this is a production build."))}Object.freeze(R);function q(e){let t=!1;return function(){if(!t)return t=!0,e.apply(this,arguments)}}const V=()=>{};function W(e){return null!==e&&"object"==typeof e}function Z(e){if(null===e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return t===Object.prototype||null===t}function $(e,t,n){Object.defineProperty(e,t,{enumerable:!1,writable:!0,configurable:!0,value:n})}function K(e,t){const n="isMobX"+e;return t.prototype[n]=!0,function(e){return W(e)&&!0===e[n]}}function Y(e){return e instanceof Map}function J(e){return e instanceof Set}function X(e){const t=new Set;for(let n in e)t.add(n);return Object.getOwnPropertySymbols(e).forEach((n=>{Object.getOwnPropertyDescriptor(e,n).enumerable&&t.add(n)})),Array.from(t)}function Q(e){return e&&e.toString?e.toString():new String(e).toString()}function ee(e){return null===e?null:"object"==typeof e?""+e:e}const te="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols?e=>Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e)):Object.getOwnPropertyNames,ne=Symbol("mobx administration");class ie{constructor(e="Atom@"+G()){this.name=e,this.isPendingUnobservation=!1,this.isBeingObserved=!1,this.observers=new Set,this.diffValue=0,this.lastAccessedBy=0,this.lowestObserverState=Be.NOT_TRACKING}onBecomeObserved(){this.onBecomeObservedListeners&&this.onBecomeObservedListeners.forEach((e=>e()))}onBecomeUnobserved(){this.onBecomeUnobservedListeners&&this.onBecomeUnobservedListeners.forEach((e=>e()))}reportObserved(){return ft(this)}reportChanged(){ct(),function(e){if(e.lowestObserverState===Be.STALE)return;e.lowestObserverState=Be.STALE,e.observers.forEach((t=>{t.dependenciesState===Be.UP_TO_DATE&&(t.isTracing!==Le.NONE&&dt(t,e),t.onBecomeStale()),t.dependenciesState=Be.STALE}))}(this),ht()}toString(){return this.name}}const re=K("Atom",ie);function oe(e,t=V,n=V){const i=new ie(e);var r;return t!==V&&Bt("onBecomeObserved",i,t,r),n!==V&&Dt(i,n),i}const ae={identity:function(e,t){return e===t},structural:function(e,t){return _n(e,t)},default:function(e,t){return Object.is(e,t)},shallow:function(e,t){return _n(e,t,1)}},se=Symbol("mobx did run lazy initializers"),ue=Symbol("mobx pending decorators"),le={},ce={};function he(e,t){const n=t?le:ce;return n[e]||(n[e]={configurable:!0,enumerable:t,get(){return fe(this),this[e]},set(t){fe(this),this[e]=t}})}function fe(e){if(!0===e[se])return;const t=e[ue];if(t){$(e,se,!0);const n=[...Object.getOwnPropertySymbols(t),...Object.keys(t)];for(const i of n){const n=t[i];n.propertyCreator(e,n.prop,n.descriptor,n.decoratorTarget,n.decoratorArguments)}}}function de(e,t){return function(){let n;const i=function(i,r,o,a){if(!0===a)return t(i,r,o,i,n),null;if(!Object.prototype.hasOwnProperty.call(i,ue)){const e=i[ue];$(i,ue,Object.assign({},e))}return i[ue][r]={prop:r,propertyCreator:t,descriptor:o,decoratorTarget:i,decoratorArguments:n},he(r,e)};return pe(arguments)?(n=F,i.apply(null,arguments)):(n=Array.prototype.slice.call(arguments),i)}}function pe(e){return(2===e.length||3===e.length)&&("string"==typeof e[1]||"symbol"==typeof e[1])||4===e.length&&!0===e[3]}function ge(e,t,n){return Rt(e)?e:Array.isArray(e)?Me.array(e,{name:n}):Z(e)?Me.object(e,void 0,{name:n}):Y(e)?Me.map(e,{name:n}):J(e)?Me.set(e,{name:n}):e}function be(e){return e}function ve(e){U(e);const t=de(!0,((t,n,i,r,o)=>{const a=i?i.initializer?i.initializer.call(t):i.value:void 0;dn(t).addObservableProp(n,a,e)}));return t.enhancer=e,t}const me={deep:!0,name:void 0,defaultDecorator:void 0,proxy:!0};function ye(e){return null==e?me:"string"==typeof e?{name:e,deep:!0,proxy:!0}:e}Object.freeze(me);const Ce=ve(ge),we=ve((function(e,t,n){return null==e||mn(e)||nn(e)||sn(e)||hn(e)?e:Array.isArray(e)?Me.array(e,{name:n,deep:!1}):Z(e)?Me.object(e,void 0,{name:n,deep:!1}):Y(e)?Me.map(e,{name:n,deep:!1}):J(e)?Me.set(e,{name:n,deep:!1}):H(!1)})),_e=ve(be),xe=ve((function(e,t,n){return _n(e,t)?t:e}));function Ae(e){return e.defaultDecorator?e.defaultDecorator.enhancer:!1===e.deep?be:ge}const Se={box(e,t){arguments.length>2&&Te("box");const n=ye(t);return new Xe(e,Ae(n),n.name,!0,n.equals)},array(e,t){arguments.length>2&&Te("array");const n=ye(t);return function(e,t,n="ObservableArray@"+G(),i=!1){const r=new Qt(n,t,i);o=r.values,a=ne,s=r,Object.defineProperty(o,a,{enumerable:!1,writable:!1,configurable:!0,value:s});var o,a,s;const u=new Proxy(r.values,Xt);if(r.proxy=u,e&&e.length){const t=Ye(!0);r.spliceWithArray(0,0,e),Je(t)}return u}(e,Ae(n),n.name)},map(e,t){arguments.length>2&&Te("map");const n=ye(t);return new an(e,Ae(n),n.name)},set(e,t){arguments.length>2&&Te("set");const n=ye(t);return new cn(e,Ae(n),n.name)},object(e,t,n){"string"==typeof arguments[1]&&Te("object");const i=ye(n);if(!1===i.proxy)return Et({},e,t,i);{const n=jt(i),r=function(e){const t=new Proxy(e,Vt);return e[ne].proxy=t,t}(Et({},void 0,void 0,i));return Ot(r,e,t,n),r}},ref:_e,shallow:we,deep:Ce,struct:xe},Me=function(e,t,n){if("string"==typeof arguments[1]||"symbol"==typeof arguments[1])return Ce.apply(null,arguments);if(Rt(e))return e;const i=Z(e)?Me.object(e,t,n):Array.isArray(e)?Me.array(e,t):Y(e)?Me.map(e,t):J(e)?Me.set(e,t):e;if(i!==e)return i;H(!1)};function Te(e){H(`Expected one or two arguments to observable.${e}. Did you accidentally try to use observable.${e} as decorator?`)}Object.keys(Se).forEach((e=>Me[e]=Se[e]));const ke=de(!1,((e,t,n,i,r)=>{const{get:o,set:a}=n,s=r[0]||{};dn(e).addComputedProp(e,t,Object.assign({get:o,set:a,context:e},s))})),ze=ke({equals:ae.structural}),De=function(e,t,n){if("string"==typeof t)return ke.apply(null,arguments);if(null!==e&&"object"==typeof e&&1===arguments.length)return ke.apply(null,arguments);const i="object"==typeof t?t:{};return i.get=e,i.set="function"==typeof t?t:i.set,i.name=i.name||e.name||"",new Qe(i)};var Be,Le;De.struct=ze,function(e){e[e.NOT_TRACKING=-1]="NOT_TRACKING",e[e.UP_TO_DATE=0]="UP_TO_DATE",e[e.POSSIBLY_STALE=1]="POSSIBLY_STALE",e[e.STALE=2]="STALE"}(Be||(Be={})),function(e){e[e.NONE=0]="NONE",e[e.LOG=1]="LOG",e[e.BREAK=2]="BREAK"}(Le||(Le={}));class Ee{constructor(e){this.cause=e}}function je(e){return e instanceof Ee}function Oe(e){switch(e.dependenciesState){case Be.UP_TO_DATE:return!1;case Be.NOT_TRACKING:case Be.STALE:return!0;case Be.POSSIBLY_STALE:{const t=He(!0),n=Re(),i=e.observing,r=i.length;for(let o=0;o<r;o++){const r=i[o];if(et(r)){if(at.disableErrorBoundaries)r.get();else try{r.get()}catch(u){return Ge(n),Ue(t),!0}if(e.dependenciesState===Be.STALE)return Ge(n),Ue(t),!0}}return qe(e),Ge(n),Ue(t),!1}}}function Ie(e){const t=e.observers.size>0;at.computationDepth>0&&t&&H(!1),at.allowStateChanges||!t&&"strict"!==at.enforceActions||H(!1)}function Ne(e,t,n){const i=He(!0);qe(e),e.newObserving=new Array(e.observing.length+100),e.unboundDepsCount=0,e.runId=++at.runId;const r=at.trackingDerivation;let o;if(at.trackingDerivation=e,!0===at.disableErrorBoundaries)o=t.call(n);else try{o=t.call(n)}catch(u){o=new Ee(u)}return at.trackingDerivation=r,function(e){const t=e.observing,n=e.observing=e.newObserving;let i=Be.UP_TO_DATE,r=0,o=e.unboundDepsCount;for(let a=0;a<o;a++){const e=n[a];0===e.diffValue&&(e.diffValue=1,r!==a&&(n[r]=e),r++),e.dependenciesState>i&&(i=e.dependenciesState)}n.length=r,e.newObserving=null,o=t.length;for(;o--;){const n=t[o];0===n.diffValue&&ut(n,e),n.diffValue=0}for(;r--;){const t=n[r];1===t.diffValue&&(t.diffValue=0,st(t,e))}i!==Be.UP_TO_DATE&&(e.dependenciesState=i,e.onBecomeStale())}(e),Ue(i),o}function Pe(e){const t=e.observing;e.observing=[];let n=t.length;for(;n--;)ut(t[n],e);e.dependenciesState=Be.NOT_TRACKING}function Fe(e){const t=Re();try{return e()}finally{Ge(t)}}function Re(){const e=at.trackingDerivation;return at.trackingDerivation=null,e}function Ge(e){at.trackingDerivation=e}function He(e){const t=at.allowStateReads;return at.allowStateReads=e,t}function Ue(e){at.allowStateReads=e}function qe(e){if(e.dependenciesState===Be.UP_TO_DATE)return;e.dependenciesState=Be.UP_TO_DATE;const t=e.observing;let n=t.length;for(;n--;)t[n].lowestObserverState=Be.UP_TO_DATE}let Ve=0,We=1;const Ze=Object.getOwnPropertyDescriptor((()=>{}),"name");Ze&&Ze.configurable;function $e(e,t,n){const i=function(){return Ke(e,t,n||this,arguments)};return i.isMobxAction=!0,i}function Ke(e,t,n,i){const r=function(e,t,n){const i=!1;let r=0;0;const o=Re();ct();const a=Ye(!0),s=He(!0),u={prevDerivation:o,prevAllowStateChanges:a,prevAllowStateReads:s,notifySpy:i,startTime:r,actionId:We++,parentActionId:Ve};return Ve=u.actionId,u}();try{return t.apply(n,i)}catch(o){throw r.error=o,o}finally{!function(e){Ve!==e.actionId&&H("invalid action stack. did you forget to finish an action?");Ve=e.parentActionId,void 0!==e.error&&(at.suppressReactionErrors=!0);Je(e.prevAllowStateChanges),Ue(e.prevAllowStateReads),ht(),Ge(e.prevDerivation),e.notifySpy;at.suppressReactionErrors=!1}(r)}}function Ye(e){const t=at.allowStateChanges;return at.allowStateChanges=e,t}function Je(e){at.allowStateChanges=e}class Xe extends ie{constructor(e,t,n="ObservableValue@"+G(),i=!0,r=ae.default){super(n),this.enhancer=t,this.name=n,this.equals=r,this.hasUnreportedChange=!1,this.value=t(e,void 0,n)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}set(e){this.value;if((e=this.prepareNewValue(e))!==at.UNCHANGED){0,this.setNewValue(e)}}prepareNewValue(e){if(Ie(this),Wt(this)){const t=$t(this,{object:this,type:"update",newValue:e});if(!t)return at.UNCHANGED;e=t.newValue}return e=this.enhancer(e,this.value,this.name),this.equals(this.value,e)?at.UNCHANGED:e}setNewValue(e){const t=this.value;this.value=e,this.reportChanged(),Kt(this)&&Jt(this,{type:"update",object:this,newValue:e,oldValue:t})}get(){return this.reportObserved(),this.dehanceValue(this.value)}intercept(e){return Zt(this,e)}observe(e,t){return t&&e({object:this,type:"update",newValue:this.value,oldValue:void 0}),Yt(this,e)}toJSON(){return this.get()}toString(){return`${this.name}[${this.value}]`}valueOf(){return ee(this.get())}[Symbol.toPrimitive](){return this.valueOf()}}K("ObservableValue",Xe);class Qe{constructor(e){this.dependenciesState=Be.NOT_TRACKING,this.observing=[],this.newObserving=null,this.isBeingObserved=!1,this.isPendingUnobservation=!1,this.observers=new Set,this.diffValue=0,this.runId=0,this.lastAccessedBy=0,this.lowestObserverState=Be.UP_TO_DATE,this.unboundDepsCount=0,this.__mapid="#"+G(),this.value=new Ee(null),this.isComputing=!1,this.isRunningSetter=!1,this.isTracing=Le.NONE,U(e.get,"missing option for computed: get"),this.derivation=e.get,this.name=e.name||"ComputedValue@"+G(),e.set&&(this.setter=$e(this.name+"-setter",e.set)),this.equals=e.equals||(e.compareStructural||e.struct?ae.structural:ae.default),this.scope=e.context,this.requiresReaction=!!e.requiresReaction,this.keepAlive=!!e.keepAlive}onBecomeStale(){!function(e){if(e.lowestObserverState!==Be.UP_TO_DATE)return;e.lowestObserverState=Be.POSSIBLY_STALE,e.observers.forEach((t=>{t.dependenciesState===Be.UP_TO_DATE&&(t.dependenciesState=Be.POSSIBLY_STALE,t.isTracing!==Le.NONE&&dt(t,e),t.onBecomeStale())}))}(this)}onBecomeObserved(){this.onBecomeObservedListeners&&this.onBecomeObservedListeners.forEach((e=>e()))}onBecomeUnobserved(){this.onBecomeUnobservedListeners&&this.onBecomeUnobservedListeners.forEach((e=>e()))}get(){this.isComputing&&H(`Cycle detected in computation ${this.name}: ${this.derivation}`),0!==at.inBatch||0!==this.observers.size||this.keepAlive?(ft(this),Oe(this)&&this.trackAndCompute()&&function(e){if(e.lowestObserverState===Be.STALE)return;e.lowestObserverState=Be.STALE,e.observers.forEach((t=>{t.dependenciesState===Be.POSSIBLY_STALE?t.dependenciesState=Be.STALE:t.dependenciesState===Be.UP_TO_DATE&&(e.lowestObserverState=Be.UP_TO_DATE)}))}(this)):Oe(this)&&(this.warnAboutUntrackedRead(),ct(),this.value=this.computeValue(!1),ht());const e=this.value;if(je(e))throw e.cause;return e}peek(){const e=this.computeValue(!1);if(je(e))throw e.cause;return e}set(e){if(this.setter){U(!this.isRunningSetter,`The setter of computed value '${this.name}' is trying to update itself. Did you intend to update an _observable_ value, instead of the computed property?`),this.isRunningSetter=!0;try{this.setter.call(this.scope,e)}finally{this.isRunningSetter=!1}}else U(!1,!1)}trackAndCompute(){const e=this.value,t=this.dependenciesState===Be.NOT_TRACKING,n=this.computeValue(!0),i=t||je(e)||je(n)||!this.equals(e,n);return i&&(this.value=n),i}computeValue(e){let t;if(this.isComputing=!0,at.computationDepth++,e)t=Ne(this,this.derivation,this.scope);else if(!0===at.disableErrorBoundaries)t=this.derivation.call(this.scope);else try{t=this.derivation.call(this.scope)}catch(u){t=new Ee(u)}return at.computationDepth--,this.isComputing=!1,t}suspend(){this.keepAlive||(Pe(this),this.value=void 0)}observe(e,t){let n,i=!0;return Tt((()=>{let r=this.get();if(!i||t){const t=Re();e({type:"update",object:this,newValue:r,oldValue:n}),Ge(t)}i=!1,n=r}))}warnAboutUntrackedRead(){}toJSON(){return this.get()}toString(){return`${this.name}[${this.derivation.toString()}]`}valueOf(){return ee(this.get())}[Symbol.toPrimitive](){return this.valueOf()}}const et=K("ComputedValue",Qe);class tt{constructor(){this.version=5,this.UNCHANGED={},this.trackingDerivation=null,this.computationDepth=0,this.runId=0,this.mobxGuid=0,this.inBatch=0,this.pendingUnobservations=[],this.pendingReactions=[],this.isRunningReactions=!1,this.allowStateChanges=!0,this.allowStateReads=!0,this.enforceActions=!1,this.spyListeners=[],this.globalReactionErrorHandlers=[],this.computedRequiresReaction=!1,this.reactionRequiresObservable=!1,this.observableRequiresReaction=!1,this.computedConfigurable=!1,this.disableErrorBoundaries=!1,this.suppressReactionErrors=!1}}const nt={};function it(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:nt}let rt=!0,ot=!1,at=function(){const e=it();return e.__mobxInstanceCount>0&&!e.__mobxGlobals&&(rt=!1),e.__mobxGlobals&&e.__mobxGlobals.version!==(new tt).version&&(rt=!1),rt?e.__mobxGlobals?(e.__mobxInstanceCount+=1,e.__mobxGlobals.UNCHANGED||(e.__mobxGlobals.UNCHANGED={}),e.__mobxGlobals):(e.__mobxInstanceCount=1,e.__mobxGlobals=new tt):(setTimeout((()=>{ot||H("There are multiple, different versions of MobX active. Make sure MobX is loaded only once or use `configure({ isolateGlobalState: true })`")}),1),new tt)}();function st(e,t){e.observers.add(t),e.lowestObserverState>t.dependenciesState&&(e.lowestObserverState=t.dependenciesState)}function ut(e,t){e.observers.delete(t),0===e.observers.size&&lt(e)}function lt(e){!1===e.isPendingUnobservation&&(e.isPendingUnobservation=!0,at.pendingUnobservations.push(e))}function ct(){at.inBatch++}function ht(){if(0==--at.inBatch){vt();const e=at.pendingUnobservations;for(let t=0;t<e.length;t++){const n=e[t];n.isPendingUnobservation=!1,0===n.observers.size&&(n.isBeingObserved&&(n.isBeingObserved=!1,n.onBecomeUnobserved()),n instanceof Qe&&n.suspend())}at.pendingUnobservations=[]}}function ft(e){const t=at.trackingDerivation;return null!==t?(t.runId!==e.lastAccessedBy&&(e.lastAccessedBy=t.runId,t.newObserving[t.unboundDepsCount++]=e,e.isBeingObserved||(e.isBeingObserved=!0,e.onBecomeObserved())),!0):(0===e.observers.size&&at.inBatch>0&&lt(e),!1)}function dt(e,t){if(console.log(`[mobx.trace] '${e.name}' is invalidated due to a change in: '${t.name}'`),e.isTracing===Le.BREAK){const n=[];pt(It(e),n,1),new Function(`debugger;\n/*\nTracing '${e.name}'\n\nYou are entering this break point because derivation '${e.name}' is being traced and '${t.name}' is now forcing it to update.\nJust follow the stacktrace you should now see in the devtools to see precisely what piece of your code is causing this update\nThe stackframe you are looking for is at least ~6-8 stack-frames up.\n\n${e instanceof Qe?e.derivation.toString().replace(/[*]\//g,"/"):""}\n\nThe dependencies for this derivation are:\n\n${n.join("\n")}\n*/\n    `)()}}function pt(e,t,n){t.length>=1e3?t.push("(and many more)"):(t.push(`${new Array(n).join("\t")}${e.name}`),e.dependencies&&e.dependencies.forEach((e=>pt(e,t,n+1))))}class gt{constructor(e="Reaction@"+G(),t,n,i=!1){this.name=e,this.onInvalidate=t,this.errorHandler=n,this.requiresObservable=i,this.observing=[],this.newObserving=[],this.dependenciesState=Be.NOT_TRACKING,this.diffValue=0,this.runId=0,this.unboundDepsCount=0,this.__mapid="#"+G(),this.isDisposed=!1,this._isScheduled=!1,this._isTrackPending=!1,this._isRunning=!1,this.isTracing=Le.NONE}onBecomeStale(){this.schedule()}schedule(){this._isScheduled||(this._isScheduled=!0,at.pendingReactions.push(this),vt())}isScheduled(){return this._isScheduled}runReaction(){if(!this.isDisposed){if(ct(),this._isScheduled=!1,Oe(this)){this._isTrackPending=!0;try{this.onInvalidate(),this._isTrackPending}catch(u){this.reportExceptionInDerivation(u)}}ht()}}track(e){if(this.isDisposed)return;ct();this._isRunning=!0;const t=Ne(this,e,void 0);this._isRunning=!1,this._isTrackPending=!1,this.isDisposed&&Pe(this),je(t)&&this.reportExceptionInDerivation(t.cause),ht()}reportExceptionInDerivation(e){if(this.errorHandler)return void this.errorHandler(e,this);if(at.disableErrorBoundaries)throw e;const t=`[mobx] Encountered an uncaught exception that was thrown by a reaction or observer component, in: '${this}'`;at.suppressReactionErrors?console.warn(`[mobx] (error in reaction '${this.name}' suppressed, fix error of causing action below)`):console.error(t,e),at.globalReactionErrorHandlers.forEach((t=>t(e,this)))}dispose(){this.isDisposed||(this.isDisposed=!0,this._isRunning||(ct(),Pe(this),ht()))}getDisposer(){const e=this.dispose.bind(this);return e[ne]=this,e}toString(){return`Reaction[${this.name}]`}trace(e=!1){!function(...e){let t=!1;"boolean"==typeof e[e.length-1]&&(t=e.pop());const n=function(e){switch(e.length){case 0:return at.trackingDerivation;case 1:return yn(e[0]);case 2:return yn(e[0],e[1])}}(e);if(!n)return H(!1);n.isTracing===Le.NONE&&console.log(`[mobx.trace] '${n.name}' tracing enabled`);n.isTracing=t?Le.BREAK:Le.LOG}(this,e)}}let bt=e=>e();function vt(){at.inBatch>0||at.isRunningReactions||bt(mt)}function mt(){at.isRunningReactions=!0;const e=at.pendingReactions;let t=0;for(;e.length>0;){100==++t&&(console.error(`Reaction doesn't converge to a stable state after 100 iterations. Probably there is a cycle in the reactive function: ${e[0]}`),e.splice(0));let n=e.splice(0);for(let e=0,t=n.length;e<t;e++)n[e].runReaction()}at.isRunningReactions=!1}const yt=K("Reaction",gt);function Ct(e){const t=bt;bt=n=>e((()=>t(n)))}function wt(e){return console.warn("[mobx.spy] Is a no-op in production builds"),function(){}}function _t(){H(!1)}function xt(e){return function(t,n,i){if(i){if(i.value)return{value:$e(e,i.value),enumerable:!1,configurable:!0,writable:!0};const{initializer:t}=i;return{enumerable:!1,configurable:!0,writable:!0,initializer(){return $e(e,t.call(this))}}}return At(e).apply(this,arguments)}}function At(e){return function(t,n,i){Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get(){},set(t){$(this,n,St(e,t))}})}}const St=function(e,t,n,i){return 1===arguments.length&&"function"==typeof e?$e(e.name||"<unnamed action>",e):2===arguments.length&&"function"==typeof t?$e(e,t):1===arguments.length&&"string"==typeof e?xt(e):!0!==i?xt(t).apply(null,arguments):void $(e,t,$e(e.name||t,n.value,this))};function Mt(e,t,n){$(e,t,$e(t,n.bind(e)))}function Tt(e,t=R){const n=t&&t.name||e.name||"Autorun@"+G();let i;if(!t.scheduler&&!t.delay)i=new gt(n,(function(){this.track(r)}),t.onError,t.requiresObservable);else{const e=zt(t);let o=!1;i=new gt(n,(()=>{o||(o=!0,e((()=>{o=!1,i.isDisposed||i.track(r)})))}),t.onError,t.requiresObservable)}function r(){e(i)}return i.schedule(),i.getDisposer()}St.bound=function(e,t,n,i){return!0===i?(Mt(e,t,n.value),null):n?{configurable:!0,enumerable:!1,get(){return Mt(this,t,n.value||n.initializer.call(this)),this[t]},set:_t}:{enumerable:!1,configurable:!0,set(e){Mt(this,t,e)},get(){}}};const kt=e=>e();function zt(e){return e.scheduler?e.scheduler:e.delay?t=>setTimeout(t,e.delay):kt}function Dt(e,t,n){return Bt("onBecomeUnobserved",e,t,n)}function Bt(e,t,n,i){const r="function"==typeof i?yn(t,n):yn(t),o="function"==typeof i?i:n,a=`${e}Listeners`;r[a]?r[a].add(o):r[a]=new Set([o]);return"function"!=typeof r[e]?H(!1):function(){const e=r[a];e&&(e.delete(o),0===e.size&&delete r[a])}}function Lt(e){const{enforceActions:t,computedRequiresReaction:n,computedConfigurable:i,disableErrorBoundaries:r,reactionScheduler:o,reactionRequiresObservable:a,observableRequiresReaction:s}=e;if(!0===e.isolateGlobalState&&((at.pendingReactions.length||at.inBatch||at.isRunningReactions)&&H("isolateGlobalState should be called before MobX is running any reactions"),ot=!0,rt&&(0==--it().__mobxInstanceCount&&(it().__mobxGlobals=void 0),at=new tt)),void 0!==t){let e;switch(t){case!0:case"observed":e=!0;break;case!1:case"never":e=!1;break;case"strict":case"always":e="strict";break;default:H(`Invalid value for 'enforceActions': '${t}', expected 'never', 'always' or 'observed'`)}at.enforceActions=e,at.allowStateChanges=!0!==e&&"strict"!==e}void 0!==n&&(at.computedRequiresReaction=!!n),void 0!==a&&(at.reactionRequiresObservable=!!a),void 0!==s&&(at.observableRequiresReaction=!!s,at.allowStateReads=!at.observableRequiresReaction),void 0!==i&&(at.computedConfigurable=!!i),void 0!==r&&(!0===r&&console.warn("WARNING: Debug feature only. MobX will NOT recover from errors when `disableErrorBoundaries` is enabled."),at.disableErrorBoundaries=!!r),o&&Ct(o)}function Et(e,t,n,i){const r=jt(i=ye(i));return fe(e),dn(e,i.name,r.enhancer),t&&Ot(e,t,n,r),e}function jt(e){return e.defaultDecorator||(!1===e.deep?_e:Ce)}function Ot(e,t,n,i){ct();try{const r=te(t);for(const o of r){const r=Object.getOwnPropertyDescriptor(t,o);0;0;const a=(n&&o in n?n[o]:r.get?ke:i)(e,o,r,!0);a&&Object.defineProperty(e,o,a)}}finally{ht()}}function It(e,t){return Nt(yn(e,t))}function Nt(e){const t={name:e.name};return e.observing&&e.observing.length>0&&(t.dependencies=function(e){const t=[];return e.forEach((e=>{-1===t.indexOf(e)&&t.push(e)})),t}(e.observing).map(Nt)),t}function Pt(){this.message="FLOW_CANCELLED"}function Ft(e,t){return null!=e&&(void 0!==t?!!mn(e)&&e[ne].values.has(t):mn(e)||!!e[ne]||re(e)||yt(e)||et(e))}function Rt(e){return 1!==arguments.length&&H(!1),Ft(e)}function Gt(e,t,n){if(2!==arguments.length||hn(e))if(mn(e)){const i=e[ne];i.values.get(t)?i.write(t,n):i.addObservableProp(t,n,i.defaultEnhancer)}else if(sn(e))e.set(t,n);else if(hn(e))e.add(t);else{if(!nn(e))return H(!1);"number"!=typeof t&&(t=parseInt(t,10)),U(t>=0,`Not a valid index: '${t}'`),ct(),t>=e.length&&(e.length=t+1),e[t]=n,ht()}else{ct();const n=t;try{for(let t in n)Gt(e,t,n[t])}finally{ht()}}}Pt.prototype=Object.create(Error.prototype);function Ht(e,t){ct();try{return e.apply(t)}finally{ht()}}function Ut(e){return e[ne]}function qt(e){return"string"==typeof e||"number"==typeof e||"symbol"==typeof e}const Vt={has(e,t){if(t===ne||"constructor"===t||t===se)return!0;const n=Ut(e);return qt(t)?n.has(t):t in e},get(e,t){if(t===ne||"constructor"===t||t===se)return e[t];const n=Ut(e),i=n.values.get(t);if(i instanceof ie){const e=i.get();return void 0===e&&n.has(t),e}return qt(t)&&n.has(t),e[t]},set:(e,t,n)=>!!qt(t)&&(Gt(e,t,n),!0),deleteProperty(e,t){if(!qt(t))return!1;return Ut(e).remove(t),!0},ownKeys:e=>(Ut(e).keysAtom.reportObserved(),Reflect.ownKeys(e)),preventExtensions:e=>(H("Dynamic observable objects cannot be frozen"),!1)};function Wt(e){return void 0!==e.interceptors&&e.interceptors.length>0}function Zt(e,t){const n=e.interceptors||(e.interceptors=[]);return n.push(t),q((()=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function $t(e,t){const n=Re();try{const i=[...e.interceptors||[]];for(let e=0,n=i.length;e<n&&(t=i[e](t),U(!t||t.type,"Intercept handlers should return nothing or a change object"),t);e++);return t}finally{Ge(n)}}function Kt(e){return void 0!==e.changeListeners&&e.changeListeners.length>0}function Yt(e,t){const n=e.changeListeners||(e.changeListeners=[]);return n.push(t),q((()=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)}))}function Jt(e,t){const n=Re();let i=e.changeListeners;if(i){i=i.slice();for(let e=0,n=i.length;e<n;e++)i[e](t);Ge(n)}}const Xt={get:(e,t)=>t===ne?e[ne]:"length"===t?e[ne].getArrayLength():"number"==typeof t?en.get.call(e,t):"string"!=typeof t||isNaN(t)?en.hasOwnProperty(t)?en[t]:e[t]:en.get.call(e,parseInt(t)),set:(e,t,n)=>("length"===t&&e[ne].setArrayLength(n),"number"==typeof t&&en.set.call(e,t,n),"symbol"==typeof t||isNaN(t)?e[t]=n:en.set.call(e,parseInt(t),n),!0),preventExtensions:e=>(H("Observable arrays cannot be frozen"),!1)};class Qt{constructor(e,t,n){this.owned=n,this.values=[],this.proxy=void 0,this.lastKnownLength=0,this.atom=new ie(e||"ObservableArray@"+G()),this.enhancer=(n,i)=>t(n,i,e+"[..]")}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}dehanceValues(e){return void 0!==this.dehancer&&e.length>0?e.map(this.dehancer):e}intercept(e){return Zt(this,e)}observe(e,t=!1){return t&&e({object:this.proxy,type:"splice",index:0,added:this.values.slice(),addedCount:this.values.length,removed:[],removedCount:0}),Yt(this,e)}getArrayLength(){return this.atom.reportObserved(),this.values.length}setArrayLength(e){if("number"!=typeof e||e<0)throw new Error("[mobx.array] Out of range: "+e);let t=this.values.length;if(e!==t)if(e>t){const n=new Array(e-t);for(let i=0;i<e-t;i++)n[i]=void 0;this.spliceWithArray(t,0,n)}else this.spliceWithArray(e,t-e)}updateArrayLength(e,t){if(e!==this.lastKnownLength)throw new Error("[mobx] Modification exception: the internal structure of an observable array was changed.");this.lastKnownLength+=t}spliceWithArray(e,t,n){Ie(this.atom);const i=this.values.length;if(void 0===e?e=0:e>i?e=i:e<0&&(e=Math.max(0,i+e)),t=1===arguments.length?i-e:null==t?0:Math.max(0,Math.min(t,i-e)),void 0===n&&(n=F),Wt(this)){const i=$t(this,{object:this.proxy,type:"splice",index:e,removedCount:t,added:n});if(!i)return F;t=i.removedCount,n=i.added}n=0===n.length?n:n.map((e=>this.enhancer(e,void 0)));const r=this.spliceItemsIntoValues(e,t,n);return 0===t&&0===n.length||this.notifyArraySplice(e,n,r),this.dehanceValues(r)}spliceItemsIntoValues(e,t,n){if(n.length<1e4)return this.values.splice(e,t,...n);{const i=this.values.slice(e,e+t);return this.values=this.values.slice(0,e).concat(n,this.values.slice(e+t)),i}}notifyArrayChildUpdate(e,t,n){const i=!this.owned&&!1,r=Kt(this),o=r||i?{object:this.proxy,type:"update",index:e,newValue:t,oldValue:n}:null;this.atom.reportChanged(),r&&Jt(this,o)}notifyArraySplice(e,t,n){const i=!this.owned&&!1,r=Kt(this),o=r||i?{object:this.proxy,type:"splice",index:e,removed:n,added:t,removedCount:n.length,addedCount:t.length}:null;this.atom.reportChanged(),r&&Jt(this,o)}}const en={intercept(e){return this[ne].intercept(e)},observe(e,t=!1){return this[ne].observe(e,t)},clear(){return this.splice(0)},replace(e){const t=this[ne];return t.spliceWithArray(0,t.values.length,e)},toJS(){return this.slice()},toJSON(){return this.toJS()},splice(e,t,...n){const i=this[ne];switch(arguments.length){case 0:return[];case 1:return i.spliceWithArray(e);case 2:return i.spliceWithArray(e,t)}return i.spliceWithArray(e,t,n)},spliceWithArray(e,t,n){return this[ne].spliceWithArray(e,t,n)},push(...e){const t=this[ne];return t.spliceWithArray(t.values.length,0,e),t.values.length},pop(){return this.splice(Math.max(this[ne].values.length-1,0),1)[0]},shift(){return this.splice(0,1)[0]},unshift(...e){const t=this[ne];return t.spliceWithArray(0,0,e),t.values.length},reverse(){const e=this.slice();return e.reverse.apply(e,arguments)},sort(e){const t=this.slice();return t.sort.apply(t,arguments)},remove(e){const t=this[ne],n=t.dehanceValues(t.values).indexOf(e);return n>-1&&(this.splice(n,1),!0)},get(e){const t=this[ne];if(t){if(e<t.values.length)return t.atom.reportObserved(),t.dehanceValue(t.values[e]);console.warn(`[mobx.array] Attempt to read an array index (${e}) that is out of bounds (${t.values.length}). Please check length first. Out of bound indices will not be tracked by MobX`)}},set(e,t){const n=this[ne],i=n.values;if(e<i.length){Ie(n.atom);const r=i[e];if(Wt(n)){const i=$t(n,{type:"update",object:n.proxy,index:e,newValue:t});if(!i)return;t=i.newValue}(t=n.enhancer(t,r))!==r&&(i[e]=t,n.notifyArrayChildUpdate(e,t,r))}else{if(e!==i.length)throw new Error(`[mobx.array] Index out of bounds, ${e} is larger than ${i.length}`);n.spliceWithArray(e,0,[t])}}};["concat","flat","includes","indexOf","join","lastIndexOf","slice","toString","toLocaleString"].forEach((e=>{"function"==typeof Array.prototype[e]&&(en[e]=function(){const t=this[ne];t.atom.reportObserved();const n=t.dehanceValues(t.values);return n[e].apply(n,arguments)})})),["every","filter","find","findIndex","flatMap","forEach","map","some"].forEach((e=>{"function"==typeof Array.prototype[e]&&(en[e]=function(t,n){const i=this[ne];i.atom.reportObserved();return i.dehanceValues(i.values)[e](((e,i)=>t.call(n,e,i,this)),n)})})),["reduce","reduceRight"].forEach((e=>{en[e]=function(){const t=this[ne];t.atom.reportObserved();const n=arguments[0];return arguments[0]=(e,i,r)=>(i=t.dehanceValue(i),n(e,i,r,this)),t.values[e].apply(t.values,arguments)}}));const tn=K("ObservableArrayAdministration",Qt);function nn(e){return W(e)&&tn(e[ne])}var rn;const on={};class an{constructor(e,t=ge,n="ObservableMap@"+G()){if(this.enhancer=t,this.name=n,this[rn]=on,this._keysAtom=oe(`${this.name}.keys()`),this[Symbol.toStringTag]="Map","function"!=typeof Map)throw new Error("mobx.map requires Map polyfill for the current browser. Check babel-polyfill or core-js/es6/map.js");this._data=new Map,this._hasMap=new Map,this.merge(e)}_has(e){return this._data.has(e)}has(e){if(!at.trackingDerivation)return this._has(e);let t=this._hasMap.get(e);if(!t){const n=t=new Xe(this._has(e),be,`${this.name}.${Q(e)}?`,!1);this._hasMap.set(e,n),Dt(n,(()=>this._hasMap.delete(e)))}return t.get()}set(e,t){const n=this._has(e);if(Wt(this)){const i=$t(this,{type:n?"update":"add",object:this,newValue:t,name:e});if(!i)return this;t=i.newValue}return n?this._updateValue(e,t):this._addValue(e,t),this}delete(e){if(Ie(this._keysAtom),Wt(this)){if(!$t(this,{type:"delete",object:this,name:e}))return!1}if(this._has(e)){const t=!1,n=Kt(this),i=n||t?{type:"delete",object:this,oldValue:this._data.get(e).value,name:e}:null;return Ht((()=>{this._keysAtom.reportChanged(),this._updateHasMapEntry(e,!1);this._data.get(e).setNewValue(void 0),this._data.delete(e)})),n&&Jt(this,i),!0}return!1}_updateHasMapEntry(e,t){let n=this._hasMap.get(e);n&&n.setNewValue(t)}_updateValue(e,t){const n=this._data.get(e);if((t=n.prepareNewValue(t))!==at.UNCHANGED){const i=!1,r=Kt(this),o=r||i?{type:"update",object:this,oldValue:n.value,name:e,newValue:t}:null;0,n.setNewValue(t),r&&Jt(this,o)}}_addValue(e,t){Ie(this._keysAtom),Ht((()=>{const n=new Xe(t,this.enhancer,`${this.name}.${Q(e)}`,!1);this._data.set(e,n),t=n.value,this._updateHasMapEntry(e,!0),this._keysAtom.reportChanged()}));const n=!1,i=Kt(this);i&&Jt(this,i?{type:"add",object:this,name:e,newValue:t}:null)}get(e){return this.has(e)?this.dehanceValue(this._data.get(e).get()):this.dehanceValue(void 0)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}keys(){return this._keysAtom.reportObserved(),this._data.keys()}values(){const e=this,t=this.keys();return Mn({next(){const{done:n,value:i}=t.next();return{done:n,value:n?void 0:e.get(i)}}})}entries(){const e=this,t=this.keys();return Mn({next(){const{done:n,value:i}=t.next();return{done:n,value:n?void 0:[i,e.get(i)]}}})}[(rn=ne,Symbol.iterator)](){return this.entries()}forEach(e,t){for(const[n,i]of this)e.call(t,i,n,this)}merge(e){return sn(e)&&(e=e.toJS()),Ht((()=>{const t=Ye(!0);try{Z(e)?X(e).forEach((t=>this.set(t,e[t]))):Array.isArray(e)?e.forEach((([e,t])=>this.set(e,t))):Y(e)?(e.constructor!==Map&&H("Cannot initialize from classes that inherit from Map: "+e.constructor.name),e.forEach(((e,t)=>this.set(t,e)))):null!=e&&H("Cannot initialize map from "+e)}finally{Je(t)}})),this}clear(){Ht((()=>{Fe((()=>{for(const e of this.keys())this.delete(e)}))}))}replace(e){return Ht((()=>{const t=function(e){if(Y(e)||sn(e))return e;if(Array.isArray(e))return new Map(e);if(Z(e)){const t=new Map;for(const n in e)t.set(n,e[n]);return t}return H(`Cannot convert to map from '${e}'`)}(e),n=new Map;let i=!1;for(const e of this._data.keys())if(!t.has(e)){if(this.delete(e))i=!0;else{const t=this._data.get(e);n.set(e,t)}}for(const[e,r]of t.entries()){const t=this._data.has(e);if(this.set(e,r),this._data.has(e)){const r=this._data.get(e);n.set(e,r),t||(i=!0)}}if(!i)if(this._data.size!==n.size)this._keysAtom.reportChanged();else{const e=this._data.keys(),t=n.keys();let i=e.next(),r=t.next();for(;!i.done;){if(i.value!==r.value){this._keysAtom.reportChanged();break}i=e.next(),r=t.next()}}this._data=n})),this}get size(){return this._keysAtom.reportObserved(),this._data.size}toPOJO(){const e={};for(const[t,n]of this)e["symbol"==typeof t?t:Q(t)]=n;return e}toJS(){return new Map(this)}toJSON(){return this.toPOJO()}toString(){return this.name+"[{ "+Array.from(this.keys()).map((e=>`${Q(e)}: ${""+this.get(e)}`)).join(", ")+" }]"}observe(e,t){return Yt(this,e)}intercept(e){return Zt(this,e)}}const sn=K("ObservableMap",an);var un;const ln={};class cn{constructor(e,t=ge,n="ObservableSet@"+G()){if(this.name=n,this[un]=ln,this._data=new Set,this._atom=oe(this.name),this[Symbol.toStringTag]="Set","function"!=typeof Set)throw new Error("mobx.set requires Set polyfill for the current browser. Check babel-polyfill or core-js/es6/set.js");this.enhancer=(e,i)=>t(e,i,n),e&&this.replace(e)}dehanceValue(e){return void 0!==this.dehancer?this.dehancer(e):e}clear(){Ht((()=>{Fe((()=>{for(const e of this._data.values())this.delete(e)}))}))}forEach(e,t){for(const n of this)e.call(t,n,n,this)}get size(){return this._atom.reportObserved(),this._data.size}add(e){if(Ie(this._atom),Wt(this)){if(!$t(this,{type:"add",object:this,newValue:e}))return this}if(!this.has(e)){Ht((()=>{this._data.add(this.enhancer(e,void 0)),this._atom.reportChanged()}));const t=!1,n=Kt(this),i=n||t?{type:"add",object:this,newValue:e}:null;0,n&&Jt(this,i)}return this}delete(e){if(Wt(this)){if(!$t(this,{type:"delete",object:this,oldValue:e}))return!1}if(this.has(e)){const t=!1,n=Kt(this),i=n||t?{type:"delete",object:this,oldValue:e}:null;return Ht((()=>{this._atom.reportChanged(),this._data.delete(e)})),n&&Jt(this,i),!0}return!1}has(e){return this._atom.reportObserved(),this._data.has(this.dehanceValue(e))}entries(){let e=0;const t=Array.from(this.keys()),n=Array.from(this.values());return Mn({next(){const i=e;return e+=1,i<n.length?{value:[t[i],n[i]],done:!1}:{done:!0}}})}keys(){return this.values()}values(){this._atom.reportObserved();const e=this;let t=0;const n=Array.from(this._data.values());return Mn({next:()=>t<n.length?{value:e.dehanceValue(n[t++]),done:!1}:{done:!0}})}replace(e){return hn(e)&&(e=e.toJS()),Ht((()=>{const t=Ye(!0);try{Array.isArray(e)||J(e)?(this.clear(),e.forEach((e=>this.add(e)))):null!=e&&H("Cannot initialize set from "+e)}finally{Je(t)}})),this}observe(e,t){return Yt(this,e)}intercept(e){return Zt(this,e)}toJS(){return new Set(this)}toString(){return this.name+"[ "+Array.from(this).join(", ")+" ]"}[(un=ne,Symbol.iterator)](){return this.values()}}const hn=K("ObservableSet",cn);class fn{constructor(e,t=new Map,n,i){this.target=e,this.values=t,this.name=n,this.defaultEnhancer=i,this.keysAtom=new ie(n+".keys")}read(e){return this.values.get(e).get()}write(e,t){const n=this.target,i=this.values.get(e);if(i instanceof Qe)i.set(t);else{if(Wt(this)){const i=$t(this,{type:"update",object:this.proxy||n,name:e,newValue:t});if(!i)return;t=i.newValue}if((t=i.prepareNewValue(t))!==at.UNCHANGED){const r=Kt(this),o=!1,a=r||o?{type:"update",object:this.proxy||n,oldValue:i.value,name:e,newValue:t}:null;0,i.setNewValue(t),r&&Jt(this,a)}}}has(e){const t=this.pendingKeys||(this.pendingKeys=new Map);let n=t.get(e);if(n)return n.get();{const i=!!this.values.get(e);return n=new Xe(i,be,`${this.name}.${Q(e)}?`,!1),t.set(e,n),n.get()}}addObservableProp(e,t,n=this.defaultEnhancer){const{target:i}=this;if(Wt(this)){const n=$t(this,{object:this.proxy||i,name:e,type:"add",newValue:t});if(!n)return;t=n.newValue}const r=new Xe(t,n,`${this.name}.${Q(e)}`,!1);this.values.set(e,r),t=r.value,Object.defineProperty(i,e,function(e){return pn[e]||(pn[e]={configurable:!0,enumerable:!0,get(){return this[ne].read(e)},set(t){this[ne].write(e,t)}})}(e)),this.notifyPropertyAddition(e,t)}addComputedProp(e,t,n){const{target:i}=this;n.name=n.name||`${this.name}.${Q(t)}`,this.values.set(t,new Qe(n)),(e===i||function(e,t){const n=Object.getOwnPropertyDescriptor(e,t);return!n||!1!==n.configurable&&!1!==n.writable}(e,t))&&Object.defineProperty(e,t,function(e){return gn[e]||(gn[e]={configurable:at.computedConfigurable,enumerable:!1,get(){return bn(this).read(e)},set(t){bn(this).write(e,t)}})}(t))}remove(e){if(!this.values.has(e))return;const{target:t}=this;if(Wt(this)){if(!$t(this,{object:this.proxy||t,name:e,type:"remove"}))return}try{ct();const n=Kt(this),i=!1,r=this.values.get(e),o=r&&r.get();if(r&&r.set(void 0),this.keysAtom.reportChanged(),this.values.delete(e),this.pendingKeys){const t=this.pendingKeys.get(e);t&&t.set(!1)}delete this.target[e];const a=n||i?{type:"remove",object:this.proxy||t,oldValue:o,name:e}:null;0,n&&Jt(this,a)}finally{ht()}}illegalAccess(e,t){console.warn(`Property '${t}' of '${e}' was accessed through the prototype chain. Use 'decorate' instead to declare the prop or access it statically through it's owner`)}observe(e,t){return Yt(this,e)}intercept(e){return Zt(this,e)}notifyPropertyAddition(e,t){const n=Kt(this),i=n?{type:"add",object:this.proxy||this.target,name:e,newValue:t}:null;if(n&&Jt(this,i),this.pendingKeys){const t=this.pendingKeys.get(e);t&&t.set(!0)}this.keysAtom.reportChanged()}getKeys(){this.keysAtom.reportObserved();const e=[];for(const[t,n]of this.values)n instanceof Xe&&e.push(t);return e}}function dn(e,t="",n=ge){if(Object.prototype.hasOwnProperty.call(e,ne))return e[ne];Z(e)||(t=(e.constructor.name||"ObservableObject")+"@"+G()),t||(t="ObservableObject@"+G());const i=new fn(e,new Map,Q(t),n);return $(e,ne,i),i}const pn=Object.create(null),gn=Object.create(null);function bn(e){const t=e[ne];return t||(fe(e),e[ne])}const vn=K("ObservableObjectAdministration",fn);function mn(e){return!!W(e)&&(fe(e),vn(e[ne]))}function yn(e,t){if("object"==typeof e&&null!==e){if(nn(e))return void 0!==t&&H(!1),e[ne].atom;if(hn(e))return e[ne];if(sn(e)){const n=e;if(void 0===t)return n._keysAtom;const i=n._data.get(t)||n._hasMap.get(t);return i||H(!1),i}if(fe(e),t&&!e[ne]&&e[t],mn(e)){if(!t)return H(!1);const n=e[ne].values.get(t);return n||H(!1),n}if(re(e)||et(e)||yt(e))return e}else if("function"==typeof e&&yt(e[ne]))return e[ne];return H(!1)}function Cn(e,t){return e||H("Expecting some object"),void 0!==t?Cn(yn(e,t)):re(e)||et(e)||yt(e)||sn(e)||hn(e)?e:(fe(e),e[ne]?e[ne]:void H(!1))}const wn=Object.prototype.toString;function _n(e,t,n=-1){return xn(e,t,n)}function xn(e,t,n,i,r){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return!1;if(e!=e)return t!=t;const o=typeof e;if("function"!==o&&"object"!==o&&"object"!=typeof t)return!1;const a=wn.call(e);if(a!==wn.call(t))return!1;switch(a){case"[object RegExp]":case"[object String]":return""+e==""+t;case"[object Number]":return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object Symbol]":return"undefined"!=typeof Symbol&&Symbol.valueOf.call(e)===Symbol.valueOf.call(t);case"[object Map]":case"[object Set]":n>=0&&n++}e=An(e),t=An(t);const s="[object Array]"===a;if(!s){if("object"!=typeof e||"object"!=typeof t)return!1;const n=e.constructor,i=t.constructor;if(n!==i&&!("function"==typeof n&&n instanceof n&&"function"==typeof i&&i instanceof i)&&"constructor"in e&&"constructor"in t)return!1}if(0===n)return!1;n<0&&(n=-1),r=r||[];let u=(i=i||[]).length;for(;u--;)if(i[u]===e)return r[u]===t;if(i.push(e),r.push(t),s){if(u=e.length,u!==t.length)return!1;for(;u--;)if(!xn(e[u],t[u],n-1,i,r))return!1}else{const o=Object.keys(e);let a;if(u=o.length,Object.keys(t).length!==u)return!1;for(;u--;)if(a=o[u],!Sn(t,a)||!xn(e[a],t[a],n-1,i,r))return!1}return i.pop(),r.pop(),!0}function An(e){return nn(e)?e.slice():Y(e)||sn(e)||J(e)||hn(e)?Array.from(e.entries()):e}function Sn(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Mn(e){return e[Symbol.iterator]=Tn,e}function Tn(){return this}if("undefined"==typeof Proxy||"undefined"==typeof Symbol)throw new Error("[mobx] MobX 5+ requires Proxy and Symbol objects. If your environment doesn't support Symbol or Proxy objects, please downgrade to MobX 4. For React Native Android, consider upgrading JSCore.");function kn(e){return"number"==typeof e.parsedSize}function zn(e,t){for(const n of e){if(!1===t(n))return!1;if(n.groups&&!1===zn(n.groups,t))return!1}}"object"==typeof __MOBX_DEVTOOLS_GLOBAL_HOOK__&&__MOBX_DEVTOOLS_GLOBAL_HOOK__.injectMobx({spy:wt,extras:{getDebugName:function(e,t){let n;return n=void 0!==t?yn(e,t):mn(e)||sn(e)||hn(e)?Cn(e):yn(e),n.name}},$mobx:ne});const Dn={getItem(e){try{return JSON.parse(window.localStorage.getItem(`wba.${e}`))}catch(t){return null}},setItem(e,t){try{window.localStorage.setItem(`wba.${e}`,JSON.stringify(t))}catch(n){}},removeItem(e){try{window.localStorage.removeItem(`wba.${e}`)}catch(t){}}};var Bn,Ln,En,jn,On,In,Nn,Pn,Fn;const Rn=new(Bn=Me.ref,Ln=Me.shallow,jn=P((En=class{constructor(){this.cid=0,this.sizes=new Set(["statSize","parsedSize","gzipSize"]),N(this,"allChunks",jn,this),N(this,"selectedChunks",On,this),N(this,"searchQuery",In,this),N(this,"defaultSize",Nn,this),N(this,"selectedSize",Pn,this),N(this,"showConcatenatedModulesContent",Fn,this)}setModules(e){zn(e,(e=>{e.cid=this.cid++})),this.allChunks=e,this.selectedChunks=this.allChunks}setEntrypoints(e){this.entrypoints=e}get hasParsedSizes(){return this.allChunks.some(kn)}get activeSize(){const e=this.selectedSize||this.defaultSize;return this.hasParsedSizes&&this.sizes.has(e)?e:"statSize"}get visibleChunks(){const e=this.allChunks.filter((e=>this.selectedChunks.includes(e)));return this.filterModulesForSize(e,this.activeSize)}get allChunksSelected(){return this.visibleChunks.length===this.allChunks.length}get totalChunksSize(){return this.allChunks.reduce(((e,t)=>e+(t[this.activeSize]||0)),0)}get searchQueryRegexp(){const e=this.searchQuery.trim();if(!e)return null;try{return new RegExp(e,"iu")}catch(t){return null}}get isSearching(){return!!this.searchQueryRegexp}get foundModulesByChunk(){if(!this.isSearching)return[];const e=this.searchQueryRegexp;return this.visibleChunks.map((t=>{let n=[];zn(t.groups,(t=>{let i=0;if(e.test(t.label)?i+=3:t.path&&e.test(t.path)&&i++,!i)return;t.groups||(i+=1);(n[i-1]=n[i-1]||[]).push(t)}));const{activeSize:i}=this;return n=n.filter(Boolean).reverse(),n.forEach((e=>e.sort(((e,t)=>t[i]-e[i])))),{chunk:t,modules:[].concat(...n)}})).filter((e=>e.modules.length>0)).sort(((e,t)=>e.modules.length-t.modules.length))}get foundModules(){return this.foundModulesByChunk.reduce(((e,t)=>e.concat(t.modules)),[])}get hasFoundModules(){return this.foundModules.length>0}get hasConcatenatedModules(){let e=!1;return zn(this.visibleChunks,(t=>{if(t.concatenated)return e=!0,!1})),e}get foundModulesSize(){return this.foundModules.reduce(((e,t)=>e+t[this.activeSize]),0)}filterModulesForSize(e,t){return e.reduce(((e,n)=>{if(n[t]){if(n.groups){const e=!n.concatenated||this.showConcatenatedModulesContent;n={...n,groups:e?this.filterModulesForSize(n.groups,t):null}}n.weight=n[t],e.push(n)}return e}),[])}}).prototype,"allChunks",[Bn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),On=P(En.prototype,"selectedChunks",[Ln],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),In=P(En.prototype,"searchQuery",[Me],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return""}}),Nn=P(En.prototype,"defaultSize",[Me],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Pn=P(En.prototype,"selectedSize",[Me],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Fn=P(En.prototype,"showConcatenatedModulesContent",[Me],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0===Dn.getItem("showConcatenatedModulesContent")}}),P(En.prototype,"hasParsedSizes",[De],Object.getOwnPropertyDescriptor(En.prototype,"hasParsedSizes"),En.prototype),P(En.prototype,"activeSize",[De],Object.getOwnPropertyDescriptor(En.prototype,"activeSize"),En.prototype),P(En.prototype,"visibleChunks",[De],Object.getOwnPropertyDescriptor(En.prototype,"visibleChunks"),En.prototype),P(En.prototype,"allChunksSelected",[De],Object.getOwnPropertyDescriptor(En.prototype,"allChunksSelected"),En.prototype),P(En.prototype,"totalChunksSize",[De],Object.getOwnPropertyDescriptor(En.prototype,"totalChunksSize"),En.prototype),P(En.prototype,"searchQueryRegexp",[De],Object.getOwnPropertyDescriptor(En.prototype,"searchQueryRegexp"),En.prototype),P(En.prototype,"isSearching",[De],Object.getOwnPropertyDescriptor(En.prototype,"isSearching"),En.prototype),P(En.prototype,"foundModulesByChunk",[De],Object.getOwnPropertyDescriptor(En.prototype,"foundModulesByChunk"),En.prototype),P(En.prototype,"foundModules",[De],Object.getOwnPropertyDescriptor(En.prototype,"foundModules"),En.prototype),P(En.prototype,"hasFoundModules",[De],Object.getOwnPropertyDescriptor(En.prototype,"hasFoundModules"),En.prototype),P(En.prototype,"hasConcatenatedModules",[De],Object.getOwnPropertyDescriptor(En.prototype,"hasConcatenatedModules"),En.prototype),P(En.prototype,"foundModulesSize",[De],Object.getOwnPropertyDescriptor(En.prototype,"foundModulesSize"),En.prototype),En);var Gn,Hn,Un,qn=n(755),Vn=n.n(qn),Wn=0,Zn=[],$n=e.__b,Kn=e.__r,Yn=e.diffed,Jn=e.__c,Xn=e.unmount;function Qn(t,n){e.__h&&e.__h(Hn,t,Wn||n),Wn=0;var i=Hn.__H||(Hn.__H={__:[],__h:[]});return t>=i.__.length&&i.__.push({}),i.__[t]}function ei(e){return Wn=1,ti(ci,e)}function ti(e,t,n){var i=Qn(Gn++,2);return i.t=e,i.__c||(i.__=[n?n(t):ci(void 0,t),function(e){var t=i.t(i.__[0],e);i.__[0]!==t&&(i.__=[t,i.__[1]],i.__c.setState({}))}],i.__c=Hn),i.__}function ni(t,n){var i=Qn(Gn++,4);!e.__s&&li(i.__H,n)&&(i.__=t,i.__H=n,Hn.__h.push(i))}function ii(e,t){var n=Qn(Gn++,7);return li(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function ri(e,t){return Wn=8,ii((function(){return e}),t)}function oi(){Zn.forEach((function(n){if(n.__P)try{n.__H.__h.forEach(si),n.__H.__h.forEach(ui),n.__H.__h=[]}catch(t){n.__H.__h=[],e.__e(t,n.__v)}})),Zn=[]}e.__b=function(e){Hn=null,$n&&$n(e)},e.__r=function(e){Kn&&Kn(e),Gn=0;var t=(Hn=e.__c).__H;t&&(t.__h.forEach(si),t.__h.forEach(ui),t.__h=[])},e.diffed=function(t){Yn&&Yn(t);var n=t.__c;n&&n.__H&&n.__H.__h.length&&(1!==Zn.push(n)&&Un===e.requestAnimationFrame||((Un=e.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(i),ai&&cancelAnimationFrame(t),setTimeout(e)},i=setTimeout(n,100);ai&&(t=requestAnimationFrame(n))})(oi)),Hn=void 0},e.__c=function(t,n){n.some((function(t){try{t.__h.forEach(si),t.__h=t.__h.filter((function(e){return!e.__||ui(e)}))}catch(a){n.some((function(e){e.__h&&(e.__h=[])})),n=[],e.__e(a,t.__v)}})),Jn&&Jn(t,n)},e.unmount=function(t){Xn&&Xn(t);var n=t.__c;if(n&&n.__H)try{n.__H.__.forEach(si)}catch(t){e.__e(t,n.__v)}};var ai="function"==typeof requestAnimationFrame;function si(e){var t=Hn;"function"==typeof e.__c&&e.__c(),Hn=t}function ui(e){var t=Hn;e.__c=e.__(),Hn=t}function li(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function ci(e,t){return"function"==typeof t?t(e):t}function hi(e,t){for(var n in t)e[n]=t[n];return e}function fi(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var i in t)if("__source"!==i&&e[i]!==t[i])return!0;return!1}function di(e){this.props=e}function pi(e,t){function n(e){var n=this.props.ref,i=n==e.ref;return!i&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!i:fi(this.props,e)}function i(t){return this.shouldComponentUpdate=n,h(e,t)}return i.displayName="Memo("+(e.displayName||e.name)+")",i.prototype.isReactComponent=!0,i.__f=!0,i}(di.prototype=new g).isPureReactComponent=!0,di.prototype.shouldComponentUpdate=function(e,t){return fi(this.props,e)||fi(this.state,t)};var gi=e.__b;e.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),gi&&gi(e)};var bi="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function vi(e){function t(t,n){var i=hi({},t);return delete i.ref,e(i,(n=t.ref||n)&&("object"!=typeof n||"current"in n)?n:null)}return t.$$typeof=bi,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var mi=function(e,t){return null==e?null:_(_(e).map(t))},yi={map:mi,forEach:mi,count:function(e){return e?_(e).length:0},only:function(e){var t=_(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:_},Ci=e.__e;e.__e=function(e,t,n){if(e.then)for(var i,r=t;r=r.__;)if((i=r.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);Ci(e,t,n)};var wi=e.unmount;function _i(){this.__u=0,this.t=null,this.__b=null}function xi(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function Ai(){this.u=null,this.o=null}e.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),wi&&wi(e)},(_i.prototype=new g).__c=function(e,t){var n=t.__c,i=this;null==i.t&&(i.t=[]),i.t.push(n);var r=xi(i.__v),o=!1,a=function(){o||(o=!0,n.__R=null,r?r(s):s())};n.__R=a;var s=function(){if(!--i.__u){if(i.state.__e){var e=i.state.__e;i.__v.__k[0]=function e(t,n,i){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return e(t,n,i)})),t.__c&&t.__c.__P===n&&(t.__e&&i.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=i)),t}(e,e.__c.__P,e.__c.__O)}var t;for(i.setState({__e:i.__b=null});t=i.t.pop();)t.forceUpdate()}},u=!0===t.__h;i.__u++||u||i.setState({__e:i.__b=i.__v.__k[0]}),e.then(a,a)},_i.prototype.componentWillUnmount=function(){this.t=[]},_i.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),i=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,i){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),t.__c.__H=null),null!=(t=hi({},t)).__c&&(t.__c.__P===i&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return e(t,n,i)}))),t}(this.__b,n,i.__O=i.__P)}this.__b=null}var r=t.__e&&h(p,null,e.fallback);return r&&(r.__h=null),[h(p,null,t.__e?null:e.children),r]};var Si=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function Mi(e){return this.getChildContext=function(){return e.context},e.children}function Ti(e){var t=this,n=e.i;t.componentWillUnmount=function(){j(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),j(h(Mi,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}(Ai.prototype=new g).__e=function(e){var t=this,n=xi(t.__v),i=t.o.get(e);return i[0]++,function(r){var o=function(){t.props.revealOrder?(i.push(r),Si(t,e,i)):r()};n?n(o):o()}},Ai.prototype.render=function(e){this.u=null,this.o=new Map;var t=_(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},Ai.prototype.componentDidUpdate=Ai.prototype.componentDidMount=function(){var e=this;this.o.forEach((function(t,n){Si(e,n,t)}))};var ki="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,zi=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Di=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(e)};g.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(g.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var Bi=e.event;function Li(){}function Ei(){return this.cancelBubble}function ji(){return this.defaultPrevented}e.event=function(e){return Bi&&(e=Bi(e)),e.persist=Li,e.isPropagationStopped=Ei,e.isDefaultPrevented=ji,e.nativeEvent=e};var Oi,Ii={configurable:!0,get:function(){return this.class}},Ni=e.vnode;e.vnode=function(e){var t=e.type,n=e.props,i=n;if("string"==typeof t){for(var r in i={},n){var o=n[r];"value"===r&&"defaultValue"in n&&null==o||("defaultValue"===r&&"value"in n&&null==n.value?r="value":"download"===r&&!0===o?o="":/ondoubleclick/i.test(r)?r="ondblclick":/^onchange(textarea|input)/i.test(r+t)&&!Di(n.type)?r="oninput":/^on(Ani|Tra|Tou|BeforeInp)/.test(r)?r=r.toLowerCase():zi.test(r)?r=r.replace(/[A-Z0-9]/,"-$&").toLowerCase():null===o&&(o=void 0),i[r]=o)}"select"==t&&i.multiple&&Array.isArray(i.value)&&(i.value=_(n.children).forEach((function(e){e.props.selected=-1!=i.value.indexOf(e.props.value)}))),"select"==t&&null!=i.defaultValue&&(i.value=_(n.children).forEach((function(e){e.props.selected=i.multiple?-1!=i.defaultValue.indexOf(e.props.value):i.defaultValue==e.props.value}))),e.props=i}t&&n.class!=n.className&&(Ii.enumerable="className"in n,null!=n.className&&(i.class=n.className),Object.defineProperty(i,"className",Ii)),e.$$typeof=ki,Ni&&Ni(e)};var Pi=e.__r;e.__r=function(e){Pi&&Pi(e),Oi=e.__c};var Fi={ReactCurrentDispatcher:{current:{readContext:function(e){return Oi.__n[e.__c].props.value}}}};"object"==typeof performance&&"function"==typeof performance.now&&performance.now.bind(performance);function Ri(e){return!!e&&e.$$typeof===ki}var Gi=function(e,t){return e(t)};const Hi={useState:ei,useReducer:ti,useEffect:function(t,n){var i=Qn(Gn++,3);!e.__s&&li(i.__H,n)&&(i.__=t,i.__H=n,Hn.__H.__h.push(i))},useLayoutEffect:ni,useRef:function(e){return Wn=5,ii((function(){return{current:e}}),[])},useImperativeHandle:function(e,t,n){Wn=6,ni((function(){"function"==typeof e?e(t()):e&&(e.current=t())}),null==n?n:n.concat(e))},useMemo:ii,useCallback:ri,useContext:function(e){var t=Hn.context[e.__c],n=Qn(Gn++,9);return n.__c=e,t?(null==n.__&&(n.__=!0,t.sub(Hn)),t.props.value):e.__},useDebugValue:function(t,n){e.useDebugValue&&e.useDebugValue(n?n(t):t)},version:"16.8.0",Children:yi,render:function(e,t,n){return null==t.__k&&(t.textContent=""),j(e,t),"function"==typeof n&&n(),e?e.__c:null},hydrate:function(e,t,n){return O(e,t),"function"==typeof n&&n(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(j(null,e),!0)},createPortal:function(e,t){return h(Ti,{__v:e,i:t})},createElement:h,createContext:function(e,t){var n={__c:t="__cC"+o++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,i;return this.getChildContext||(n=[],(i={})[t]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some(m)},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n},createFactory:function(e){return h.bind(null,e)},cloneElement:function(e){return Ri(e)?I.apply(null,arguments):e},createRef:d,Fragment:p,isValidElement:Ri,findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:g,PureComponent:di,memo:pi,forwardRef:vi,unstable_batchedUpdates:Gi,StrictMode:p,Suspense:_i,SuspenseList:Ai,lazy:function(e){var t,n,i;function r(r){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){i=e})),i)throw i;if(!n)throw t;return h(n,r)}return r.displayName="Lazy",r.__f=!0,r},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Fi};if(!ei)throw new Error("mobx-react-lite requires React with Hooks support");if(!wt)throw new Error("mobx-react-lite requires mobx at least version 4 to be available");var Ui=function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var i,r,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)a.push(i.value)}catch(s){r={error:s}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return a};function qi(){var e=Ui(ei(0),2)[1];return ri((function(){e((function(e){return e+1}))}),[])}var Vi={};var Wi,Zi=(Wi="observerBatching","function"==typeof Symbol?Symbol.for(Wi):"__$mobx-react "+Wi+"__");function $i(e){e()}var Ki=!1;function Yi(){return Ki}function Ji(e){return It(e)}var Xi,Qi=1e4,er=new Set;function tr(){void 0===Xi&&(Xi=setTimeout(nr,1e4))}function nr(){Xi=void 0;var e=Date.now();er.forEach((function(t){var n=t.current;n&&e>=n.cleanAt&&(n.reaction.dispose(),t.current=null,er.delete(t))})),er.size>0&&tr()}var ir=!1,rr=[];var or={};function ar(e){return"observer"+e}function sr(e,t,n){if(void 0===t&&(t="observed"),void 0===n&&(n=or),Yi())return e();var i,r=function(e){return function(){ir?rr.push(e):e()}}((n.useForceUpdate||qi)()),o=Hi.useRef(null);if(!o.current){var a=new gt(ar(t),(function(){s.mounted?r():(a.dispose(),o.current=null)})),s=function(e){return{cleanAt:Date.now()+Qi,reaction:e}}(a);o.current=s,i=o,er.add(i),tr()}var l=o.current.reaction;return Hi.useDebugValue(l,Ji),Hi.useEffect((function(){var e;return e=o,er.delete(e),o.current?o.current.mounted=!0:(o.current={reaction:new gt(ar(t),(function(){r()})),cleanAt:1/0},r()),function(){o.current.reaction.dispose(),o.current=null}}),[]),function(e){ir=!0,rr=[];try{var t=e();ir=!1;var n=rr.length>0?rr:void 0;return Hi.useLayoutEffect((function(){n&&n.forEach((function(e){return e()}))}),[n]),t}finally{ir=!1}}((function(){var t,n;if(l.track((function(){try{t=e()}catch(u){n=u}})),n)throw n;return t}))}var ur=function(){return ur=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},ur.apply(this,arguments)};function lr(e,t){if(Yi())return e;var n,i,r,o=ur({forwardRef:!1},t),a=e.displayName||e.name,s=function(t,n){return sr((function(){return e(t,n)}),a)};return s.displayName=a,n=o.forwardRef?pi(vi(s)):pi(s),i=e,r=n,Object.keys(i).forEach((function(e){cr[e]||Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(i,e))})),n.displayName=a,n}var cr={$$typeof:!0,render:!0,compare:!0,type:!0};function hr(e){var t=e.children,n=e.render,i=t||n;return"function"!=typeof i?null:sr(i)}function fr(e,t,n,i,r){var o="children"===t?"render":"children",a="function"==typeof e[t],s="function"==typeof e[o];return a&&s?new Error("MobX Observer: Do not use children and render in the same time in`"+n):a||s?null:new Error("Invalid prop `"+r+"` of type `"+typeof e[t]+"` supplied to `"+n+"`, expected `function`.")}hr.propTypes={children:fr,render:fr},hr.displayName="Observer";!function(e){e||(e=$i),Lt({reactionScheduler:e}),("undefined"!=typeof window?window:"undefined"!=typeof self?self:Vi)[Zi]=!0}(Gi);var dr=0;var pr={};function gr(e){return pr[e]||(pr[e]=function(e){if("function"==typeof Symbol)return Symbol(e);var t="__$mobx-react "+e+" ("+dr+")";return dr++,t}(e)),pr[e]}function br(e,t){if(vr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(var r=0;r<n.length;r++)if(!Object.hasOwnProperty.call(t,n[r])||!vr(e[n[r]],t[n[r]]))return!1;return!0}function vr(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function mr(e,t,n){Object.hasOwnProperty.call(e,t)?e[t]=n:Object.defineProperty(e,t,{enumerable:!1,configurable:!0,writable:!0,value:n})}var yr=gr("patchMixins"),Cr=gr("patchedDefinition");function wr(e,t){for(var n=this,i=arguments.length,r=new Array(i>2?i-2:0),o=2;o<i;o++)r[o-2]=arguments[o];t.locks++;try{var a;return null!=e&&(a=e.apply(this,r)),a}finally{t.locks--,0===t.locks&&t.methods.forEach((function(e){e.apply(n,r)}))}}function _r(e,t){return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];wr.call.apply(wr,[this,e,t].concat(i))}}function xr(e,t,n){var i=function(e,t){var n=e[yr]=e[yr]||{},i=n[t]=n[t]||{};return i.locks=i.locks||0,i.methods=i.methods||[],i}(e,t);i.methods.indexOf(n)<0&&i.methods.push(n);var r=Object.getOwnPropertyDescriptor(e,t);if(!r||!r[Cr]){var o=e[t],a=Ar(e,t,r?r.enumerable:void 0,i,o);Object.defineProperty(e,t,a)}}function Ar(e,t,n,i,r){var o,a=_r(r,i);return(o={})[Cr]=!0,o.get=function(){return a},o.set=function(r){if(this===e)a=_r(r,i);else{var o=Ar(this,t,n,i,r);Object.defineProperty(this,t,o)}},o.configurable=!0,o.enumerable=n,o}var Sr=ne||"$mobx",Mr=gr("isMobXReactObserver"),Tr=gr("isUnmounted"),kr=gr("skipRender"),zr=gr("isForcingUpdate");function Dr(e){var t=e.prototype;if(e[Mr]){var n=Br(t);console.warn("The provided component class ("+n+") \n                has already been declared as an observer component.")}else e[Mr]=!0;if(t.componentWillReact)throw new Error("The componentWillReact life-cycle event is no longer supported");if(e.__proto__!==di)if(t.shouldComponentUpdate){if(t.shouldComponentUpdate!==Er)throw new Error("It is not allowed to use shouldComponentUpdate in observer based components.")}else t.shouldComponentUpdate=Er;jr(t,"props"),jr(t,"state");var i=t.render;return t.render=function(){return Lr.call(this,i)},xr(t,"componentWillUnmount",(function(){var e;if(!0!==Yi()&&(null===(e=this.render[Sr])||void 0===e||e.dispose(),this[Tr]=!0,!this.render[Sr])){var t=Br(this);console.warn("The reactive render of an observer class component ("+t+") \n                was overriden after MobX attached. This may result in a memory leak if the \n                overriden reactive render was not properly disposed.")}})),e}function Br(e){return e.displayName||e.name||e.constructor&&(e.constructor.displayName||e.constructor.name)||"<component>"}function Lr(e){var t=this;if(!0===Yi())return e.call(this);mr(this,kr,!1),mr(this,zr,!1);var n=Br(this),i=e.bind(this),r=!1,o=new gt(n+".render()",(function(){if(!r&&(r=!0,!0!==t[Tr])){var e=!0;try{mr(t,zr,!0),t[kr]||g.prototype.forceUpdate.call(t),e=!1}finally{mr(t,zr,!1),e&&o.dispose()}}}));function a(){r=!1;var e=void 0,t=void 0;if(o.track((function(){try{t=function(e,t){const n=Ye(e);let i;try{i=t()}finally{Je(n)}return i}(!1,i)}catch(u){e=u}})),e)throw e;return t}return o.reactComponent=this,a[Sr]=o,this.render=a,a.call(this)}function Er(e,t){return Yi()&&console.warn("[mobx-react] It seems that a re-rendering of a React component is triggered while in static (server-side) mode. Please make sure components are rendered only once server-side."),this.state!==t||!br(this.props,e)}function jr(e,t){var n=gr("reactProp_"+t+"_valueHolder"),i=gr("reactProp_"+t+"_atomHolder");function r(){return this[i]||mr(this,i,oe("reactive "+t)),this[i]}Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){var e=!1;return He&&Ue&&(e=He(!0)),r.call(this).reportObserved(),He&&Ue&&Ue(e),this[n]},set:function(e){this[zr]||br(this[n],e)?mr(this,n,e):(mr(this,n,e),mr(this,kr,!0),r.call(this).reportChanged(),mr(this,kr,!1))}})}var Or="function"==typeof Symbol&&Symbol.for,Ir=Or?Symbol.for("react.forward_ref"):vi((function(e){return null})).$$typeof,Nr=Or?Symbol.for("react.memo"):pi((function(e){return null})).$$typeof;function Pr(e){if(!0===e.isMobxInjector&&console.warn("Mobx observer: You are trying to use 'observer' on a component that already has 'inject'. Please apply 'observer' before applying 'inject'"),Nr&&e.$$typeof===Nr)throw new Error("Mobx observer: You are trying to use 'observer' on a function component wrapped in either another observer or 'React.memo'. The observer already applies 'React.memo' for you.");if(Ir&&e.$$typeof===Ir){var t=e.render;if("function"!=typeof t)throw new Error("render property of ForwardRef was not a function");return vi((function(){var e=arguments;return h(hr,null,(function(){return t.apply(void 0,e)}))}))}return"function"!=typeof e||e.prototype&&e.prototype.render||e.isReactClass||Object.prototype.isPrototypeOf.call(g,e)?Dr(e):lr(e)}if(!g)throw new Error("mobx-react requires React to be available");if(!Me)throw new Error("mobx-react requires mobx to be available");
/**
 * Carrot Search FoamTree HTML5 (demo variant)
 * v3.5.0, bugfix/3.5.x/e3b91c8e, build FOAMTREE-SOFTWARE5-DIST-3, Jan 18, 2021
 * 
 * Carrot Search confidential.
 * Copyright 2002-2021, Carrot Search s.c, All Rights Reserved.
 */
!function(){var e,t=function(){var e=window.navigator.userAgent;try{window.localStorage.setItem("ftap5caavc","ftap5caavc"),window.localStorage.removeItem("ftap5caavc");var n=!0}catch(i){n=!1}return{Se:function(){return/webkit/i.test(e)},Lh:function(){return/Mac/.test(e)},Qe:function(){return/iPad|iPod|iPhone/.test(e)},Kh:function(){return/Android/.test(e)},Gh:function(){return"ontouchstart"in window||!!window.DocumentTouch&&document instanceof window.DocumentTouch},Fh:function(){return n},Eh:function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},md:function(e,n){return[].forEach&&t.Eh()?e&&e():n&&n()}}}(),n=function(){function e(){return window.performance&&(window.performance.now||window.performance.mozNow||window.performance.msNow||window.performance.oNow||window.performance.webkitNow)||Date.now}var t=e();return{create:function(){return{now:function(){var t=e();return function(){return t.call(window.performance)}}()}},now:function(){return t.call(window.performance)}}}();function i(){function i(){if(!u)throw"AF0";var e=n.now();0!==l&&(o.sd=e-l),l=e,s=s.filter((function(e){return null!==e})),o.frames++;for(var t=0;t<s.length;t++){var i=s[t];null!==i&&(!0===i.ee.call(i.context)?s[t]=null:y.zc(i.repeat)&&(i.repeat=i.repeat-1,0>=i.repeat&&(s[t]=null)))}s=s.filter((function(e){return null!==e})),u=!1,r(),0!==(e=n.now()-e)&&(o.rd=e),o.totalTime+=e,o.ue=1e3*o.frames/o.totalTime,l=0===s.length?0:n.now()}function r(){0<s.length&&!u&&(u=!0,a(i))}var o=this.Wf={frames:0,totalTime:0,rd:0,sd:0,ue:0};e=o;var a=t.Qe()?function(e){window.setTimeout(e,0)}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(){var e=n.create();return function(t){var n=0;window.setTimeout((function(){var i=e.now();t(),n=e.now()-i}),16>n?16-n:0)}}(),s=[],u=!1,l=0;this.repeat=function(e,t,n){this.cancel(e),s.push({ee:e,context:n,repeat:t}),r()},this.once=function(e,t){this.repeat(e,1,t)},this.cancel=function(e){for(var t=0;t<s.length;t++){var n=s[t];null!==n&&n.ee===e&&(s[t]=null)}},this.i=function(){s=[]}}var r=t.md((function(){function e(){this.buffer=[],this.ma=0,this.current=y.extend({},s)}function t(e){return function(){var t,n=this.buffer,i=this.ma;for(n[i++]="call",n[i++]=e,n[i++]=arguments.length,t=0;t<arguments.length;t++)n[i++]=arguments[t];this.ma=i}}function n(e){return function(){return o[e].apply(o,arguments)}}var i=document.createElement("canvas");i.width=1,i.height=1;var o=i.getContext("2d");i=["font"];var a="fillStyle globalAlpha globalCompositeOperation lineCap lineDashOffset lineJoin lineWidth miterLimit shadowBlur shadowColor shadowOffsetX shadowOffsetY strokeStyle textAlign textBaseline".split(" "),s={};return a.concat(i).forEach((function(e){s[e]=o[e]})),e.prototype.clear=function(){this.ma=0},e.prototype.Ga=function(){return 0===this.ma},e.prototype.Na=function(e){e instanceof r?function(e,t,n){for(var i=0,r=e.ma,o=e.buffer;i<n;)o[r++]=t[i++];e.ma=r}(e,this.buffer,this.ma):function(e,t,n,i){for(var r=0;r<n;)switch(t[r++]){case"set":e[t[r++]]=t[r++];break;case"setGlobalAlpha":e[t[r++]]=t[r++]*i;break;case"call":var o=t[r++];switch(t[r++]){case 0:e[o]();break;case 1:e[o](t[r++]);break;case 2:e[o](t[r++],t[r++]);break;case 3:e[o](t[r++],t[r++],t[r++]);break;case 4:e[o](t[r++],t[r++],t[r++],t[r++]);break;case 5:e[o](t[r++],t[r++],t[r++],t[r++],t[r++]);break;case 6:e[o](t[r++],t[r++],t[r++],t[r++],t[r++],t[r++]);break;case 7:e[o](t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++]);break;case 8:e[o](t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++]);break;case 9:e[o](t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++],t[r++]);break;default:throw"CB0"}}}(e,this.buffer,this.ma,y.I(e.globalAlpha,1))},e.prototype.replay=e.prototype.Na,e.prototype.i=function(){return new e},e.prototype.scratch=e.prototype.i,"arc arcTo beginPath bezierCurveTo clearRect clip closePath drawImage fill fillRect fillText lineTo moveTo putImageData quadraticCurveTo rect rotate scale setLineDash setTransform stroke strokeRect strokeText transform translate".split(" ").forEach((function(n){e.prototype[n]=t(n)})),["measureText","createLinearGradient","createRadialGradient","createPattern","getLineDash"].forEach((function(t){e.prototype[t]=n(t)})),["save","restore"].forEach((function(i){var r=n(i),o=t(i);e.prototype[i]=function(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}(o,r)})),i.forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){o[t]=e,this.current[t]=e;var n=this.buffer;n[this.ma++]="set",n[this.ma++]=t,n[this.ma++]=e},get:function(){return this.current[t]}})})),a.forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.current[t]=e;var n=this.buffer;n[this.ma++]="globalAlpha"===t?"setGlobalAlpha":"set",n[this.ma++]=t,n[this.ma++]=e},get:function(){return this.current[t]}})})),e.prototype.roundRect=function(e,t,n,i,r){this.beginPath(),this.moveTo(e+r,t),this.lineTo(e+n-r,t),this.quadraticCurveTo(e+n,t,e+n,t+r),this.lineTo(e+n,t+i-r),this.quadraticCurveTo(e+n,t+i,e+n-r,t+i),this.lineTo(e+r,t+i),this.quadraticCurveTo(e,t+i,e,t+i-r),this.lineTo(e,t+r),this.quadraticCurveTo(e,t,e+r,t),this.closePath()},e.prototype.fillPolygonWithText=function(e,t,n,i,o){o||(o={});var a={hb:y.I(o.maxFontSize,B.ya.hb),Gc:y.I(o.minFontSize,B.ya.Gc),lineHeight:y.I(o.lineHeight,B.ya.lineHeight),cb:y.I(o.horizontalPadding,B.ya.cb),Ua:y.I(o.verticalPadding,B.ya.Ua),ib:y.I(o.maxTotalTextHeight,B.ya.ib),fontFamily:y.I(o.fontFamily,B.ya.fontFamily),fontStyle:y.I(o.fontStyle,B.ya.fontStyle),fontVariant:y.I(o.fontVariant,B.ya.fontVariant),fontWeight:y.I(o.fontWeight,B.ya.fontWeight),verticalAlign:y.I(o.verticalAlign,B.ya.verticalAlign)},s=o.cache;if(s&&y.has(o,"area")){s.Qc||(s.Qc=new r);var u=o.area,l=y.I(o.cacheInvalidationThreshold,.05);e=B.de(a,this,i,e,M.F(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1,s,u,l,o.invalidateCache)}else e=B.re(a,this,i,e,M.F(e,{}),{x:t,y:n},o.allowForcedSplit||!1,o.allowEllipsis||!1);return e.ka?{fit:!0,lineCount:e.bc,fontSize:e.fontSize,box:{x:e.box.x,y:e.box.y,w:e.box.w,h:e.box.o},ellipsis:e.Ub}:{fit:!1}},e})),o=t.md((function(){function e(e){this.S=e,this.canvas=e.canvas,this.i=[],this.zb=[void 0],this.vc=["#SIZE#px sans-serif"],this.td=[0],this.ud=[1],this.Rd=[0],this.Sd=[0],this.Td=[0],this.yd=[10],this.Xb=[10],this.Hb=[this.zb,this.vc,this.Xb,this.td,this.ud,this.Rd,this.yd,this.Sd,this.Td],this.da=[1,0,0,1,0,0]}function t(e){var t=e.S,n=e.Hb[0].length-1;e.zb[n]&&(t.setLineDash(e.zb[n]),t.lineDashOffset=e.td[n]),t.miterLimit=e.yd[n],t.lineWidth=e.ud[n],t.shadowBlur=e.Rd[n],t.shadowOffsetX=e.Sd[n],t.shadowOffsetY=e.Td[n],t.font=e.vc[n].replace("#SIZE#",e.Xb[n].toString())}function n(e,t,n){return e*n[0]+t*n[2]+n[4]}function i(e,t,n){return e*n[1]+t*n[3]+n[5]}function r(e,t){for(var n=0;n<e.length;n++)e[n]*=t[0];return e}e.prototype.save=function(){this.i.push(this.da.slice(0));for(var e=0;e<this.Hb.length;e++){var t=this.Hb[e];t.push(t[t.length-1])}this.S.save()},e.prototype.restore=function(){this.da=this.i.pop();for(var e=0;e<this.Hb.length;e++)this.Hb[e].pop();this.S.restore(),t(this)},e.prototype.scale=function(e,n){var i=this.da;i[0]*=e,i[1]*=e,i[2]*=n,i[3]*=n,e=this.da,i=(n=this.Hb)[0].length-1;var o=this.zb[i];for(o&&r(o,e),o=2;o<n.length;o++){n[o][i]*=e[0]}t(this)},e.prototype.translate=function(e,t){var n=this.da;n[4]+=n[0]*e+n[2]*t,n[5]+=n[1]*e+n[3]*t},["moveTo","lineTo"].forEach((function(t){e.prototype[t]=function(e){return function(t,r){var o=this.da;return this.S[e].call(this.S,n(t,r,o),i(t,r,o))}}(t)})),["clearRect","fillRect","strokeRect","rect"].forEach((function(t){e.prototype[t]=function(e){return function(t,r,o,a){var s=this.da;return this.S[e].call(this.S,n(t,r,s),i(t,r,s),o*s[0],a*s[3])}}(t)})),"fill stroke beginPath closePath clip createImageData createPattern getImageData putImageData getLineDash setLineDash".split(" ").forEach((function(t){e.prototype[t]=function(e){return function(){return this.S[e].apply(this.S,arguments)}}(t)})),[{p:"lineDashOffset",a:function(e){return e.td}},{p:"lineWidth",a:function(e){return e.ud}},{p:"miterLimit",a:function(e){return e.yd}},{p:"shadowBlur",a:function(e){return e.Rd}},{p:"shadowOffsetX",a:function(e){return e.Sd}},{p:"shadowOffsetY",a:function(e){return e.Td}}].forEach((function(t){Object.defineProperty(e.prototype,t.p,{set:function(e){var n=t.a(this);e*=this.da[0],n[n.length-1]=e,this.S[t.p]=e}})}));var o=/(\d+(?:\.\d+)?)px/;return Object.defineProperty(e.prototype,"font",{set:function(e){var t=o.exec(e);if(1<t.length){var n=this.Xb.length-1;this.Xb[n]=parseFloat(t[1]),this.vc[n]=e.replace(o,"#SIZE#px"),e=this.S,n=this.vc[n].replace("#SIZE#",(this.Xb[n]*this.da[0]).toString()),e.font=n}}}),"fillStyle globalAlpha globalCompositeOperation lineCap lineJoin shadowColor strokeStyle textAlign textBaseline".split(" ").forEach((function(t){Object.defineProperty(e.prototype,t,{set:function(e){this.S[t]=e}})})),e.prototype.arc=function(e,t,r,o,a,s){var u=this.da;this.S.arc(n(e,t,u),i(e,t,u),r*u[0],o,a,s)},e.prototype.arcTo=function(e,t,r,o,a){var s=this.da;this.S.arc(n(e,t,s),i(e,t,s),n(r,o,s),i(r,o,s),a*s[0])},e.prototype.bezierCurveTo=function(e,t,r,o,a,s){var u=this.da;this.S.bezierCurveTo(n(e,t,u),i(e,t,u),n(r,o,u),i(r,o,u),n(a,s,u),i(a,s,u))},e.prototype.drawImage=function(e,t,r,o,a,s,u,l,c){function h(t,r,o,a){d.push(n(t,r,f)),d.push(i(t,r,f)),o=y.V(o)?e.width:o,a=y.V(a)?e.height:a,d.push(o*f[0]),d.push(a*f[3])}var f=this.da,d=[e];y.V(s)?h(t,r,o,a):h(s,u,l,c),this.S.drawImage.apply(this.S,d)},e.prototype.quadraticCurveTo=function(e,t,r,o){var a=this.da;this.S.quadraticCurveTo(n(e,t,a),i(e,t,a),n(r,o,a),i(r,o,a))},e.prototype.fillText=function(e,t,r,o){var a=this.da;this.S.fillText(e,n(t,r,a),i(t,r,a),y.zc(o)?o*a[0]:1e20)},e.prototype.setLineDash=function(e){e=r(e.slice(0),this.da),this.zb[this.zb.length-1]=e,this.S.setLineDash(e)},e})),a=function(){var e=!t.Se()||t.Qe()||t.Kh()?1:7;return{estimate:function(){function t(e){e.beginPath(),s.Ud(e,l)}var i=document.createElement("canvas");i.width=800,i.height=600;var r=i.getContext("2d"),o=i.width;i=i.height;var a,u=0,l=[{x:0,y:100}];for(a=1;6>=a;a++)u=2*a*Math.PI/6,l.push({x:100*Math.sin(u),y:100*Math.cos(u)});a={polygonPlainFill:[t,function(e){e.fillStyle="rgb(255, 0, 0)",e.fill()}],polygonPlainStroke:[t,function(e){e.strokeStyle="rgb(128, 0, 0)",e.lineWidth=2,e.closePath(),e.stroke()}],polygonGradientFill:[t,function(e){var t=e.createRadialGradient(0,0,10,0,0,60);t.addColorStop(0,"rgb(255, 0, 0)"),t.addColorStop(1,"rgb(255, 255, 0)"),e.fillStyle=t,e.fill()}],polygonGradientStroke:[t,function(e){var t=e.createLinearGradient(-100,-100,100,100);t.addColorStop(0,"rgb(224, 0, 0)"),t.addColorStop(1,"rgb(32, 0, 0)"),e.strokeStyle=t,e.lineWidth=2,e.closePath(),e.stroke()}],polygonExposureShadow:[t,function(e){e.shadowBlur=50,e.shadowColor="rgba(0, 0, 0, 1)",e.fillStyle="rgba(0, 0, 0, 1)",e.globalCompositeOperation="source-over",e.fill(),e.shadowBlur=0,e.shadowColor="transparent",e.globalCompositeOperation="destination-out",e.fill()}],labelPlainFill:[function(e){e.fillStyle="#000",e.font="24px sans-serif",e.textAlign="center"},function(e){e.fillText("Some text",0,-16),e.fillText("for testing purposes",0,16)}]},u=100/Object.keys(a).length;var c,h=n.now(),f={};for(c in a){var d=a[c],p=n.now(),g=0;do{r.save(),r.translate(Math.random()*o,Math.random()*i);var b=3*Math.random()+.5;for(r.scale(b,b),b=0;b<d.length;b++)d[b](r);r.restore(),g++,b=n.now()}while(b-p<u);f[c]=e*(b-p)/g}return f.total=n.now()-h,f}}}(),s={Ud:function(e,t){var n=t[0];e.moveTo(n.x,n.y);for(var i=t.length-1;0<i;i--)n=t[i],e.lineTo(n.x,n.y)},Ti:function(e,t,n,i){var r,o=[],a=0,s=t.length;for(r=0;r<s;r++){var u=t[r],l=t[(r+1)%s];u=M.i(u,l),u=Math.sqrt(u),o.push(u),a+=u}n=i*(n+.5*i*a/s),a={};var c={},h={};for(r=0;r<s;r++){u=t[r],l=t[(r+1)%s],i=t[(r+2)%s];var f=o[(r+1)%s];f=Math.min(.5,n/f),M.ga(1-f,l,i,c),M.ga(f,l,i,h),0==r&&(M.ga(Math.min(.5,n/o[0]),u,l,a),e.moveTo(a.x,a.y)),e.quadraticCurveTo(l.x,l.y,c.x,c.y),e.lineTo(h.x,h.y)}return!0}};function u(e){function t(){return"embedded"===r.getAttribute("data-foamtree")}function n(e){h[e]&&(h[e].style.opacity=d*f[e])}function i(e){e.width=Math.round(a*e.B),e.height=Math.round(s*e.B)}var r,o,a,s,u,l,c=[],h={},f={},d=0;this.M=function(n){0!==(r=n).clientWidth&&0!==r.clientHeight||j.i("element has zero dimensions: "+r.clientWidth+" x "+r.clientHeight+"."),r.innerHTML="",a=r.clientWidth,s=r.clientHeight,u=0!==a?a:void 0,l=0!==s?s:void 0,t()&&j.i("visualization already embedded in the element."),r.setAttribute("data-foamtree","embedded"),(o=document.createElement("div")).style.width="100%",o.style.height="100%",o.style.position="relative",r.appendChild(o),e.j.D("stage:initialized",this,o,a,s)},this.Za=function(){t()&&(r.removeAttribute("data-foamtree"),c=[],h={},r.removeChild(o),e.j.D("stage:disposed",this,o))},this.u=function(){if(a=r.clientWidth,s=r.clientHeight,0!==a&&0!==s&&(a!==u||s!==l)){for(var t=c.length-1;0<=t;t--)i(c[t]);e.j.D("stage:resized",u,l,a,s),u=a,l=s}},this.Hi=function(e,t){e.B=t,i(e)},this.dc=function(t,r,a){var s=document.createElement("canvas");return s.setAttribute("style","position: absolute; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;"),s.B=r,i(s),c.push(s),h[t]=s,f[t]=1,n(t),a||o.appendChild(s),e.j.D("stage:newLayer",t,s),s},this.$b=function(e,t){return y.V(t)||(f[e]=t,n(e)),f[e]},this.i=function(e){return y.V(e)||(d=e,y.Aa(h,(function(e,t){n(t)}))),d}}function l(e){function t(e,t,n){return m=!0,p.x=0,p.y=0,g.x=0,g.y=0,a=f,s.x=d.x,s.y=d.y,t(),u*=e,l=n?u/a:e,l=Math.max(.25/a,l),!0}function n(e,t){return t.x=e.x/f+d.x,t.y=e.y/f+d.y,t}function i(e,t,n,i,r,o,a,s,u){var l=(e-n)*(o-s)-(t-i)*(r-a);return!(1e-5>Math.abs(l))&&(u.x=((e*i-t*n)*(r-a)-(e-n)*(r*s-o*a))/l,u.y=((e*i-t*n)*(o-s)-(t-i)*(r*s-o*a))/l,!0)}var r,o,a=1,s={x:0,y:0},u=1,l=1,c=1,h={x:0,y:0},f=1,d={x:0,y:0},p={x:0,y:0},g={x:0,y:0},b={x:0,y:0,w:0,o:0},v={x:0,y:0,w:0,o:0,scale:1},m=!0;e.j.subscribe("stage:initialized",(function(e,t,n,i){r=n,o=i,b.x=0,b.y=0,b.w=n,b.o=i,v.x=0,v.y=0,v.w=n,v.o=i,v.scale=1})),e.j.subscribe("stage:resized",(function(e,t,n,i){function a(e){e.x*=l,e.y*=c}function u(e){a(e),e.w*=l,e.o*=c}r=n,o=i;var l=n/e,c=i/t;a(s),a(d),a(h),a(p),a(g),u(b),u(v)})),this.Nb=function(e,i){return t(i,(function(){n(e,h)}),!0)},this.ga=function(e,n){if(1==Math.round(1e4*n)/1e4){n=b.x-d.x;var r=b.y-d.y;return t(1,(function(){}),!0),this.i(-n,-r)}return t(n,(function(){for(var t=!1;!t;){t=Math.random();var n=Math.random(),r=Math.random(),o=Math.random();t=i(e.x+t*e.w,e.y+n*e.o,b.x+t*b.w,b.y+n*b.o,e.x+r*e.w,e.y+o*e.o,b.x+r*b.w,b.y+o*b.o,h)}}),!0)},this.ic=function(e,n){var a=e.w/e.o,s=r/o;if(a<s){var u=e.o*s,l=e.o;a=e.x-.5*(u-e.w),s=e.y}else a>s?(u=e.w,l=e.w*o/r,a=e.x,s=e.y-.5*(l-e.o)):(a=e.x,s=e.y,u=e.w,l=e.o);return a-=u*n,u*=1+2*n,i(a,s-=l*n,d.x,d.y,a+u,s,d.x+r/f,d.y,h)?t(r/f/u,y.qa,!1):(m=!1,this.i(f*(d.x-a),f*(d.y-s)))},this.i=function(e,t){return e=Math.round(1e4*e)/1e4,t=Math.round(1e4*t)/1e4,g.x+=e/f,g.y+=t/f,0!==e||0!==t},this.reset=function(e){return e&&this.content(0,0,r,o),this.ga({x:b.x+d.x,y:b.y+d.y,w:b.w/f,o:b.o/f},c/u)},this.Fb=function(e){c=Math.min(1,Math.round(1e4*(e||u))/1e4)},this.u=function(){return d.x<b.x?(b.x-d.x)*f:d.x+r/f>b.x+b.w?-(d.x+r/f-b.x-b.w)*f:0},this.H=function(){return d.y<b.y?(b.y-d.y)*f:d.y+o/f>b.y+b.o?-(d.y+o/f-b.y-b.o)*f:0},this.update=function(e){var t=Math.abs(Math.log(l));6>t?t=2:(t/=4,t+=3*t*(1<l?e:1-e)),t=1<l?Math.pow(e,t):1-Math.pow(1-e,t),f=a*(t=(m?t:1)*(l-1)+1),d.x=h.x-(h.x-s.x)/t,d.y=h.y-(h.y-s.y)/t,d.x-=p.x*(1-e)+g.x*e,d.y-=p.y*(1-e)+g.y*e,1===e&&(p.x=g.x,p.y=g.y),v.x=d.x,v.y=d.y,v.w=r/f,v.o=o/f,v.scale=f},this.T=function(e){return e.x=v.x,e.y=v.y,e.scale=v.scale,e},this.absolute=function(e,t){return n(e,t||{})},this.Uc=function(e,t){return(t=t||{}).x=(e.x-d.x)*f,t.y=(e.y-d.y)*f,t},this.pc=function(e){return this.scale()<c/e},this.zd=function(){return y.od(f,1)},this.scale=function(){return Math.round(1e4*f)/1e4},this.content=function(e,t,n,i){b.x=e,b.y=t,b.w=n,b.o=i},this.rc=function(e,t){var n;for(n=e.length-1;0<=n;n--){var i=e[n];i.save(),i.scale(f,f),i.translate(-d.x,-d.y)}for(t(v),n=e.length-1;0<=n;n--)(i=e[n]).restore()}}var c=new function(){function e(e){if("hsl"==e.model||"hsla"==e.model)return e;var t=e.r/=255,n=e.g/=255,i=e.b/=255,r=Math.max(t,n,i),o=Math.min(t,n,i),a=(r+o)/2;if(r==o)var s=o=0;else{var u=r-o;switch(o=.5<a?u/(2-r-o):u/(r+o),r){case t:s=(n-i)/u+(n<i?6:0);break;case n:s=(i-t)/u+2;break;case i:s=(t-n)/u+4}s/=6}return e.h=360*s,e.s=100*o,e.l=100*a,e.model="hsl",e}var t={h:0,s:0,l:0,a:1,model:"hsla"};this.u=function(n){return y.Ac(n)?e(c.ga(n)):y.wb(n)?e(n):t},this.ga=function(e){var n;return(n=/rgba\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:parseFloat(n[4]),model:"rgba"}:(n=/hsla\(\s*([^,\s]+)\s*,\s*([^,%\s]+)%\s*,\s*([^,\s%]+)%\s*,\s*([^,\s]+)\s*\)/.exec(e))&&5==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:parseFloat(n[4]),model:"hsla"}:(n=/rgb\(\s*([^,\s]+)\s*,\s*([^,\s]+)\s*,\s*([^,\s]+)\s*\)/.exec(e))&&4==n.length?{r:parseFloat(n[1]),g:parseFloat(n[2]),b:parseFloat(n[3]),a:1,model:"rgb"}:(n=/hsl\(\s*([^,\s]+)\s*,\s*([^,\s%]+)%\s*,\s*([^,\s%]+)%\s*\)/.exec(e))&&4==n.length?{h:parseFloat(n[1]),s:parseFloat(n[2]),l:parseFloat(n[3]),a:1,model:"hsl"}:(n=/#([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})/.exec(e))&&4==n.length?{r:parseInt(n[1],16),g:parseInt(n[2],16),b:parseInt(n[3],16),a:1,model:"rgb"}:(n=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/.exec(e))&&4==n.length?{r:17*parseInt(n[1],16),g:17*parseInt(n[2],16),b:17*parseInt(n[3],16),a:1,model:"rgb"}:t},this.T=function(e){function t(e,t,n){return 0>n&&(n+=1),1<n&&--n,n<1/6?e+6*(t-e)*n:.5>n?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function n(e,t,n){return Math.sqrt(e*e*.241+t*t*.691+n*n*.068)/255}if("rgb"==e.model||"rgba"==e.model)return n(e.r,e.g,e.b);var i=e.l/100,r=e.s/100,o=e.h/360;if(0==e.zj)i=e=o=i;else{var a=2*i-(r=.5>i?i*(1+r):i+r-i*r);i=t(a,r,o+1/3),e=t(a,r,o),o=t(a,r,o-1/3)}return n(255*i,255*e,255*o)},this.wa=function(e){if(y.Ac(e))return e;if(!y.wb(e))return"#000";switch(e.model){case"hsla":return c.sa(e);case"hsl":return c.H(e);case"rgba":return c.ua(e);case"rgb":return c.ta(e);default:return"#000"}},this.ua=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+","+e.a+")"},this.ta=function(e){return"rgba("+(.5+e.r|0)+","+(.5+e.g|0)+","+(.5+e.b|0)+")"},this.sa=function(e){return"hsla("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%,"+e.a+")"},this.H=function(e){return"hsl("+(.5+e.h|0)+","+(.5+e.s|0)+"%,"+(.5+e.l|0)+"%)"},this.i=function(e,t,n){return"hsl("+(.5+e|0)+","+(.5+t|0)+"%,"+(.5+n|0)+"%)"}};function h(){var e,t=!1,n=[],i=this,r=new function(){this.then=function(r){return r&&(t?r.apply(i,e):n.push(r)),this},this.Fg=function(e){return i=e,{then:this.then}}};this.resolve=function(){e=arguments;for(var r=0;r<n.length;r++)n[r].apply(i,e);return t=!0,this},this.promise=function(){return r}}function f(e){var t=new h,n=e.length;if(0<e.length)for(var i=e.length-1;0<=i;i--)e[i].then((function(){0==--n&&t.resolve()}));else t.resolve();return t.promise()}function d(e){var t=0;this.i=function(){t++},this.u=function(){0===--t&&e()},this.clear=function(){t=0},this.initial=function(){return 0===t}}var p={oe:function(e,t,n,i){i=i||{};try{var r=e.getBoundingClientRect()}catch(g){if(!p.Ih){p.Ih=!0,window.console.log("getBoundingClientRect() failed."),window.console.log("Element",e);for(var o=(r=window.console).log;null!==e.parentElement;)e=e.parentElement;o.call(r,"Attached to DOM",e===document.body.parentElement)}r={left:0,top:0}}return i.x=t-r.left,i.y=n-r.top,i}};function b(){var e=document,t={};this.addEventListener=function(n,i,r){var o=t[n];o||(o=[],t[n]=o),o.push(i),e.addEventListener(n,i,r)},this.i=function(){y.Aa(t,(function(t,n){for(var i=t.length-1;0<=i;i--)e.removeEventListener(n,t[i])}))}}function v(e){function t(e){return function(t){n(t)&&e.apply(this,arguments)}}function n(t){for(t=t.target;t;){if(t===e)return!0;t=t.parentElement}return!1}function i(e,t,n){r(e,n=n||{});for(var i=0;i<t.length;i++)t[i].call(e.target,n);return r(e,n),(void 0===n.Db&&n.$h||"prevent"===n.Db)&&e.preventDefault(),n}function r(t,n){return p.oe(e,t.clientX,t.clientY,n),n.altKey=t.altKey,n.metaKey=t.metaKey,n.ctrlKey=t.ctrlKey,n.shiftKey=t.shiftKey,n.lb=3===t.which,n}var o=new b,a=[],s=[],u=[],l=[],c=[],h=[],f=[],d=[],g=[],v=[],m=[];this.i=function(e){a.push(e)},this.u=function(e){c.push(e)},this.sa=function(e){s.push(e)},this.wa=function(e){u.push(e)},this.Ka=function(e){l.push(e)},this.ua=function(e){m.push(e)},this.ta=function(e){h.push(e)},this.Ja=function(e){f.push(e)},this.ga=function(e){d.push(e)},this.H=function(e){g.push(e)},this.T=function(e){v.push(e)},this.Za=function(){o.i()};var y,C,w,_,x={x:0,y:0},A={x:0,y:0},S=!1,T=!1;o.addEventListener("mousedown",t((function(t){if(t.target!==e){var n=i(t,u);A.x=n.x,A.y=n.y,x.x=n.x,x.y=n.y,S=!0,i(t,d),C=!1,y=window.setTimeout((function(){100>M.i(x,n)&&(window.clearTimeout(_),i(t,s),C=!0)}),400)}}))),o.addEventListener("mouseup",(function(e){var t=i(e,l);S&&(T&&i(e,v),window.clearTimeout(y),C||T||!n(e)||(t={x:t.x,y:t.y},w&&100>M.i(t,w)?i(e,c):i(e,a),w=t,_=window.setTimeout((function(){w=null}),350)),T=S=!1)})),o.addEventListener("mousemove",(function(e){var t=r(e,{});n(e)&&i(e,h,{type:"move"}),x.x=t.x,x.y=t.y,S&&!T&&100<M.i(A,x)&&(T=!0),T&&i(e,g,t)})),o.addEventListener("mouseout",t((function(e){i(e,f,{type:"out"})}))),o.addEventListener("wheel",t((function(e){if("deltaY"in e)var t=e.deltaY;else t=0,"detail"in e&&(t=e.detail),"wheelDelta"in e&&(t=-e.wheelDelta/120),"wheelDeltaY"in e&&(t=-e.wheelDeltaY/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=0),t*=10;t&&e.deltaMode&&(t=1===e.deltaMode?67*t:800*t),i(e,m,{ed:-t/200,$h:!0})})),{passive:!1}),o.addEventListener("contextmenu",t((function(e){e.preventDefault()})))}var m=function(){function e(e){return function(t){return Math.pow(t,e)}}function t(e){return function(t){return 1-Math.pow(1-t,e)}}function n(e){return function(t){return 1>(t*=2)?.5*Math.pow(t,e):1-.5*Math.abs(Math.pow(2-t,e))}}function i(e){return function(t){for(var n=0;n<e.length;n++)t=(0,e[n])(t);return t}}return{ia:function(e){switch(e){case"linear":default:return m.Ab;case"bounce":return m.tg;case"squareIn":return m.Tf;case"squareOut":return m.Gb;case"squareInOut":return m.Uf;case"cubicIn":return m.wg;case"cubicOut":return m.fe;case"cubicInOut":return m.xg;case"quadIn":return m.si;case"quadOut":return m.ui;case"quadInOut":return m.ti}},Ab:function(e){return e},tg:i([n(2),function(e){return 0===e?0:1===e?1:e*(e*(e*(e*(25.9425*e-85.88)+105.78)-58.69)+13.8475)}]),Tf:e(2),Gb:t(2),Uf:n(2),wg:e(3),fe:t(3),xg:n(3),si:e(2),ui:t(2),ti:n(2),oj:i}}(),y={V:function(e){return void 0===e},Re:function(e){return null===e},zc:function(e){return"[object Number]"===Object.prototype.toString.call(e)},Ac:function(e){return"[object String]"===Object.prototype.toString.call(e)},Pe:function(e){return"function"==typeof e},wb:function(e){return e===Object(e)},od:function(e,t){return 1e-6>e-t&&-1e-6<e-t},Ne:function(e){return y.V(e)||y.Re(e)||y.Ac(e)&&!/\S/.test(e)},has:function(e,t){return e&&e.hasOwnProperty(t)},bb:function(e,t){if(e)for(var n=t.length-1;0<=n;n--)if(e.hasOwnProperty(t[n]))return!0;return!1},extend:function(e){return y.Bg(Array.prototype.slice.call(arguments,1),(function(t){if(t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})),e},xj:function(e,t){return e.map((function(e){return e[t]}),[])},Bg:function(e,t,n){null!=e&&(e.forEach?e.forEach(t,n):y.Aa(e,t,n))},Aa:function(e,t,n){for(var i in e)if(e.hasOwnProperty(i)&&!1===t.call(n,e[i],i,e))break},I:function(){for(var e=0;e<arguments.length;e++){var t=arguments[e];if(!(y.V(t)||y.zc(t)&&isNaN(t)||y.Ac(t)&&y.Ne(t)))return t}},If:function(e,t){0<=(t=e.indexOf(t))&&e.splice(t,1)},yg:function(e,t,n){var i;return function(){var r=this,o=arguments,a=n&&!i;clearTimeout(i),i=setTimeout((function(){i=null,n||e.apply(r,o)}),t),a&&e.apply(r,o)}},defer:function(e){setTimeout(e,1)},vj:function(e){return e},qa:function(){}},C=function(e,n,i){return t.Fh()?function(){var t=n+":"+JSON.stringify(arguments),r=window.localStorage.getItem(t);return r&&(r=JSON.parse(r)),r&&Date.now()-r.t<i?r.v:(r=e.apply(this,arguments),window.localStorage.setItem(t,JSON.stringify({v:r,t:Date.now()})),r)}:e},w=function(e,t){function n(){var n=[];if(Array.isArray(e))for(var i=0;i<e.length;i++){var r=e[i];r&&n.push(r.apply(t,arguments))}else e&&n.push(e.apply(t,arguments));return n}return n.empty=function(){return 0===e.length&&!y.Pe(e)},n};function _(){var e={};this.subscribe=function(t,n){var i=e[t];i||(i=[],e[t]=i),i.push(n)},this.D=function(t,n){var i=e[t];if(i)for(var r=Array.prototype.slice.call(arguments,1),o=0;o<i.length;o++)i[o].apply(this,r)}}var x=function(e){for(var t="",n=0;n<e.length;n++)t+=String.fromCharCode(1^e.charCodeAt(n));return t};function A(e){function t(t,n,u){var l,c=this,h=0;this.id=o++,this.name=u||"{unnamed on "+t+"}",this.target=function(){return t},this.xb=function(){return-1!=s.indexOf(c)},this.start=function(){if(!c.xb()){if(-1==s.indexOf(c)){var t=a.now();!0===c.af(t)&&(s=s.slice()).push(c)}0<s.length&&e.repeat(i)}return this},this.stop=function(){for(r(c);l<n.length;l++){var e=n[l];e.Xa&&e.gb.call()}return this},this.af=function(e){if(h++,0!==n.length){if(y.V(l)){var t=n[l=0];t.before&&t.before.call(t,e,h,c)}else t=n[l];for(;l<n.length;){if(t.gb&&t.gb.call(t,e,h,c))return!0;t.after&&t.after.call(t,e,h,c),y.V(l)&&(l=-1),++l<n.length&&((t=n[l]).before&&t.before.call(t,e,h,c))}}return!1}}function i(){!function(){var e=a.now();s.forEach((function(t){!0!==t.af(e)&&r(t)}))}(),0==s.length&&e.cancel(i)}function r(e){s=s.filter((function(t){return t!==e}))}var o=0,a=n.create(),s=[];this.i=function(){for(var e=s.length-1;0<=e;e--)s[e].stop();s=[]},this.K=function(){function e(){}function n(e){function t(e){return y.Pe(e)?e.call(void 0):e}var n,i,r=e.target,o=e.duration,s=e.ba;this.before=function(){for(var o in n={},e.P)r.hasOwnProperty(o)&&(n[o]={start:y.V(e.P[o].start)?r[o]:t(e.P[o].start),end:y.V(e.P[o].end)?r[o]:t(e.P[o].end),easing:y.V(e.P[o].easing)?m.Ab:e.P[o].easing});i=a.now()},this.gb=function(){var e=a.now()-i;for(var t in e=0===o?1:Math.min(o,e)/o,n){var u=n[t];r[t]=u.start+(u.end-u.start)*u.easing(e)}return s&&s.call(r,e),1>e}}function i(e,t,n){this.Xa=n,this.gb=function(){return e.call(t),!1}}function r(e){var t;this.before=function(n,i){t=i+e},this.gb=function(e,n){return n<t}}function o(e){var t;this.before=function(n){t=n+e},this.gb=function(e){return e<t}}function u(e){this.before=function(){e.forEach((function(e){e.start()}))},this.gb=function(){for(var t=0;t<e.length;t++)if(e[t].xb())return!0;return!1}}return e.A=function(e,a){return new function(){function s(t,n,r,o){return n?(y.V(r)&&(r=e),t.Mb(new i(n,r,o))):t}var l=[];this.Mb=function(e){return l.push(e),this},this.wait=function(e){return this.Mb(new o(e))},this.Xd=function(e){return this.Mb(new r(e||1))},this.call=function(e,t){return s(this,e,t,!1)},this.Xa=function(e,t){return s(this,e,t,!0)},this.fa=function(t){return y.V(t.target)&&(t.target=e),this.Mb(new n(t))},this.Qa=function(e){return this.Mb(new u(e))},this.done=function(){return new t(e,l,a)},this.start=function(){return this.done().start()},this.i=function(){var e=new h;return this.Xd().call(e.resolve).done(),e.promise()},this.Ta=function(){var e=this.i();return this.start(),e}}},e.jc=function(t){return function(e){return y.V(e)?s.slice():s.filter((function(t){return t.target()===e}))}(t).forEach((function(e){e.stop()})),e.A(t,void 0)},e}()}var S=function(){var e={ne:function(e,t){if(e.m){e=e.m;for(var n=0;n<e.length;n++)t(e[n],n)}},sc:function(t,n){if(t.m){t=t.m;for(var i=0;i<t.length;i++)if(!1===e.sc(t[i],n)||!1===n(t[i],i))return!1}}};return e.L=e.sc,e.tc=function(t,n){if(t.m){t=t.m;for(var i=0;i<t.length;i++)if(!1===n(t[i],i)||!1===e.tc(t[i],n))return!1}},e.za=function(t,n){if(t.m)for(var i=t.m,r=0;r<i.length;r++)if(!1===e.za(i[r],n))return!1;return n(t)},e.pj=e.za,e.fd=function(t,n){!1!==n(t)&&e.tc(t,n)},e.uc=function(t,n){var i=[];return e.tc(t,(function(e){i.push(e)})),n?i.filter(n):i},e.me=function(e,t){for(e=e.parent;e&&!1!==t(e);)e=e.parent},e.Jh=function(e,t){for(e=e.parent;e&&e!==t;)e=e.parent;return!!e},e}(),M=new function(){function e(e,t){var n=e.x-t.x;return n*n+(e=e.y-t.y)*e}function t(e,t,n){for(var i=0;i<e.length;i++){var r=M.T(e[i],e[i+1]||e[0],t,n,!0);if(r)return r}}return this.T=function(e,t,n,i,r){var o=e.x;e=e.y;var a=t.x-o;t=t.y-e;var s=n.x,u=n.y;n=i.x-s;var l=i.y-u;if(!(1e-12>=(i=a*l-n*t)&&-1e-12<=i)&&(n=((s-=o)*l-n*(u-=e))/i,0<=(i=(s*t-a*u)/i)&&(r||1>=i)&&0<=n&&1>=n))return{x:o+a*n,y:e+t*n}},this.Lb=function(e,t,n,i){var r=e.x;e=e.y;var o=t.x-r;t=t.y-e;var a=n.x;n=n.y;var s=i.x-a,u=o*(i=i.y-n)-s*t;if(!(1e-12>=u&&-1e-12<=u)&&(0<=(i=((a-r)*i-s*(n-e))/u)&&1>=i))return{x:r+o*i,y:e+t*i}},this.wa=function(e,n,i){var r=M.u(n,{}),o=M.u(i,{}),a=o.x-r.x,s=o.y-r.y,u=[];for(o=0;o<i.length;o++){var l=i[o];u.push({x:l.x-a,y:l.y-s})}for(i=[],l=[],o=0;o<e.length;o++){var c=e[o],h=t(n,r,c);h?(i.push(h),l.push(t(u,r,c))):(i.push(null),l.push(null))}for(o=0;o<e.length;o++)if(h=i[o],c=l[o],h&&c){n=e[o],u=r;var f=h.x-r.x;if(h=h.y-r.y,1e-12<(h=Math.sqrt(f*f+h*h))){f=n.x-r.x;var d=n.y-r.y;h=Math.sqrt(f*f+d*d)/h,n.x=u.x+h*(c.x-u.x),n.y=u.y+h*(c.y-u.y)}else n.x=u.x,n.y=u.y}for(o=0;o<e.length;o++)(l=e[o]).x+=a,l.y+=s},this.F=function(e,t){if(0!==e.length){for(var n,i,r=n=e[0].x,o=i=e[0].y,a=e.length;0<--a;)r=Math.min(r,e[a].x),n=Math.max(n,e[a].x),o=Math.min(o,e[a].y),i=Math.max(i,e[a].y);return t.x=r,t.y=o,t.w=n-r,t.o=i-o,t}},this.H=function(e){return[{x:e.x,y:e.y},{x:e.x+e.w,y:e.y},{x:e.x+e.w,y:e.y+e.o},{x:e.x,y:e.y+e.o}]},this.u=function(e,t){for(var n=0,i=0,r=e.length,o=e[0],a=0,s=1;s<r-1;s++){var u=e[s],l=e[s+1],c=o.y+u.y+l.y,h=(u.x-o.x)*(l.y-o.y)-(l.x-o.x)*(u.y-o.y);n+=h*(o.x+u.x+l.x),i+=h*c,a+=h}return t.x=n/(3*a),t.y=i/(3*a),t.ha=a/2,t},this.Ja=function(e,t){this.u(e,t),t.r=Math.sqrt(t.ha/Math.PI)},this.sa=function(e,t){for(var n=0;n<e.length;n++){var i=e[n],r=e[n+1]||e[0];if(0>(t.y-i.y)*(r.x-i.x)-(t.x-i.x)*(r.y-i.y))return!1}return!0},this.Vc=function(e,t,n){var i=e.x,r=t.x;if(e.x>t.x&&(i=t.x,r=e.x),r>n.x+n.w&&(r=n.x+n.w),i<n.x&&(i=n.x),i>r)return!1;var o=e.y,a=t.y,s=t.x-e.x;return 1e-7<Math.abs(s)&&(o=(a=(t.y-e.y)/s)*i+(e=e.y-a*e.x),a=a*r+e),o>a&&(i=a,a=o,o=i),a>n.y+n.o&&(a=n.y+n.o),o<n.y&&(o=n.y),o<=a},this.Ka=function(n,i,r,o,a){var s;function u(r,o,a){if(i.x===h.x&&i.y===h.y)return a;var u=t(n,i,h),f=Math.sqrt(e(u,i)/(r*r+o*o));return f<l?(l=f,s=u.x,c=u.y,0!==o?Math.abs(c-i.y)/Math.abs(o):Math.abs(s-i.x)/Math.abs(r)):a}o=y.I(o,.5),a=y.I(a,.5),r=y.I(r,1);var l=Number.MAX_VALUE,c=s=0,h={x:0,y:0},f=o*r;r*=1-o,o=1-a,h.x=i.x-f,h.y=i.y-a;var d=u(f,a,d);return h.x=i.x+r,h.y=i.y-a,d=u(r,a,d),h.x=i.x-f,h.y=i.y+o,d=u(f,o,d),h.x=i.x+r,h.y=i.y+o,u(r,o,d)},this.pb=function(e,t){function n(e,t,n){var i=t.x,r=n.x;t=t.y;var o=r-i,a=(n=n.y)-t;return Math.abs(a*e.x-o*e.y-i*n+r*t)/Math.sqrt(o*o+a*a)}for(var i=e.length,r=n(t,e[i-1],e[0]),o=0;o<i-1;o++){var a=n(t,e[o],e[o+1]);a<r&&(r=a)}return r},this.ua=function(e,t,n){var i;n={x:t.x+Math.cos(n),y:t.y-Math.sin(n)};var r=[],o=[],a=e.length;for(i=0;i<a;i++){var s=M.Lb(e[i],e[(i+1)%a],t,n);if(s&&(r.push(s),2==o.push(i)))break}if(2==r.length){s=r[0],r=r[1];var u=o[0];o=o[1];var l=[r,s];for(i=u+1;i<=o;i++)l.push(e[i]);for(i=[s,r];o!=u;)o=(o+1)%a,i.push(e[o]);return e=[l,i],a=n.x-t.x,i=r.x-s.x,0===a&&(a=n.y-t.y,i=r.y-s.y),(0>a?-1:0<a?1:0)!=(0>i?-1:0<i?1:0)&&e.reverse(),e}},this.ga=function(e,t,n,i){return i.x=e*(t.x-n.x)+n.x,i.y=e*(t.y-n.y)+n.y,i},this.i=e,this.ta=function(e,n,i){if(y.zc(n))var r=2*Math.PI*n/360;else switch(r=M.F(e,{}),n){case"random":r=Math.random()*Math.PI*2;break;case"top":r=Math.atan2(-r.o,0);break;case"bottom":r=Math.atan2(r.o,0);break;case"left":r=Math.atan2(0,-r.w);break;case"right":r=Math.atan2(0,r.w);break;case"topleft":r=Math.atan2(-r.o,-r.w);break;case"topright":r=Math.atan2(-r.o,r.w);break;case"bottomleft":r=Math.atan2(r.o,-r.w);break;default:r=Math.atan2(r.o,r.w)}return e=t(e,n=M.u(e,{}),{x:n.x+Math.cos(r),y:n.y+Math.sin(r)}),M.ga(i,e,n,{})},this},T=new function(){function e(e,t){this.face=e,this.Rc=t,this.ec=this.Lc=null}function t(e,t,n){this.la=[e,t,n],this.J=Array(3);var i=t.y-e.y,r=n.z-e.z,o=t.x-e.x;t=t.z-e.z;var a=n.x-e.x;e=n.y-e.y,this.Ha={x:i*r-t*e,y:t*a-o*r,z:o*e-i*a},this.Ya=[],this.ad=this.visible=!1}this.i=function(i){function o(t,n,i){var r=t.la[0],o=t.Ha,a=o.x,l=o.y;o=o.z;var c=Array(u),h=(n=n.Ya).length;for(s=0;s<h;s++){var f=n[s].Rc;c[f.index]=!0,0>a*(f.x-r.x)+l*(f.y-r.y)+o*(f.z-r.z)&&e.add(t,f)}for(h=(n=i.Ya).length,s=0;s<h;s++)!0!==c[(f=n[s].Rc).index]&&0>a*(f.x-r.x)+l*(f.y-r.y)+o*(f.z-r.z)&&e.add(t,f)}var a,s,u=i.length;for(a=0;a<u;a++)i[a].index=a,i[a].Pb=null;var l,c=[];if(0<(l=function(){function n(e,n,i,r){var o={x:n.x-e.x,y:n.y-e.y,z:n.z-e.z},a=i.x-e.x,s=i.y-e.y,u=i.z-e.z,l=o.y*u-o.z*s,c=o.z*a-o.x*u;return o=o.x*s-o.y*a,l*r.x+c*r.y+o*r.z>l*e.x+c*e.y+o*e.z?new t(e,n,i):new t(i,n,e)}function r(e,t,n,i){function r(e,t,n){return(e=e.la)[((t=e[0]==t?0:e[1]==t?1:2)+1)%3]!=n?(t+2)%3:t}t.J[r(t,n,i)]=e,e.J[r(e,i,n)]=t}if(4>u)return 0;var o=i[0],a=i[1],s=i[2],l=i[3],h=n(o,a,s,l),f=n(o,s,l,a),d=n(o,a,l,s),p=n(a,s,l,o);for(r(h,f,s,o),r(h,d,o,a),r(h,p,a,s),r(f,d,l,o),r(f,p,s,l),r(d,p,l,a),c.push(h,f,d,p),o=4;o<u;o++)for(a=i[o],s=0;4>s;s++)h=(l=c[s]).la[0],0>(f=l.Ha).x*(a.x-h.x)+f.y*(a.y-h.y)+f.z*(a.z-h.z)&&e.add(l,a);return 4}())){for(;l<u;){var h=i[l];if(h.Pb){for(a=h.Pb;null!==a;)a.face.visible=!0,a=a.ec;a=0;e:for(;a<c.length;a++){var f=c[a];if(!1===f.visible){var d=f.J;for(s=0;3>s;s++)if(!0===d[s].visible){var p=f,g=s;break e}}}f=[],d=[];var b=p,v=g;do{if(f.push(b),d.push(v),v=(v+1)%3,!1===b.J[v].visible)do{for(a=b.la[v],b=b.J[v],s=0;3>s;s++)b.la[s]==a&&(v=s)}while(!1===b.J[v].visible&&(b!==p||v!==g))}while(b!==p||v!==g);var m=null,y=null;for(a=0;a<f.length;a++){b=f[a],v=d[a];var C=b.J[v],w=b.la[(v+1)%3],_=b.la[v],x=w.y-h.y,A=_.z-h.z,S=w.x-h.x,M=w.z-h.z,T=_.x-h.x,k=_.y-h.y;if(0<r.length){var z=r.pop();z.la[0]=h,z.la[1]=w,z.la[2]=_,z.Ha.x=x*A-M*k,z.Ha.y=M*T-S*A,z.Ha.z=S*k-x*T,z.Ya.length=0,z.visible=!1,z.ad=!0}else z={la:[h,w,_],J:Array(3),Ha:{x:x*A-M*k,y:M*T-S*A,z:S*k-x*T},Ya:[],visible:!1};c.push(z),b.J[v]=z,z.J[1]=b,null!==y&&(y.J[0]=z,z.J[2]=y),y=z,null===m&&(m=z),o(z,b,C)}for(y.J[0]=m,m.J[2]=y,a=[],s=0;s<c.length;s++)if(!0===(f=c[s]).visible){for(b=(d=f.Ya).length,h=0;h<b;h++)m=(v=d[h]).Lc,y=v.ec,null!==m&&(m.ec=y),null!==y&&(y.Lc=m),null===m&&(v.Rc.Pb=y),n.push(v);f.ad&&r.push(f)}else a.push(f);c=a}l++}for(a=0;a<c.length;a++)(f=c[a]).ad&&r.push(f)}return{pe:c}},e.add=function(t,i){if(0<n.length){var r=n.pop();r.face=t,r.Rc=i,r.ec=null,r.Lc=null}else r=new e(t,i);t.Ya.push(r),null!==(t=i.Pb)&&(t.Lc=r),r.ec=t,i.Pb=r};for(var n=Array(2e3),i=0;i<n.length;i++)n[i]=new e(null,null);var r=Array(1e3);for(i=0;i<r.length;i++)r[i]={la:Array(3),J:Array(3),Ha:{x:0,y:0,z:0},Ya:[],visible:!1}},k=new function(){function e(e,t,n,i,r,o,a,s){var u=(e-n)*(o-s)-(t-i)*(r-a);if(!(1e-12>Math.abs(u)))return{x:((e*i-t*n)*(r-a)-(e-n)*(r*s-o*a))/u,y:((e*i-t*n)*(o-s)-(t-i)*(r*s-o*a))/u}}return this.i=function(t,n){for(var i=t[0],r=i.x,o=i.y,a=i.x,s=i.y,u=t.length-1;0<u;u--)i=t[u],r=Math.min(r,i.x),o=Math.min(o,i.y),a=Math.max(a,i.x),s=Math.max(s,i.y);if(!(a-r<3*n||s-o<3*n)){e:{for(null==(i=!0)&&(i=!1),r=[],o=t.length,a=0;a<=o;a++){s=t[a%o],u=t[(a+1)%o];var l=t[(a+2)%o],c=u.x-s.x,h=u.y-s.y,f=Math.sqrt(c*c+h*h),d=n*c/f,p=n*h/f;if(c=l.x-u.x,h=l.y-u.y,c=n*c/(f=Math.sqrt(c*c+h*h)),h=n*h/f,(s=e(s.x-p,s.y+d,u.x-p,u.y+d,u.x-h,u.y+c,l.x-h,l.y+c))&&(r.push(s),l=r.length,i&&3<=l&&(s=r[l-3],u=r[l-2],l=r[l-1],0>(u.x-s.x)*(l.y-s.y)-(l.x-s.x)*(u.y-s.y)))){i=void 0;break e}}r.shift(),i=3>r.length?void 0:r}if(!i)e:{for(r=t.slice(0),i=0;i<t.length;i++){if(a=t[i%t.length],l=(u=t[(i+1)%t.length]).x-a.x,o=u.y-a.y,l=n*l/(s=Math.sqrt(l*l+o*o)),s=n*o/s,o=a.x-s,a=a.y+l,s=u.x-s,l=u.y+l,0!=r.length){for(p=o-s,h=a-l,d=[],c=f=!0,u=0;u<r.length;u++){var g=p*(a-r[u].y)-(o-r[u].x)*h;1e-12>=g&&-1e-12<=g&&(g=0),d.push(g),0<g&&(f=!1),0>g&&(c=!1)}if(f)r=[];else if(!c){for(p=[],u=0;u<r.length;u++)h=(u+1)%r.length,f=d[u],c=d[h],0<=f&&p.push(r[u]),(0<f&&0>c||0>f&&0<c)&&p.push(e(r[u].x,r[u].y,r[h].x,r[h].y,o,a,s,l));r=p}}if(3>r.length){i=void 0;break e}}i=r}return i}},this},z=new function(){function e(e){for(var t=e[0].x,n=e[0].y,i=t,r=n,o=1;o<e.length;o++){var a=e[o];t=Math.min(t,a.x),n=Math.min(n,a.y),i=Math.max(i,a.x),r=Math.max(r,a.y)}return[{x:t+2*(e=i-t),y:n+2*(r-=n),w:0},{x:t+2*e,y:n-2*r,w:0},{x:t-2*e,y:n+2*r,w:0}]}this.i=function(t,n){function i(e){var t=[e[0]],n=e[0][0],i=e[0][1],r=e.length,o=1;e:for(;o<r;o++)for(var a=1;a<r;a++){var s=e[a];if(null!==s){if(s[1]===n){if(t.unshift(s),n=s[0],e[a]=null,t.length===r)break e;continue}if(s[0]===i&&(t.push(s),i=s[1],e[a]=null,t.length===r))break e}}return t[0][0]!=t[r-1][1]&&t.push([t[r-1][1],t[0][0]]),t}function r(e,t,n,i){var r,o,a=[],s=[],u=n.length,l=t.length,c=0,h=-1,f=-1,d=i;for(i=0;i<u;i++){var p=(d+1)%u,g=n[d][0],b=n[p][0];if(1e-12<M.i(g.ea,b.ea))if(g.jb&&b.jb){var v=[],m=[];for(r=0;r<l;r++){var y=(c+1)%l;if((o=M.T(t[c],t[y],g.ea,b.ea,!1))&&(m.push(c),2===v.push(o)))break;c=y}if(2===v.length){if(r=v[1],g=(o=M.i(g.ea,v[0]))<(r=M.i(g.ea,r))?0:1,o=o<r?1:0,r=m[g],-1===h&&(h=r),-1!==f)for(;r!=f;)f=(f+1)%l,a.push(t[f]),s.push(null);a.push(v[g],v[o]),s.push(n[d][2],null),f=m[o]}}else if(g.jb&&!b.jb)for(r=0;r<l;r++){if(y=(c+1)%l,o=M.T(t[c],t[y],g.ea,b.ea,!1)){if(-1!==f)for(v=f;c!=v;)v=(v+1)%l,a.push(t[v]),s.push(null);a.push(o),s.push(n[d][2]),-1===h&&(h=c);break}c=y}else if(!g.jb&&b.jb)for(r=0;r<l;r++){if(y=(c+1)%l,o=M.T(t[c],t[y],g.ea,b.ea,!1)){a.push(g.ea,o),s.push(n[d][2],null),f=c;break}c=y}else a.push(g.ea),s.push(n[d][2]);d=p}if(0==a.length)s=a=null;else if(-1!==f)for(;h!=f;)f=(f+1)%l,a.push(t[f]),s.push(null);e.C=a,e.J=s}if(1===t.length)t[0].C=n.slice(0),t[0].J=[];else{var o,a=e(n),s=[];for(o=0;o<a.length;o++){var u=a[o];s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y-u.w})}for(o=0;o<t.length;o++)(u=t[o]).C=null,s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y-u.w});var l=T.i(s).pe;for(function(){for(o=0;o<l.length;o++){var e=l[o],t=e.la,n=t[0],i=t[1],r=t[2];t=n.x;var a=n.y;n=n.z;var s=i.x,u=i.y;i=i.z;var c=r.x,h=r.y;r=r.z;var f=t*(u-h)+s*(h-a)+c*(a-u);e.ea={x:-(a*(i-r)+u*(r-n)+h*(n-i))/f/2,y:-(n*(s-c)+i*(c-t)+r*(t-s))/f/2}}}(),function(e){for(o=0;o<l.length;o++){var t=l[o];t.jb=!M.sa(e,t.ea)}}(n),s=function(e,t){var n,i=Array(t.length);for(n=0;n<i.length;n++)i[n]=[];for(n=0;n<e.length;n++){var r=e[n];if(!(0>r.Ha.z))for(var o=r.J,a=0;a<o.length;a++){var s=o[a];if(!(0>s.Ha.z)){var u=r.la,l=u[(a+1)%3].index;u=u[a].index,2<l&&i[l-3].push([r,s,2<u?t[u-3]:null])}}}return i}(l,t),o=0;o<t.length;o++)if(0!==(u=s[o]).length){var c=t[o],h=(u=i(u)).length,f=-1;for(a=0;a<h;a++)u[a][0].jb&&(f=a);if(0<=f)r(c,n,u,f);else{f=[];var d=[];for(a=0;a<h;a++)1e-12<M.i(u[a][0].ea,u[(a+1)%h][0].ea)&&(f.push(u[a][0].ea),d.push(u[a][2]));c.C=f,c.J=d}c.C&&3>c.C.length&&(c.C=null,c.J=null)}}},this.u=function(t,n){var i,r=!1,o=t.length;for(i=0;i<o;i++){var a=t[i];null===a.C&&(r=!0),a.Yd=a.w}if(r){r=e(n);var s=[];for(i=t.length,a=0;a<r.length;a++){var u=r[a];s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y})}for(a=0;a<i;a++)u=t[a],s.push({x:u.x,y:u.y,z:u.x*u.x+u.y*u.y});for(u=T.i(s).pe,r=Array(i),a=0;a<i;a++)r[a]={};for(s=u.length,a=0;a<s;a++){var l=u[a];if(0<l.Ha.z){var c=l.la,h=c.length;for(l=0;l<h-1;l++){var f=c[l].index-3,d=c[l+1].index-3;0<=f&&0<=d&&(r[f][d]=!0,r[d][f]=!0)}l=c[0].index-3,0<=d&&0<=l&&(r[d][l]=!0,r[l][d]=!0)}}for(a=0;a<i;a++){for(var p in l=r[a],u=t[a],d=Number.MAX_VALUE,s=null,l)l=t[p],d>(c=M.i(u,l))&&(d=c,s=l);u.wj=s,u.Ze=Math.sqrt(d)}for(i=0;i<o;i++)a=t[i],p=Math.min(Math.sqrt(a.w),.95*a.Ze),a.w=p*p;for(this.i(t,n),i=0;i<o;i++)(a=t[i]).Yd!==a.w&&0<a.kc&&(n=Math.min(a.kc,a.Yd-a.w),a.w+=n,a.kc-=n)}}},D=new function(){this.H=function(e){for(var t=0,n=(e=e.m).length,i=0;i<n;i++){var r=e[i];if(r.C){var o=r.x,a=r.y;M.u(r.C,r),t<(r=(0<(o-=r.x)?o:-o)+(0<(r=a-r.y)?r:-r))&&(t=r)}}return t},this.i=function(e,t){var n=e.m;switch(t){case"random":return e.m[Math.floor(n.length*Math.random())];case"topleft":var i=(e=n[0]).x+e.y;for(t=1;t<n.length;t++){var r=n[t],o=r.x+r.y;o<i&&(i=o,e=r)}return e;case"bottomright":for(i=(e=n[0]).x+e.y,t=1;t<n.length;t++)(o=(r=n[t]).x+r.y)>i&&(i=o,e=r);return e;default:for(r=n[0],i=o=M.i(e,r),t=n.length-1;1<=t;t--){var a=n[t];(o=M.i(e,a))<i&&(i=o,r=a)}return r}},this.u=function(e,t,n){var i=e.m;if(i[0].J){var r,o=i.length;for(e=0;e<o;e++)i[e].Sc=!1,i[e].Zb=0;var a=r=0;for((o=[])[r++]=t||i[0],t=t.Zb=0;a<r;)if(!(i=o[a++]).Sc&&i.J){n(i,t++,i.Zb),i.Sc=!0;var s=i.J,u=s.length;for(e=0;e<u;e++){var l=s[e];l&&!0!==l.Sc&&(0===l.Zb&&(l.Zb=i.Zb+1),o[r++]=l)}}}else for(e=0;e<i.length;e++)n(i[e],e,1)}},B=function(){function e(e,r,u,l,c,d,p,g){var b=y.extend({},a,e);1>e.lineHeight&&(e.lineHeight=1),e=b.fontFamily;var v=b.fontStyle+" "+b.fontVariant+" "+b.fontWeight,m=b.hb,C=b.Gc,w=v+" "+e;b.te=w;var _={ka:!1,bc:0,fontSize:0};if(r.save(),r.font=v+" 100px "+e,r.textBaseline="middle",r.textAlign="center",function(e,t){t=t.te;var n=s[t];void 0===n&&(n={},s[t]=n),n[" "]=e.measureText(" ").width,n["…"]=e.measureText("…").width}(r,b),u=u.trim(),h.text=u,function(e,t,n,i){for(var r,o,a=0;a<e.length;a++)e[a].y===t.y&&(void 0===r?r=a:o=a);void 0===o&&(o=r),r!==o&&e[o].x<e[r].x&&(a=r,r=o,o=a),i.C=e,i.F=t,i.cd=n,i.Xe=r,i.Ye=o}(l,c,d,f),/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/.test(u)?(n(h),t(r,h,w),i(b,h,f,C,m,!0,_)):(t(r,h,w),i(b,h,f,C,m,!1,_),!_.ka&&(p&&(n(h),t(r,h,w)),g||p)&&(g&&(_.Ub=!0),i(b,h,f,C,C,!0,_))),_.ka){var x="",A=0,S=Number.MAX_VALUE,M=Number.MIN_VALUE;o(b,h,_.bc,_.fontSize,f,_.Ub,(function(e,t){0<x.length&&" "===t&&(x+=" "),x+=e}),(function(e,t,n,i,o){"­"===i&&(x+="‐"),r.save(),r.translate(d.x,t),e=_.fontSize/100,r.scale(e,e),r.fillText(x,0,0),r.restore(),x=n,A<o&&(A=o),S>t&&(S=t),M<t&&(M=t)})),_.box={x:d.x-A/2,y:S-_.fontSize/2,w:A,o:M-S+_.fontSize},r.restore()}else r.clear&&r.clear();return _}function t(e,t,n){var i,r=t.text.split(/(\n|[ \f\r\t\v\u2028\u2029]+|\u00ad+|\u200b+)/),o=[],a=[],u=r.length>>>1;for(i=0;i<u;i++)o.push(r[2*i]),a.push(r[2*i+1]);for(2*i<r.length&&(o.push(r[2*i]),a.push(void 0)),n=s[n],i=0;i<o.length;i++)void 0===(u=n[r=o[i]])&&(u=e.measureText(r).width,n[r]=u);t.Tc=o,t.Qf=a}function n(e){for(var t=e.text.split(/\s+/),n=[],i={".":!0,",":!0,";":!0,"?":!0,"!":!0,":":!0,"。":!0},r=0;r<t.length;r++){var o=t[r];if(3<o.length){var a="";a+=o.charAt(0),a+=o.charAt(1);for(var s=2;s<o.length-2;s++){var u=o.charAt(s);i[u]||(a+="​"),a+=u}a+="​",a+=o.charAt(o.length-2),a+=o.charAt(o.length-1),n.push(a)}else n.push(o)}e.text=n.join(" ")}function i(e,t,n,i,r,a,s){var u=e.lineHeight,l=Math.max(e.Ua,.001),c=e.ib,h=t.Tc,f=n.cd,d=n.F,p=void 0,g=void 0;switch(e.verticalAlign){case"top":f=d.y+d.o-f.y;break;case"bottom":f=f.y-d.y;break;default:f=2*Math.min(f.y-d.y,d.y+d.o-f.y)}if(!(0>=(c=Math.min(f,c*n.F.o)))){f=i,r=Math.min(r,c),d=Math.min(1,c/Math.max(20,t.Tc.length));do{var b=(f+r)/2,v=Math.min(h.length,Math.floor((c+b*(u-1-2*l))/(b*u))),m=void 0;if(0<v)for(var y=1,C=v;;){var w=Math.floor((y+C)/2);if(o(e,t,w,b,n,a&&b===i&&w===v,null,null)){if(y===(C=p=m=w))break}else if((y=w+1)>C)break}void 0!==m?f=g=b:r=b}while(r-f>d);return void 0===g?(s.ka=!1,s.fontSize=0):(s.ka=!0,s.fontSize=g,s.bc=p,s.Ub=a&&b===f),s}s.ka=!1}function o(e,t,n,i,r,o,a,h){var f=e.cb,d=i*(e.lineHeight-1),p=Math.max(e.Ua,.001),g=s[e.te],b=t.Tc;t=t.Qf;var v=r.C,m=r.cd,y=r.Xe,C=r.Ye;switch(e.verticalAlign){case"top":r=m.y+i/2+i*p;var w=1;break;case"bottom":r=m.y-(i*n+d*(n-1))+i/2-i*p,w=-1;break;default:r=m.y-(i*(n-1)/2+d*(n-1)/2),w=1}for(e=r,p=0;p<n;p++)u[2*p]=r-i/2,u[2*p+1]=r+i/2,r+=w*i,r+=w*d;for(;l.length<u.length;)l.push(Array(2));p=u,r=2*n,w=l;var _=v.length,x=y;y=(y-1+_)%_;var A=C;C=(C+1)%_;for(var S=0;S<r;){for(var M=p[S],T=v[y];T.y<M;)x=y,T=v[y=(y-1+_)%_];for(var k=v[C];k.y<M;)A=C,k=v[C=(C+1)%_];var z=v[x],D=v[A];k=D.x+(k.x-D.x)*(M-D.y)/(k.y-D.y),w[S][0]=z.x+(T.x-z.x)*(M-z.y)/(T.y-z.y),w[S][1]=k,S++}for(p=0;p<n;p++)v=2*p,w=(w=(r=m.x)-l[v][0])<(_=l[v][1]-r)?w:_,v=(_=r-l[v+1][0])<(v=l[v+1][1]-r)?_:v,c[p]=2*(w<v?w:v)-f*i;for(x=g[" "]*i/100,w=g["…"]*i/100,y=c[f=0],m=0,v=void 0,p=0;p<b.length;p++){if(r=b[p],A=t[p],m+(_=g[r]*i/100)<y&&b.length-p>=n-f&&"\n"!=v)m+=_," "===A&&(m+=x),a&&a(r,v);else{if(_>y&&(f!==n-1||!o))return!1;if(f+1>=n)return!!o&&(((n=y-m-w)>w||_>w)&&(0<(n=Math.floor(r.length*n/_))&&a&&a(r.substring(0,n),v)),a&&a("…",void 0),h&&h(f,e,r,v,m),!0);if(f++,h&&h(f,e,r,v,m),e+=i,e+=d,m=_," "===A&&(m+=x),_>(y=c[f])&&(f!==n||!o))return!1}v=A}return h&&h(f,e,void 0,void 0,m),!0}var a={hb:72,Gc:0,lineHeight:1.05,cb:1,Ua:.5,ib:.9,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",verticalAlign:"center"},s={},u=[],l=[],c=[],h={text:"",Tc:void 0,Qf:void 0},f={C:void 0,F:void 0,cd:void 0,Xe:0,Ye:0};return{re:e,de:function(t,n,i,r,o,a,s,u,l,c,h,f){var d=0,p=0;if(i=i.toString().trim(),!f&&l.result&&i===l.Xf&&Math.abs(c-l.Zd)/c<=h){var g=l.result;g.ka&&(d=a.x-l.eg,p=a.y-l.fg,h=l.Qc,n.save(),n.translate(d,p),h.Na(n),n.restore())}return g||((h=l.Qc).clear(),(g=e(t,h,i,r,o,a,s,u)).ka&&h.Na(n),l.Zd=c,l.eg=a.x,l.fg=a.y,l.result=g,l.Xf=i),g.ka?{ka:!0,bc:g.bc,fontSize:g.fontSize,box:{x:g.box.x+d,y:g.box.y+p,w:g.box.w,o:g.box.o},Ub:g.Ub}:{ka:!1}},Zh:function(){return{Zd:0,eg:0,fg:0,result:void 0,Qc:new r,Xf:void 0}},ya:a}}(),L=new function(){function e(e,t){return function(i,r,o,a){function s(e,t,n,i,r){e.C=[{x:t,y:n},{x:t+i,y:n},{x:t+i,y:n+r},{x:t,y:n+r}]}var u=r.x,l=r.y,c=r.w;if(r=r.o,0!=i.length)if(1==i.length)i[0].x=u+c/2,i[0].y=l+r/2,i[0].nd=0,o&&s(i[0],u,l,c,r);else{i=i.slice(0);for(var h=0,f=0;f<i.length;f++)h+=i[f].weight;for(h=c*r/h,f=0;f<i.length;f++)i[f].lc=i[f].weight*h;(function e(i,r,a,u,l){if(0!=i.length){var c=i.shift(),h=n(c);if(t(u,l)){var f=r,d=h/u;do{var p=(h=c.shift()).lc,g=p/d,b=a,v=d;(p=h).x=f+g/2,p.y=b+v/2,o&&s(h,f,a,g,d),f+=g}while(0<c.length);return e(i,r,a+d,u,l-d)}f=a,g=h/l;do{b=f,v=d=(p=(h=c.shift()).lc)/g,(p=h).x=r+g/2,p.y=b+v/2,o&&s(h,r,f,g,d),f+=d}while(0<c.length);return e(i,r+g,a,u-g,l)}})(a=e(i,c,r,[[i.shift()]],a),u,l,c,r)}}}function t(e,t,i,r){function o(e){return Math.max(Math.pow(u*e/s,i),Math.pow(s/(u*e),r))}var a=n(e),s=a*a,u=t*t;for(t=o(e[0].lc),a=1;a<e.length;a++)t=Math.max(t,o(e[a].lc));return t}function n(e){for(var t=0,n=0;n<e.length;n++)t+=e[n].lc;return t}this.u=e((function(e,i,r,o,a){for(var s=1/(a=Math.pow(2,a)),u=i<r;0<e.length;){var l=o[o.length-1],c=e.shift(),h=u?i:r,f=u?a:s,d=u?s:a,p=t(l,h,f,d);l.push(c),p<(h=t(l,h,f,d))&&(l.pop(),o.push([c]),u?r-=n(l)/i:i-=n(l)/r,u=i<r)}return o}),(function(e,t){return e<t})),this.i=e((function(e,n,i,r,o){function a(e){if(1<r.length){for(var i=r[r.length-1],o=r[r.length-2].slice(0),a=0;a<i.length;a++)o.push(i[a]);t(o,n,s,u)<e&&r.splice(-2,2,o)}}for(var s=Math.pow(2,o),u=1/s;0<e.length;){if(o=t(i=r[r.length-1],n,s,u),0==e.length)return;var l=e.shift();i.push(l),o<t(i,n,s,u)&&(i.pop(),a(o),r.push([l]))}return a(t(r[r.length-1],n,s,u)),r}),(function(){return!0}))};function E(e){var t,n={},i=e.Cd;e.j.subscribe("model:loaded",(function(e){t=e})),this.M=function(){e.j.D("api:initialized",this)},this.nc=function(e,t,r,o){this.Xc(n,t),this.Yc(n,t),this.Wc(n,t,!1),o&&o(n),e(i,n,r)},this.bd=function(e,n,i,r,o,a,s){if(e){for(e=n.length-1;0<=e;e--){var u=n[e],l=y.extend({group:u.group},o);l[i]=r(u),a(l)}0<n.length&&s(y.extend({groups:S.uc(t,r).map((function(e){return e.group}))},o))}},this.Yc=function(e,t){return e.selected=t.selected,e.hovered=t.ub,e.open=t.open,e.openness=t.Cb,e.exposed=t.U,e.exposure=t.ja,e.transitionProgress=t.ra,e.revealed=!t.aa.Ga(),e.browseable=t.Ia?t.R:void 0,e.visible=t.Y,e.labelDrawn=t.oa&&t.oa.ka,e},this.Xc=function(e,t){var n=t.parent;return e.group=t.group,e.parent=n&&n.group,e.weightNormalized=t.cg,e.level=t.level-1,e.siblingCount=n&&n.m.length,e.hasChildren=!t.empty(),e.index=t.index,e.indexByWeight=t.nd,e.description=t.description,e.attribution=t.attribution,e},this.Wc=function(e,t,n){if(e.polygonCenterX=t.O.x,e.polygonCenterY=t.O.y,e.polygonArea=t.O.ha,e.boxLeft=t.F.x,e.boxTop=t.F.y,e.boxWidth=t.F.w,e.boxHeight=t.F.o,t.oa&&t.oa.ka){var i=t.oa.box;e.labelBoxLeft=i.x,e.labelBoxTop=i.y,e.labelBoxWidth=i.w,e.labelBoxHeight=i.o,e.labelFontSize=t.oa.fontSize}return n&&t.$&&(e.polygon=t.$.map((function(e){return{x:e.x,y:e.y}})),e.neighbors=t.J&&t.J.map((function(e){return e&&e.group}))),e}}var j=new function(){var e=window.console;this.i=function(e){throw"FoamTree: "+e},this.info=function(t){e.info("FoamTree: "+t)},this.warn=function(t){e.warn("FoamTree: "+t)}};function O(e){function t(t,i){t.m=[],t.Ea=!0;var o=r(i),a=0;if(("flattened"===e.mb||"always"===e.zg&&t.group&&t.group.description)&&0<i.length&&0<t.level){var s=i.reduce((function(e,t){return e+y.I(t.weight,1)}),0),u=n(t.group,!1);u.description=!0,u.weight=s*e.Sb,u.index=a++,u.parent=t,u.level=t.level+1,u.id=u.id+"_d",t.m.push(u)}for(s=0;s<i.length;s++){var l=i[s];if(0>=(u=y.I(l.weight,1))){if(!e.Wi)continue;u=.9*o}(l=n(l,!0)).weight=u,l.index=a,l.parent=t,l.level=t.level+1,t.m.push(l),a++}}function n(e,t){var n=new ee;return i(e),n.id=e.__id,n.group=e,t&&(l[e.__id]=n),n}function i(e){y.has(e,"__id")||(Object.defineProperty(e,"__id",{enumerable:!1,configurable:!1,writable:!1,value:u}),u++)}function r(e){for(var t=Number.MAX_VALUE,n=0;n<e.length;n++){var i=e[n].weight;0<i&&t>i&&(t=i)}return t===Number.MAX_VALUE&&(t=1),t}function o(e){if(!e.empty()){var t,n=0;for(t=(e=e.m).length-1;0<=t;t--){var i=e[t].weight;n<i&&(n=i)}for(t=e.length-1;0<=t;t--)(i=e[t]).cg=i.weight/n}}function a(e){if(!e.empty()){e=e.m.slice(0).sort((function(e,t){return e.weight<t.weight?1:e.weight>t.weight?-1:e.index-t.index}));for(var t=0;t<e.length;t++)e[t].nd=t}}function s(){for(var t=p.m.reduce((function(e,t){return e+t.weight}),0),n=0;n<p.m.length;n++){var i=p.m[n];i.attribution&&(i.weight=Math.max(.025,e.sg)*t)}}var u,l,c,h,f,d=this,p=new ee;this.M=function(){return p},this.T=function(n){var i=n.group.groups,r=e.Rh;return!!(!n.m&&!n.description&&i&&0<i.length&&f+i.length<=r)&&(f+=i.length,t(n,i),o(n),a(n),!0)},this.load=function(e){p.group=e,p.xa=!1,p.R=!1,p.Ia=!1,p.open=!0,p.Cb=1,u=function e(t,n){if(!t)return n;if(n=Math.max(n,t.__id||0),(t=t.groups)&&0<t.length)for(var i=t.length-1;0<=i;i--)n=e(t[i],n);return n}(e,0)+1,l={},c={},h={},f=0,e&&(i(e),l[e.__id]=p,y.V(e.id)||(c[e.id]=e),function e(t){var n=t.groups;if(n)for(var r=0;r<n.length;r++){var o=n[r];i(o);var a=o.__id;l[a]=null,h[a]=t,a=o.id,y.V(a)||(c[a]=o),e(o)}}(e)),t(p,e&&e.groups||[]),function(e){if(!e.empty()){var t=n({attribution:!0});t.index=e.m.length,t.parent=e,t.level=e.level+1,t.attribution=!0,e.m.push(t)}}(p),o(p),s(),a(p)},this.update=function(e){e.forEach((function(e){S.za(e,(function(e){if(!e.empty())for(var t=r((e=e.m).map((function(e){return e.group}))),n=0;n<e.length;n++){var i=e[n];i.weight=0<i.group.weight?i.group.weight:.9*t}})),o(e),e===p&&s(),a(e)}))},this.u=function(e){return function(){if(y.V(e)||y.Re(e))return[];if(Array.isArray(e))return e.map(d.i,d);if(y.wb(e)){if(y.has(e,"__id"))return[d.i(e)];if(y.has(e,"all")){var t=[];return S.L(p,(function(e){t.push(e)})),t}if(y.has(e,"groups"))return d.u(e.groups)}return[d.i(e)]}().filter((function(e){return void 0!==e}))},this.i=function(e){if(y.wb(e)&&y.has(e,"__id")){if(e=e.__id,y.has(l,e)){if(null===l[e]){for(var t=h[e],n=[];t&&(t=t.__id,n.push(t),!l[t]);)t=h[t];for(t=n.length-1;0<=t;t--)this.T(l[n[t]])}return l[e]}}else if(y.has(c,e))return this.i(c[e])},this.H=function(e,t,n){return{m:d.u(e),Ca:y.I(e&&e[t],!0),Ba:y.I(e&&e.keepPrevious,n)}}}function I(e,t,n){var i={};t.Ba&&S.L(e,(function(e){n(e)&&(i[e.id]=e)})),e=t.m,t=t.Ca;for(var r=e.length-1;0<=r;r--){var o=e[r];i[o.id]=t?o:void 0}var a=[];return y.Aa(i,(function(e){void 0!==e&&a.push(e)})),a}function N(e){function t(e,t){e=e.ja,t.opacity=1,t.Da=1,t.va=0>e?1-A.Ch/100*e:1,t.saturation=0>e?1-A.Dh/100*e:1,t.ca=0>e?1+.5*e:1}function n(e){return e=e.ja,Math.max(.001,0===e?1:1+e*(A.Pa-1))}function i(t,n,i,u){var l=o();if(0===t.length&&!l)return(new h).resolve().promise();var v=t.reduce((function(e,t){return e[t.id]=!0,e}),{}),y=[];if(t=[],C.reduce((function(e,t){return e||v[t.id]&&(!t.U||1!==t.ja)||!v[t.id]&&!t.parent.U&&(t.U||-1!==t.ja)}),!1)){var M=[],T={};C.forEach((function(e){v[e.id]&&(e.U||y.push(e),e.U=!0,S.za(e,(function(e){M.push(a(e,1)),T[e.id]=!0})))})),0<M.length?(S.L(c,(function(e){v[e.id]||(e.U&&y.push(e),e.U=!1),T[e.id]||M.push(a(e,-1))})),t.push(b.K.A({}).Qa(M).call(s).Ta()),r(v),t.push(function(t){return t||!g.zd()?b.K.A(d).fa({duration:.7*A.Oa,P:{x:{end:w.x+w.w/2,easing:m.ia(A.Wb)},y:{end:w.y+w.o/2,easing:m.ia(A.Wb)}},ba:function(){e.j.D("foamtree:dirty",!0)}}).Ta():(d.x=w.x+w.w/2,d.y=w.y+w.o/2,(new h).resolve().promise())}(l)),i&&(g.ic(w,A.Yb,A.Oa,m.ia(A.Wb)),g.Fb())):(t.push(function(e){var t=[],n=[];return S.L(c,(function(e){0!==e.ja&&n.push(a(e,0,(function(){this.U=!1})))})),t.push(b.K.A({}).Qa(n).Ta()),g.content(0,0,_,x),e&&(t.push(g.reset(A.Oa,m.ia(A.Wb))),g.Fb()),f(t)}(i)),n&&S.L(c,(function(e){e.U&&y.push(e)})))}return f(t).then((function(){p.bd(n,y,"exposed",(function(e){return e.U}),{indirect:u},e.options.hf,e.options.gf)}))}function r(e){C.reduce(u(!0,void 0,(function(t){return t.U||e[t.id]})),l(w)),w.x-=w.w*(A.Pa-1)/2,w.y-=w.o*(A.Pa-1)/2,w.w*=A.Pa,w.o*=A.Pa}function o(){return!!C&&C.reduce((function(e,t){return e||0!==t.ja}),!1)}function a(n,i,r){var o=b.K.A(n);return 0===n.ja&&0!==i&&o.call((function(){this.mc(M),this.qb(t)})),o.fa({duration:A.Oa,P:{ja:{end:i,easing:m.ia(A.Wb)}},ba:function(){c.N=!0,c.Fa=!0,e.j.D("foamtree:dirty",!0)}}),0===i&&o.call((function(){this.vd(),this.cc(),this.Nc(M),this.Mc(t)})),o.call(r).done()}function s(){var e=c.m.reduce(u(!1,M.transformPoint,void 0),l({})).box,t=A.Yb,n=Math.min(e.x,w.x-w.w*t),i=Math.min(e.y,w.y-w.o*t);g.content(n,i,Math.max(e.x+e.w,w.x+w.w*(1+t))-n,Math.max(e.y+e.o,w.y+w.o*(1+t))-i)}function u(e,t,n){var i={};return function(r,o){if(!n||n(o)){for(var a,s=e&&o.$||o.C,u=s.length-1;0<=u;u--)a=void 0!==t?t(o,s[u],i):s[u],r.Hc=Math.min(r.Hc,a.x),r.wd=Math.max(r.wd,a.x),r.Ic=Math.min(r.Ic,a.y),r.xd=Math.max(r.xd,a.y);r.box.x=r.Hc,r.box.y=r.Ic,r.box.w=r.wd-r.Hc,r.box.o=r.xd-r.Ic}return r}}function l(e){return{Hc:Number.MAX_VALUE,wd:Number.MIN_VALUE,Ic:Number.MAX_VALUE,xd:Number.MIN_VALUE,box:e}}var c,d,p,g,b,v,C,w,_,x,A=e.options,M={Ve:function(e,t){return t.scale=n(e),!1},Ib:function(e,t){e=n(e);var i=d.x,r=d.y;t.translate(i,r),t.scale(e,e),t.translate(-i,-r)},Jb:function(e,t,i){e=n(e);var r=d.x,o=d.y;i.x=(t.x-r)/e+r,i.y=(t.y-o)/e+o},transformPoint:function(e,t,i){e=n(e);var r=d.x,o=d.y;return i.x=(t.x-r)*e+r,i.y=(t.y-o)*e+o,i}};e.j.subscribe("stage:initialized",(function(e,t,n,i){d={x:n/2,y:i/2},w={x:0,y:0,w:_=n,o:x=i}})),e.j.subscribe("stage:resized",(function(e,t,n,i){d.x*=n/e,d.y*=i/t,_=n,x=i})),e.j.subscribe("api:initialized",(function(e){p=e})),e.j.subscribe("zoom:initialized",(function(e){g=e})),e.j.subscribe("model:loaded",(function(e,t){c=e,C=t})),e.j.subscribe("model:childrenAttached",(function(e){C=e})),e.j.subscribe("timeline:initialized",(function(e){b=e})),e.j.subscribe("openclose:initialized",(function(e){v=e}));var T=["groupExposureScale","groupUnexposureScale","groupExposureZoomMargin"];e.j.subscribe("options:changed",(function(e){y.bb(e,T)&&o()&&(r({}),g.cj(w,A.Yb),g.Fb())})),this.M=function(){e.j.D("expose:initialized",this)},this.Vb=function(e,t,n,r){var o=e.m.reduce((function(e,t){for(;t=t.parent;)e[t.id]=!0;return e}),{}),a=I(c,e,(function(e){return e.U&&!e.open&&!o[e.id]})),s=new h;return function(e,t){for(var n=e.reduce((function(e,t){return e[t.id]=t,e}),{}),i=e.length-1;0<=i;i--)S.L(e[i],(function(e){n[e.id]=void 0}));var r=[];y.Aa(n,(function(e){e&&S.me(e,(function(e){e.open||r.push(e)}))}));var o=[];return y.Aa(n,(function(e){e&&e.open&&o.push(e)})),e=[],0!==r.length&&e.push(v.Bb({m:r,Ca:!0,Ba:!0},t,!0)),f(e)}(a,t).then((function(){i(a.filter((function(e){return e.C&&e.$})),t,n,r).then(s.resolve)})),s.promise()}}function P(e){var t,n,i=[],r=new d(y.qa);e.j.subscribe("stage:initialized",(function(){})),e.j.subscribe("stage:resized",(function(){})),e.j.subscribe("stage:newLayer",(function(e,t){i.push(t)})),e.j.subscribe("model:loaded",(function(e){t=e,r.clear()})),e.j.subscribe("zoom:initialized",(function(){})),e.j.subscribe("timeline:initialized",(function(e){n=e}));var o=!1;e.j.subscribe("render:renderers:resolved",(function(e){o=e.labelPlainFill||!1}));var a=new function(){var e=0,t=0,n=0,i=0,r=0,o=0;this.i=function(a,s,u,l,c){t=1-(e=1+s),n=u,i=l,r=c,o=a},this.Ve=function(o,a){return a.scale=e+t*o.ra,0!==r||0!==n||0!==i},this.Ib=function(a,s){var u=e+t*a.ra,l=a.parent,c=o*a.x+(1-o)*l.x,h=o*a.y+(1-o)*l.y;s.translate(c,h),s.scale(u,u),a=1-a.ra,s.rotate(r*Math.PI*a),s.translate(-c,-h),s.translate(l.F.w*n*a,l.F.o*i*a)},this.Jb=function(r,a,s){var u=e+t*r.ra,l=o*r.x+(1-o)*r.parent.x,c=o*r.y+(1-o)*r.parent.y,h=1-r.ra;r=r.parent,s.x=(a.x-l)/u+l-r.F.w*n*h,s.y=(a.y-c)/u+c-r.F.o*i*h},this.transformPoint=function(r,a,s){var u=e+t*r.ra,l=o*r.x+(1-o)*r.parent.x,c=o*r.y+(1-o)*r.parent.y,h=1-r.ra;r=r.parent,s.x=(a.x-l)*u+l-r.F.w*n*h,s.y=(a.y-c)*u+c-r.F.o*i*h}};this.M=function(){},this.u=function(){function i(e,t){var n=Math.min(1,Math.max(0,e.ra));t.opacity=n,t.va=1,t.saturation=n,t.Da=n,t.ca=e.yb}function s(e,t){var n=Math.min(1,Math.max(0,e.Hd));t.opacity=n,t.Da=n,t.va=1,t.saturation=1,t.ca=e.yb}var u=e.options,l=u.Gd,c=u.ii,h=u.ji,f=u.ki,d=u.ei,p=u.fi,g=u.gi,b=u.ai,v=u.bi,y=u.ci,C=d+p+g+b+v+y+c+h+f,w=0<C?l/C:0,_=[];return r.initial()?a.i(u.oi,u.mi,u.pi,u.ri,u.li):a.i(u.Mf,u.Lf,u.Nf,u.Of,u.Kf),D.u(t,D.i(t,e.options.ni),(function(t,r,l){var C="groups"===e.options.hi?l:r;_.push(n.K.A(t).call((function(){this.qb(i)})).wait(o?w*(d+C*p):0).fa({duration:o?w*g:0,P:{yb:{end:0,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).done()),S.L(t,(function(t){_.push(n.K.A(t).call((function(){this.mc(a),this.qb(s)})).wait(w*(b+v*C)).fa({duration:w*y,P:{Hd:{end:0,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).Xa((function(){this.selected=!1,this.Nc(a)})).done())})),_.push(n.K.A(t).call((function(){this.mc(a)})).wait(w*(c+h*C)).fa({duration:w*f,P:{ra:{end:0,easing:m.ia(u.di)}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).Xa((function(){this.selected=!1,this.Nc(a)})).done())})),n.K.A({}).Qa(_).Ta()},this.i=function(t){return function(t){function i(e,t){var n=Math.min(1,Math.max(0,e.ra));t.opacity=n,t.va=1,t.saturation=n,t.Da=n,t.ca=e.yb}var s=e.options,u=s.Ri,l=s.Si,c=s.Oi,f=s.Pi,d=s.Qi,p=s.Od,g=u+l+c+f+d,b=0<g?p/g:0,v=[];if(a.i(s.Mf,s.Lf,s.Nf,s.Of,s.Kf),0===b&&t.m&&t.R){for(p=t.m,g=0;g<p.length;g++){var y=p[g];y.ra=1,y.yb=1,y.qb(i),y.cc(),y.Mc(i)}return t.N=!0,e.j.D("foamtree:dirty",0<b),(new h).resolve().promise()}if(t.m&&t.R){D.u(t,D.i(t,e.options.Qd),(function(t,r,h){t.mc(a),t.qb(i),h="groups"===e.options.Pd?h:r,r=n.K.A(t).wait(h*b*u).fa({duration:b*l,P:{ra:{end:1,easing:m.ia(s.Ni)}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",0<b)}}).done(),h=n.K.A(t).wait(o?b*(c+h*f):0).fa({duration:o?b*d:0,P:{yb:{end:1,easing:m.Ab}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",0<b)}}).done(),t=n.K.A(t).Qa([r,h]).Xd().Xa((function(){this.vd(),this.cc(),this.Nc(a),this.Mc(i)})).done(),v.push(t)})),r.i();var C=new h;return n.K.A({}).Qa(v).call((function(){r.u(),C.resolve()})).start(),C.promise()}return(new h).resolve().promise()}(t)}}function F(e){function t(t,r,s){function u(e,t){t.opacity=1-e.Cb,t.va=1,t.saturation=1,t.ca=1,t.Da=1}var l=[],c=[];return S.L(o,(function(r){if(r.R&&r.X){var o=y.has(t,r.id),a=n[r.id];if(a&&a.xb())a.stop();else if(r.open===o)return;r.Va=o,o||(r.open=o,r.Bd=!1),c.push(r),l.push(function(t,r){t.qb(u);var o=i.K.A(t).fa({duration:e.options.Kc,P:{Cb:{end:r?1:0,easing:m.fe}},ba:function(){this.N=!0,e.j.D("foamtree:dirty",!0)}}).call((function(){this.open=r,t.Va=!1})).Xa((function(){this.cc(),this.Mc(u),delete n[this.id]})).done();return n[t.id]=o}(r,o))}})),0<l.length?(e.j.D("openclose:changing"),i.K.A({}).Qa(l).Ta().then((function(){a.bd(r,c,"open",(function(e){return e.open}),{indirect:s},e.options.rf,e.options.qf)}))):(new h).resolve().promise()}var n,i,r,o,a;e.j.subscribe("api:initialized",(function(e){a=e})),e.j.subscribe("model:loaded",(function(e){o=e,n={}})),e.j.subscribe("timeline:initialized",(function(e){i=e})),e.j.subscribe("expose:initialized",(function(e){r=e})),this.M=function(){e.j.D("openclose:initialized",this)},this.Bb=function(n,i,a){if("flattened"==e.options.mb)return(new h).resolve().promise();n=I(o,n,(function(e){return e.open||e.Va}));for(var s=new h,u=0;u<n.length;u++)n[u].Va=!0;0<n.length&&e.j.D("foamtree:attachChildren",n);var l=n.reduce((function(e,t){return e[t.id]=!0,e}),{});return function(e,t){var n,i=[];if(S.L(o,(function(t){if(t.m){var n=y.has(e,t.id);t.open!==n&&(n||t.U||S.L(t,(function(e){if(e.U)return i.push(t),!1})))}})),0===i.length)return(new h).resolve().promise();for(n=i.length-1;0<=n;n--)i[n].open=!1;for(t=r.Vb({m:i,Ca:!0,Ba:!0},t,!0,!0),n=i.length-1;0<=n;n--)i[n].open=!0;return t}(l,i).then((function(){t(l,i,a).then(s.resolve)})),s.promise()}}function R(e){var t,n;e.j.subscribe("api:initialized",(function(e){n=e})),e.j.subscribe("model:loaded",(function(e){t=e})),this.M=function(){e.j.D("select:initialized",this)},this.select=function(i,r){return function(i,r){var o;for(i=I(t,i,(function(e){return e.selected})),S.L(t,(function(e){!0===e.selected&&(e.selected=!e.selected,e.N=!e.N,e.Sa=!e.Sa)})),o=i.length-1;0<=o;o--){var a=i[o];a.selected=!a.selected,a.N=!a.N,a.Sa=!a.Sa}var s=[];S.L(t,(function(e){e.N&&s.push(e)})),0<s.length&&e.j.D("foamtree:dirty",!1),n.bd(r,s,"selected",(function(e){return e.selected}),{},e.options.tf,e.options.sf)}(i,r)}}function G(e){function n(e){return function(t){e.call(this,{x:t.x,y:t.y,scale:t.scale,ed:t.delta,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,shiftKey:t.shiftKey,lb:t.secondary,touches:t.touches})}}function i(){u.pc(2)?e.j.D("interaction:reset"):u.normalize(N.ob,m.ia(N.Kb))}function r(e){return function(){g.empty()||e.apply(this,arguments)}}function o(e,t,n){var i={},r={};return function(o){switch(e){case"click":var s=N.bf;break;case"doubleclick":s=N.cf;break;case"hold":s=N.jf;break;case"hover":s=N.kf;break;case"mousemove":s=N.mf;break;case"mousewheel":s=N.pf;break;case"mousedown":s=N.lf;break;case"mouseup":s=N.nf;break;case"dragstart":s=N.ff;break;case"drag":s=N.df;break;case"dragend":s=N.ef;break;case"transformstart":s=N.wf;break;case"transform":s=N.uf;break;case"transformend":s=N.vf}var l=!1,c=!s.empty(),h=u.absolute(o,i),f=(t||c)&&a(h),d=(t||c)&&function(e){var t=void 0,n=0;return S.sc(g,(function(i){!0===i.open&&i.Y&&i.scale>n&&H(i,e)&&(t=i,n=i.scale)})),t}(h);c&&(c=f?f.group:null,h=f?f.Jb(h,r):h,o.Db=void 0,s=s({type:e,group:c,topmostClosedGroup:c,bottommostOpenGroup:d?d.group:null,x:o.x,y:o.y,xAbsolute:h.x,yAbsolute:h.y,scale:y.I(o.scale,1),secondary:o.lb,touches:y.I(o.touches,1),delta:y.I(o.ed,0),ctrlKey:o.ctrlKey,metaKey:o.metaKey,altKey:o.altKey,shiftKey:o.shiftKey,preventDefault:function(){l=!0},preventOriginalEventDefault:function(){o.Db="prevent"},allowOriginalEventDefault:function(){o.Db="allow"}}),l=l||0<=s.indexOf(!1),f&&f.attribution&&"click"===e&&(l=!1)),l||n&&n({dd:f,ug:d},o)}}function a(e,t){if("flattened"===N.mb)e=function(e){function t(e,n){var i=n.m;if(i){for(var r,o=-Number.MAX_VALUE,a=0;a<i.length;a++){var s=i[a];!s.description&&s.Y&&H(s,e)&&s.scale>o&&(r=s,o=s.scale)}var u;return r&&(u=t(e,r)),u||r||n}return n}for(var n=j.length,i=j[0].scale,r=j[0].scale,o=0;o<n;o++){var a=j[o];(a=a.scale)<i&&(i=a),a>r&&(r=a)}if(i!==r)for(o=0;o<n;o++)if((a=j[o]).scale===r&&a.Y&&H(a,e))return t(e,a);return t(e,g)}(e);else{t=t||0;for(var n=j.length,i=void 0,r=0;r<n;r++){var o=j[r];o.scale>t&&!1===o.open&&o.Y&&H(o,e)&&(i=o,t=o.scale)}e=i}return e&&e.description&&(e=e.parent),e}var s,u,l,c,h,f,d,g,C,w,_,A,T,k,z,D,B,L,E,j,O=t.Lh(),I=this,N=e.options,P=!1;e.j.subscribe("stage:initialized",(function(t,n,i,r){s=n,L=i,E=r,function(){function t(e){return function(t){return t.x*=L/s.clientWidth,t.y*=E/s.clientHeight,e(t)}}"external"!==N.Me&&("hammerjs"===N.Me&&y.has(window,"Hammer")&&(G.M(s),G.A("tap",t(I.i),!0),G.A("doubletap",t(I.u),!0),G.A("hold",t(I.sa),!0),G.A("touch",t(I.ua),!0),G.A("release",t(I.wa),!1),G.A("dragstart",t(I.ga),!0),G.A("drag",t(I.H),!0),G.A("dragend",t(I.T),!0),G.A("transformstart",t(I.pb),!0),G.A("transform",t(I.transform),!0),G.A("transformend",t(I.Ka),!0)),D=new v(s),B=new b,D.i(t(I.i)),D.u(t(I.u)),D.sa(t(I.sa)),D.wa(t(I.ua)),D.Ka(t(I.wa)),D.ga(t(I.ga)),D.H(t(I.H)),D.T(t(I.T)),D.ta(t(I.ta)),D.Ja(t(I.ta)),D.ua(t(I.Ja)),B.addEventListener("keyup",(function(t){var n=!1,i=void 0,r=N.xf({keyCode:t.keyCode,preventDefault:function(){n=!0},preventOriginalEventDefault:function(){i="prevent"},allowOriginalEventDefault:function(){i="allow"}});"prevent"===i&&t.preventDefault(),(n=n||0<=r.indexOf(!1))||27===t.keyCode&&e.j.D("interaction:reset")})))}()})),e.j.subscribe("stage:resized",(function(e,t,n,i){L=n,E=i})),e.j.subscribe("stage:disposed",(function(){D.Za(),G.Za(),B.i()})),e.j.subscribe("expose:initialized",(function(e){c=e})),e.j.subscribe("zoom:initialized",(function(e){u=e})),e.j.subscribe("openclose:initialized",(function(e){h=e})),e.j.subscribe("select:initialized",(function(e){f=e})),e.j.subscribe("titlebar:initialized",(function(e){d=e})),e.j.subscribe("timeline:initialized",(function(e){l=e})),e.j.subscribe("model:loaded",(function(e,t){g=e,j=t})),e.j.subscribe("model:childrenAttached",(function(e){j=e})),this.M=function(){},this.ua=r(o("mousedown",!1,(function(){u.Wh()}))),this.wa=r(o("mouseup",!1,void 0)),this.i=r(o("click",!0,(function(e,t){t.lb||t.shiftKey||!(e=e.dd)||(e.attribution?t.ctrlKey?document.location.href=x("iuuqr;..b`ssnurd`sbi/bnl.gn`lusdd"):(t=m.ia(N.Kb),e.be?(u.reset(N.ob,t),e.be=!1):(u.bg(e,N.Yb,N.ob,t),e.be=!0)):f.select({m:[e],Ca:!e.selected,Ba:t.metaKey||t.ctrlKey},!0))}))),this.u=r(o("doubleclick",!0,(function(t,n){var i=t.dd;i&&i.attribution||(n.lb||n.shiftKey?i&&(i.parent.U&&(i=i.parent),t={m:i.parent!==g?[i.parent]:[],Ca:!0,Ba:!1},f.select(t,!0),c.Vb(t,!0,!0,!1)):i&&(t={m:[i],Ca:!0,Ba:!1},i.Va=!0,e.j.D("foamtree:attachChildren",[i]),c.Vb(t,!0,!0,!1)),i&&l.K.A({}).wait(N.Oa/2).call((function(){h.Bb({m:S.uc(g,(function(e){return e.Bd&&!S.Jh(i,e)})),Ca:!1,Ba:!0},!0,!0),i.Bd=!0,h.Bb({m:[i],Ca:!(n.lb||n.shiftKey),Ba:!0},!0,!0)})).start())}))),this.sa=r(o("hold",!0,(function(e,t){(e=(t=!(t.metaKey||t.ctrlKey||t.shiftKey||t.lb))?e.dd:e.ug)&&e!==g&&h.Bb({m:[e],Ca:t,Ba:!0},!0,!1)}))),this.ga=r(o("dragstart",!1,(function(e,t){C=t.x,w=t.y,_=Date.now(),P=!0}))),this.H=r(o("drag",!1,(function(e,t){if(P){e=Date.now(),k=Math.min(1,e-_),_=e,e=t.x-C;var n=t.y-w;u.Uh(e,n),A=e,T=n,C=t.x,w=t.y}}))),this.T=r(o("dragend",!1,(function(){if(P){P=!1;var e=Math.sqrt(A*A+T*T)/k;4<=e?u.Vh(e,A,T):u.$e()}}))),this.pb=r(o("transformstart",!1,(function(e,t){z=1,C=t.x,w=t.y})));var F=1,R=!1;this.transform=r(o("transform",!1,(function(e,t){e=t.scale-.01,u.pg(t,e/z,t.x-C,t.y-w),z=e,C=t.x,w=t.y,F=z,R=R||2<t.touches}))),this.Ka=r(o("transformend",!1,(function(){R&&.8>F?e.j.D("interaction:reset"):i(),R=!1}))),this.Ja=r(o("mousewheel",!1,function(){var e=y.yg((function(){i()}),300);return function(t,n){1!==(t=N.ij)&&(t=Math.pow(t,n.ed),O?(u.qg(n,t),e()):u.Nb(n,t,N.ob,m.ia(N.Kb)).then(i))}}())),this.ta=r(function(){var t,n=void 0,i={},r=!1,s=o("hover",!1,(function(){n&&(n.ub=!1,0<n.level&&(n.N=!0)),t&&(t.ub=!0,0<t.level&&(t.N=!0)),d.update(t),e.j.D("foamtree:dirty",!1)})),l=o("mousemove",!1,void 0);return function(e){if("out"===e.type)r=(t=void 0)!==n;else if(u.absolute(e,i),n&&!n.open&&H(n,i)){var o=a(i,n.scale);o&&o!==n?(r=!0,t=o):r=!1}else t=a(i),r=t!==n;r&&(s(e),n=t,r=!1),n&&l(e)}}()),this.Lb={click:n(this.i),doubleclick:n(this.u),hold:n(this.sa),mouseup:n(this.wa),mousedown:n(this.ua),dragstart:n(this.ga),drag:n(this.H),dragend:n(this.T),transformstart:n(this.pb),transform:n(this.transform),transformend:n(this.Ka),hover:n(this.ta),mousewheel:n(this.Ja)};var G=function(){var e,t={};return{M:function(t){e=window.Hammer(t,{doubletap_interval:350,hold_timeout:400,doubletap_distance:10})},A:function(n,i,r){t[n]=i,e.on(n,function(e,t){return function(n){var i=(n=n.gesture).center;(i=p.oe(s,i.pageX,i.pageY,{})).scale=n.scale,i.lb=1<n.touches.length,i.touches=n.touches.length,e.call(s,i),(void 0===i.Db&&t||"prevent"===i.Db)&&n.preventDefault()}}(i,r))},Za:function(){e&&y.Aa(t,(function(t,n){e.off(n,t)}))}}}(),H=function(){var e={};return function(t,n){return t.Jb(n,e),t.$&&M.sa(t.$,e)}}()}function H(e){function t(e,t,n,i){var r,o=0,a=[];for(r=0;r<t.length;r++){var s=Math.sqrt(M.i(t[r],t[(r+1)%t.length]));a.push(s),o+=s}for(r=0;r<a.length;r++)a[r]/=o;e[0].x=n.x,e[0].y=n.y;var u=s=o=0;for(r=1;r<e.length;r++){var l=e[r],c=.95*Math.pow(r/e.length,i);for(o+=.3819;s<o;)s+=a[u],u=(u+1)%a.length;var h=(u-1+a.length)%a.length,f=1-(s-o)/a[h],d=t[h].x;h=t[h].y;var p=t[u].x,g=t[u].y;d=(d-n.x)*c+n.x,h=(h-n.y)*c+n.y,p=(p-n.x)*c+n.x,g=(g-n.y)*c+n.y,l.x=d*(1-f)+p*f,l.y=h*(1-f)+g*f}}var n={random:{vb:function(e,t){for(var n=0;n<e.length;n++){var i=e[n];i.x=t.x+Math.random()*t.w,i.y=t.y+Math.random()*t.o}},Ob:"box"},ordered:{vb:function(e,t){e=e.slice(0),i.ac&&e.sort(te),L.i(e,t,!1,i.Ld)},Ob:"box"},squarified:{vb:function(e,t){e=e.slice(0),i.ac&&e.sort(te),L.u(e,t,!1,i.Ld)},Ob:"box"},fisheye:{vb:function(e,n,r){e=e.slice(0),i.ac&&e.sort(te),t(e,n,r,.25)},Ob:"polygon"},blackhole:{vb:function(e,n,r){e=e.slice(0),i.ac&&e.sort(te).reverse(),t(e,n,r,1)},Ob:"polygon"}};n.order=n.ordered,n.treemap=n.squarified;var i=e.options;this.i=function(e,t,r){if(0<e.length){if("box"===(r=n[r.relaxationInitializer||r.initializer||i.Ii||"random"]).Ob){var o=M.F(t,{});r.vb(e,o),M.wa(e,M.H(o),t)}else r.vb(e,t,M.u(t,{}));for(r=e.length-1;0<=r;r--){if((o=e[r]).description){var a=M.ta(t,i.qc,i.Ag);o.x=a.x,o.y=a.y}o.attribution&&(a=M.ta(t,i.$d,i.rg),o.x=a.x,o.y=a.y),y.wb(o.group.initialPosition)&&(a=o.group.initialPosition,a=M.ta(t,a.position||"bottomright",a.distanceFromCenter||1),o.x=a.x,o.y=a.y)}}}}function U(e){var t,n=e.options,i=new q(e,this),r=new V(e,this),o={relaxed:i,ordered:r,squarified:r},a=o[e.options.Dc]||i;this.jg=5e-5,e.j.subscribe("model:loaded",(function(e){t=e})),e.j.subscribe("options:changed",(function(e){e.layout&&y.has(o,n.Dc)&&(a=o[n.Dc])})),this.step=function(e,t,n,i){return a.step(e,t,n,i)},this.complete=function(e){a.complete(e)},this.Oe=function(e){return e===t||2*Math.sqrt(e.O.ha/(Math.PI*e.m.length))>=Math.max(n.Be,5e-5)},this.gd=function(e,t){var i=Math.pow(n.La,e.level),r=n.$a*i;i*=n.jd;for(var o=(e=e.m).length-1;0<=o;o--){var s=e[o];a.ce(s,i);var u=s;u.$=0<r?k.i(u.C,r):u.C,u.$&&(M.F(u.$,u.F),M.Ja(u.$,u.O)),s.m&&t.push(s)}},this.fc=function(e){a.fc(e)},this.Eb=function(e){a.Eb(e)}}function q(e,t){function n(e){if(e.m){e=e.m;for(var t=0;t<e.length;t++){var n=e[t];n.kc=n.hc*c.oh}}}function i(e,i){t.Oe(e)&&(e.G||(e.G=k.i(e.C,c.jd*Math.pow(c.La,e.level-1)),e.G&&e.m[0]&&e.m[0].description&&"stab"==c.Tb&&s(e)),e.G&&(l.Eb(e),h.i(r(e),e.G,e.group),e.R=!0,i(e)),n(e))}function r(e){return"stab"===c.Tb&&0<e.m.length&&e.m[0].description?e.m.slice(1):e.m}function o(e){var t=r(e);return z.i(t,e.G),z.u(t,e.G),D.H(e)*Math.sqrt(u.O.ha/e.O.ha)}function a(e){return e<c.Hf||1e-4>e}function s(e){var t=c.Sb/(1+c.Sb),n=M.F(e.G,{}),i={x:n.x,y:0},r=n.y,o=n.o,a=c.ie*Math.pow(c.La,e.level-1),s=o*c.he,u=c.qc;"bottom"==u||0<=u&&180>u?(u=Math.PI,r+=o,o=-1):(u=0,o=1);var l=e.G,h=u,f=0,d=1,p=M.u(l,{}),g=p.ha;t*=g;for(var b=0;f<d&&20>b++;){var v=(f+d)/2;i.y=n.y+n.o*v;var m=M.ua(l,i,h);M.u(m[0],p);var y=p.ha-t;if(.01>=Math.abs(y)/g)break;0<(0==h?1:-1)*y?d=v:f=v}M.F(m[0],n),(n.o<a||n.o>s)&&(i.y=n.o<a?r+o*Math.min(a,s):r+o*s,m=M.ua(e.G,i,u)),e.m[0].C=m[0],e.G=m[1]}var u,l=this,c=e.options,h=new H(e),f=0;e.j.subscribe("model:loaded",(function(e){u=e,f=0})),this.step=function(e,n,s,l){function h(n){if(n.R&&n.xa?function(e){e!==u&&2*Math.sqrt(e.O.ha/(Math.PI*e.m.length))<Math.max(.85*c.Be,t.jg)&&(e.R=!1,e.xa=!1,e.Ia=!0,e.G=null)}(n):n.Ia&&n.C&&i(n,(function(){var t=r(n);z.i(t,n.G),z.u(t,n.G),e(n)})),!n.G||!n.R)return 0;if(n.parent&&n.parent.Z||n.Ea){var h=o(n);l&&l(n),n.Ea=!a(h)&&!s,n.Z=!0}else h=0;return t.gd(n,p),h}for(var d=0,p=[u];0<p.length;)d=Math.max(d,h(p.shift()));var g=a(d);return n&&function(e,t,n){f<e&&(f=e);var i=c.Hf;c.Ad(t?1:1-(e-i)/(f-i||1),t,n),t&&(f=0)}(d,g,s),g},this.complete=function(e){for(var n=[u];0<n.length;){var r=n.shift();if(!r.R&&r.Ia&&r.C&&i(r,e),r.G){if(r.parent&&r.parent.Z||r.Ea){for(var s=1e-4>r.O.ha,l=0;!(a(o(r))||s&&32<l++););r.Z=!0,r.Ea=!1}t.gd(r,n)}}},this.fc=function(e){S.L(e,n)},this.ce=function(e,t){if(e.R){var n=e.G;n&&(e.Fd=n),e.G=k.i(e.C,t),e.G&&e.m[0]&&e.m[0].description&&"stab"==c.Tb&&s(e),n&&!e.G&&(e.Z=!0),e.G&&e.Fd&&M.wa(r(e),e.Fd,e.G)}},this.Eb=function(e){for(var t,n=r(e),i=e.ha,o=t=0;o<n.length;o++)t+=n[o].weight;for(e.Dj=t,e=0;e<n.length;e++)(o=n[e]).Vf=o.w,o.hc=i/Math.PI*(0<t?o.weight/t:1/n.length)}}function V(e,t){function n(e,n){if(t.Oe(e)){if(!e.G||e.parent&&e.parent.Z){var i=a.jd*Math.pow(a.La,e.level-1);e.G=M.H(function(e,t){var n=2*t;return e.x+=t,e.y+=t,e.w-=n,e.o-=n,e}(M.F(e.C,{}),i))}e.G&&(e.R=!0,n(e))}else e.R=!1,S.za(e,(function(e){e.G=null}))}function i(e){if("stab"==a.Tb&&0<e.m.length&&e.m[0].description){var t=e.m.slice(1);!function(e){function t(){i.C=M.H(r),i.x=r.x+r.w/2,i.y=r.y+r.o/2}var n=a.Sb/(1+a.Sb),i=e.m[0],r=M.F(e.G,{}),o=r.o;n=Math.min(Math.max(o*n,a.ie*Math.pow(a.La,e.level-1)),o*a.he);var s=a.qc;"bottom"==s||0<=s&&180>s?(r.o=o-n,e.G=M.H(r),r.y+=o-n,r.o=n,t()):(r.o=n,t(),r.y+=n,r.o=o-n,e.G=M.H(r))}(e)}else t=e.m;a.ac&&t.sort(te),"floating"==a.Tb&&r(t,a.qc,(function(e){return e.description})),r(t,a.$d,(function(e){return e.attribution}));var n=M.F(e.G,{});(s[a.Dc]||L.i)(t,n,!0,a.Ld),e.Ea=!1,e.Z=!0,e.N=!0,e.Fa=!0}function r(e,t,n){for(var i=0;i<e.length;i++){var r=e[i];if(n(r)){e.splice(i,1),"topleft"==t||135<=t&&315>t?e.unshift(r):e.push(r);break}}}var o,a=e.options,s={squarified:L.u,ordered:L.i};e.j.subscribe("model:loaded",(function(e){o=e})),this.step=function(e,t,n){return this.complete(e),t&&a.Ad(1,!0,n),!0},this.complete=function(e){for(var r=[o];0<r.length;){var a=r.shift();(!a.R||a.parent&&a.parent.Z)&&a.Ia&&a.C&&n(a,e),a.G&&((a.parent&&a.parent.Z||a.Ea)&&i(a),t.gd(a,r))}},this.Eb=this.fc=this.ce=y.qa}var W,Z,$,K,Y=new function(){this.u=function(e,t){var n=e.globalAlpha;e.fillStyle="dark"===t?"white":"#1d3557",e.globalAlpha=1*n,e.save(),e.transform(.94115,0,0,.94247,-78.54,-58),e.beginPath(),e.moveTo(86.47,533.3),e.bezierCurveTo(83.52,531.5,83.45,530.6,83.45,488.3),e.bezierCurveTo(83.45,444.6,83.35,445.7,87.34,443.7),e.bezierCurveTo(88.39,443.1,90.5,442.5,92.02,442.4),e.bezierCurveTo(93.54,442.2,113,441.7,135.3,441.4),e.bezierCurveTo(177.9,440.7,179.3,440.7,182.7,443.4),e.bezierCurveTo(185.9,445.9,185.6,445,206.2,510.7),e.bezierCurveTo(207.8,515.8,209.5,521.3,210.1,522.9),e.bezierCurveTo(211.7,528,211.9,531.3,210.6,532.7),e.bezierCurveTo(209.5,534,208.4,534,148.5,534),e.bezierCurveTo(106.4,533.9,87.3,533.7,86.47,533.2),e.closePath(),e.fill(),e.globalAlpha=.8*n,e.beginPath(),e.moveTo(237.3,533.3),e.bezierCurveTo(234.8,532.5,233.1,530.9,231.7,528.1),e.bezierCurveTo(231,526.8,224.6,507,217.4,484.1),e.bezierCurveTo(203.1,438.8,202.6,436.7,205,431.4),e.bezierCurveTo(206.3,428.5,239.2,383.2,242.9,379.3),e.bezierCurveTo(245,377,246.9,376.7,249.7,378.2),e.bezierCurveTo(250.6,378.7,263.1,390.8,277.3,405.2),e.bezierCurveTo(301.1,429.2,303.4,431.6,305.1,435.5),e.bezierCurveTo(306.7,439,306.9,440.4,306.9,445.2),e.bezierCurveTo(306.8,455.3,302.2,526.4,301.5,528.9),e.bezierCurveTo(300.2,533.7,301,533.6,268.3,533.7),e.bezierCurveTo(252.2,533.8,238.3,533.6,237.3,533.3),e.closePath(),e.fill(),e.beginPath(),e.globalAlpha=.05*n,e.moveTo(329,533.3),e.bezierCurveTo(326.2,532.5,323.1,528.8,322.6,525.8),e.bezierCurveTo(322,521.6,327.2,446.1,328.4,442.2),e.bezierCurveTo(330.6,434.9,332.8,432.8,368.5,402.4),e.bezierCurveTo(387,386.7,403.9,372.8,406,371.4),e.bezierCurveTo(413.1,366.7,416,366.2,436.5,365.7),e.bezierCurveTo(456.8,365.2,463.6,365.6,470.2,367.6),e.bezierCurveTo(476.2,369.5,546.1,402.8,549.1,405.3),e.bezierCurveTo(550.4,406.3,552.2,408.7,553.2,410.5),e.lineTo(555,413.9),e.lineTo(555.2,459.5),e.bezierCurveTo(555.3,484.6,555.2,505.8,555,506.5),e.bezierCurveTo(554.4,509.1,548.1,517.9,543.8,522.2),e.bezierCurveTo(537.7,528.3,534.2,530.5,527.8,532.4),e.lineTo(522.3,534),e.lineTo(426.6,533.9),e.bezierCurveTo(371.1,533.9,330.1,533.6,328.9,533.3),e.closePath(),e.fill(),e.globalAlpha=.8*n,e.beginPath(),e.moveTo(87.66,423),e.bezierCurveTo(86.23,422.4,85.02,422,84.97,422),e.bezierCurveTo(84.91,422,84.55,421.1,84.16,419.9),e.bezierCurveTo(83.67,418.6,83.45,404.7,83.45,375.9),e.bezierCurveTo(83.45,328.4,83.27,330.3,88.12,328.1),e.bezierCurveTo(90.22,327.2,101.7,325.6,135.4,321.7),e.bezierCurveTo(159.9,318.8,181.1,316.5,182.5,316.5),e.bezierCurveTo(183.9,316.5,187,317.3,189.4,318.2),e.bezierCurveTo(193.5,319.8,194.7,320.8,210.1,336.2),e.bezierCurveTo(226.6,352.7,229.1,355.7,229.1,360),e.bezierCurveTo(229.1,363,226.8,366.5,212.9,385.4),e.bezierCurveTo(187.3,420.2,189.3,417.7,183.4,420.5),e.lineTo(179.5,422.3),e.lineTo(155.3,422.7),e.bezierCurveTo(89.91,424,90.39,423.9,87.65,423),e.closePath(),e.fill(),e.globalAlpha=.6*n,e.beginPath(),e.moveTo(314.6,415),e.bezierCurveTo(311.4,413.4,213.2,314.6,210.9,310.7),e.bezierCurveTo(208.9,307.2,208.5,303.4,209.9,300),e.bezierCurveTo(211.2,297,241.3,257,244.2,254.4),e.bezierCurveTo(247.3,251.7,252.9,249.7,257.4,249.7),e.bezierCurveTo(261.1,249.7,344.7,255.2,350.8,255.8),e.bezierCurveTo(358.5,256.6,363.1,259.5,366,265.1),e.bezierCurveTo(368.7,270.5,394.3,343.7,394.7,347.2),e.bezierCurveTo(395.1,351.6,393.6,356.1,390.5,359.5),e.bezierCurveTo(389.1,361,375.7,372.6,360.5,385.4),e.bezierCurveTo(326.7,414,327,413.7,324.5,415),e.bezierCurveTo(321.8,416.4,317.4,416.3,314.6,414.9),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(547.9,383.4),e.bezierCurveTo(547.1,383.2,533,376.6,516.5,368.7),e.bezierCurveTo(497.2,359.5,485.7,353.7,484.3,352.4),e.bezierCurveTo(481.6,349.8,480.2,346.5,480.2,342.5),e.bezierCurveTo(480.2,339.2,499.2,237,500.4,233.9),e.bezierCurveTo(502.2,229.1,506.2,225.8,511.3,224.9),e.bezierCurveTo(516.2,224,545.8,222.2,548.2,222.6),e.bezierCurveTo(551.5,223.2,553.7,224.7,555.1,227.3),e.bezierCurveTo(556.2,229.3,556.3,234,556.5,301.9),e.bezierCurveTo(556.6,341.8,556.5,375.7,556.3,377.2),e.bezierCurveTo(555.6,381.8,552,384.4,547.8,383.4),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(418.7,347),e.bezierCurveTo(416,346.1,413.6,344.3,412.3,342.1),e.bezierCurveTo(411.6,341,404.4,321.3,396.3,298.3),e.bezierCurveTo(382,258.1,381.5,256.4,381.5,251.7),e.bezierCurveTo(381.5,248.2,381.8,246.2,382.7,244.7),e.bezierCurveTo(383.4,243.4,389.5,233.9,396.5,223.4),e.bezierCurveTo(412.6,199,411.3,199.9,430.6,198.6),e.bezierCurveTo(445,197.6,449.5,197.9,454.2,200.4),e.bezierCurveTo(460.5,203.7,479.6,217.5,481.3,220.1),e.bezierCurveTo(484.3,224.6,484.3,224.6,473.1,284),e.bezierCurveTo(465.3,325.9,462.4,339.9,461.3,341.8),e.bezierCurveTo(458.7,346.4,457.1,346.7,437.5,347.1),e.bezierCurveTo(428.1,347.3,419.6,347.3,418.7,347),e.closePath(),e.fill(),e.globalAlpha=.05*n,e.beginPath(),e.moveTo(89.33,308.2),e.bezierCurveTo(88.1,307.5,86.5,306.2,85.77,305.2),e.bezierCurveTo(84.42,303.4,84.42,303.4,84.24,202.6),e.bezierCurveTo(84.11,131.7,84.27,100.2,84.77,96.34),e.bezierCurveTo(85.65,89.58,87.91,84.64,92.77,78.81),e.bezierCurveTo(96.86,73.9,103.2,68.42,107.1,66.53),e.bezierCurveTo(108.6,65.81,112.8,64.64,116.5,63.92),e.bezierCurveTo(122.7,62.73,125.4,62.64,148.5,62.81),e.lineTo(173.7,63),e.lineTo(177.4,64.82),e.bezierCurveTo(179.5,65.82,182.1,67.75,183.3,69.12),e.bezierCurveTo(185.6,71.9,228.8,145.1,231.3,150.7),e.bezierCurveTo(234.5,157.7,234.9,160.8,234.9,176.9),e.bezierCurveTo(234.8,201.7,233.8,229.6,232.8,233.2),e.bezierCurveTo(232.3,235,231.1,238.1,230.2,240),e.bezierCurveTo(228.3,243.9,196.9,286.6,192.7,290.9),e.bezierCurveTo(189.8,293.9,184.3,297.1,180.2,298.2),e.bezierCurveTo(177.6,298.9,95.84,309.3,93.04,309.3),e.bezierCurveTo(92.22,309.3,90.55,308.8,89.33,308.1),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(305.7,235.6),e.bezierCurveTo(254.5,232,256.5,232.3,253.9,227.1),e.lineTo(252.4,224.2),e.lineTo(253.1,196.7),e.bezierCurveTo(253.8,170.5,253.8,169.1,255.2,166.3),e.bezierCurveTo(257.7,161.2,256.9,161.4,309.3,151.9),e.bezierCurveTo(354.1,143.8,356.8,143.4,359.7,144.2),e.bezierCurveTo(361.4,144.6,363.8,145.8,365,146.8),e.bezierCurveTo(367.3,148.6,389,179.6,391.9,185.2),e.bezierCurveTo(393.8,188.7,394.1,193.5,392.6,196.9),e.bezierCurveTo(391.5,199.6,370.6,231.4,368.4,233.8),e.bezierCurveTo(365.4,237,362,238.3,356.3,238.5),e.bezierCurveTo(353.5,238.6,330.7,237.3,305.7,235.5),e.closePath(),e.fill(),e.globalAlpha=.2*n,e.beginPath(),e.moveTo(497.1,207.1),e.bezierCurveTo(496.2,206.8,494.4,206,493.2,205.4),e.bezierCurveTo(490,203.8,472.7,191.6,469.7,189),e.bezierCurveTo(467,186.6,465.7,183.2,466.2,180.2),e.bezierCurveTo(466.5,178.1,482.4,138.6,484.9,133.5),e.bezierCurveTo(486.5,130.3,488.4,128.2,490.9,126.8),e.bezierCurveTo(492.6,125.9,496.3,125.7,522.2,125.6),e.lineTo(551.5,125.4),e.lineTo(553.7,127.6),e.bezierCurveTo(555.2,129.1,556,130.5,556.3,132.6),e.bezierCurveTo(556.5,134.2,556.6,149.6,556.5,166.9),e.bezierCurveTo(556.3,195.4,556.2,198.5,555.1,200.4),e.bezierCurveTo(553.1,204.1,551.7,204.4,529.8,206.1),e.bezierCurveTo(509.2,207.7,499.9,207.9,497,207.1),e.closePath(),e.fill(),e.globalAlpha=.2*n,e.beginPath(),e.moveTo(412.5,180.5),e.bezierCurveTo(410.9,179.7,408.7,177.9,407.5,176.4),e.bezierCurveTo(403.5,171.3,380.5,137.2,379.2,134.3),e.bezierCurveTo(377.2,129.6,377.1,126.1,378.9,116.8),e.bezierCurveTo(386.5,77.56,388.4,68.28,389.5,66.46),e.bezierCurveTo(390.1,65.34,391.7,63.83,392.9,63.1),e.bezierCurveTo(395.1,61.84,396.2,61.78,419.4,61.78),e.bezierCurveTo(443.4,61.78,443.7,61.8,446.5,63.25),e.bezierCurveTo(448,64.06,449.9,65.81,450.7,67.14),e.bezierCurveTo(452.3,69.73,468,105.5,470,111.1),e.bezierCurveTo(471.4,114.9,471.6,119.1,470.5,122.3),e.bezierCurveTo(470.1,123.5,465.2,135.8,459.7,149.5),e.bezierCurveTo(446.7,181.4,448.1,179.8,431.5,181.2),e.bezierCurveTo(419,182.2,415.7,182,412.5,180.5),e.closePath(),e.fill(),e.globalAlpha=.4*n,e.beginPath(),e.moveTo(253.6,142.8),e.bezierCurveTo(250.2,141.8,246.6,139.4,244.7,136.7),e.bezierCurveTo(242.1,132.9,207.4,73.28,206.2,70.42),e.bezierCurveTo(205.1,67.89,205,67.1,205.7,65.54),e.bezierCurveTo(207.3,61.54,202.3,61.8,284.4,61.59),e.bezierCurveTo(325.7,61.48,360.8,61.58,362.4,61.81),e.bezierCurveTo(366,62.32,369.3,65.36,369.9,68.75),e.bezierCurveTo(370.4,71.55,362.4,113.9,360.5,118.1),e.bezierCurveTo(359.1,121.3,355,125,351.4,126.4),e.bezierCurveTo(348.9,127.3,267.1,142.3,259.5,143.2),e.bezierCurveTo(257.9,143.4,255.2,143.2,253.6,142.7),e.closePath(),e.fill(),e.globalAlpha=.1*n,e.beginPath(),e.moveTo(493.4,106.8),e.bezierCurveTo(490.3,106,488.2,104.5,486.5,101.7),e.bezierCurveTo(483.8,97.43,471.8,68.81,471.8,66.76),e.bezierCurveTo(471.8,62.64,470.7,62.76,512.1,62.76),e.bezierCurveTo(553.3,62.76,552.3,62.67,554.4,66.68),e.bezierCurveTo(555.2,68.34,555.3,71.23,555.2,85.75),e.lineTo(555,102.8),e.lineTo(551.4,106.4),e.lineTo(534.1,106.8),e.bezierCurveTo(510.7,107.4,495.9,107.4,493.3,106.8),e.closePath(),e.fill(),e.restore(),e.transform(.15905,0,0,.15905,-88.65,443.2),e.globalAlpha=1*n,e.save(),e.beginPath(),e.moveTo(557.4,564.9),e.lineTo(557.4,98),e.lineTo(885.8,98),e.lineTo(885.8,185.1),e.lineTo(650.8,185.1),e.lineTo(650.8,284.7),e.lineTo(824.1,284.7),e.lineTo(824.1,371.6),e.lineTo(650.8,371.6),e.lineTo(650.8,564.9),e.lineTo(557.4,564.9),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1029,568),e.quadraticCurveTo(961.1,568,915.7,522.5),e.quadraticCurveTo(870.2,476.7,870.2,409.2),e.quadraticCurveTo(870.2,341.3,915.7,295.9),e.quadraticCurveTo(961.1,250.4,1029,250.4),e.quadraticCurveTo(1096.8,250.4,1142.3,295.9),e.quadraticCurveTo(1187.7,341.3,1187.7,409.2),e.quadraticCurveTo(1187.7,477.1,1142.3,522.5),e.quadraticCurveTo(1097.3,568.1,1029.3,568.1),e.closePath(),e.moveTo(1028.6,492.6),e.quadraticCurveTo(1064.1,492.6,1086.2,469),e.quadraticCurveTo(1108.3,445,1108.3,409.5),e.quadraticCurveTo(1108.3,374,1086.2,350),e.quadraticCurveTo(1064.1,326.1,1028.3,326.1),e.quadraticCurveTo(993.1,326.1,971,350),e.quadraticCurveTo(948.9,374,948.9,409.5),e.quadraticCurveTo(948.9,445,971,469),e.quadraticCurveTo(993.1,492.6,1028.6,492.6),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1253,291),e.quadraticCurveTo(1312.1,253.6,1390,253.6),e.quadraticCurveTo(1446,253.6,1478.7,284.7),e.quadraticCurveTo(1511.4,315.9,1511.4,378.1),e.lineTo(1511.4,564.9),e.lineTo(1424.2,564.9),e.lineTo(1424.2,540),e.quadraticCurveTo(1386.2,564.9,1355.7,564.9),e.quadraticCurveTo(1293.5,564.9,1262.3,538.5),e.quadraticCurveTo(1231.2,512,1231.2,465.3),e.quadraticCurveTo(1231.2,421.7,1260.4,387.5),e.quadraticCurveTo(1290,353.3,1355.7,353.3),e.quadraticCurveTo(1385.9,353.3,1424.2,371.9),e.lineTo(1424.2,362.6),e.quadraticCurveTo(1423.6,328.4,1374.4,325.2),e.quadraticCurveTo(1318.3,325.2,1287.2,343.9),e.lineTo(1253,291),e.closePath(),e.moveTo(1424.2,471.5),e.lineTo(1424.2,436.3),e.quadraticCurveTo(1411.7,412.3,1365,412.3),e.quadraticCurveTo(1309,418.5,1305.9,455.9),e.quadraticCurveTo(1309,492.9,1365,496),e.quadraticCurveTo(1411.7,496,1424.2,471.5),e.closePath(),e.fill(),e.beginPath(),e.moveTo(1675,365.7),e.lineTo(1675,564.9),e.lineTo(1587.8,564.9),e.lineTo(1587.8,262.5),e.lineTo(1675,253.2),e.lineTo(1675,280.9),e.quadraticCurveTo(1704.2,253.5,1749.7,253.5),e.quadraticCurveTo(1808.8,253.5,1839.9,289.3),e.quadraticCurveTo(1874.2,253.5,1942.6,253.5),e.quadraticCurveTo(2001.8,253.5,2032.9,289.3),e.quadraticCurveTo(2064,325.1,2064,371.8),e.lineTo(2064,564.8),e.lineTo(1976.9,564.8),e.lineTo(1976.9,393.6),e.quadraticCurveTo(1976.9,362.5,1962.9,345.4),e.quadraticCurveTo(1948.8,328.2,1917.4,327.3),e.quadraticCurveTo(1891.6,329.2,1872.6,361.6),e.quadraticCurveTo(1871,371.2,1871,381.2),e.lineTo(1871,564.9),e.lineTo(1783.9,564.9),e.lineTo(1783.9,393.7),e.quadraticCurveTo(1783.9,362.5,1769.9,345.4),e.quadraticCurveTo(1755.9,328.3,1724.4,327.4),e.quadraticCurveTo(1695.8,329.2,1674.9,365.7),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2058,97.96),e.lineTo(2058,185.1),e.lineTo(2213.6,185.1),e.lineTo(2213.6,564.9),e.lineTo(2306.9,564.9),e.lineTo(2306.9,185.1),e.lineTo(2462.5,185.1),e.lineTo(2462.5,97.96),e.lineTo(2057.8,97.96),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2549,287.8),e.quadraticCurveTo(2582.3,253.5,2630.2,253.5),e.quadraticCurveTo(2645.5,253.5,2659.2,256),e.lineTo(2645.5,341.9),e.quadraticCurveTo(2630.2,328.2,2601.9,327.3),e.quadraticCurveTo(2570.1,329.5,2549,373.4),e.lineTo(2549,564.8),e.lineTo(2461.8,564.8),e.lineTo(2461.8,262.5),e.lineTo(2549,253.1),e.lineTo(2549,287.7),e.closePath(),e.fill(),e.beginPath(),e.moveTo(2694,409.2),e.quadraticCurveTo(2694,340.7,2737.5,297.1),e.quadraticCurveTo(2781.1,253.5,2849.6,253.5),e.quadraticCurveTo(2918.1,253.5,2958.5,297.1),e.quadraticCurveTo(2999,340.6,2999,409.2),e.lineTo(2999,440.3),e.lineTo(2784.2,440.3),e.quadraticCurveTo(2787.3,465.2,2806,479.2),e.quadraticCurveTo(2824.7,493.2,2849.6,493.2),e.quadraticCurveTo(2893.1,493.2,2927.4,468.3),e.lineTo(2977.2,518.1),e.quadraticCurveTo(2943,564.8,2849.6,564.8),e.quadraticCurveTo(2781.1,564.8,2737.5,521.2),e.quadraticCurveTo(2693.9,477.6,2693.9,409.1),e.closePath(),e.moveTo(2911.9,378),e.quadraticCurveTo(2911.9,353.1,2893.2,339.1),e.quadraticCurveTo(2874.5,325.1,2849.6,325.1),e.quadraticCurveTo(2824.7,325.1,2806,339.1),e.quadraticCurveTo(2787.3,353.1,2787.3,378),e.lineTo(2911.8,378),e.closePath(),e.fill(),e.beginPath(),e.moveTo(3052,409.2),e.quadraticCurveTo(3052,340.7,3095.5,297.1),e.quadraticCurveTo(3139.1,253.5,3207.6,253.5),e.quadraticCurveTo(3276.1,253.5,3316.5,297.1),e.quadraticCurveTo(3357,340.6,3357,409.2),e.lineTo(3357,440.3),e.lineTo(3142.2,440.3),e.quadraticCurveTo(3145.3,465.2,3164,479.2),e.quadraticCurveTo(3182.7,493.2,3207.6,493.2),e.quadraticCurveTo(3251.1,493.2,3285.4,468.3),e.lineTo(3335.2,518.1),e.quadraticCurveTo(3301,564.8,3207.6,564.8),e.quadraticCurveTo(3139.1,564.8,3095.5,521.2),e.quadraticCurveTo(3051.9,477.6,3051.9,409.1),e.closePath(),e.moveTo(3269.9,378),e.quadraticCurveTo(3269.9,353.1,3251.2,339.1),e.quadraticCurveTo(3232.5,325.1,3207.6,325.1),e.quadraticCurveTo(3182.7,325.1,3164,339.1),e.quadraticCurveTo(3145.3,353.1,3145.3,378),e.lineTo(3269.8,378),e.closePath(),e.fill(),e.restore()}};function J(e,n){function i(e,t){var n=e.O.r,i=n/15,r=.5*n/15;n/=5;var o=e.O.x;e=e.O.y,t.fillRect(o-r,e-r,i,i),t.fillRect(o-r-n,e-r,i,i),t.fillRect(o-r+n,e-r,i,i)}function r(e,t,n,i){null===e&&n.clearRect(0,0,D,B);var r,o=Array(te.length);for(r=te.length-1;0<=r;r--)o[r]=te[r].na(n,i);for(r=te.length-1;0<=r;r--)o[r]&&te[r].before(n,i);for(E.rc([n,z],(function(i){var r;if(null!==e){for(n.save(),n.globalCompositeOperation="destination-out",n.fillStyle=n.strokeStyle="rgba(255, 255, 255, 1)",r=e.length-1;0<=r;r--){var a=e[r],u=a.C;u&&(n.save(),n.beginPath(),a.Ib(n),s.Ud(n,u),n.fill(),0<(a=q.$a*Math.pow(q.La,a.level-1))&&(n.lineWidth=a/2,n.stroke()),n.restore())}n.restore()}if(i=i.scale,0!==t.length){for(r={},u=te.length-1;0<=u;u--)te[u].ng(r);for(a=ee.length-1;0<=a;a--)if(r[(u=ee[a]).id]){var l=u.Kd;for(u=0;u<t.length;u++){var c=t[u];!c.parent||c.parent.xa&&c.parent.R?l(c,i):c.aa.clear()}}}for(r=te.length-1;0<=r;r--)a=te[r],o[r]&&a.Nd(t,n,i)})),r=te.length-1;0<=r;r--)o[r]&&te[r].after(n);q.Zc&&(n.canvas.style.opacity=.99,setTimeout((function(){n.canvas.style.opacity=1}),1))}function u(){function e(t,n,i){t.sb=Math.floor(1e3*t.scale)-i*n,0<t.opacity&&!t.open&&n++;var r=t.m;if(r)for(var o=r.length-1;0<=o;o--)t.W&&e(r[o],n,i)}var t=null,n=null,i=null;return E.rc([],(function(r){!function(e){b===w?e<.9*G&&(b=v,x=A,h()):e>=G&&(b=w,x=T,h())}(r.scale);var o=!1;S.L(I,(function(e){e.W&&(o=e.vd()||o,e.cc(),e.Ma=H.i(e)||e.Ma)})),o&&(I.N=!0);var a="onSurfaceDirty"===q.Lg;S.fd(I,(function(e){e.parent&&e.parent.Z&&(e.aa.clear(),e.Ma=!0,a||(e.oc=!0,e.Qb.clear())),a&&(e.oc=!0,e.Qb.clear())}));var s=r.scale*r.scale;if(S.fd(I,(function(e){if(e.R){for(var t=e.m,n=0;n<t.length;n++)if(5<t[n].O.ha*s)return void(e.X=!0);e.X=!1}})),function(e){I.Y=!0,S.fd(I,(function(t){if(t.W&&t.X&&t.xa&&t.R&&(I.N||t.Z||t.Vd)){t.Vd=!1;var n=t.m,i={x:0,y:0,w:0,o:0},r=!!t.G;if(1<D/e.w){var o;for(o=n.length-1;0<=o;o--)n[o].Y=!1;if(t.Y&&r)for(o=n.length-1;0<=o;o--)if(1!==(t=n[o]).scale&&(t.Jb(e,i),i.w=e.w/t.scale,i.o=e.o/t.scale),!1===t.Y&&t.C){var a=(r=t.C).length;if(M.sa(t.C,1===t.scale?e:i))t.Y=!0;else for(var s=0;s<a;s++)if(M.Vc(r[s],r[(s+1)%a],1===t.scale?e:i)){t.Y=!0,t.J&&(t=t.J[s])&&(n[t.index].Y=!0);break}}}else for(o=0;o<n.length;o++)n[o].Y=r}}))}(r),i=[],S.tc(I,(function(e){if(e.parent.X&&e.Y&&e.W){i.push(e);for(var t=e.parent;t!==I&&(t.open||0===t.opacity);)t=t.parent;t!==I&&.02>Math.abs(t.scale-e.scale)&&(e.scale=Math.min(e.scale,t.scale))}})),e(I,0,"flattened"===q.mb?-1:1),i.sort((function(e,t){return e.sb-t.sb})),l())t=i,n=null;else{var u={},c={},f="none"!=q.ld&&q.$a<q.ab/2,d=q.$a<q.yc/2+q.kd*q.De.a;S.L(I,(function(e){if(e.W&&!e.description&&(e.Z||e.N||e.Fc&&e.parent.X&&e.Ma)){var t,n,i,r=[e],o=e.J||e.parent.m;if(f)for(t=0;t<o.length;t++)(n=o[t])&&r.push(n);else if(d)if(!e.selected&&e.Sa){for(n=!0,t=0;t<o.length;t++)o[t]?r.push(o[t]):n=!1;!n&&1<e.level&&r.push(e.parent)}else for(t=0;t<o.length;t++)(n=o[t])&&n.selected&&r.push(n);for(t=e.parent;t!=I;)t.selected&&(i=t),t=t.parent;for(i&&r.push(i),t=0;t<r.length;t++){for(e=(i=r[t]).parent;e&&e!==I;)0<e.opacity&&(i=e),e=e.parent;c[i.id]=!0,S.za(i,(function(e){u[e.id]=!0}))}}})),t=i.filter((function(e){return u[e.id]})),n=t.filter((function(e){return c[e.id]}))}})),function(){var e=!1;q.Gf&&S.L(I,(function(t){if(t.W&&0!==t.pa.a&&1!==t.pa.a)return e=!0,!1})),e?(S.sc(I,(function(e){if(e.W&&(e.opacity!==e.Jc||e.Fa)){var t=e.m;if(t){for(var n=0,i=t.length-1;0<=i;i--)n=Math.max(n,t[i].Ec);e.Ec=n+e.opacity*e.pa.a}else e.Ec=e.opacity*e.pa.a}})),S.L(I,(function(e){if(e.W&&(e.opacity!==e.Jc||e.Fa)){for(var t=e.Ec,n=e;(n=n.parent)&&n!==I;)t+=n.opacity*n.pa.a*q.Ef;e.$c=0<t?1-Math.pow(1-e.pa.a,1/t):0,e.Jc=e.opacity}}))):S.L(I,(function(e){e.W&&(e.$c=1,e.Jc=-1)}))}(),{ag:t,$f:n,Y:i}}function l(){var e=I.Z||I.N||"none"==q.Ke;if(!e&&!I.empty()){var t=I.m[0].scale;S.L(I,(function(n){if(n.W&&n.Y&&n.scale!==t)return e=!0,!1}))}return!e&&0<q.xe&&1!=q.Pa&&S.L(I,(function(t){if(t.W&&0<t.ja)return e=!0,!1})),"accurate"==q.Ke&&(!(e=(e=e||0===q.$a)||"none"!=q.ld&&q.$a<q.ab/2)&&q.$a<q.yc/2+q.kd*q.De.a&&S.L(I,(function(t){if(t.W&&(t.selected&&!t.Sa||!t.selected&&t.Sa))return e=!0,!1}))),e}function h(){function e(e,n,i,r,o){function a(e,t,n,i,r){return e[i]&&(t-=n*p[i],e[i]=!1,r&&(t+=n*p[r],e[r]=!0)),t}switch(e=y.extend({},e),i){case"never":e.labelPlainFill=!1;break;case"always":case"auto":e.labelPlainFill=!0}if(q.xc)switch(r){case"never":e.contentDecoration=!1;break;case"always":case"auto":e.contentDecoration=!0}else e.contentDecoration=!1;var s=0;return y.Aa(e,(function(e,t){e&&(s+=n*p["contentDecoration"===t?"labelPlainFill":t])})),e.polygonExposureShadow=t,(s+=2*p.polygonExposureShadow)<=o||(s=a(e,s,2,"polygonExposureShadow"))<=o||(s=a(e,s,n,"polygonGradientFill","polygonPlainFill"))<=o||(s=a(e,s,n,"polygonGradientStroke"))<=o||(s=a(e,s,n,"polygonPlainStroke"))<=o||"auto"===r&&(s=a(e,s,n,"contentDecoration"))<=o||"auto"===i&&(s=a(e,s,n,"labelPlainFill")),e}var t=b===v,n=0,i=0;S.ne(I,(function(e){var t=1;S.L(e,(function(){t++})),n+=t,i=Math.max(i,t)}));var r={};switch(q.Ug){case"plain":r.polygonPlainFill=!0;break;case"gradient":r.polygonPlainFill=!t,r.polygonGradientFill=t}switch(q.ld){case"plain":r.polygonPlainStroke=!0;break;case"gradient":r.polygonPlainStroke=!t,r.polygonGradientStroke=t}P=e(r,n,q.gj,q.ej,q.fj),R=e(r,2*i,"always","always",q.Eg),F=e(r,n,"always","always",q.Dg)}function f(e){return function(t,n){return t===b?!0===P[e]:!0===(n?R:F)[e]}}function d(e,t){return function(n,i){return e(n,i)&&t(n,i)}}var p,g,b,v,w,_,x,A,T,k,z,D,B,L,E,j,O,I,N,P,F,R,G=t.Se()?50:1e4,H=new X(e),U=new Q(e),q=e.options;e.j.subscribe("stage:initialized",(function(e,t,n,i){D=n,B=i,g=(L=e).dc("wireframe",q.nb,!1),v=g.getContext("2d"),w=new o(v),_=L.dc("hifi",q.B,!1),A=_.getContext("2d"),T=new o(A),b=v,x=A,v.B=q.nb,w.B=q.nb,A.B=q.B,T.B=q.B,k=L.dc("tmp",Math.max(q.B,q.nb),!0),(z=k.getContext("2d")).B=1,[v,A,z].forEach((function(e){e.scale(e.B,e.B)}))})),e.j.subscribe("stage:resized",(function(e,t,n,i){D=n,B=i,[v,A,z].forEach((function(e){e.scale(e.B,e.B)}))})),e.j.subscribe("model:loaded",(function(t){N=!0,function e(t){var n=0;if(!t.empty()){for(var i=t.m,r=i.length-1;0<=r;r--)n=Math.max(n,e(i[r]));n+=1}return t.Sf=n}(I=t),h(),e.j.D("render:renderers:resolved",P,R,F)}));var V="groupFillType groupStrokeType wireframeDrawMaxDuration wireframeLabelDrawing wireframeContentDecorationDrawing finalCompleteDrawMaxDuration finalIncrementalDrawMaxDuration groupContentDecorator".split(" "),J=["groupLabelLightColor","groupLabelDarkColor","groupLabelColorThreshold","groupUnexposureLabelColorThreshold"];e.j.subscribe("options:changed",(function(e){function t(e,t,n,i){L.Hi(e,n),t.B=n,i&&t.scale(n,n)}e.dataObject||(y.bb(e,V)&&h(),y.bb(e,J)&&S.L(I,(function(e){e.hd=-1})));var n=y.has(e,"pixelRatio");e=y.has(e,"wireframePixelRatio"),(n||e)&&(n&&t(_,x,q.B,!0),e&&t(g,b,q.nb,!0),t(k,z,Math.max(q.B,q.nb),!1))})),e.j.subscribe("zoom:initialized",(function(e){E=e})),e.j.subscribe("timeline:initialized",(function(e){j=e})),e.j.subscribe("api:initialized",(function(e){O=e}));var ee=[{id:"offsetPolygon",Kd:function(e){if((e.selected||0<e.opacity&&!1===e.open||!e.X)&&e.aa.Ga()){var t=e.aa;if(t.clear(),e.$){var n=e.$,i=q.Gg;0<i?s.Ti(t,n,e.parent.O.r/32,Math.min(1,i*Math.pow(1-q.Hg*i,e.Sf))):s.Ud(t,n)}e.Dd=!0}}},{id:"label",Kd:function(e){e.Ma&&e.Fc&&H.u(e)}},{id:"custom",Kd:function(t,n){if(t.$&&(0<t.opacity&&(!1===t.open||!0===t.selected)||!t.X)&&t.oc&&e.options.xc&&!t.attribution){var i={};O.Xc(i,t),O.Yc(i,t),O.Wc(i,t,!0),i.context=t.Qb,i.polygonContext=t.aa,i.labelContext=t.Bc,i.shapeDirty=t.Dd,i.viewportScale=n,n={groupLabelDrawn:!0,groupPolygonDrawn:!0},e.options.Kg(e.Cd,i,n),t.Te=n.groupLabelDrawn,t.Ed=n.groupPolygonDrawn,t.Dd=!1,t.oc=!1}}}].reverse(),te=[new function(e){var t=Array(e.length);this.Nd=function(n,i,r){if(0!==n.length){var o,a=[],s=n[0].sb;for(o=0;o<n.length;o++){var u=n[o];u.sb!==s&&(a.push(o),s=u.sb)}a.push(o);for(var l=s=0;l<a.length;l++){for(var c=a[l],h=e.length-1;0<=h;h--)if(t[h]){var f=e[h];for(i.save(),o=s;o<c;o++)u=n[o],i.save(),u.Ib(i),f.kb.call(f,u,i,r),i.restore();f.Wa.call(f,i,r),i.restore()}s=c}}},this.na=function(n,i){for(var r=!1,o=e.length-1;0<=o;o--)t[o]=e[o].na(n,i),r|=t[o];return r},this.before=function(n,i){for(var r=e.length-1;0<=r;r--)if(t[r]){var o=e[r];o.before.call(o,n,i)}},this.after=function(n){for(var i=e.length-1;0<=i;i--)if(t[i]){var r=e[i];r.after.call(r,n)}},this.ng=function(n){for(var i=e.length-1;0<=i;i--){var r=e[i];if(t[i])for(var o=r.Ra.length-1;0<=o;o--)n[r.Ra[o]]=!0}}}([{Ra:["offsetPolygon"],na:f("polygonExposureShadow"),before:function(e){z.save(),z.scale(e.B,e.B)},after:function(){z.restore()},rb:function(){},Wa:function(e){this.Rf&&(this.Rf=!1,e.save(),e.setTransform(1,0,0,1,0,0),e.drawImage(k,0,0,e.canvas.width,e.canvas.height,0,0,e.canvas.width,e.canvas.height),e.restore(),z.save(),z.setTransform(1,0,0,1,0,0),z.clearRect(0,0,k.width,k.height),z.restore())},kb:function(e,t,n){if(!(e.open&&e.X||e.aa.Ga())){var i=q.xe*e.opacity*e.ja*("flattened"===q.mb?1-e.parent.ja:(1-e.Cb)*e.parent.Cb)*(1.1<=q.Pa?1:(q.Pa-1)/.1);0<i&&(z.save(),z.beginPath(),e.Ib(z),e.aa.Na(z),z.shadowBlur=n*t.B*i,z.shadowColor=q.Mg,z.fillStyle="rgba(0, 0, 0, 1)",z.globalCompositeOperation="source-over",z.globalAlpha=e.opacity,z.fill(),z.shadowBlur=0,z.shadowColor="transparent",z.globalCompositeOperation="destination-out",z.fill(),z.restore(),this.Rf=!0)}}},{Ra:["offsetPolygon"],na:function(){return!0},before:function(){function e(e){var n=e.pa,i=e.ub,r=e.selected,o=(n.h+(i?q.Yg:0)+(r?q.ph:0))%360,a=t(n.l*e.va+(i?q.Zg:0)+(r?q.qh:0));return n=t(n.s*e.saturation+(i?q.$g:0)+(r?q.rh:0)),(e=e.we).h=o,e.s=n,e.l=a,e}function t(e){return 100<e?100:0>e?0:e}var n=[{type:"fill",na:f("polygonPlainFill"),Pc:function(t,n){n.fillStyle=c.H(e(t))}},{type:"fill",na:f("polygonGradientFill"),Pc:function(n,i){var r=n.O.r,o=e(n);r=i.createRadialGradient(n.x,n.y,0,n.x,n.y,r*q.Qg);var a=o.l,s=q.Og;r.addColorStop(0,c.i((o.h+q.Ng)%360,t(o.s+q.Pg),t(a+s))),a=o.l,s=q.Sg,r.addColorStop(1,c.i((o.h+q.Rg)%360,t(o.s+q.Tg),t(a+s))),n.aa.Na(i),i.fillStyle=r}},{type:"stroke",na:d(f("polygonPlainStroke"),(function(){return 0<q.ab})),Pc:function(e,n){var i=e.pa,r=e.ub,o=e.selected,a=(i.h+q.He+(r?q.ye:0)+(o?q.Ee:0))%360,s=t(i.s*e.saturation+q.Je+(r?q.Ae:0)+(o?q.Ge:0));i=t(i.l*e.va+q.Ie+(r?q.ze:0)+(o?q.Fe:0)),n.strokeStyle=c.i(a,s,i),n.lineWidth=q.ab*Math.pow(q.La,e.level-1)}},{type:"stroke",na:d(f("polygonGradientStroke"),(function(){return 0<q.ab})),Pc:function(e,n){var i=e.O.r*q.xh,r=e.pa,o=Math.PI*q.th/180;i=n.createLinearGradient(e.x+i*Math.cos(o),e.y+i*Math.sin(o),e.x+i*Math.cos(o+Math.PI),e.y+i*Math.sin(o+Math.PI));var a=e.ub,s=e.selected;o=(r.h+q.He+(a?q.ye:0)+(s?q.Ee:0))%360;var u=t(r.s*e.saturation+q.Je+(a?q.Ae:0)+(s?q.Ge:0));r=t(r.l*e.va+q.Ie+(a?q.ze:0)+(s?q.Fe:0)),a=q.vh,i.addColorStop(0,c.i((o+q.uh)%360,t(u+q.wh),t(r+a))),a=q.zh,i.addColorStop(1,c.i((o+q.yh)%360,t(u+q.Ah),t(r+a))),n.strokeStyle=i,n.lineWidth=q.ab*Math.pow(q.La,e.level-1)}}],i=Array(n.length);return function(e,t){for(var r=n.length-1;0<=r;r--)i[r]=n[r].na(e,t);this.Xi=n,this.vg=i}}(),after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){if(!(!e.Ed||(0===e.opacity||e.open)&&e.X||e.aa.Ga()||!q.je&&e.description)){var n=this.Xi,i=this.vg;t.beginPath(),e.aa.Na(t);for(var r=!1,o=!1,a=n.length-1;0<=a;a--){var s=n[a];if(i[a])switch(s.Pc(e,t),s.type){case"fill":r=!0;break;case"stroke":o=!0}}n=(e.X?e.opacity:1)*e.pa.a,i=!e.empty(),a=q.Gf?e.$c:1,r&&(e=i&&e.X&&e.R&&e.m[0].W?1-e.m.reduce((function(e,t){return e+t.ra*t.Hd}),0)/e.m.length*(1-q.Ef):1,t.globalAlpha=n*e*a,W(t)),o&&(t.globalAlpha=n*(i?q.Xh:1)*a,t.closePath(),Z(t),t.stroke())}}},{Ra:["offsetPolygon"],na:function(){return 0<q.yc},before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){if(e.Ed&&e.selected&&!e.aa.Ga()){t.globalAlpha=e.Da,t.beginPath();var i=Math.pow(q.La,e.level-1);t.lineWidth=q.yc*i,t.strokeStyle=q.sh;var r=q.kd;0<r&&(t.shadowBlur=r*i*n*t.B,t.shadowColor=q.Ce),e.aa.Na(t),t.closePath(),t.stroke()}}},{Ra:[],na:function(){return!0},before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){e.attribution&&!e.aa.Ga()&&function(n,i,r){var o=M.Ka(e.$,e.O,n/i);o=Math.min(Math.min(.9*o,.5*e.F.o)/i,.5*e.F.w/n),t.save(),t.translate(e.x,e.y),t.globalAlpha=e.opacity*e.ca,t.scale(o,o),t.translate(-n/2,-i/2),r(t),t.restore()}(Y.i.width,Y.i.height,(function(e){Y.u(e,q.ae)}))}},{Ra:[],na:function(e,t){return function(n,i){return e(n,i)||t(n,i)}}(f("labelPlainFill"),d(f("contentDecoration"),(function(){return q.xc}))),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){(0<e.opacity&&0<e.ca&&!e.open||!e.X)&&!e.aa.Ga()&&(e.Cc=e.oa&&e.oa.ka&&q.B*e.oa.fontSize*e.scale*n>=q.mh,"auto"===e.pd?!q.je&&e.description?e.fb=e.parent.fb:(t=(n=e.we).h+(n.s<<9)+(n.l<<16),e.hd!==t&&(n=c.T(n),e.fb=n>(0>e.ja?q.Bh:q.ah)?q.bh:q.lh,e.hd=t)):e.fb=e.pd)}},{Ra:["custom"],na:d(f("contentDecoration"),(function(){return q.xc})),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t){!(0<e.opacity&&0<e.ca&&!e.open||!e.X)||e.Qb.Ga()||e.aa.Ga()||(e.Cc||void 0===e.oa?(t.globalAlpha=e.ca*(e.X?e.opacity:1)*(e.empty()?1:q.Ff),t.fillStyle=e.fb,t.strokeStyle=e.fb,e.Qb.Na(t)):i(e,t))}},{Ra:["label"],na:f("labelPlainFill"),before:function(){},after:function(){},rb:function(){},Wa:function(){},kb:function(e,t,n){e.Te&&e.Fc&&(0<e.opacity&&0<e.ca&&!e.open||!e.X)&&!e.aa.Ga()&&e.oa&&(t.fillStyle=e.fb,t.globalAlpha=e.ca*(e.X?e.opacity:1)*(e.empty()?1:q.Ff),e.Cc?K(e,t,n):i(e,t))}}].reverse())];this.M=function(){p=C((function(){return a.estimate()}),"CarrotSearchFoamTree",12096e5)({version:"3.5.0",build:"bugfix/3.5.x/e3b91c8e",brandingAllowed:!1}),U.M()},this.clear=function(){b.clearRect(0,0,D,B),x.clearRect(0,0,D,B)};var ne=!1,ie=void 0;this.u=function(e){ne?ie=e:e()},this.Nd=function(){function e(){window.clearTimeout(t),ne=!0,t=setTimeout((function(){if(ne=!1,function(){if(q.B!==q.nb)return!0;var e="polygonPlainFill polygonPlainStroke polygonGradientFill polygonGradientStroke labelPlainFill contentDecoration".split(" ");S.L(I,(function(t){if(t.W&&t.U)return e.push("polygonExposureShadow"),!1}));for(var t=e.length-1;0<=t;t--){var n=e[t];if(!!P[n]!=!!R[n])return!0}return!1}()){var e=!l();r(null,i.Y,x,e),y.defer((function(){re.Ui(),ie&&(ie(),ie=void 0)}))}else ie&&(ie(),ie=void 0)}),Math.max(q.hj,3*n.Wf.sd,3*n.Wf.rd))}var t,i;return function(t){$(U);var n=null!==(i=u()).$f,o=0<L.$b("hifi"),a=o&&(n||!t);t=n||N||!t,N=!1,o&&!a&&re.Vi(),r(i.$f,i.ag,a?x:b,t),S.za(I,(function(e){e.Z=!1,e.N=!1,e.Sa=!1})),a||e(),q.Af(n)}}(),this.i=function(e){e=e||{},$(U),I.N=!0;var t=u(),n=q.B;try{var i=y.I(e.pixelRatio,q.B);q.B=i;var a=L.dc("export",i,!0),s=a.getContext("2d");b===w&&(s=new o(s)),s.scale(i,i);var l=y.has(e,"backgroundColor");l&&(s.save(),s.fillStyle=e.backgroundColor,s.fillRect(0,0,D,B),s.restore()),r(l?[]:null,t.ag,s,!0)}finally{q.B=n}return a.toDataURL(y.I(e.format,"image/png"),y.I(e.quality,.8))};var re=function(){function e(e,t,i,r,o,a){function s(e,t,n,i){return j.K.A({opacity:L.$b(e)}).fa({duration:n,P:{opacity:{end:t,easing:i}},ba:function(){L.$b(e,this.opacity)}}).done()}var u=y.od(L.$b(e),t),l=y.od(L.$b(r),o);if(!u||!l){for(var c=n.length-1;0<=c;c--)n[c].stop();return n=[],u||n.push(s(e,t,i,m.Gb)),l||n.push(s(r,o,a,m.Tf)),j.K.A({}).Qa(n).start()}}var t,n=[];return{Vi:function(){q.Zc?1!==g.style.opacity&&(g.style.visibility="visible",_.style.visibility="hidden",g.style.opacity=1,_.style.opacity=0):t&&t.xb()||(t=e("wireframe",1,q.se,"hifi",0,q.se))},Ui:function(){q.Zc?(_.style.visibility="visible",g.style.visibility="hidden",g.style.opacity=0,_.style.opacity=1):e("hifi",1,q.dg,"wireframe",0,q.dg)}}}();return $=function(e){e.apply()},W=function(e){e.fill()},Z=function(e){e.stroke()},this}function X(e){function t(e){void 0!==e.groupLabelFontFamily&&(r.fontFamily=e.groupLabelFontFamily),void 0!==e.groupLabelFontStyle&&(r.fontStyle=e.groupLabelFontStyle),void 0!==e.groupLabelFontVariant&&(r.fontVariant=e.groupLabelFontVariant),void 0!==e.groupLabelFontWeight&&(r.fontWeight=e.groupLabelFontWeight),void 0!==e.groupLabelLineHeight&&(r.lineHeight=e.groupLabelLineHeight),void 0!==e.groupLabelHorizontalPadding&&(r.cb=e.groupLabelHorizontalPadding),void 0!==e.groupLabelVerticalPadding&&(r.Ua=e.groupLabelVerticalPadding),void 0!==e.groupLabelMaxTotalHeight&&(r.ib=e.groupLabelMaxTotalHeight),void 0!==e.groupLabelMaxFontSize&&(r.hb=e.groupLabelMaxFontSize)}var n,i=e.options,r={},o={},a={groupLabel:""},s={};e.j.subscribe("api:initialized",(function(e){n=e})),e.j.subscribe("options:changed",t),t(e.Cd),this.i=function(e){if(!e.$)return!1;var t=e.group.label;return i.eh&&!e.attribution&&(a.labelText=t,n.nc(i.dh,e,a),t=a.labelText),e.Ue=t,e.qd!==t},this.u=function(e){var t=e.Ue;if(e.qd=t,e.Bc.clear(),e.oa=void 0,e.$&&!y.Ne(t)&&("flattened"!==i.mb||e.empty()||!e.R||!e.m[0].W)){var a=B,u=a.de;if(i.kh){s.fontFamily=r.fontFamily,s.fontStyle=r.fontStyle,s.fontVariant=r.fontVariant,s.fontWeight=r.fontWeight,s.lineHeight=r.lineHeight,s.horizontalPadding=r.cb,s.verticalPadding=r.Ua,s.maxTotalTextHeight=r.ib,s.maxFontSize=r.hb,n.nc(i.jh,e,s),o.fontFamily=s.fontFamily,o.fontStyle=s.fontStyle,o.fontVariant=s.fontVariant,o.fontWeight=s.fontWeight,o.lineHeight=s.lineHeight,o.cb=s.horizontalPadding,o.Ua=s.verticalPadding,o.ib=s.maxTotalTextHeight,o.hb=s.maxFontSize;var l=o}else l=r;e.oa=u.call(a,l,e.Bc,t,e.$,e.F,e.O,!1,!1,e.Mh,e.O.ha,i.nh,e.Ma)}e.Ma=!1},K=this.H=function(e,t){e.Bc.Na(t)}}function Q(e){function t(e,t){var n,i=e.m,r=i.length,a=o.O.r;for(n=0;n<r;n++){var s=i[n];s.tb=(180*(Math.atan2(s.x-e.x,s.y-e.y)+t)/Math.PI+180)/360,s.wc=Math.min(1,Math.sqrt(M.i(s,e))/a)}}function n(e,t){var n=(e=e.m).length;if(1===n||2===n&&e[0].description)e[0].tb=.5;else{var i=0,r=Number.MAX_VALUE,o=Math.sin(t),a=Math.cos(t);for(t=0;t<n;t++){var s=e[t],u=s.x*o+s.y*a;i<u&&(i=u),r>u&&(r=u),s.tb=u,s.wc=1}for(t=0;t<n;t++)(s=e[t]).tb=(s.tb-r)/(i-r)}}function i(e,t,n,i){return(t=t[i])+(n[i]-t)*e}var r,o,a={radial:t,linear:n},s=e.options,u={groupColor:null,labelColor:null};return e.j.subscribe("model:loaded",(function(e){o=e})),e.j.subscribe("api:initialized",(function(e){r=e})),this.M=function(){},this.apply=function(){function e(e,t,n,i){var r=l(e+n*i);return r+t*((e=l(e-n*(1-i)))-r)}function l(e){return 0>e?0:100<e?100:e}var h=a[s.vi]||t,f=n,d=s.Fi,p=s.yi,g=s.Ig,b=s.Jg,v=s.zi,m=s.Di;!function t(n){if(n.R&&n.xa){var o,a=n.m;if(n.Z||n.Fa||b){for(0===n.level?h(n,s.wi*Math.PI/180):f(n,s.Ai*Math.PI/180),o=a.length-1;0<=o;o--){var l=a[o];l.Fa=!0;var C=l.tb,w=l.ve;if(0===n.level)var _=i(C,d,p,"h"),x=(m+(1-m)*l.wc)*i(C,d,p,"s"),A=(1+(0>l.ja?v*(l.ja+1):v)*(1-l.wc))*i(C,d,p,"l"),S=i(C,d,p,"a");else _=(A=n.pa).h,x=A.s,A=e(A.l,C,s.Bi,s.Ci),S=n.ve.a;w.h=_,w.s=x,w.l=A,w.a=S,_=l.pa,l.attribution?(_.h=0,_.s=0,_.l="light"==s.ae?90:10,_.a=1):(_.h=w.h,_.s=w.s,_.l=w.l,_.a=w.a),b&&!l.attribution&&(u.groupColor=_,u.labelColor="auto",r.nc(g,l,u,(function(e){e.ratio=C})),l.pa=c.u(u.groupColor),l.pa.a=y.has(u.groupColor,"a")?u.groupColor.a:1,"auto"!==u.labelColor&&(l.pd=c.wa(u.labelColor)))}n.Fa=!1}for(o=a.length-1;0<=o;o--)t(a[o])}}(o)},this}function ee(){this.kc=this.Yd=this.hc=this.Vf=this.w=this.cg=this.weight=this.y=this.x=this.id=0,this.C=this.parent=this.m=null,this.F={x:0,y:0,w:0,o:0},this.J=null,this.qd=this.Ue=void 0,this.Sc=!1,this.wc=this.tb=0,this.ve={h:0,s:0,l:0,a:0,model:"hsla"},this.pa={h:0,s:0,l:0,a:0,model:"hsla"},this.we={h:0,s:0,l:0,model:"hsl"},this.hd=-1,this.pd="auto",this.fb="#000",this.Sf=this.level=this.nd=this.index=0,this.attribution=!1,this.ha=this.Ze=0,this.Y=!1,this.$=null,this.O={x:0,y:0,ha:0,r:0},this.Fd=this.G=null,this.Fc=this.W=this.Sa=this.oc=this.Vd=this.Dd=this.Ma=this.Fa=this.N=this.Z=this.Ea=this.xa=this.R=this.Ia=!1,this.saturation=this.va=this.Da=this.ca=this.opacity=this.scale=1,this.ra=0,this.Hd=1,this.Cb=this.ja=this.yb=0,this.description=this.selected=this.ub=this.Bd=this.open=this.U=!1,this.sb=0,this.Te=this.Ed=this.X=!0,this.oa=void 0,this.Cc=!1,this.Bc=new r,this.aa=new r,this.Qb=new r,this.Mh=B.Zh(),this.Ec=0,this.$c=1,this.Jc=-1,this.empty=function(){return!this.m||0===this.m.length};var e=[];this.mc=function(t){e.push(t)},this.Nc=function(t){y.If(e,t)};var t={scale:1};this.vd=function(){var n=!1;this.scale=1;for(var i=0;i<e.length;i++)n=e[i].Ve(this,t)||n,this.scale*=t.scale;return n},this.Ib=function(t){for(var n=0;n<e.length;n++)e[n].Ib(this,t)},this.transformPoint=function(t,n){for(n.x=t.x,n.y=t.y,t=0;t<e.length;t++)e[t].transformPoint(this,n,n);return n},this.Jb=function(t,n){for(n.x=t.x,n.y=t.y,t=0;t<e.length;t++)e[t].Jb(this,n,n);return n};var n=[];this.qb=function(e){n.push(e)},this.Mc=function(e){y.If(n,e)};var i={opacity:1,saturation:1,va:1,ca:1,Da:1};this.cc=function(){if(0!==n.length){this.Da=this.ca=this.va=this.saturation=this.opacity=1;for(var e=n.length-1;0<=e;e--)(0,n[e])(this,i),this.opacity*=i.opacity,this.va*=i.va,this.saturation*=i.saturation,this.ca*=i.ca,this.Da*=i.Da}}}function te(e,t){return t.weight>e.weight?1:t.weight<e.weight?-1:e.index-t.index}function ne(e){var t,n,i,r,o,a,s=this,u=e.options;e.j.subscribe("stage:initialized",(function(o,a,l,c){i=l,r=c,t=o.dc("titlebar",u.B,!1),(n=t.getContext("2d")).B=u.B,n.scale(n.B,n.B),e.j.D("titlebar:initialized",s)})),e.j.subscribe("stage:resized",(function(e,t,o,a){i=o,r=a,n.scale(n.B,n.B)})),e.j.subscribe("zoom:initialized",(function(e){a=e})),e.j.subscribe("api:initialized",(function(e){o=e})),e.j.subscribe("model:loaded",(function(){n.clearRect(0,0,i,r)})),this.update=function(e){if(n.clearRect(0,0,i,r),e){!e.empty()&&e.m[0].description&&(e=e.m[0]);var t=u.bj,s=u.aj,l=Math.min(r/2,u.Wd+2*t),c=l-2*t,h=i-2*s;if(!(0>=c||0>=h)){var f=e.Cc?e.oa.fontSize*e.scale*a.scale():0,d={titleBarText:e.qd,titleBarTextColor:u.Zf,titleBarBackgroundColor:u.Yf,titleBarMaxFontSize:u.Wd,titleBarShown:f<u.Sh};if(e.attribution)var p=x("B`ssnu!Rd`sbi!Gn`lUsdd!whrt`mh{`uhno/!Busm,bmhbj!uid!mnfn!un!fn!un!iuuqr;..b`ssnurd`sbi/bnl.gn`lusdd!gns!lnsd!edu`hmr/");else o.nc(u.Yi,e,d,(function(e){e.titleBarWidth=h,e.titleBarHeight=c,e.labelFontSize=f,e.viewportScale=a.scale()})),p=d.titleBarText;p&&0!==p.length&&d.titleBarShown&&(t={x:s,y:(e=a.Uc(e.transformPoint(e,{}),{}).y>r/2)?t:r-l+t,w:h,o:c},s=M.H(t),n.fillStyle=u.Yf,n.fillRect(0,e?0:r-l,i,l),n.fillStyle=u.Zf,B.re({fontFamily:u.Zi||u.fh,fontStyle:u.Aj||u.gh,fontWeight:u.Cj||u.ih,fontVariant:u.Bj||u.hh,hb:u.Wd,Gc:u.$i,cb:0,Ua:0,ib:1},n,p,s,t,{x:t.x+t.w/2,y:t.y+t.o/2},!0,!0).ka||n.clearRect(0,0,i,r))}}}}function ie(e){function t(e,t,n){return C=!0,u&&u.stop(),c&&c.stop(),a(p.reset(e),t,n).then((function(){C=!1}))}function n(t){p.update(t),f.N=!0,e.j.D("foamtree:dirty",!0)}function i(e,t){return p.i((0!==p.u()?.35:1)*e,(0!==p.H()?.35:1)*t)}function r(){if(1===g.ratio){var e=Math.round(1e4*p.u())/1e4;0!==e&&(b.Id=e,u=d.K.jc(b).fa({duration:500,P:{x:{start:e,end:0,easing:m.Gb}},ba:function(){p.i(b.x-b.Id,0),n(1),b.Id=b.x}}).start())}}function o(){if(1===g.ratio){var e=Math.round(1e4*p.H())/1e4;0!==e&&(v.Jd=e,c=d.K.jc(v).fa({duration:500,P:{y:{start:e,end:0,easing:m.Gb}},ba:function(){p.i(0,v.y-v.Jd),n(1),v.Jd=v.y}}).start())}}function a(e,t,i){return e?d.K.jc(g).fa({duration:void 0===t?700:t,P:{ratio:{start:0,end:1,easing:i||m.Uf}},ba:function(){n(g.ratio)}}).Ta():(new h).resolve().promise()}function s(e){return function(){return C?(new h).resolve().promise():e.apply(this,arguments)}}var u,c,f,d,p=new l(e),g={ratio:1},b={ke:0,x:0,Id:0},v={le:0,y:0,Jd:0},y=this,C=!1;e.j.subscribe("model:loaded",(function(e){f=e,p.reset(!1),p.update(1)})),e.j.subscribe("timeline:initialized",(function(e){d=e})),this.M=function(){e.j.D("zoom:initialized",this)},this.reset=function(e,n){return p.Fb(1),t(!0,e,n)},this.normalize=s((function(e,n){p.pc(1)?t(!1,e,n):y.$e()})),this.$e=function(){r(),o()},this.bg=s((function(e,t,n,i){return y.ic(e.F,t,n,i)})),this.Nb=s((function(e,t,n,i){return a(p.Nb(e,t),n,i)})),this.ic=s((function(e,t,n,i){return a(p.ic(e,t),n,i)})),this.cj=s((function(e,t){p.ic(e,t)&&n(1)})),this.Uh=s((function(e,t){1===g.ratio&&i(e,t)&&n(1)})),this.qg=s((function(e,t){p.Nb(e,t)&&n(1)})),this.pg=s((function(e,t,r,o){e=0|p.Nb(e,t),(e|=i(r,o))&&n(1)})),this.Vh=s((function(e,t,a){1===g.ratio&&(u=d.K.jc(b).fa({duration:e/.03,P:{ke:{start:t,end:0,easing:m.Gb}},ba:function(){p.i(b.ke,0)&&n(1),r()}}).start(),c=d.K.jc(v).fa({duration:e/.03,P:{le:{start:a,end:0,easing:m.Gb}},ba:function(){i(0,v.le)&&n(1),o()}}).start())})),this.Wh=function(){u&&0===p.u()&&u.stop(),c&&0===p.H()&&c.stop()},this.rc=function(e,t){p.rc(e,t)},this.Fb=function(e){return p.Fb(e)},this.pc=function(e){return p.pc(e)},this.zd=function(){return p.zd()},this.absolute=function(e,t){return p.absolute(e,t)},this.Uc=function(e,t){return p.Uc(e,t)},this.scale=function(){return p.scale()},this.i=function(e){return p.T(e)},this.content=function(e,t,n,i){p.content(e,t,n,i)}}function re(t,r,o){function a(e){var t=[];return S.L(v,(function(n){e(n)&&t.push(n.group)})),{groups:t}}function s(e,t){var n=w.options,i=n.Mi,r=n.Li;n=n.Od;var o=0<i+r?n:0,a=[];return D.u(e,D.i(e,w.options.Qd),(function(e,n,s){n="groups"===w.options.Pd?s:n,e.m&&(e=T.K.A(e).wait(o*(r+i*n)).call(t).done(),a.push(e))})),T.K.A({}).Qa(a).Ta()}function l(e){ce||(ce=!0,x.once((function(){ce=!1,w.j.D("repaint:before"),H.Nd(this.og)}),{og:e}))}function c(e){function t(e,r){var o=e.W;if(e.W=r<=n,e.Fc=r<=i,e.W!==o&&S.me(e,(function(e){e.Vd=!0})),e.open||e.Va||r++,e=e.m)for(o=0;o<e.length;o++)t(e[o],r)}var n=w.options.We,i=Math.min(w.options.We,w.options.Ph);if(e)for(var r=0;r<e.length;r++){var o=e[r];t(o,b(o))}else t(v,0)}function p(e,t){var n=[];for((e=g(e,t)).Th&&w.j.D("model:childrenAttached",S.uc(v)),e.Gi&&I.complete((function(e){ue.eb(e),n.push(e)})),t=e=0;t<n.length;t++){var i=n[t];i.m&&(e+=i.m.length),i.xa=!0,W.i(i)}return e}function g(e,t){function n(e,t){var n=!e.attribution&&t-(e.Va?1:0)<o;s=s||n,e.Ia=e.Ia||n,e.open||e.Va||t++;var r=e.m;if(!r&&n&&(a=j.T(e)||a,r=e.m,u&&(e.Ma=!0)),r)for(e=0;e<r.length;e++)i.push(r[e],t)}var i,o=t||w.options.Qh,a=!1,s=!1,u="flattened"===r.mb;for(i=e?e.reduce((function(e,t){return e.push(t,1),e}),[]):[v,1];0<i.length;)n(i.shift(),i.shift());return{Th:a,Gi:s}}function b(e){for(var t=0;e.parent;)e.open||e.Va||t++,e=e.parent;return t}var v,C=this,w={j:new _,options:r,Cd:o},x=new i,T=new A(x),k=n.create(),z=new u(w),B=new ie(w),L=new E(w),j=new O(w.options),I=new U(w),H=new J(w,x),q=new G(w);new ne(w);var V=new N(w),W=new P(w),Z=new F(w),$=new R(w);w.j.subscribe("stage:initialized",(function(e,t,n,i){re.Le(n,i)})),w.j.subscribe("stage:resized",(function(e,t,n,i){re.Ki(e,t,n,i)})),w.j.subscribe("foamtree:attachChildren",p),w.j.subscribe("openclose:changing",c),w.j.subscribe("interaction:reset",(function(){le(!0)})),w.j.subscribe("foamtree:dirty",l),this.M=function(){w.j.D("timeline:initialized",T),v=j.M(),z.M(t),L.M(),H.M(),q.M(),V.M(),W.M(),B.M(),Z.M(),$.M()},this.Za=function(){T.i(),se.stop(),x.i(),z.Za()};var K="groupLabelFontFamily groupLabelFontStyle groupLabelFontVariant groupLabelFontWeight groupLabelLineHeight groupLabelHorizontalPadding groupLabelVerticalPadding groupLabelDottingThreshold groupLabelMaxTotalHeight groupLabelMinFontSize groupLabelMaxFontSize groupLabelDecorator".split(" "),Y="rainbowColorDistribution rainbowLightnessDistribution rainbowColorDistributionAngle rainbowLightnessDistributionAngle rainbowColorModelStartPoint rainbowLightnessCorrection rainbowSaturationCorrection rainbowStartColor rainbowEndColor rainbowHueShift rainbowHueShiftCenter rainbowSaturationShift rainbowSaturationShiftCenter rainbowLightnessShift rainbowLightnessShiftCenter attributionTheme".split(" "),X=!1,Q=["groupBorderRadius","groupBorderRadiusCorrection","groupBorderWidth","groupInsetWidth","groupBorderWidthScaling"],ee=["maxGroupLevelsDrawn","maxGroupLabelLevelsDrawn"];this.hg=function(e){w.j.D("options:changed",e),y.bb(e,K)&&S.L(v,(function(e){e.Ma=!0})),y.bb(e,Y)&&(v.Fa=!0),y.bb(e,Q)&&(X=!0),y.bb(e,ee)&&(c(),p())},this.reload=function(){oe.reload()},this.ig=function(e,t){y.defer((function(){if(X)re.Nh(e),X=!1;else{if(t)for(var n=j.u(t),i=n.length-1;0<=i;i--)n[i].N=!0;else v.N=!0;l(e)}}))},this.ga=function(){z.u()},this.update=function(e){var t=(e=e?j.u(e):[v]).reduce((function(e,t){return e[t.id]=t,e}),{});e=e.filter((function(e){for(e=e.parent;e;){if(y.has(t,e.id))return!1;e=e.parent}return!0})),j.update(e),re.dj(e)},this.reset=function(){return le(!1)},this.T=H.i,this.Ja=function(){var e={};return function(t,n){return(t=j.i(t))?L.Wc(e,t,n):null}}(),this.wa=function(){var e={x:0,y:0},t={x:0,y:0};return function(n,i){return(n=j.i(n))?(e.x=i.x,e.y=i.y,n.transformPoint(e,e),B.Uc(e,e),t.x=e.x,t.y=e.y,t):null}}(),this.sa=function(){var e={};return function(t){return(t=j.i(t))?L.Yc(e,t):null}}(),this.gg=function(){var e={};return function(t){return(t=j.i(t))?L.Xc(e,t):null}}(),this.ta=function(){var e={};return function(){return B.i(e)}}(),this.kg=function(){this.H({groups:a((function(e){return e.group.selected})),newState:!0,keepPrevious:!1}),this.u({groups:a((function(e){return e.group.open})),newState:!0,keepPrevious:!1}),this.i({groups:a((function(e){return e.group.exposed})),newState:!0,keepPrevious:!1})},this.Ka=function(){return a((function(e){return e.U}))},this.i=function(e){return oe.submit((function(){return V.Vb(j.H(e,"exposed",!1),!1,!0,!1)}))},this.pb=function(){return a((function(e){return e.open}))},this.u=function(e){return oe.submit((function(){return Z.Bb(j.H(e,"open",!0),!1,!1)}))},this.Lb=function(){return a((function(e){return e.selected}))},this.H=function(e){return oe.submit((function(){return $.select(j.H(e,"selected",!0),!1),(new h).resolve().promise()}))},this.mg=function(e){return(e=j.i(e))?e===v?B.reset(r.ob,m.ia(r.Kb)):B.bg(e,r.Yb,r.ob,m.ia(r.Kb)):(new h).resolve().promise()},this.ua=function(e,t){return(e=j.u(e))?(t=p(e,t),c(e),t):0},this.Vc=function(e){return q.Lb[e]},this.lg=function(){var t=e;return{frames:t.frames,totalTime:t.totalTime,lastFrameTime:t.rd,lastInterFrameTime:t.sd,fps:t.ue}};var te,re=function(){function e(e,o){var a=e||n,s=o||i;n=a,i=s,(e=r.Rb&&r.Rb.boundary)&&2<e.length?v.C=e.map((function(e){return{x:a*e.x,y:s*e.y}})):v.C=[{x:0,y:0},{x:a,y:0},{x:a,y:s},{x:0,y:s}],t()}function t(){v.Z=!0,v.G=v.C,v.F=M.F(v.C,v.F),v.O=v,M.Ja(v.C,v.O)}var n,i;return{Le:e,Ki:function(t,n,i,r){ue.stop();var o=i/t,a=r/n;S.ne(v,(function(e){e.x=e.x*o+(Math.random()-.5)*i/1e3,e.y=e.y*a+(Math.random()-.5)*r/1e3})),e(i,r),v.Ea=!0,I.step(ue.eb,!0,!1,(function(e){var t=e.m;if(t){I.Eb(e);for(var n=t.length-1;0<=n;n--){var i=t[n];i.w=i.hc}e.Ea=!0}}))?l(!1):(I.fc(v),w.options.Md?(l(!1),se.Jf(),se.Oc()):(I.complete(ue.eb),v.Fa=!0,l(!1)))},Nh:function(e){var n=!1;return v.empty()||(t(),se.xb()||(n=I.step(ue.eb,!1,!1),l(e))),n},dj:function(e){e.forEach((function(e){S.za(e,(function(e){e.empty()||I.Eb(e)})),I.fc(e),w.options.Md?(se.Jf(),S.za(e,(function(e){e.empty()||ue.grow(e)}))):(S.za(e,(function(e){e.empty()||ue.eb(e)})),I.complete(ue.eb),e.Fa=!0,l(!1))}))}}}(),oe=function(){function e(){if(0===r.Gd&&B.reset(0),w.options.zf(r.Rb),re.Le(),j.load(r.Rb),g(),c(),w.j.D("model:loaded",v,S.uc(v)),!v.empty()){if(v.open=!0,v.Ia=!0,r.Md)var e=se.Oc();else se.Yh(),e=function(){S.za(v,(function(e){e.xa=!1}));var e=new h,t=new d(e.resolve);return t.i(),v.xa=!0,W.i(v).then(t.u),s(v,(function e(){this.R&&this.C&&(this.Z=this.xa=!0,t.i(),W.i(this).then(t.u),t.i(),s(this,e).then(t.u))})),e.promise()}();!function(){var e=r.Oa,t=r.Kc;r.Oa=0,r.Kc=0,C.kg(),r.Oa=e,r.Kc=t}(),0<r.Od?(H.clear(),z.i(1)):e=f([e,t(1)])}w.options.yf(r.Rb),e&&(w.options.Cf(),e.then((function(){H.u((function(){x.once(w.options.Bf)}))})))}function t(e,t){return 0===r.qe||t?(z.i(e),(new h).resolve().promise()):T.K.A({opacity:z.i()}).Xd(2).fa({duration:r.qe,P:{opacity:{end:e,easing:m.ia(r.Cg)}},ba:function(){z.i(this.opacity)}}).Ta()}function n(){for(var e=0;e<o.length;e++){var t=o[e],n=t.action();y.has(n,"then")?n.then(t.ge.resolve):t.ge.resolve()}o=[]}var i=!1,o=[];return{reload:function(){i||(v.empty()?e():(ue.stop(),T.i(),se.stop(),i=!0,f(0<r.Gd?[W.u(),le(!1)]:[t(0)]).then((function(){t(0,!0),i=!1,e(),y.defer(n)}))))},submit:function(e){if(i){var t=new h;return o.push({action:e,ge:t}),t.promise()}return e()}}}(),ae=new d((function(){te.resolve()})),se=function(){function e(){return o||(ae.initial()&&(te=new h),ae.i(),t(),o=!0,x.repeat(n)),te.promise()}function t(){i=k.now()}function n(){var t=k.now()-i>r.Ji;return t=I.step((function(t){t.xa=!0,ue.grow(t),ae.i(),W.i(t).then(ae.u),ae.i(),s(t,(function(){this.Ia=!0,e()})).then(ae.u)}),!0,t)||t,l(!0),t&&(o=!1,ae.u()),t}var i,o=!1;return{Yh:function(){I.complete(ue.eb)},Oc:e,Jf:t,xb:function(){return!ae.initial()},stop:function(){x.cancel(n),o=!1,ae.clear()}}}(),ue=function(){function e(e){var t=!e.empty();if(e.xa=!0,t){for(var n=e.m,i=n.length-1;0<=i;i--){var r=n[i];r.w=r.hc}e.Ea=!0}return t}var t=[];return{grow:function(n){var i=w.options,r=i.Wg;0<r?D.u(n,D.i(n,w.options.Qd),(function(e,n,o){n="groups"===w.options.Pd?o:n,ae.i(),t.push(T.K.A(e).wait(n*i.Vg*r).fa({duration:r,P:{w:{start:e.Vf,end:e.hc,easing:m.ia(i.Xg)}},ba:function(){this.w=Math.max(0,this.w),this.parent.Ea=!0,se.Oc()}}).Xa(ae.u).start())})):e(n)&&se.Oc()},eb:e,stop:function(){for(var e=t.length-1;0<=e;e--)t[e].stop();t=[]}}}(),le=function(){var e=!1;return function(t){if(e)return(new h).resolve().promise();e=!0;var n=[];n.push(B.reset(r.ob,m.ia(r.Kb)));var i=new h;return V.Vb({m:[],Ca:!1,Ba:!1},t,!1,!0).then((function(){Z.Bb({m:[],Ca:!1,Ba:!1},t,!1).then(i.resolve)})),n.push(i.promise()),f(n).then((function(){e=!1,t&&r.Df()}))}}(),ce=!1}function oe(){return{version:"3.5.0",build:"bugfix/3.5.x/e3b91c8e",brandingAllowed:!1}}Y.i={width:445.2,height:533.5},t.md((function(){window.CarrotSearchFoamTree=function(e){function t(e,t){if(!s||s.exists(e))switch(e){case"selection":return h.Lb();case"open":return h.pb();case"exposure":return h.Ka();case"state":return h.sa.apply(this,t);case"geometry":return h.Ja.apply(this,t);case"hierarchy":return h.gg.apply(this,t);case"containerCoordinates":return h.wa.apply(this,t);case"imageData":return h.T.apply(this,t);case"viewport":return h.ta();case"times":return h.lg();case"onModelChanged":case"onRedraw":case"onRolloutStart":case"onRolloutComplete":case"onRelaxationStep":case"onGroupHover":case"onGroupOpenOrCloseChanging":case"onGroupExposureChanging":case"onGroupSelectionChanging":case"onGroupSelectionChanged":case"onGroupClick":case"onGroupDoubleClick":case"onGroupHold":return e=u[e],Array.isArray(e)?e:[e];default:return u[e]}}function n(e){function t(e,t){return y.has(n,e)?(t(n[e]),delete n[e],1):0}if(0===arguments.length)return 0;if(1===arguments.length)var n=y.extend({},arguments[0]);else 2===arguments.length&&((n={})[arguments[0]]=arguments[1]);s&&s.validate(n,l.Oh);var i=0;h&&(i+=t("selection",h.H),i+=t("open",h.u),i+=t("exposure",h.i));var o={};return y.Aa(n,(function(e,t){(u[t]!==e||y.wb(e))&&(o[t]=e,i++),u[t]=e})),0<i&&r(o),i}function i(e,t){e="on"+e.charAt(0).toUpperCase()+e.slice(1);var n=u[e];u[e]=t(Array.isArray(n)?n:[n]),(t={})[e]=u[e],r(t)}function r(e){!function(){function t(t,n){return y.has(e,t)||void 0===n?w(u[t],a):n}l.Oh=u.logging,l.Rb=u.dataObject,l.B=u.pixelRatio,l.nb=u.wireframePixelRatio,l.mb=u.stacking,l.zg=u.descriptionGroup,l.Tb=u.descriptionGroupType,l.qc=u.descriptionGroupPosition,l.Ag=u.descriptionGroupDistanceFromCenter,l.Sb=u.descriptionGroupSize,l.ie=u.descriptionGroupMinHeight,l.he=u.descriptionGroupMaxHeight,l.je=u.descriptionGroupPolygonDrawn,l.Dc=u.layout,l.ac=u.layoutByWeightOrder,l.Wi=u.showZeroWeightGroups,l.Be=u.groupMinDiameter,l.Ld=u.rectangleAspectRatioPreference,l.Ii=u.initializer||u.relaxationInitializer,l.Ji=u.relaxationMaxDuration,l.Md=u.relaxationVisible,l.Hf=u.relaxationQualityThreshold,l.oh=u.groupResizingBudget,l.Wg=u.groupGrowingDuration,l.Vg=u.groupGrowingDrag,l.Xg=u.groupGrowingEasing,l.Gg=u.groupBorderRadius,l.$a=u.groupBorderWidth,l.La=u.groupBorderWidthScaling,l.jd=u.groupInsetWidth,l.Hg=u.groupBorderRadiusCorrection,l.ab=u.groupStrokeWidth,l.yc=u.groupSelectionOutlineWidth,l.sh=u.groupSelectionOutlineColor,l.kd=u.groupSelectionOutlineShadowSize,l.Ce=u.groupSelectionOutlineShadowColor,l.ph=u.groupSelectionFillHueShift,l.rh=u.groupSelectionFillSaturationShift,l.qh=u.groupSelectionFillLightnessShift,l.Ee=u.groupSelectionStrokeHueShift,l.Ge=u.groupSelectionStrokeSaturationShift,l.Fe=u.groupSelectionStrokeLightnessShift,l.Ug=u.groupFillType,l.Qg=u.groupFillGradientRadius,l.Ng=u.groupFillGradientCenterHueShift,l.Pg=u.groupFillGradientCenterSaturationShift,l.Og=u.groupFillGradientCenterLightnessShift,l.Rg=u.groupFillGradientRimHueShift,l.Tg=u.groupFillGradientRimSaturationShift,l.Sg=u.groupFillGradientRimLightnessShift,l.ld=u.groupStrokeType,l.ab=u.groupStrokeWidth,l.He=u.groupStrokePlainHueShift,l.Je=u.groupStrokePlainSaturationShift,l.Ie=u.groupStrokePlainLightnessShift,l.xh=u.groupStrokeGradientRadius,l.th=u.groupStrokeGradientAngle,l.yh=u.groupStrokeGradientUpperHueShift,l.Ah=u.groupStrokeGradientUpperSaturationShift,l.zh=u.groupStrokeGradientUpperLightnessShift,l.uh=u.groupStrokeGradientLowerHueShift,l.wh=u.groupStrokeGradientLowerSaturationShift,l.vh=u.groupStrokeGradientLowerLightnessShift,l.Yg=u.groupHoverFillHueShift,l.$g=u.groupHoverFillSaturationShift,l.Zg=u.groupHoverFillLightnessShift,l.ye=u.groupHoverStrokeHueShift,l.Ae=u.groupHoverStrokeSaturationShift,l.ze=u.groupHoverStrokeLightnessShift,l.Pa=u.groupExposureScale,l.Mg=u.groupExposureShadowColor,l.xe=u.groupExposureShadowSize,l.Yb=u.groupExposureZoomMargin,l.Ch=u.groupUnexposureLightnessShift,l.Dh=u.groupUnexposureSaturationShift,l.Bh=u.groupUnexposureLabelColorThreshold,l.Oa=u.exposeDuration,l.Wb=u.exposeEasing,l.Kc=u.openCloseDuration,l.Ig=w(u.groupColorDecorator,a),l.Jg=u.groupColorDecorator!==y.qa,l.dh=w(u.groupLabelDecorator,a),l.eh=u.groupLabelDecorator!==y.qa,l.jh=w(u.groupLabelLayoutDecorator,a),l.kh=u.groupLabelLayoutDecorator!==y.qa,l.Kg=w(u.groupContentDecorator,a),l.xc=u.groupContentDecorator!==y.qa,l.Lg=u.groupContentDecoratorTriggering,l.Ei=u.rainbowStartColor,l.xi=u.rainbowEndColor,l.vi=u.rainbowColorDistribution,l.wi=u.rainbowColorDistributionAngle,l.Ai=u.rainbowLightnessDistributionAngle,l.Bi=u.rainbowLightnessShift,l.Ci=u.rainbowLightnessShiftCenter,l.Di=u.rainbowSaturationCorrection,l.zi=u.rainbowLightnessCorrection,l.Ef=u.parentFillOpacity,l.Xh=u.parentStrokeOpacity,l.Ff=u.parentLabelOpacity,l.Gf=u.parentOpacityBalancing,l.nh=u.groupLabelUpdateThreshold,l.fh=u.groupLabelFontFamily,l.gh=u.groupLabelFontStyle,l.hh=u.groupLabelFontVariant,l.ih=u.groupLabelFontWeight,l.mh=u.groupLabelMinFontSize,l.sj=u.groupLabelMaxFontSize,l.rj=u.groupLabelLineHeight,l.qj=u.groupLabelHorizontalPadding,l.uj=u.groupLabelVerticalPadding,l.tj=u.groupLabelMaxTotalHeight,l.bh=u.groupLabelDarkColor,l.lh=u.groupLabelLightColor,l.ah=u.groupLabelColorThreshold,l.fj=u.wireframeDrawMaxDuration,l.gj=u.wireframeLabelDrawing,l.ej=u.wireframeContentDecorationDrawing,l.dg=u.wireframeToFinalFadeDuration,l.hj=u.wireframeToFinalFadeDelay,l.Dg=u.finalCompleteDrawMaxDuration,l.Eg=u.finalIncrementalDrawMaxDuration,l.se=u.finalToWireframeFadeDuration,l.Zc=u.androidStockBrowserWorkaround,l.Ke=u.incrementalDraw,l.Rh=u.maxGroups,l.Qh=u.maxGroupLevelsAttached,l.We=u.maxGroupLevelsDrawn,l.Ph=u.maxGroupLabelLevelsDrawn,l.Qd=u.rolloutStartPoint,l.Pd=u.rolloutMethod,l.Ni=u.rolloutEasing,l.Od=u.rolloutDuration,l.Lf=u.rolloutScalingStrength,l.Nf=u.rolloutTranslationXStrength,l.Of=u.rolloutTranslationYStrength,l.Kf=u.rolloutRotationStrength,l.Mf=u.rolloutTransformationCenter,l.Ri=u.rolloutPolygonDrag,l.Si=u.rolloutPolygonDuration,l.Oi=u.rolloutLabelDelay,l.Pi=u.rolloutLabelDrag,l.Qi=u.rolloutLabelDuration,l.Mi=u.rolloutChildGroupsDrag,l.Li=u.rolloutChildGroupsDelay,l.ni=u.pullbackStartPoint,l.hi=u.pullbackMethod,l.di=u.pullbackEasing,l.yj=u.pullbackType,l.Gd=u.pullbackDuration,l.mi=u.pullbackScalingStrength,l.pi=u.pullbackTranslationXStrength,l.ri=u.pullbackTranslationYStrength,l.li=u.pullbackRotationStrength,l.oi=u.pullbackTransformationCenter,l.ii=u.pullbackPolygonDelay,l.ji=u.pullbackPolygonDrag,l.ki=u.pullbackPolygonDuration,l.ei=u.pullbackLabelDelay,l.fi=u.pullbackLabelDrag,l.gi=u.pullbackLabelDuration,l.ai=u.pullbackChildGroupsDelay,l.bi=u.pullbackChildGroupsDrag,l.ci=u.pullbackChildGroupsDuration,l.qe=u.fadeDuration,l.Cg=u.fadeEasing,l.ij=u.zoomMouseWheelFactor,l.ob=u.zoomMouseWheelDuration,l.Kb=u.zoomMouseWheelEasing,l.Sh=u.maxLabelSizeForTitleBar,l.Zi=u.titleBarFontFamily,l.Yf=u.titleBarBackgroundColor,l.Zf=u.titleBarTextColor,l.$i=u.titleBarMinFontSize,l.Wd=u.titleBarMaxFontSize,l.aj=u.titleBarTextPaddingLeftRight,l.bj=u.titleBarTextPaddingTopBottom,l.Yi=u.titleBarDecorator,l.mj=u.attributionText,l.jj=u.attributionLogo,l.lj=u.attributionLogoScale,l.nj=u.attributionUrl,l.$d=u.attributionPosition,l.rg=u.attributionDistanceFromCenter,l.sg=u.attributionWeight,l.ae=u.attributionTheme,l.Me=u.interactionHandler,l.zf=t("onModelChanging",l.zf),l.yf=t("onModelChanged",l.yf),l.Af=t("onRedraw",l.Af),l.Cf=t("onRolloutStart",l.Cf),l.Bf=t("onRolloutComplete",l.Bf),l.Ad=t("onRelaxationStep",l.Ad),l.Df=t("onViewReset",l.Df),l.rf=t("onGroupOpenOrCloseChanging",l.rf),l.qf=t("onGroupOpenOrCloseChanged",l.qf),l.hf=t("onGroupExposureChanging",l.hf),l.gf=t("onGroupExposureChanged",l.gf),l.tf=t("onGroupSelectionChanging",l.tf),l.sf=t("onGroupSelectionChanged",l.sf),l.kf=t("onGroupHover",l.kf),l.mf=t("onGroupMouseMove",l.mf),l.bf=t("onGroupClick",l.bf),l.cf=t("onGroupDoubleClick",l.cf),l.jf=t("onGroupHold",l.jf),l.pf=t("onGroupMouseWheel",l.pf),l.nf=t("onGroupMouseUp",l.nf),l.lf=t("onGroupMouseDown",l.lf),l.ff=t("onGroupDragStart",l.ff),l.df=t("onGroupDrag",l.df),l.ef=t("onGroupDragEnd",l.ef),l.wf=t("onGroupTransformStart",l.wf),l.uf=t("onGroupTransform",l.uf),l.vf=t("onGroupTransformEnd",l.vf),l.xf=t("onKeyUp",l.xf)}(),l.Fi=c.u(l.Ei),l.yi=c.u(l.xi),l.De=c.u(l.Ce),l.kj=null,h&&(h.hg(e),y.has(e,"dataObject")&&h.reload())}function o(e){return function(){return e.apply(this,arguments).Fg(a)}}var a=this,s=window.CarrotSearchFoamTree.asserts,u=y.extend({},window.CarrotSearchFoamTree.defaults),l={};n(e),(e=u.element||document.getElementById(u.id))||j.i("Element to embed FoamTree in not found."),u.element=e;var h=new re(e,l,u);h.M();var f={get:function(e){return 0===arguments.length?y.extend({},u):t(arguments[0],Array.prototype.slice.call(arguments,1))},set:n,on:function(e,t){i(e,(function(e){return e.push(t),e}))},off:function(e,t){i(e,(function(e){return e.filter((function(e){return e!==t}))}))},resize:h.ga,redraw:h.ig,update:h.update,attach:h.ua,select:o(h.H),expose:o(h.i),open:o(h.u),reset:o(h.reset),zoom:o(h.mg),trigger:function(e,t){(e=h.Vc(e))&&e(t)},dispose:function(){function e(){throw"FoamTree instance disposed"}h.Za(),y.Aa(f,(function(t,n){"dispose"!==n&&(a[n]=e)}))}};y.Aa(f,(function(e,t){a[t]=e})),h.reload()},window["CarrotSearchFoamTree.asserts"]&&(window.CarrotSearchFoamTree.asserts=window["CarrotSearchFoamTree.asserts"],delete window["CarrotSearchFoamTree.asserts"]),window.CarrotSearchFoamTree.supported=!0,window.CarrotSearchFoamTree.version=oe,window.CarrotSearchFoamTree.defaults=Object.freeze({id:void 0,element:void 0,logging:!1,dataObject:void 0,pixelRatio:1,wireframePixelRatio:1,layout:"relaxed",layoutByWeightOrder:!0,showZeroWeightGroups:!0,groupMinDiameter:10,rectangleAspectRatioPreference:-1,relaxationInitializer:"fisheye",relaxationMaxDuration:3e3,relaxationVisible:!1,relaxationQualityThreshold:1,stacking:"hierarchical",descriptionGroup:"auto",descriptionGroupType:"stab",descriptionGroupPosition:225,descriptionGroupDistanceFromCenter:1,descriptionGroupSize:.125,descriptionGroupMinHeight:35,descriptionGroupMaxHeight:.5,descriptionGroupPolygonDrawn:!1,maxGroups:5e4,maxGroupLevelsAttached:4,maxGroupLevelsDrawn:4,maxGroupLabelLevelsDrawn:3,groupGrowingDuration:0,groupGrowingEasing:"bounce",groupGrowingDrag:0,groupResizingBudget:2,groupBorderRadius:.15,groupBorderWidth:4,groupBorderWidthScaling:.6,groupInsetWidth:6,groupBorderRadiusCorrection:1,groupSelectionOutlineWidth:5,groupSelectionOutlineColor:"#222",groupSelectionOutlineShadowSize:0,groupSelectionOutlineShadowColor:"#fff",groupSelectionFillHueShift:0,groupSelectionFillSaturationShift:0,groupSelectionFillLightnessShift:0,groupSelectionStrokeHueShift:0,groupSelectionStrokeSaturationShift:0,groupSelectionStrokeLightnessShift:-10,groupFillType:"gradient",groupFillGradientRadius:1,groupFillGradientCenterHueShift:0,groupFillGradientCenterSaturationShift:0,groupFillGradientCenterLightnessShift:20,groupFillGradientRimHueShift:0,groupFillGradientRimSaturationShift:0,groupFillGradientRimLightnessShift:-5,groupStrokeType:"plain",groupStrokeWidth:1.5,groupStrokePlainHueShift:0,groupStrokePlainSaturationShift:0,groupStrokePlainLightnessShift:-10,groupStrokeGradientRadius:1,groupStrokeGradientAngle:45,groupStrokeGradientUpperHueShift:0,groupStrokeGradientUpperSaturationShift:0,groupStrokeGradientUpperLightnessShift:20,groupStrokeGradientLowerHueShift:0,groupStrokeGradientLowerSaturationShift:0,groupStrokeGradientLowerLightnessShift:-20,groupHoverFillHueShift:0,groupHoverFillSaturationShift:0,groupHoverFillLightnessShift:20,groupHoverStrokeHueShift:0,groupHoverStrokeSaturationShift:0,groupHoverStrokeLightnessShift:-10,groupExposureScale:1.15,groupExposureShadowColor:"rgba(0, 0, 0, 0.5)",groupExposureShadowSize:50,groupExposureZoomMargin:.1,groupUnexposureLightnessShift:65,groupUnexposureSaturationShift:-65,groupUnexposureLabelColorThreshold:.35,exposeDuration:700,exposeEasing:"squareInOut",groupColorDecorator:y.qa,groupLabelDecorator:y.qa,groupLabelLayoutDecorator:y.qa,groupContentDecorator:y.qa,groupContentDecoratorTriggering:"onLayoutDirty",openCloseDuration:500,rainbowColorDistribution:"radial",rainbowColorDistributionAngle:-45,rainbowLightnessDistributionAngle:45,rainbowSaturationCorrection:.1,rainbowLightnessCorrection:.4,rainbowStartColor:"hsla(0, 100%, 55%, 1)",rainbowEndColor:"hsla(359, 100%, 55%, 1)",rainbowLightnessShift:30,rainbowLightnessShiftCenter:.4,parentFillOpacity:.7,parentStrokeOpacity:1,parentLabelOpacity:1,parentOpacityBalancing:!0,wireframeDrawMaxDuration:15,wireframeLabelDrawing:"auto",wireframeContentDecorationDrawing:"auto",wireframeToFinalFadeDuration:500,wireframeToFinalFadeDelay:300,finalCompleteDrawMaxDuration:80,finalIncrementalDrawMaxDuration:100,finalToWireframeFadeDuration:200,androidStockBrowserWorkaround:!1,incrementalDraw:"fast",groupLabelFontFamily:"sans-serif",groupLabelFontStyle:"normal",groupLabelFontWeight:"normal",groupLabelFontVariant:"normal",groupLabelLineHeight:1.05,groupLabelHorizontalPadding:1,groupLabelVerticalPadding:1,groupLabelMinFontSize:6,groupLabelMaxFontSize:160,groupLabelMaxTotalHeight:.9,groupLabelUpdateThreshold:.05,groupLabelDarkColor:"#000",groupLabelLightColor:"#fff",groupLabelColorThreshold:.35,rolloutStartPoint:"center",rolloutEasing:"squareOut",rolloutMethod:"groups",rolloutDuration:2e3,rolloutScalingStrength:-.7,rolloutTranslationXStrength:0,rolloutTranslationYStrength:0,rolloutRotationStrength:-.7,rolloutTransformationCenter:.7,rolloutPolygonDrag:.1,rolloutPolygonDuration:.5,rolloutLabelDelay:.8,rolloutLabelDrag:.1,rolloutLabelDuration:.5,rolloutChildGroupsDrag:.1,rolloutChildGroupsDelay:.2,pullbackStartPoint:"center",pullbackEasing:"squareIn",pullbackMethod:"groups",pullbackDuration:1500,pullbackScalingStrength:-.7,pullbackTranslationXStrength:0,pullbackTranslationYStrength:0,pullbackRotationStrength:-.7,pullbackTransformationCenter:.7,pullbackPolygonDelay:.3,pullbackPolygonDrag:.1,pullbackPolygonDuration:.8,pullbackLabelDelay:0,pullbackLabelDrag:.1,pullbackLabelDuration:.3,pullbackChildGroupsDelay:.1,pullbackChildGroupsDrag:.1,pullbackChildGroupsDuration:.3,fadeDuration:700,fadeEasing:"cubicInOut",zoomMouseWheelFactor:1.5,zoomMouseWheelDuration:500,zoomMouseWheelEasing:"squareOut",maxLabelSizeForTitleBar:8,titleBarFontFamily:null,titleBarFontStyle:"normal",titleBarFontWeight:"normal",titleBarFontVariant:"normal",titleBarBackgroundColor:"rgba(0, 0, 0, 0.5)",titleBarTextColor:"rgba(255, 255, 255, 1)",titleBarMinFontSize:10,titleBarMaxFontSize:40,titleBarTextPaddingLeftRight:20,titleBarTextPaddingTopBottom:15,titleBarDecorator:y.qa,attributionText:null,attributionLogo:null,attributionLogoScale:.5,attributionUrl:"http://carrotsearch.com/foamtree",attributionPosition:"bottomright",attributionDistanceFromCenter:1,attributionWeight:.025,attributionTheme:"light",interactionHandler:t.Gh()?"hammerjs":"builtin",onModelChanging:[],onModelChanged:[],onRedraw:[],onRolloutStart:[],onRolloutComplete:[],onRelaxationStep:[],onViewReset:[],onGroupOpenOrCloseChanging:[],onGroupOpenOrCloseChanged:[],onGroupExposureChanging:[],onGroupExposureChanged:[],onGroupSelectionChanging:[],onGroupSelectionChanged:[],onGroupHover:[],onGroupMouseMove:[],onGroupClick:[],onGroupDoubleClick:[],onGroupHold:[],onGroupMouseWheel:[],onGroupMouseUp:[],onGroupMouseDown:[],onGroupDragStart:[],onGroupDrag:[],onGroupDragEnd:[],onGroupTransformStart:[],onGroupTransform:[],onGroupTransformEnd:[],onKeyUp:[],selection:null,open:null,exposure:null,imageData:null,hierarchy:null,geometry:null,containerCoordinates:null,state:null,viewport:null,times:null}),window.CarrotSearchFoamTree.geometry=Object.freeze({rectangleInPolygon:function(e,t,n,i,r,o,a){return r=y.I(r,1),o=y.I(o,.5),a=y.I(a,.5),{x:t-(e=M.Ka(e,{x:t,y:n},i,o,a)*r)*i*o,y:n-e*a,w:e*i,h:e}},circleInPolygon:function(e,t,n){return M.pb(e,{x:t,y:n})},stabPolygon:function(e,t,n,i){return M.ua(e,{x:t,y:n},i)},polygonCentroid:function(e){return{x:(e=M.u(e,{})).x,y:e.y,area:e.ha}},boundingBox:function(e){for(var t=e[0].x,n=e[0].y,i=e[0].x,r=e[0].y,o=1;o<e.length;o++){var a=e[o];a.x<t&&(t=a.x),a.y<n&&(n=a.y),a.x>i&&(i=a.x),a.y>r&&(r=a.y)}return{x:t,y:n,w:i-t,h:r-n}}})}),(function(){window.CarrotSearchFoamTree=function(){window.console.error("FoamTree is not supported on this browser.")},window.CarrotSearchFoamTree.supported=!1}))}();const Fr=window.CarrotSearchFoamTree;function Rr(t,n,i,r,o){var a={};for(var s in n)"ref"!=s&&(a[s]=n[s]);var u,l,c={type:t,props:a,key:i,ref:n&&n.ref,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:++e.__v,__source:r,__self:o};if("function"==typeof t&&(u=t.defaultProps))for(l in u)void 0===a[l]&&(a[l]=u[l]);return e.vnode&&e.vnode(c),c}class Gr extends g{constructor(e){super(e),this.saveNodeRef=e=>this.node=e,this.resize=()=>{const{props:e}=this;this.treemap.resize(),e.onResize&&e.onResize()},this.treemap=null,this.zoomOutDisabled=!1,this.findChunkNamePartIndex()}componentDidMount(){this.treemap=this.createTreemap(),window.addEventListener("resize",this.resize)}componentWillReceiveProps(e){e.data!==this.props.data?(this.findChunkNamePartIndex(),this.treemap.set({dataObject:this.getTreemapDataObject(e.data)})):e.highlightGroups!==this.props.highlightGroups&&setTimeout((()=>this.treemap.redraw()))}shouldComponentUpdate(){return!1}componentWillUnmount(){window.removeEventListener("resize",this.resize),this.treemap.dispose()}render(){return Rr("div",{...this.props,ref:this.saveNodeRef})}getTreemapDataObject(e=this.props.data){return{groups:e}}createTreemap(){const e=this,{props:t}=this;return new Fr({element:this.node,layout:"squarified",stacking:"flattened",pixelRatio:window.devicePixelRatio||1,maxGroups:1/0,maxGroupLevelsDrawn:1/0,maxGroupLabelLevelsDrawn:1/0,maxGroupLevelsAttached:1/0,wireframeLabelDrawing:"always",groupMinDiameter:0,groupLabelVerticalPadding:.2,rolloutDuration:0,pullbackDuration:0,fadeDuration:0,groupExposureZoomMargin:.2,zoomMouseWheelDuration:300,openCloseDuration:200,dataObject:this.getTreemapDataObject(),titleBarDecorator(e,t,n){n.titleBarShown=!1},groupColorDecorator(t,n,i){const r=e.getGroupRoot(n.group),o=e.getChunkNamePart(r.label),a=/[^0-9]/u.test(o)?function(e){let t=0;for(let n=0;n<e.length;n++){t=(t<<5)-t+e.charCodeAt(n),t&=t}return t}(o):parseInt(o)/1e3*360;i.groupColor={model:"hsla",h:Math.round(Math.abs(a)%360),s:60,l:50,a:.9};const{highlightGroups:s}=e.props,u=n.group;s&&s.has(u)?i.groupColor={model:"rgba",r:255,g:0,b:0,a:.8}:s&&s.size>0&&(i.groupColor.s=10)},onGroupClick(n){Hr(n),(n.ctrlKey||n.secondary)&&t.onGroupSecondaryClick?t.onGroupSecondaryClick.call(e,n):(e.zoomOutDisabled=!1,this.zoom(n.group))},onGroupDoubleClick:Hr,onGroupHover(n){if(n.group&&(n.group.attribution||n.group===this.get("dataObject")))return n.preventDefault(),void(t.onMouseLeave&&t.onMouseLeave.call(e,n));t.onGroupHover&&t.onGroupHover.call(e,n)},onGroupMouseWheel(t){const{scale:n}=this.get("viewport");if(t.delta<0){if(e.zoomOutDisabled)return Hr(t);n<1&&(e.zoomOutDisabled=!0,Hr(t))}else e.zoomOutDisabled=!1}})}getGroupRoot(e){let t;for(;!e.isAsset&&(t=this.treemap.get("hierarchy",e).parent);)e=t;return e}zoomToGroup(e){for(this.zoomOutDisabled=!1;e&&!this.treemap.get("state",e).revealed;)e=this.treemap.get("hierarchy",e).parent;e&&this.treemap.zoom(e)}isGroupRendered(e){const t=this.treemap.get("state",e);return!!t&&t.revealed}update(){this.treemap.update()}findChunkNamePartIndex(){const e=this.props.data.map((e=>e.label.split(/[^a-z0-9]/iu))),t={index:0,votes:0};for(let n=Math.max(...e.map((e=>e.length)))-1;n>=0;n--){const i={name:0,hash:0,ext:0};let r="";for(const t of e){const e=t[n];void 0!==e&&""!==e&&(e===r?i.ext++:/[a-z]/u.test(e)&&/[0-9]/u.test(e)&&e.length===r.length?i.hash++:(/^[a-z]+$/iu.test(e)||/^[0-9]+$/u.test(e))&&i.name++,r=e)}i.name>=t.votes&&(t.index=n,t.votes=i.name)}this.chunkNamePartIndex=t.index}getChunkNamePart(e){return e.split(/[^a-z0-9]/iu)[this.chunkNamePartIndex]||e}}function Hr(e){e.preventDefault()}var Ur=n(184),qr=n.n(Ur),Vr=n(379),Wr=n.n(Vr),Zr=n(527),$r={insert:"head",singleton:!1};Wr()(Zr.Z,$r);const Kr=Zr.Z.locals||{};class Yr extends g{constructor(...e){super(...e),this.mouseCoords={x:0,y:0},this.state={left:0,top:0},this.handleMouseMove=e=>{Object.assign(this.mouseCoords,{x:e.pageX,y:e.pageY}),this.props.visible&&this.updatePosition()},this.saveNode=e=>this.node=e}componentDidMount(){document.addEventListener("mousemove",this.handleMouseMove,!0)}shouldComponentUpdate(e){return this.props.visible||e.visible}componentWillUnmount(){document.removeEventListener("mousemove",this.handleMouseMove,!0)}render(){const{children:e,visible:t}=this.props,n=qr()({[Kr.container]:!0,[Kr.hidden]:!t});return Rr("div",{ref:this.saveNode,className:n,style:this.getStyle(),children:e})}getStyle(){return{left:this.state.left,top:this.state.top}}updatePosition(){if(!this.props.visible)return;const e={left:this.mouseCoords.x+Yr.marginX,top:this.mouseCoords.y+Yr.marginY},t=this.node.getBoundingClientRect();e.left+t.width>window.innerWidth&&(e.left=window.innerWidth-t.width),e.top+t.height>window.innerHeight&&(e.top=this.mouseCoords.y-Yr.marginY-t.height),this.setState(e)}}Yr.marginX=10,Yr.marginY=30;var Jr=n(908),Xr={insert:"head",singleton:!1};Wr()(Jr.Z,Xr);const Qr=Jr.Z.locals||{};class eo extends g{shouldComponentUpdate(e,t){return!to(e,this.props)||!to(this.state,t)}}function to(e,t){if(e===t)return!0;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let i=0;i<n.length;i++){const r=n[i];if(e[r]!==t[r])return!1}return!0}class no extends eo{constructor(...e){super(...e),this.handleClick=e=>{this.elem.blur(),this.props.onClick(e)},this.saveRef=e=>this.elem=e}render({active:e,toggle:t,className:n,children:i,...r}){const o=qr()(n,{[Qr.button]:!0,[Qr.active]:e,[Qr.toggle]:t});return Rr("button",{...r,ref:this.saveRef,type:"button",className:o,disabled:this.disabled,onClick:this.handleClick,children:i})}get disabled(){const{props:e}=this;return e.disabled||e.active&&!e.toggle}}class io extends eo{constructor(...e){super(...e),this.handleClick=()=>{this.props.onClick(this.props.item)}}render({item:e,...t}){return Rr(no,{...t,onClick:this.handleClick,children:e.label})}}var ro=n(897),oo={insert:"head",singleton:!1};Wr()(ro.Z,oo);const ao=ro.Z.locals||{};class so extends eo{render(){const{label:e,items:t,activeItem:n,onSwitch:i}=this.props;return Rr("div",{className:ao.container,children:[Rr("div",{className:ao.label,children:[e,":"]}),Rr("div",{children:t.map((e=>Rr(io,{className:ao.item,item:e,active:e===n,onClick:i},e.label)))})]})}}var uo=n(826),lo={insert:"head",singleton:!1};Wr()(uo.Z,lo);const co=uo.Z.locals||{};var ho=n(746),fo={insert:"head",singleton:!1};Wr()(ho.Z,fo);const po=ho.Z.locals||{},go={"arrow-right":{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNLjgyMiAxMi44MTFhLjQ0NS40NDUgMCAwIDEtLjMyMi4xMzMuNDU2LjQ1NiAwIDAgMS0uMzIyLS43NzhMNS44NDQgNi41LjE3OC44MzNBLjQ1Ni40NTYgMCAwIDEgLjgyMi4xOWw1Ljk5IDUuOTg5YS40NTYuNDU2IDAgMCAxIDAgLjY0NGwtNS45OSA1Ljk5eiIvPjwvc3ZnPg==",size:[7,13]},pin:{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYuMDEyIDE3Ljk0OWwtMS40OTMtNi4zNzZhMTAuOTM1IDEwLjkzNSAwIDAgMCAyLjk4NS4wMDJMNi4wMTIgMTcuOTV6TTkuMjQ2IDEuODU3YzAgLjUyLS40MTUuOTg1LTEuMDcgMS4zMDhsLS4wMDEgMi42MTZjMS43MjUuNDEgMi45MjIgMS4yOTQgMi45MjIgMi4zMiAwIC40MTYtLjE5NS44MDktLjU0MiAxLjE1Ni0uNjQuNjM5LTEuNzk0IDEuMTI0LTMuMTg3IDEuMzE4LS4wMy4wMDUtLjA2Mi4wMDgtLjA5My4wMTJhOC45MTcgOC45MTcgMCAwIDEtLjcyNS4wNjVsLS4xMi4wMDdhMTAuMTU0IDEwLjE1NCAwIDAgMS0uODk1LS4wMDNjLS4wOTgtLjAwNS0uMTkzLS4wMTMtLjI4OC0uMDItLjA1My0uMDA0LS4xMDYtLjAwNy0uMTU4LS4wMTJhOS4yNDcgOS4yNDcgMCAwIDEtLjM3Mi0uMDQzbC0uMDQ1LS4wMDZDMi41MTQgMTAuMjc4LjkyNiA5LjI4NS45MjYgOC4xMDFjMC0uNDE1LjE5Ni0uODA3LjU0My0xLjE1NC41MTEtLjUxMiAxLjM1Mi0uOTI0IDIuMzgtMS4xNjhWMy4xNjVjLS42NTYtLjMyMy0xLjA3LS43ODktMS4wNy0xLjMwOUMyLjc3OC44ODIgNC4yMjUuMDkyIDYuMDExLjA5MnMzLjIzNC43OSAzLjIzNCAxLjc2NXoiLz48L3N2Zz4=",size:[12,18]}};class bo extends eo{render({className:e}){return Rr("i",{className:qr()(po.icon,e),style:this.style})}get style(){const{name:e,size:t,rotate:n}=this.props,i=go[e];if(!i)throw new TypeError(`Can't find "${e}" icon.`);let[r,o]=i.size;if(t){const e=t/Math.max(r,o);r=Math.min(Math.ceil(r*e),t),o=Math.min(Math.ceil(o*e),t)}return{backgroundImage:`url(${i.src})`,width:`${r}px`,height:`${o}px`,transform:n?`rotate(${n}deg)`:""}}}const vo=parseInt(co.toggleTime);class mo extends g{constructor(...e){super(...e),this.allowHide=!0,this.toggling=!1,this.hideContentTimeout=null,this.width=null,this.state={visible:!0,renderContent:!0},this.handleClick=()=>{this.allowHide=!1},this.handleMouseEnter=()=>{this.toggling||this.props.pinned||(clearTimeout(this.hideTimeoutId),this.toggleVisibility(!0))},this.handleMouseMove=()=>{this.allowHide=!0},this.handleMouseLeave=()=>{!this.allowHide||this.toggling||this.props.pinned||this.toggleVisibility(!1)},this.handleToggleButtonClick=()=>{this.toggleVisibility()},this.handlePinButtonClick=()=>{const e=!this.props.pinned;this.width=e?this.node.getBoundingClientRect().width:null,this.updateNodeWidth(),this.props.onPinStateChange(e)},this.handleResizeStart=e=>{this.resizeInfo={startPageX:e.pageX,initialWidth:this.width},document.body.classList.add("resizing","col"),document.addEventListener("mousemove",this.handleResize,!0),document.addEventListener("mouseup",this.handleResizeEnd,!0)},this.handleResize=e=>{this.width=this.resizeInfo.initialWidth+(e.pageX-this.resizeInfo.startPageX),this.updateNodeWidth()},this.handleResizeEnd=()=>{document.body.classList.remove("resizing","col"),document.removeEventListener("mousemove",this.handleResize,!0),document.removeEventListener("mouseup",this.handleResizeEnd,!0),this.props.onResize()},this.saveNode=e=>this.node=e}componentDidMount(){this.hideTimeoutId=setTimeout((()=>this.toggleVisibility(!1)),3e3)}componentWillUnmount(){clearTimeout(this.hideTimeoutId),clearTimeout(this.hideContentTimeout)}render(){const{position:e,pinned:t,children:n}=this.props,{visible:i,renderContent:r}=this.state,o=qr()({[co.container]:!0,[co.pinned]:t,[co.left]:"left"===e,[co.hidden]:!i,[co.empty]:!r});return Rr("div",{ref:this.saveNode,className:o,onClick:this.handleClick,onMouseLeave:this.handleMouseLeave,children:[i&&Rr(no,{type:"button",title:"Pin",className:co.pinButton,active:t,toggle:!0,onClick:this.handlePinButtonClick,children:Rr(bo,{name:"pin",size:13})}),Rr(no,{type:"button",title:i?"Hide":"Show sidebar",className:co.toggleButton,onClick:this.handleToggleButtonClick,children:Rr(bo,{name:"arrow-right",size:10,rotate:i?180:0})}),t&&i&&Rr("div",{className:co.resizer,onMouseDown:this.handleResizeStart}),Rr("div",{className:co.content,onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,children:r?n:null})]})}toggleVisibility(e){clearTimeout(this.hideContentTimeout);const{visible:t}=this.state,{onToggle:n,pinned:i}=this.props;if(void 0===e)e=!t;else if(e===t)return;this.setState({visible:e}),this.toggling=!0,setTimeout((()=>{this.toggling=!1}),vo),i&&this.updateNodeWidth(e?this.width:null),e||i?(this.setState({renderContent:e}),n(e)):e||(this.hideContentTimeout=setTimeout((()=>{this.hideContentTimeout=null,this.setState({renderContent:!1}),n(!1)}),vo))}updateNodeWidth(e=this.width){this.node.style.width=e?`${e}px`:""}}mo.defaultProps={pinned:!1,position:"left"};var yo=n(396),Co={insert:"head",singleton:!1};Wr()(yo.Z,Co);const wo=yo.Z.locals||{};class _o extends g{constructor(...e){super(...e),this.handleChange=()=>{this.props.onChange(!this.props.checked)}}render(){const{checked:e,className:t,children:n}=this.props;return Rr("label",{className:qr()(wo.label,t),children:[Rr("input",{className:wo.checkbox,type:"checkbox",checked:e,onChange:this.handleChange}),Rr("span",{className:wo.itemText,children:n})]})}}var xo=n(213),Ao={insert:"head",singleton:!1};Wr()(xo.Z,Ao);const So=xo.Z.locals||{};class Mo extends g{constructor(...e){super(...e),this.handleChange=()=>{this.props.onChange(this.props.item)}}render(){return Rr("div",{className:So.item,children:Rr(_o,{...this.props,onChange:this.handleChange,children:this.renderLabel()})})}renderLabel(){const{children:e,item:t}=this.props;return e?e(t):t===ko.ALL_ITEM?"All":t.label}}const To=Symbol("ALL_ITEM");class ko extends eo{constructor(e){super(e),this.handleToggleAllCheck=()=>{const e=this.isAllChecked()?[]:this.props.items;this.setState({checkedItems:e}),this.informAboutChange(e)},this.handleItemCheck=e=>{let t;t=this.isItemChecked(e)?this.state.checkedItems.filter((t=>t!==e)):[...this.state.checkedItems,e],this.setState({checkedItems:t}),this.informAboutChange(t)},this.state={checkedItems:e.checkedItems||e.items}}componentWillReceiveProps(e){if(e.items!==this.props.items){if(this.isAllChecked())this.setState({checkedItems:e.items}),this.informAboutChange(e.items);else if(this.state.checkedItems.length){const t=e.items.filter((e=>this.state.checkedItems.find((t=>t.label===e.label))));this.setState({checkedItems:t}),this.informAboutChange(t)}}else e.checkedItems!==this.props.checkedItems&&this.setState({checkedItems:e.checkedItems})}render(){const{label:e,items:t,renderLabel:n}=this.props;return Rr("div",{className:So.container,children:[Rr("div",{className:So.label,children:[e,":"]}),Rr("div",{children:[Rr(Mo,{item:To,checked:this.isAllChecked(),onChange:this.handleToggleAllCheck,children:n}),t.map((e=>Rr(Mo,{item:e,checked:this.isItemChecked(e),onChange:this.handleItemCheck,children:n},e.label)))]})]})}isItemChecked(e){return this.state.checkedItems.includes(e)}isAllChecked(){return this.props.items.length===this.state.checkedItems.length}informAboutChange(e){setTimeout((()=>this.props.onChange(e)))}}ko.ALL_ITEM=To;var zo=n(270),Do={insert:"head",singleton:!1};Wr()(zo.Z,Do);const Bo=zo.Z.locals||{};function Lo(){return!1}function Eo({children:e,disabled:t,onClick:n}){return Rr("li",{className:qr()({[Bo.item]:!0,[Bo.disabled]:t}),onClick:t?Lo:n,children:e})}var jo=n(580),Oo={insert:"head",singleton:!1};Wr()(jo.Z,Oo);const Io=jo.Z.locals||{};class No extends eo{constructor(...e){super(...e),this.handleClickHideChunk=()=>{const{chunk:e}=this.props;if(e&&e.label){const t=Rn.selectedChunks.filter((t=>t.label!==e.label));Rn.selectedChunks=t}this.hide()},this.handleClickFilterToChunk=()=>{const{chunk:e}=this.props;if(e&&e.label){const t=Rn.allChunks.filter((t=>t.label===e.label));Rn.selectedChunks=t}this.hide()},this.handleClickShowAllChunks=()=>{Rn.selectedChunks=Rn.allChunks,this.hide()},this.handleDocumentMousedown=e=>{var t,n;e.ctrlKey||2===e.button||(t=e.target,n=this.node,t===n||n.contains(t))||(e.preventDefault(),e.stopPropagation(),this.hide())},this.saveNode=e=>this.node=e}componentDidMount(){this.boundingRect=this.node.getBoundingClientRect()}componentDidUpdate(e){this.props.visible&&!e.visible?document.addEventListener("mousedown",this.handleDocumentMousedown,!0):e.visible&&!this.props.visible&&document.removeEventListener("mousedown",this.handleDocumentMousedown,!0)}render(){const{visible:e}=this.props,t=qr()({[Io.container]:!0,[Io.hidden]:!e}),n=Rn.selectedChunks.length>1;return Rr("ul",{ref:this.saveNode,className:t,style:this.getStyle(),children:[Rr(Eo,{disabled:!n,onClick:this.handleClickHideChunk,children:"Hide chunk"}),Rr(Eo,{disabled:!n,onClick:this.handleClickFilterToChunk,children:"Hide all other chunks"}),Rr("hr",{}),Rr(Eo,{disabled:Rn.allChunksSelected,onClick:this.handleClickShowAllChunks,children:"Show all chunks"})]})}hide(){this.props.onHide&&this.props.onHide()}getStyle(){const{boundingRect:e}=this;if(!e)return;const{coords:t}=this.props,n={left:t.x,top:t.y};return n.left+e.width>window.innerWidth&&(n.left=window.innerWidth-e.width),n.top+e.height>window.innerHeight&&(n.top=t.y-e.height),n}}var Po=n(393),Fo={insert:"head",singleton:!1};Wr()(Po.Z,Fo);const Ro=Po.Z.locals||{};var Go=n(296),Ho=n.n(Go),Uo=n(976),qo={insert:"head",singleton:!1};Wr()(Uo.Z,qo);const Vo=Uo.Z.locals||{};class Wo extends eo{constructor(...e){super(...e),this.handleValueChange=Ho()((e=>{this.informChange(e.target.value)}),400),this.handleInputBlur=()=>{this.handleValueChange.flush()},this.handleClearClick=()=>{this.clear(),this.focus()},this.handleKeyDown=e=>{let t=!0;switch(e.key){case"Escape":this.clear();break;case"Enter":this.handleValueChange.flush();break;default:t=!1}t&&e.stopPropagation()},this.saveInputNode=e=>this.input=e}componentDidMount(){this.props.autofocus&&this.focus()}componentWillUnmount(){this.handleValueChange.clear()}render(){const{label:e,query:t}=this.props;return Rr("div",{className:Vo.container,children:[Rr("div",{className:Vo.label,children:[e,":"]}),Rr("div",{className:Vo.row,children:[Rr("input",{ref:this.saveInputNode,className:Vo.input,type:"text",value:t,placeholder:"Enter regexp",onInput:this.handleValueChange,onBlur:this.handleInputBlur,onKeyDown:this.handleKeyDown}),Rr(no,{className:Vo.clear,onClick:this.handleClearClick,children:"x"})]})]})}focus(){this.input&&this.input.focus()}clear(){this.handleValueChange.clear(),this.informChange(""),this.input.value=""}informChange(e){this.props.onQueryChange(e)}}var Zo=n(784),$o={insert:"head",singleton:!1};Wr()(Zo.Z,$o);const Ko=Zo.Z.locals||{};var Yo=n(150),Jo=n.n(Yo),Xo="".replace,Qo=/[&<>'"]/g,ea={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"};function ta(e){return Xo.call(e,Qo,na)}function na(e){return ea[e]}var ia=n(697),ra={insert:"head",singleton:!1};Wr()(ia.Z,ra);const oa=ia.Z.locals||{};class aa extends eo{constructor(...e){super(...e),this.state={visible:!0},this.handleClick=()=>this.props.onClick(this.props.module),this.handleMouseEnter=()=>{this.props.isVisible&&this.setState({visible:this.isVisible})}}render({module:e,showSize:t}){const n=!this.state.visible;return Rr("div",{className:qr()(oa.container,oa[this.itemType],{[oa.invisible]:n}),title:n?this.invisibleHint:null,onClick:this.handleClick,onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,children:[Rr("span",{dangerouslySetInnerHTML:{__html:this.titleHtml}}),t&&[" (",Rr("strong",{children:Vn()(e[t])}),")"]]})}get itemType(){const{module:e}=this.props;return e.path?e.groups?"folder":"module":"chunk"}get titleHtml(){let e;const{module:t}=this.props,n=t.path||t.label,i=this.props.highlightedText;if(i){const t=i instanceof RegExp?new RegExp(i.source,"igu"):new RegExp(`(?:${Jo()(i)})+`,"iu");let r,o;do{o=r,r=t.exec(n)}while(r);o&&(e=ta(n.slice(0,o.index))+`<strong>${ta(o[0])}</strong>`+ta(n.slice(o.index+o[0].length)))}return e||(e=ta(n)),e}get invisibleHint(){return`${this.itemType.charAt(0).toUpperCase()+this.itemType.slice(1)} is not rendered in the treemap because it's too small.`}get isVisible(){const{isVisible:e}=this.props;return!e||e(this.props.module)}}class sa extends eo{constructor(...e){super(...e),this.handleModuleClick=e=>this.props.onModuleClick(e)}render({modules:e,showSize:t,highlightedText:n,isModuleVisible:i,className:r}){return Rr("div",{className:qr()(Ko.container,r),children:e.map((e=>Rr(aa,{module:e,showSize:t,highlightedText:n,isVisible:i,onClick:this.handleModuleClick},e.cid)))})}}var ua=n(172),la={insert:"head",singleton:!1};Wr()(ua.Z,la);const ca=ua.Z.locals||{};class ha extends eo{constructor(...e){super(...e),this.input={current:null},this.state={query:"",showOptions:!1},this.handleClickOutside=e=>{const t=this.input.current;t&&e&&!t.contains(e.target)&&(this.setState({showOptions:!1}),this.state.query&&!this.props.options.some((e=>e===this.state.query))&&(this.setState({query:""}),this.props.onSelectionChange(void 0)))},this.handleInput=e=>{const{value:t}=e.target;this.setState({query:t}),t||this.props.onSelectionChange(void 0)},this.handleFocus=()=>{this.input.current.value=this.state.query,this.setState({showOptions:!0})},this.getOptionClickHandler=e=>()=>{this.props.onSelectionChange(e),this.setState({query:e,showOptions:!1})}}componentDidMount(){document.addEventListener("click",this.handleClickOutside,!0)}componentWillUnmount(){document.removeEventListener("click",this.handleClickOutside,!0)}render(){const{label:e,options:t}=this.props,n=this.state.query?t.filter((e=>e.toLowerCase().includes(this.state.query.toLowerCase()))):t;return Rr("div",{className:ca.container,children:[Rr("div",{className:ca.label,children:[e,":"]}),Rr("div",{children:[Rr("input",{ref:this.input,className:ca.input,type:"text",value:this.state.query,onInput:this.handleInput,onFocus:this.handleFocus}),this.state.showOptions?Rr("div",{className:ca.options,children:n.map((e=>Rr("div",{className:ca.option,onClick:this.getOptionClickHandler(e),children:e},e)))}):null]})]})}}var fa,da;const pa=[{label:"Stat",prop:"statSize"},{label:"Parsed",prop:"parsedSize"},{label:"Gzipped",prop:"gzipSize"}];let ga=Pr((P((da=class extends g{constructor(...e){super(...e),this.mouseCoords={x:0,y:0},this.state={selectedChunk:null,selectedMouseCoords:{x:0,y:0},sidebarPinned:!1,showChunkContextMenu:!1,showTooltip:!1,tooltipContent:null},this.renderChunkItemLabel=e=>{const t=e===ko.ALL_ITEM,n=t?"All":e.label,i=t?Rn.totalChunksSize:e[Rn.activeSize];return[`${n} (`,Rr("strong",{children:Vn()(i)}),")"]},this.handleSelectionChange=e=>{Rn.selectedChunks=e?Rn.allChunks.filter((t=>t.isInitialByEntrypoint[e]??!1)):Rn.allChunks},this.handleConcatenatedModulesContentToggle=e=>{Rn.showConcatenatedModulesContent=e,e?Dn.setItem("showConcatenatedModulesContent",!0):Dn.removeItem("showConcatenatedModulesContent")},this.handleChunkContextMenuHide=()=>{this.setState({showChunkContextMenu:!1})},this.handleResize=()=>{this.state.showChunkContextMenu&&this.setState({showChunkContextMenu:!1})},this.handleSidebarToggle=()=>{this.state.sidebarPinned&&setTimeout((()=>this.treemap.resize()))},this.handleSidebarPinStateChange=e=>{this.setState({sidebarPinned:e}),setTimeout((()=>this.treemap.resize()))},this.handleSidebarResize=()=>{this.treemap.resize()},this.handleSizeSwitch=e=>{Rn.selectedSize=e.prop},this.handleQueryChange=e=>{Rn.searchQuery=e},this.handleSelectedChunksChange=e=>{Rn.selectedChunks=e},this.handleMouseLeaveTreemap=()=>{this.setState({showTooltip:!1})},this.handleTreemapGroupSecondaryClick=e=>{const{group:t}=e;t&&t.isAsset?this.setState({selectedChunk:t,selectedMouseCoords:{...this.mouseCoords},showChunkContextMenu:!0}):this.setState({selectedChunk:null,showChunkContextMenu:!1})},this.handleTreemapGroupHover=e=>{const{group:t}=e;t?this.setState({showTooltip:!0,tooltipContent:this.getTooltipContent(t)}):this.setState({showTooltip:!1})},this.handleFoundModuleClick=e=>this.treemap.zoomToGroup(e),this.handleMouseMove=e=>{Object.assign(this.mouseCoords,{x:e.pageX,y:e.pageY})},this.isModuleVisible=e=>this.treemap.isGroupRendered(e),this.saveTreemapRef=e=>this.treemap=e}componentDidMount(){document.addEventListener("mousemove",this.handleMouseMove,!0)}componentWillUnmount(){document.removeEventListener("mousemove",this.handleMouseMove,!0)}render(){const{selectedChunk:e,selectedMouseCoords:t,sidebarPinned:n,showChunkContextMenu:i,showTooltip:r,tooltipContent:o}=this.state;return Rr("div",{className:Ro.container,children:[Rr(mo,{pinned:n,onToggle:this.handleSidebarToggle,onPinStateChange:this.handleSidebarPinStateChange,onResize:this.handleSidebarResize,children:[Rr("div",{className:Ro.sidebarGroup,children:[Rr(so,{label:"Treemap sizes",items:this.sizeSwitchItems,activeItem:this.activeSizeItem,onSwitch:this.handleSizeSwitch}),Rn.hasConcatenatedModules&&Rr("div",{className:Ro.showOption,children:Rr(_o,{checked:Rn.showConcatenatedModulesContent,onChange:this.handleConcatenatedModulesContentToggle,children:"Show content of concatenated modules"+("statSize"===Rn.activeSize?"":" (inaccurate)")})})]}),Rr("div",{className:Ro.sidebarGroup,children:Rr(ha,{label:"Filter to initial chunks",options:Rn.entrypoints,onSelectionChange:this.handleSelectionChange})}),Rr("div",{className:Ro.sidebarGroup,children:[Rr(Wo,{label:"Search modules",query:Rn.searchQuery,autofocus:!0,onQueryChange:this.handleQueryChange}),Rr("div",{className:Ro.foundModulesInfo,children:this.foundModulesInfo}),Rn.isSearching&&Rn.hasFoundModules&&Rr("div",{className:Ro.foundModulesContainer,children:Rn.foundModulesByChunk.map((({chunk:e,modules:t})=>Rr("div",{className:Ro.foundModulesChunk,children:[Rr("div",{className:Ro.foundModulesChunkName,onClick:()=>this.treemap.zoomToGroup(e),children:e.label}),Rr(sa,{className:Ro.foundModulesList,modules:t,showSize:Rn.activeSize,highlightedText:Rn.searchQueryRegexp,isModuleVisible:this.isModuleVisible,onModuleClick:this.handleFoundModuleClick})]},e.cid)))})]}),this.chunkItems.length>1&&Rr("div",{className:Ro.sidebarGroup,children:Rr(ko,{label:"Show chunks",items:this.chunkItems,checkedItems:Rn.selectedChunks,renderLabel:this.renderChunkItemLabel,onChange:this.handleSelectedChunksChange})})]}),Rr(Gr,{ref:this.saveTreemapRef,className:Ro.map,data:Rn.visibleChunks,highlightGroups:this.highlightedModules,weightProp:Rn.activeSize,onMouseLeave:this.handleMouseLeaveTreemap,onGroupHover:this.handleTreemapGroupHover,onGroupSecondaryClick:this.handleTreemapGroupSecondaryClick,onResize:this.handleResize}),Rr(Yr,{visible:r,children:o}),Rr(No,{visible:i,chunk:e,coords:t,onHide:this.handleChunkContextMenuHide})]})}renderModuleSize(e,t){const n=`${t}Size`,i=e[n],r=pa.find((e=>e.prop===n)).label,o=Rn.activeSize===n;return"number"==typeof i?Rr("div",{className:o?Ro.activeSize:"",children:[r," size: ",Rr("strong",{children:Vn()(i)})]}):null}get sizeSwitchItems(){return Rn.hasParsedSizes?pa:pa.slice(0,1)}get activeSizeItem(){return this.sizeSwitchItems.find((e=>e.prop===Rn.activeSize))}get chunkItems(){const{allChunks:e,activeSize:t}=Rn;let n=[...e];return"statSize"!==t&&(n=n.filter(kn)),n.sort(((e,n)=>n[t]-e[t])),n}get highlightedModules(){return new Set(Rn.foundModules)}get foundModulesInfo(){return Rn.isSearching?Rn.hasFoundModules?[Rr("div",{className:Ro.foundModulesInfoItem,children:["Count: ",Rr("strong",{children:Rn.foundModules.length})]}),Rr("div",{className:Ro.foundModulesInfoItem,children:["Total size: ",Rr("strong",{children:Vn()(Rn.foundModulesSize)})]})]:"Nothing found"+(Rn.allChunksSelected?"":" in selected chunks"):" "}getTooltipContent(e){return e?Rr("div",{children:[Rr("div",{children:Rr("strong",{children:e.label})}),Rr("br",{}),this.renderModuleSize(e,"stat"),!e.inaccurateSizes&&this.renderModuleSize(e,"parsed"),!e.inaccurateSizes&&this.renderModuleSize(e,"gzip"),e.path&&Rr("div",{children:["Path: ",Rr("strong",{children:e.path})]}),e.isAsset&&Rr("div",{children:[Rr("br",{}),Rr("strong",{children:Rr("em",{children:"Right-click to view options related to this chunk"})})]})]}):null}}).prototype,"sizeSwitchItems",[De],Object.getOwnPropertyDescriptor(da.prototype,"sizeSwitchItems"),da.prototype),P(da.prototype,"activeSizeItem",[De],Object.getOwnPropertyDescriptor(da.prototype,"activeSizeItem"),da.prototype),P(da.prototype,"chunkItems",[De],Object.getOwnPropertyDescriptor(da.prototype,"chunkItems"),da.prototype),P(da.prototype,"highlightedModules",[De],Object.getOwnPropertyDescriptor(da.prototype,"highlightedModules"),da.prototype),P(da.prototype,"foundModulesInfo",[De],Object.getOwnPropertyDescriptor(da.prototype,"foundModulesInfo"),da.prototype),fa=da))||fa;var ba=n(194),va={insert:"head",singleton:!1};Wr()(ba.Z,va);ba.Z.locals;let ma;try{window.enableWebSocket&&(ma=new WebSocket(`ws://${location.host}`))}catch(ya){console.warn("Couldn't connect to analyzer websocket server so you'll have to reload page manually to see updates in the treemap")}window.addEventListener("load",(()=>{Rn.defaultSize=`${window.defaultSizes}Size`,Rn.setModules(window.chartData),Rn.setEntrypoints(window.entrypoints),j(Rr(ga,{}),document.getElementById("app")),ma&&ma.addEventListener("message",(e=>{const t=JSON.parse(e.data);"chartDataUpdated"===t.event&&Rn.setModules(t.data)}))}),!1)})()})();
//# sourceMappingURL=viewer.js.map