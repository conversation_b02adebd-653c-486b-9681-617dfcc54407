{"name": "optimism", "version": "0.18.1", "author": "<PERSON> <<EMAIL>>", "description": "Composable reactive caching with efficient invalidation.", "keywords": ["caching", "cache", "invalidation", "reactive", "reactivity", "dependency", "tracking", "tracker", "memoization"], "type": "module", "main": "lib/bundle.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "license": "MIT", "homepage": "https://github.com/benjamn/optimism#readme", "repository": {"type": "git", "url": "git+https://github.com/benjamn/optimism.git"}, "bugs": {"url": "https://github.com/benjamn/optimism/issues"}, "scripts": {"build": "npm run clean && npm run tsc:es5 && tsc && rollup -c && rimraf lib/es5", "tsc:es5": "tsc -p tsconfig.es5.json", "clean": "<PERSON><PERSON><PERSON> lib", "prepare": "npm run build", "mocha": "mocha --require source-map-support/register --reporter spec --full-trace", "test:cjs": "npm run mocha -- lib/tests/bundle.cjs", "test:esm": "npm run mocha -- lib/tests/bundle.js", "test": "npm run test:esm && npm run test:cjs"}, "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "^20.2.5", "@wry/equality": "^0.5.7", "mocha": "^10.2.0", "rimraf": "^5.0.0", "rollup": "^3.20.0", "source-map-support": "^0.5.19", "typescript": "^5.0.2"}, "dependencies": {"@wry/caches": "^1.0.0", "@wry/context": "^0.7.0", "@wry/trie": "^0.5.0", "tslib": "^2.3.0"}}